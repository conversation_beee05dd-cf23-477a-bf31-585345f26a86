#!/usr/bin/env python3
"""
AI-Powered Ticket Converter
Converts raw ticket text into structured format with intelligent component/label detection.
"""

import json
import re
import subprocess
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import argparse

class AITicketConverter:
    """AI-powered ticket text converter with codebase analysis"""
    
    def __init__(self, repo_path: str = "/Users/<USER>/dev/rma-mono"):
        self.repo_path = Path(repo_path)
        
        # Common patterns for ticket parsing
        self.ticket_id_patterns = [
            r'([A-Z]+\d+-\d+)',  # CMS20-1166
            r'([A-Z]+-\d+)',     # CMS-1166  
            r'([A-Z]+_\d+)',     # CMS_1166
        ]
        
        # Priority keywords
        self.priority_keywords = {
            'critical': ['critical', 'urgent', 'emergency', 'hotfix', 'blocker'],
            'high': ['high', 'important', 'asap', 'priority', 'soon'],
            'medium': ['medium', 'normal', 'standard'],
            'low': ['low', 'minor', 'nice-to-have', 'future', 'enhancement']
        }
        
        # Issue type keywords
        self.issue_type_keywords = {
            'Story': ['feature', 'story', 'new', 'implement', 'add', 'create'],
            'Bug': ['bug', 'fix', 'error', 'issue', 'problem', 'broken'],
            'Task': ['task', 'update', 'refactor', 'improve', 'optimize'],
            'Epic': ['epic', 'major', 'initiative', 'project']
        }
    
    def convert_ticket_text(self, raw_text: str, ticket_id: str = None, 
                           analyze_codebase: bool = True) -> str:
        """
        Convert raw ticket text to structured format
        
        Args:
            raw_text: Raw ticket description text
            ticket_id: Optional ticket ID (will be extracted if not provided)
            analyze_codebase: Whether to analyze codebase for components/labels
        """
        
        print(f"🎯 Converting ticket text to structured format...")
        
        # Step 1: Parse basic ticket information
        ticket_info = self._parse_ticket_text(raw_text, ticket_id)
        
        # Step 2: Analyze codebase for intelligent components/labels
        if analyze_codebase:
            print(f"🔍 Analyzing codebase for intelligent component detection...")
            codebase_info = self._analyze_codebase_with_claude(raw_text)
            ticket_info.update(codebase_info)
        
        # Step 3: Generate structured ticket
        structured_ticket = self._generate_structured_ticket(ticket_info, raw_text)
        
        return structured_ticket
    
    def _parse_ticket_text(self, text: str, ticket_id: str = None) -> Dict:
        """Parse basic information from raw ticket text"""
        
        # Extract ticket ID if not provided
        if not ticket_id:
            for pattern in self.ticket_id_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    ticket_id = match.group(1).upper()
                    break
        
        if not ticket_id:
            ticket_id = "UNKNOWN-001"
        
        # Extract title (first line or sentence)
        lines = text.strip().split('\n')
        title = lines[0].strip() if lines else "Auto-generated Ticket"
        
        # Clean title
        title = re.sub(r'^[#\*\-\s]*', '', title)  # Remove markdown prefixes
        title = title[:100] if len(title) > 100 else title  # Limit length
        
        # Determine priority
        priority = self._extract_priority(text)
        
        # Determine issue type
        issue_type = self._extract_issue_type(text)
        
        # Extract acceptance criteria
        acceptance_criteria = self._extract_acceptance_criteria(text)
        
        return {
            'ticket_id': ticket_id,
            'summary': title,
            'issue_type': issue_type,
            'priority': priority,
            'acceptance_criteria': acceptance_criteria,
            'description': text.strip()
        }
    
    def _extract_priority(self, text: str) -> str:
        """Extract priority from text content"""
        text_lower = text.lower()
        
        for priority, keywords in self.priority_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return priority.capitalize()
        
        return "Medium"  # Default
    
    def _extract_issue_type(self, text: str) -> str:
        """Extract issue type from text content"""
        text_lower = text.lower()
        
        for issue_type, keywords in self.issue_type_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return issue_type
        
        return "Story"  # Default
    
    def _extract_acceptance_criteria(self, text: str) -> List[str]:
        """Extract acceptance criteria from text"""
        criteria = []
        
        # Look for numbered lists
        numbered_items = re.findall(r'^\s*\d+\.\s*(.+?)(?=\n\s*\d+\.|\n\n|\Z)', text, re.MULTILINE | re.DOTALL)
        for item in numbered_items:
            cleaned = item.strip().replace('\n', ' ')
            if len(cleaned) > 10:  # Filter out very short items
                criteria.append(cleaned)
        
        # Look for bullet points
        if not criteria:
            bullet_items = re.findall(r'^\s*[-\*\+]\s*(.+?)(?=\n\s*[-\*\+]|\n\n|\Z)', text, re.MULTILINE | re.DOTALL)
            for item in bullet_items:
                cleaned = item.strip().replace('\n', ' ')
                if len(cleaned) > 10:
                    criteria.append(cleaned)
        
        # If no structured criteria found, generate from content
        if not criteria:
            criteria = self._generate_default_criteria(text)
        
        return criteria[:8]  # Limit to 8 criteria
    
    def _generate_default_criteria(self, text: str) -> List[str]:
        """Generate default acceptance criteria from text content"""
        
        # Extract key requirements from text
        sentences = re.split(r'[.!?]+', text)
        criteria = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            # Look for requirement indicators
            if any(indicator in sentence.lower() for indicator in ['soll', 'muss', 'wird', 'should', 'must', 'need']):
                if len(sentence) > 20 and len(sentence) < 200:
                    criteria.append(sentence)
        
        # Add default criteria if none found
        if not criteria:
            criteria = [
                "Implementation should work correctly",
                "Code should follow coding standards",
                "Tests should be included",
                "Documentation should be updated"
            ]
        
        return criteria[:5]
    
    def _analyze_codebase_with_claude(self, ticket_text: str) -> Dict:
        """Use Claude Code to analyze codebase and determine components/labels"""
        
        try:
            # Create analysis prompt for Claude
            prompt = f"""
Analyze this ticket description and the current codebase to determine appropriate components and labels:

TICKET DESCRIPTION:
{ticket_text}

TASK:
Based on the ticket content and codebase analysis, determine:

1. **Components** - Which parts of the codebase are affected?
   - Look at directory structure, service names, modules
   - Common examples: content-service, widgets, api, frontend, backend
   
2. **Labels** - Descriptive tags for categorization
   - Technical labels: backend, frontend, database, api
   - Domain labels: widgets, articles, content, user-management
   - Priority labels: enhancement, bugfix, feature

ANALYSIS APPROACH:
1. Examine the codebase structure with `find` or `ls` commands
2. Look for relevant service directories, modules, or components mentioned in ticket
3. Identify the technical domain (frontend/backend/api/database)
4. Suggest appropriate labels based on functionality

OUTPUT FORMAT:
Provide a JSON response with:
```json
{{
  "components": ["component1", "component2"],
  "labels": ["label1", "label2", "label3"],
  "technical_analysis": "Brief explanation of component/label choices"
}}
```

IMPORTANT: Keep components to 1-3 items and labels to 3-5 items. Be specific and relevant.
"""
            
            # Execute Claude Code analysis
            cmd = [
                "claude", 
                "-p", prompt,
                "--output-format", "text",
                "--max-turns", "3"
            ]
            
            result = subprocess.run(
                cmd,
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                timeout=120  # 2 minutes
            )
            
            if result.returncode == 0:
                response = result.stdout.strip()
                
                # Extract JSON from response
                json_match = re.search(r'```json\s*(\{.*?\})\s*```', response, re.DOTALL)
                if json_match:
                    try:
                        analysis = json.loads(json_match.group(1))
                        
                        print(f"✅ Claude analysis completed:")
                        print(f"   Components: {analysis.get('components', [])}")
                        print(f"   Labels: {analysis.get('labels', [])}")
                        
                        return {
                            'components': analysis.get('components', []),
                            'labels': analysis.get('labels', []),
                            'analysis_note': analysis.get('technical_analysis', '')
                        }
                    except json.JSONDecodeError:
                        print(f"⚠️  Could not parse Claude analysis JSON")
                
                # Fallback: try to extract from text
                return self._extract_components_from_text(response, ticket_text)
            
        except Exception as e:
            print(f"⚠️  Claude codebase analysis failed: {e}")
        
        # Fallback to rule-based analysis
        return self._rule_based_component_analysis(ticket_text)
    
    def _extract_components_from_text(self, claude_response: str, ticket_text: str) -> Dict:
        """Extract components and labels from Claude's text response"""
        
        components = []
        labels = []
        
        # Look for component mentions in response
        component_keywords = ['service', 'module', 'component', 'app', 'api']
        for keyword in component_keywords:
            matches = re.findall(rf'{keyword}[:\s]+([a-z-]+(?:-[a-z]+)*)', claude_response, re.IGNORECASE)
            components.extend(matches)
        
        # Look for label mentions
        label_keywords = ['label', 'tag', 'category']
        for keyword in label_keywords:
            matches = re.findall(rf'{keyword}[:\s]+([a-z-]+(?:-[a-z]+)*)', claude_response, re.IGNORECASE)
            labels.extend(matches)
        
        # Add fallback based on ticket content
        if not components:
            components = self._guess_components_from_ticket(ticket_text)
        
        if not labels:
            labels = self._guess_labels_from_ticket(ticket_text)
        
        return {
            'components': list(set(components))[:3],  # Unique, max 3
            'labels': list(set(labels))[:5],          # Unique, max 5
            'analysis_note': 'Extracted from Claude response'
        }
    
    def _rule_based_component_analysis(self, ticket_text: str) -> Dict:
        """Fallback rule-based component and label analysis"""
        
        text_lower = ticket_text.lower()
        
        # Component mapping based on keywords
        component_map = {
            'articleupdate-service': ['article', 'widget', 'update', 'refresh'],
            'content-service': ['content', 'cms', 'article'],
            'api': ['api', 'endpoint', 'service'],
            'frontend': ['ui', 'frontend', 'interface', 'user'],
            'widgets': ['widget', 'component', 'element'],
            'database': ['database', 'data', 'db', 'query']
        }
        
        components = []
        for component, keywords in component_map.items():
            if any(keyword in text_lower for keyword in keywords):
                components.append(component)
        
        # Label mapping based on keywords
        label_map = {
            'auto-refresh': ['refresh', 'update', 'automatic'],
            'widgets': ['widget', 'component'],
            'backend': ['service', 'api', 'logic'],
            'scheduled': ['daily', 'cron', 'schedule', 'time'],
            'content': ['article', 'content', 'cms'],
            'enhancement': ['new', 'add', 'implement', 'feature']
        }
        
        labels = []
        for label, keywords in label_map.items():
            if any(keyword in text_lower for keyword in keywords):
                labels.append(label)
        
        return {
            'components': components[:3] if components else ['general'],
            'labels': labels[:5] if labels else ['enhancement'],
            'analysis_note': 'Rule-based analysis (Claude analysis unavailable)'
        }
    
    def _guess_components_from_ticket(self, text: str) -> List[str]:
        """Guess components from ticket text"""
        text_lower = text.lower()
        
        if 'widget' in text_lower or 'article' in text_lower:
            return ['articleupdate-service', 'widgets']
        elif 'api' in text_lower:
            return ['api']
        elif 'frontend' in text_lower or 'ui' in text_lower:
            return ['frontend']
        else:
            return ['general']
    
    def _guess_labels_from_ticket(self, text: str) -> List[str]:
        """Guess labels from ticket text"""
        text_lower = text.lower()
        labels = []
        
        if 'widget' in text_lower:
            labels.append('widgets')
        if 'refresh' in text_lower or 'update' in text_lower:
            labels.append('auto-refresh')
        if 'daily' in text_lower or 'schedule' in text_lower:
            labels.append('scheduled')
        if 'service' in text_lower:
            labels.append('backend')
        
        return labels if labels else ['enhancement']
    
    def _generate_structured_ticket(self, ticket_info: Dict, raw_text: str) -> str:
        """Generate the final structured ticket format"""
        
        # Prepare acceptance criteria for YAML
        ac_yaml = []
        for criterion in ticket_info.get('acceptance_criteria', []):
            ac_yaml.append(f'  - "{criterion}"')
        ac_yaml_str = '\n'.join(ac_yaml) if ac_yaml else '  - "Implementation should work correctly"'
        
        # Prepare components for YAML
        components = ticket_info.get('components', ['general'])
        components_yaml = json.dumps(components)
        
        # Prepare labels for YAML
        labels = ticket_info.get('labels', ['enhancement'])
        labels_yaml = json.dumps(labels)
        
        # Split raw text into sections
        text_lines = raw_text.strip().split('\n')
        
        # Find main description (usually first paragraph)
        description_lines = []
        background_lines = []
        in_background = False
        
        for line in text_lines[1:]:  # Skip title line
            line = line.strip()
            if not line:
                continue
            
            if any(keyword in line.lower() for keyword in ['hintergrund', 'background', 'context']):
                in_background = True
                continue
            
            if in_background:
                background_lines.append(line)
            else:
                description_lines.append(line)
        
        main_description = ' '.join(description_lines)  # Use full description
        background_text = ' '.join(background_lines) if background_lines else ""
        
        # Generate structured ticket
        structured_ticket = f"""---
ticket_id: {ticket_info['ticket_id']}
summary: {ticket_info['summary']}
issue_type: {ticket_info['issue_type']}
status: In Progress
priority: {ticket_info['priority']}
assignee: Developer
components: {components_yaml}
labels: {labels_yaml}
acceptance_criteria:
{ac_yaml_str}
---

# {ticket_info['summary']}

{main_description}

{f"## Hintergrund" if background_text else ""}
{background_text}

## Acceptance Criteria

"""
        
        # Add numbered acceptance criteria
        for i, criterion in enumerate(ticket_info.get('acceptance_criteria', []), 1):
            structured_ticket += f"{i}. **{self._extract_criterion_title(criterion)}**: {criterion}\n"
        
        # Add analysis note if available
        if ticket_info.get('analysis_note'):
            structured_ticket += f"""
## Technical Analysis

{ticket_info['analysis_note']}
"""
        
        structured_ticket += f"""
---

*Auto-generated by AI Ticket Converter on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return structured_ticket
    
    def _extract_criterion_title(self, criterion: str) -> str:
        """Extract a short title from acceptance criterion"""
        
        # Look for common patterns
        if 'widget' in criterion.lower() and 'pattern' in criterion.lower():
            return "Widget Detection"
        elif 'täglich' in criterion.lower() or 'daily' in criterion.lower():
            return "Daily Update"
        elif 'timestamp' in criterion.lower() or 'aktualisiert' in criterion.lower():
            return "Timestamp Update"
        elif 'muster' in criterion.lower() or 'pattern' in criterion.lower():
            return "Pattern Consistency"
        elif 'zeit' in criterion.lower() or 'timing' in criterion.lower():
            return "Flexible Timing"
        else:
            # Extract first 2-3 words
            words = criterion.split()[:3]
            return ' '.join(words).strip('.,!?:')


def main():
    """CLI interface for ticket converter"""
    parser = argparse.ArgumentParser(
        description='AI-Powered Ticket Converter',
        epilog="""
Examples:
  # Convert from text file
  python ticket_converter.py --input ticket.txt --output CMS20-1166.md
  
  # Convert from stdin with ticket ID
  echo "Autorefresh von Luftqualität..." | python ticket_converter.py --ticket-id CMS20-1166
  
  # Convert with codebase analysis
  python ticket_converter.py --input raw_ticket.txt --analyze-codebase
  
  # Convert without Claude analysis (faster)
  python ticket_converter.py --input ticket.txt --no-analysis
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--input', '-i', help='Input file with raw ticket text')
    parser.add_argument('--output', '-o', help='Output file for structured ticket')
    parser.add_argument('--ticket-id', '-t', help='Ticket ID (if not auto-detected)')
    parser.add_argument('--repo', '-r', default='/Users/<USER>/dev/rma-mono', help='Repository path')
    parser.add_argument('--analyze-codebase', action='store_true', default=True, help='Analyze codebase with Claude (default)')
    parser.add_argument('--no-analysis', action='store_true', help='Skip Claude codebase analysis')
    
    # Allow reading from stdin if no input file
    parser.add_argument('text', nargs='?', help='Raw ticket text (alternative to --input)')
    
    args = parser.parse_args()
    
    # Initialize converter early to access patterns
    converter = AITicketConverter(args.repo)
    
    # Get input text
    if args.input:
        try:
            with open(args.input, 'r', encoding='utf-8') as f:
                raw_text = f.read()
            
            # Extract ticket_id from filename if not provided
            if not args.ticket_id:
                input_path = Path(args.input)
                filename = input_path.stem  # filename without extension
                
                # Try to extract ticket ID from filename
                for pattern in converter.ticket_id_patterns:
                    match = re.search(pattern, filename, re.IGNORECASE)
                    if match:
                        args.ticket_id = match.group(1).upper()
                        print(f"🎯 Extracted ticket ID from filename: {args.ticket_id}")
                        break
                        
        except Exception as e:
            print(f"❌ Could not read input file: {e}")
            sys.exit(1)
    elif args.text:
        raw_text = args.text
    else:
        # Read from stdin
        print("📝 Enter ticket text (Ctrl+D to finish):")
        raw_text = sys.stdin.read()
    
    if not raw_text.strip():
        print("❌ No input text provided")
        sys.exit(1)
    
    # Convert ticket
    analyze_codebase = args.analyze_codebase and not args.no_analysis
    
    try:
        structured_ticket = converter.convert_ticket_text(
            raw_text, 
            args.ticket_id,
            analyze_codebase
        )
        
        # Output result
        if args.output:
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(structured_ticket)
                print(f"✅ Structured ticket saved to: {args.output}")
            except Exception as e:
                print(f"❌ Could not write output file: {e}")
                print("\n📄 Structured ticket content:")
                print(structured_ticket)
        else:
            print("\n📄 Structured ticket:")
            print("=" * 60)
            print(structured_ticket)
            print("=" * 60)
        
        print(f"\n🎉 Ticket conversion completed successfully!")
        
    except Exception as e:
        print(f"❌ Ticket conversion failed: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
