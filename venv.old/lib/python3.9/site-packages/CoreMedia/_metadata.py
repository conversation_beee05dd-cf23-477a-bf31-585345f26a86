# This file is generated by objective.metadata
#
# Last update: Sat Mar  1 10:56:51 2025
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "CMTimeMapping": objc.createStructType(
            "CoreMedia.CMTimeMapping",
            b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}",
            ["source", "target"],
        ),
        "CMVideoDimensions": objc.createStructType(
            "CoreMedia.CMVideoDimensions",
            b"{CMVideoDimensions=ii}",
            ["width", "height"],
        ),
        "CMTime": objc.createStructType(
            "CoreMedia.CMTime",
            b"{CMTime=qiIq}",
            ["value", "timescale", "flags", "epoch"],
        ),
        "CMTimeRange": objc.createStructType(
            "CoreMedia.CMTimeRange",
            b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
            ["start", "duration"],
        ),
        "CMTag": objc.createStructType(
            "CoreMedia.CMTag", b"{CMTag=IIQ}", ["category", "dataType", "value"]
        ),
        "CMSampleTimingInfo": objc.createStructType(
            "CoreMedia.CMSampleTimingInfo",
            b"{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}",
            ["duration", "presentationTimeStamp", "decodeTimeStamp"],
        ),
    }
)
constants = """$kCMFormatDescriptionAlphaChannelMode_PremultipliedAlpha$kCMFormatDescriptionAlphaChannelMode_StraightAlpha$kCMFormatDescriptionChromaLocation_Bottom$kCMFormatDescriptionChromaLocation_BottomLeft$kCMFormatDescriptionChromaLocation_Center$kCMFormatDescriptionChromaLocation_DV420$kCMFormatDescriptionChromaLocation_Left$kCMFormatDescriptionChromaLocation_Top$kCMFormatDescriptionChromaLocation_TopLeft$kCMFormatDescriptionColorPrimaries_DCI_P3$kCMFormatDescriptionColorPrimaries_EBU_3213$kCMFormatDescriptionColorPrimaries_ITU_R_2020$kCMFormatDescriptionColorPrimaries_ITU_R_709_2$kCMFormatDescriptionColorPrimaries_P22$kCMFormatDescriptionColorPrimaries_P3_D65$kCMFormatDescriptionColorPrimaries_SMPTE_C$kCMFormatDescriptionConformsToMPEG2VideoProfile$kCMFormatDescriptionExtensionKey_MetadataKeyTable$kCMFormatDescriptionExtension_AlphaChannelMode$kCMFormatDescriptionExtension_AlternativeTransferCharacteristics$kCMFormatDescriptionExtension_AmbientViewingEnvironment$kCMFormatDescriptionExtension_AuxiliaryTypeInfo$kCMFormatDescriptionExtension_BitsPerComponent$kCMFormatDescriptionExtension_BytesPerRow$kCMFormatDescriptionExtension_ChromaLocationBottomField$kCMFormatDescriptionExtension_ChromaLocationTopField$kCMFormatDescriptionExtension_CleanAperture$kCMFormatDescriptionExtension_ColorPrimaries$kCMFormatDescriptionExtension_ContainsAlphaChannel$kCMFormatDescriptionExtension_ContentColorVolume$kCMFormatDescriptionExtension_ContentLightLevelInfo$kCMFormatDescriptionExtension_Depth$kCMFormatDescriptionExtension_FieldCount$kCMFormatDescriptionExtension_FieldDetail$kCMFormatDescriptionExtension_FormatName$kCMFormatDescriptionExtension_FullRangeVideo$kCMFormatDescriptionExtension_GammaLevel$kCMFormatDescriptionExtension_HasAdditionalViews$kCMFormatDescriptionExtension_HasLeftStereoEyeView$kCMFormatDescriptionExtension_HasRightStereoEyeView$kCMFormatDescriptionExtension_HeroEye$kCMFormatDescriptionExtension_HorizontalDisparityAdjustment$kCMFormatDescriptionExtension_HorizontalFieldOfView$kCMFormatDescriptionExtension_ICCProfile$kCMFormatDescriptionExtension_LogTransferFunction$kCMFormatDescriptionExtension_MasteringDisplayColorVolume$kCMFormatDescriptionExtension_OriginalCompressionSettings$kCMFormatDescriptionExtension_PixelAspectRatio$kCMFormatDescriptionExtension_ProjectionKind$kCMFormatDescriptionExtension_ProtectedContentOriginalFormat$kCMFormatDescriptionExtension_RevisionLevel$kCMFormatDescriptionExtension_SampleDescriptionExtensionAtoms$kCMFormatDescriptionExtension_SpatialQuality$kCMFormatDescriptionExtension_StereoCameraBaseline$kCMFormatDescriptionExtension_TemporalQuality$kCMFormatDescriptionExtension_TransferFunction$kCMFormatDescriptionExtension_Vendor$kCMFormatDescriptionExtension_VerbatimISOSampleEntry$kCMFormatDescriptionExtension_VerbatimImageDescription$kCMFormatDescriptionExtension_VerbatimSampleDescription$kCMFormatDescriptionExtension_Version$kCMFormatDescriptionExtension_ViewPackingKind$kCMFormatDescriptionExtension_YCbCrMatrix$kCMFormatDescriptionFieldDetail_SpatialFirstLineEarly$kCMFormatDescriptionFieldDetail_SpatialFirstLineLate$kCMFormatDescriptionFieldDetail_TemporalBottomFirst$kCMFormatDescriptionFieldDetail_TemporalTopFirst$kCMFormatDescriptionHeroEye_Left$kCMFormatDescriptionHeroEye_Right$kCMFormatDescriptionKey_CleanApertureHeight$kCMFormatDescriptionKey_CleanApertureHeightRational$kCMFormatDescriptionKey_CleanApertureHorizontalOffset$kCMFormatDescriptionKey_CleanApertureHorizontalOffsetRational$kCMFormatDescriptionKey_CleanApertureVerticalOffset$kCMFormatDescriptionKey_CleanApertureVerticalOffsetRational$kCMFormatDescriptionKey_CleanApertureWidth$kCMFormatDescriptionKey_CleanApertureWidthRational$kCMFormatDescriptionKey_PixelAspectRatioHorizontalSpacing$kCMFormatDescriptionKey_PixelAspectRatioVerticalSpacing$kCMFormatDescriptionLogTransferFunction_AppleLog$kCMFormatDescriptionProjectionKind_Equirectangular$kCMFormatDescriptionProjectionKind_HalfEquirectangular$kCMFormatDescriptionProjectionKind_Rectilinear$kCMFormatDescriptionTransferFunction_ITU_R_2020$kCMFormatDescriptionTransferFunction_ITU_R_2100_HLG$kCMFormatDescriptionTransferFunction_ITU_R_709_2$kCMFormatDescriptionTransferFunction_Linear$kCMFormatDescriptionTransferFunction_SMPTE_240M_1995$kCMFormatDescriptionTransferFunction_SMPTE_ST_2084_PQ$kCMFormatDescriptionTransferFunction_SMPTE_ST_428_1$kCMFormatDescriptionTransferFunction_UseGamma$kCMFormatDescriptionTransferFunction_sRGB$kCMFormatDescriptionVendor_Apple$kCMFormatDescriptionViewPackingKind_OverUnder$kCMFormatDescriptionViewPackingKind_SideBySide$kCMFormatDescriptionYCbCrMatrix_ITU_R_2020$kCMFormatDescriptionYCbCrMatrix_ITU_R_601_4$kCMFormatDescriptionYCbCrMatrix_ITU_R_709_2$kCMFormatDescriptionYCbCrMatrix_SMPTE_240M_1995$kCMHEVCTemporalLevelInfoKey_ConstraintIndicatorFlags$kCMHEVCTemporalLevelInfoKey_LevelIndex$kCMHEVCTemporalLevelInfoKey_ProfileCompatibilityFlags$kCMHEVCTemporalLevelInfoKey_ProfileIndex$kCMHEVCTemporalLevelInfoKey_ProfileSpace$kCMHEVCTemporalLevelInfoKey_TemporalLevel$kCMHEVCTemporalLevelInfoKey_TierFlag$kCMImageDescriptionFlavor_3GPFamily$kCMImageDescriptionFlavor_ISOFamily$kCMImageDescriptionFlavor_ISOFamilyWithAppleExtensions$kCMImageDescriptionFlavor_QuickTimeMovie$kCMMemoryPoolOption_AgeOutPeriod$kCMMetadataBaseDataType_AffineTransformF64$kCMMetadataBaseDataType_BMP$kCMMetadataBaseDataType_DimensionsF32$kCMMetadataBaseDataType_Float32$kCMMetadataBaseDataType_Float64$kCMMetadataBaseDataType_GIF$kCMMetadataBaseDataType_JPEG$kCMMetadataBaseDataType_JSON$kCMMetadataBaseDataType_PNG$kCMMetadataBaseDataType_PerspectiveTransformF64$kCMMetadataBaseDataType_PointF32$kCMMetadataBaseDataType_PolygonF32$kCMMetadataBaseDataType_PolylineF32$kCMMetadataBaseDataType_RawData$kCMMetadataBaseDataType_RectF32$kCMMetadataBaseDataType_SInt16$kCMMetadataBaseDataType_SInt32$kCMMetadataBaseDataType_SInt64$kCMMetadataBaseDataType_SInt8$kCMMetadataBaseDataType_UInt16$kCMMetadataBaseDataType_UInt32$kCMMetadataBaseDataType_UInt64$kCMMetadataBaseDataType_UInt8$kCMMetadataBaseDataType_UTF16$kCMMetadataBaseDataType_UTF8$kCMMetadataDataType_QuickTimeMetadataDirection$kCMMetadataDataType_QuickTimeMetadataLocation_ISO6709$kCMMetadataDataType_QuickTimeMetadataMilliLux$kCMMetadataDataType_QuickTimeMetadataUUID$kCMMetadataFormatDescriptionKey_ConformingDataTypes$kCMMetadataFormatDescriptionKey_DataType$kCMMetadataFormatDescriptionKey_DataTypeNamespace$kCMMetadataFormatDescriptionKey_LanguageTag$kCMMetadataFormatDescriptionKey_LocalID$kCMMetadataFormatDescriptionKey_Namespace$kCMMetadataFormatDescriptionKey_SetupData$kCMMetadataFormatDescriptionKey_StructuralDependency$kCMMetadataFormatDescriptionKey_Value$kCMMetadataFormatDescriptionMetadataSpecificationKey_DataType$kCMMetadataFormatDescriptionMetadataSpecificationKey_ExtendedLanguageTag$kCMMetadataFormatDescriptionMetadataSpecificationKey_Identifier$kCMMetadataFormatDescriptionMetadataSpecificationKey_SetupData$kCMMetadataFormatDescriptionMetadataSpecificationKey_StructuralDependency$kCMMetadataFormatDescription_StructuralDependencyKey_DependencyIsInvalidFlag$kCMMetadataIdentifier_QuickTimeMetadataDirection_Facing$kCMMetadataIdentifier_QuickTimeMetadataLivePhotoStillImageTransform$kCMMetadataIdentifier_QuickTimeMetadataLivePhotoStillImageTransformReferenceDimensions$kCMMetadataIdentifier_QuickTimeMetadataLocation_ISO6709$kCMMetadataIdentifier_QuickTimeMetadataPreferredAffineTransform$kCMMetadataIdentifier_QuickTimeMetadataSceneIlluminance$kCMMetadataIdentifier_QuickTimeMetadataSegmentIdentifier$kCMMetadataIdentifier_QuickTimeMetadataVideoOrientation$kCMMetadataKeySpace_HLSDateRange$kCMMetadataKeySpace_ID3$kCMMetadataKeySpace_ISOUserData$kCMMetadataKeySpace_Icy$kCMMetadataKeySpace_QuickTimeMetadata$kCMMetadataKeySpace_QuickTimeUserData$kCMMetadataKeySpace_iTunes$kCMSampleAttachmentKey_AudioIndependentSampleDecoderRefreshCount$kCMSampleAttachmentKey_CryptorSubsampleAuxiliaryData$kCMSampleAttachmentKey_DependsOnOthers$kCMSampleAttachmentKey_DisplayImmediately$kCMSampleAttachmentKey_DoNotDisplay$kCMSampleAttachmentKey_EarlierDisplayTimesAllowed$kCMSampleAttachmentKey_HDR10PlusPerFrameData$kCMSampleAttachmentKey_HEVCStepwiseTemporalSubLayerAccess$kCMSampleAttachmentKey_HEVCSyncSampleNALUnitType$kCMSampleAttachmentKey_HEVCTemporalLevelInfo$kCMSampleAttachmentKey_HEVCTemporalSubLayerAccess$kCMSampleAttachmentKey_HasRedundantCoding$kCMSampleAttachmentKey_IsDependedOnByOthers$kCMSampleAttachmentKey_NotSync$kCMSampleAttachmentKey_PartialSync$kCMSampleAttachmentKey_PostDecodeProcessingMetadata$kCMSampleBufferAttachmentKey_CameraIntrinsicMatrix$kCMSampleBufferAttachmentKey_DisplayEmptyMediaImmediately$kCMSampleBufferAttachmentKey_DrainAfterDecoding$kCMSampleBufferAttachmentKey_DroppedFrameReason$kCMSampleBufferAttachmentKey_DroppedFrameReasonInfo$kCMSampleBufferAttachmentKey_EmptyMedia$kCMSampleBufferAttachmentKey_EndsPreviousSampleDuration$kCMSampleBufferAttachmentKey_FillDiscontinuitiesWithSilence$kCMSampleBufferAttachmentKey_ForceKeyFrame$kCMSampleBufferAttachmentKey_GradualDecoderRefresh$kCMSampleBufferAttachmentKey_PermanentEmptyMedia$kCMSampleBufferAttachmentKey_PostNotificationWhenConsumed$kCMSampleBufferAttachmentKey_ResetDecoderBeforeDecoding$kCMSampleBufferAttachmentKey_ResumeOutput$kCMSampleBufferAttachmentKey_Reverse$kCMSampleBufferAttachmentKey_SampleReferenceByteOffset$kCMSampleBufferAttachmentKey_SampleReferenceURL$kCMSampleBufferAttachmentKey_SpeedMultiplier$kCMSampleBufferAttachmentKey_StillImageLensStabilizationInfo$kCMSampleBufferAttachmentKey_TransitionID$kCMSampleBufferAttachmentKey_TrimDurationAtEnd$kCMSampleBufferAttachmentKey_TrimDurationAtStart$kCMSampleBufferConduitNotificationParameter_MaxUpcomingOutputPTS$kCMSampleBufferConduitNotificationParameter_MinUpcomingOutputPTS$kCMSampleBufferConduitNotificationParameter_ResumeTag$kCMSampleBufferConduitNotificationParameter_UpcomingOutputPTSRangeMayOverlapQueuedOutputPTSRange$kCMSampleBufferConduitNotification_InhibitOutputUntil$kCMSampleBufferConduitNotification_ResetOutput$kCMSampleBufferConduitNotification_UpcomingOutputPTSRangeChanged$kCMSampleBufferConsumerNotification_BufferConsumed$kCMSampleBufferDroppedFrameReasonInfo_CameraModeSwitch$kCMSampleBufferDroppedFrameReason_Discontinuity$kCMSampleBufferDroppedFrameReason_FrameWasLate$kCMSampleBufferDroppedFrameReason_OutOfBuffers$kCMSampleBufferLensStabilizationInfo_Active$kCMSampleBufferLensStabilizationInfo_Off$kCMSampleBufferLensStabilizationInfo_OutOfRange$kCMSampleBufferLensStabilizationInfo_Unavailable$kCMSampleBufferNotificationParameter_OSStatus$kCMSampleBufferNotification_DataBecameReady$kCMSampleBufferNotification_DataFailed$kCMSoundDescriptionFlavor_3GPFamily$kCMSoundDescriptionFlavor_ISOFamily$kCMSoundDescriptionFlavor_QuickTimeMovie$kCMSoundDescriptionFlavor_QuickTimeMovieV2$kCMTagCategoryKey$kCMTagCollectionTagsArrayKey$kCMTagDataTypeKey$kCMTagInvalid@{CMTag=IIQ}$kCMTagMediaSubTypeMebx@{CMTag=IIQ}$kCMTagMediaTypeAudio@{CMTag=IIQ}$kCMTagMediaTypeMetadata@{CMTag=IIQ}$kCMTagMediaTypeVideo@{CMTag=IIQ}$kCMTagPackingTypeNone@{CMTag=IIQ}$kCMTagPackingTypeOverUnder@{CMTag=IIQ}$kCMTagPackingTypeSideBySide@{CMTag=IIQ}$kCMTagProjectionTypeEquirectangular@{CMTag=IIQ}$kCMTagProjectionTypeFisheye@{CMTag=IIQ}$kCMTagProjectionTypeHalfEquirectangular@{CMTag=IIQ}$kCMTagProjectionTypeRectangular@{CMTag=IIQ}$kCMTagStereoInterpretationOrderReversed@{CMTag=IIQ}$kCMTagStereoLeftAndRightEye@{CMTag=IIQ}$kCMTagStereoLeftEye@{CMTag=IIQ}$kCMTagStereoNone@{CMTag=IIQ}$kCMTagStereoRightEye@{CMTag=IIQ}$kCMTagValueKey$kCMTextFormatDescriptionColor_Alpha$kCMTextFormatDescriptionColor_Blue$kCMTextFormatDescriptionColor_Green$kCMTextFormatDescriptionColor_Red$kCMTextFormatDescriptionExtension_BackgroundColor$kCMTextFormatDescriptionExtension_DefaultFontName$kCMTextFormatDescriptionExtension_DefaultStyle$kCMTextFormatDescriptionExtension_DefaultTextBox$kCMTextFormatDescriptionExtension_DisplayFlags$kCMTextFormatDescriptionExtension_FontTable$kCMTextFormatDescriptionExtension_HorizontalJustification$kCMTextFormatDescriptionExtension_TextJustification$kCMTextFormatDescriptionExtension_VerticalJustification$kCMTextFormatDescriptionRect_Bottom$kCMTextFormatDescriptionRect_Left$kCMTextFormatDescriptionRect_Right$kCMTextFormatDescriptionRect_Top$kCMTextFormatDescriptionStyle_Ascent$kCMTextFormatDescriptionStyle_EndChar$kCMTextFormatDescriptionStyle_Font$kCMTextFormatDescriptionStyle_FontFace$kCMTextFormatDescriptionStyle_FontSize$kCMTextFormatDescriptionStyle_ForegroundColor$kCMTextFormatDescriptionStyle_Height$kCMTextFormatDescriptionStyle_StartChar$kCMTextMarkupAlignmentType_End$kCMTextMarkupAlignmentType_Left$kCMTextMarkupAlignmentType_Middle$kCMTextMarkupAlignmentType_Right$kCMTextMarkupAlignmentType_Start$kCMTextMarkupAttribute_Alignment$kCMTextMarkupAttribute_BackgroundColorARGB$kCMTextMarkupAttribute_BaseFontSizePercentageRelativeToVideoHeight$kCMTextMarkupAttribute_BoldStyle$kCMTextMarkupAttribute_CharacterBackgroundColorARGB$kCMTextMarkupAttribute_CharacterEdgeStyle$kCMTextMarkupAttribute_FontFamilyName$kCMTextMarkupAttribute_FontFamilyNameList$kCMTextMarkupAttribute_ForegroundColorARGB$kCMTextMarkupAttribute_GenericFontFamilyName$kCMTextMarkupAttribute_ItalicStyle$kCMTextMarkupAttribute_OrthogonalLinePositionPercentageRelativeToWritingDirection$kCMTextMarkupAttribute_RelativeFontSize$kCMTextMarkupAttribute_TextPositionPercentageRelativeToWritingDirection$kCMTextMarkupAttribute_UnderlineStyle$kCMTextMarkupAttribute_VerticalLayout$kCMTextMarkupAttribute_WritingDirectionSizePercentage$kCMTextMarkupCharacterEdgeStyle_Depressed$kCMTextMarkupCharacterEdgeStyle_DropShadow$kCMTextMarkupCharacterEdgeStyle_None$kCMTextMarkupCharacterEdgeStyle_Raised$kCMTextMarkupCharacterEdgeStyle_Uniform$kCMTextMarkupGenericFontName_Casual$kCMTextMarkupGenericFontName_Cursive$kCMTextMarkupGenericFontName_Default$kCMTextMarkupGenericFontName_Fantasy$kCMTextMarkupGenericFontName_Monospace$kCMTextMarkupGenericFontName_MonospaceSansSerif$kCMTextMarkupGenericFontName_MonospaceSerif$kCMTextMarkupGenericFontName_ProportionalSansSerif$kCMTextMarkupGenericFontName_ProportionalSerif$kCMTextMarkupGenericFontName_SansSerif$kCMTextMarkupGenericFontName_Serif$kCMTextMarkupGenericFontName_SmallCapital$kCMTextVerticalLayout_LeftToRight$kCMTextVerticalLayout_RightToLeft$kCMTimeCodeFormatDescriptionExtension_SourceReferenceName$kCMTimeCodeFormatDescriptionKey_LangCode$kCMTimeCodeFormatDescriptionKey_Value$kCMTimeEpochKey$kCMTimeFlagsKey$kCMTimeIndefinite@{CMTime=qiIq}$kCMTimeInvalid@{CMTime=qiIq}$kCMTimeMappingInvalid@{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}$kCMTimeMappingSourceKey$kCMTimeMappingTargetKey$kCMTimeNegativeInfinity@{CMTime=qiIq}$kCMTimePositiveInfinity@{CMTime=qiIq}$kCMTimeRangeDurationKey$kCMTimeRangeInvalid@{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}$kCMTimeRangeStartKey$kCMTimeRangeZero@{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}$kCMTimeScaleKey$kCMTimeValueKey$kCMTimeZero@{CMTime=qiIq}$kCMTimebaseNotificationKey_EventTime$kCMTimebaseNotification_EffectiveRateChanged$kCMTimebaseNotification_TimeJumped$kCMTimingInfoInvalid@{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}$"""
enums = """$CMTIMEBASE_USE_SOURCE_TERMINOLOGY@0$kCMAttachmentMode_ShouldNotPropagate@0$kCMAttachmentMode_ShouldPropagate@1$kCMAudioCodecType_AAC_AudibleProtected@1633771875$kCMAudioCodecType_AAC_LCProtected@1885430115$kCMAudioFormatDescriptionMask_All@15$kCMAudioFormatDescriptionMask_ChannelLayout@4$kCMAudioFormatDescriptionMask_Extensions@8$kCMAudioFormatDescriptionMask_MagicCookie@2$kCMAudioFormatDescriptionMask_StreamBasicDescription@1$kCMBlockBufferAlwaysCopyDataFlag@2$kCMBlockBufferAssureMemoryNowFlag@1$kCMBlockBufferBadCustomBlockSourceErr@-12702$kCMBlockBufferBadLengthParameterErr@-12704$kCMBlockBufferBadOffsetParameterErr@-12703$kCMBlockBufferBadPointerParameterErr@-12705$kCMBlockBufferBlockAllocationFailedErr@-12701$kCMBlockBufferCustomBlockSourceVersion@0$kCMBlockBufferDontOptimizeDepthFlag@4$kCMBlockBufferEmptyBBufErr@-12706$kCMBlockBufferInsufficientSpaceErr@-12708$kCMBlockBufferNoErr@0$kCMBlockBufferPermitEmptyReferenceFlag@8$kCMBlockBufferStructureAllocationFailedErr@-12700$kCMBlockBufferUnallocatedBlockErr@-12707$kCMBufferQueueError_AllocationFailed@-12760$kCMBufferQueueError_BadTriggerDuration@-12765$kCMBufferQueueError_CannotModifyQueueFromTriggerCallback@-12766$kCMBufferQueueError_EnqueueAfterEndOfData@-12763$kCMBufferQueueError_InvalidBuffer@-12769$kCMBufferQueueError_InvalidCMBufferCallbacksStruct@-12762$kCMBufferQueueError_InvalidTriggerCondition@-12767$kCMBufferQueueError_InvalidTriggerToken@-12768$kCMBufferQueueError_QueueIsFull@-12764$kCMBufferQueueError_RequiredParameterMissing@-12761$kCMBufferQueueTrigger_WhenBufferCountBecomesGreaterThan@11$kCMBufferQueueTrigger_WhenBufferCountBecomesLessThan@10$kCMBufferQueueTrigger_WhenDataBecomesReady@7$kCMBufferQueueTrigger_WhenDurationBecomesGreaterThan@3$kCMBufferQueueTrigger_WhenDurationBecomesGreaterThanOrEqualTo@4$kCMBufferQueueTrigger_WhenDurationBecomesGreaterThanOrEqualToAndBufferCountBecomesGreaterThan@12$kCMBufferQueueTrigger_WhenDurationBecomesLessThan@1$kCMBufferQueueTrigger_WhenDurationBecomesLessThanOrEqualTo@2$kCMBufferQueueTrigger_WhenEndOfDataReached@8$kCMBufferQueueTrigger_WhenMaxPresentationTimeStampChanges@6$kCMBufferQueueTrigger_WhenMinPresentationTimeStampChanges@5$kCMBufferQueueTrigger_WhenReset@9$kCMClockError_AllocationFailed@-12747$kCMClockError_InvalidParameter@-12746$kCMClockError_MissingRequiredParameter@-12745$kCMClockError_UnsupportedOperation@-12756$kCMClosedCaptionFormatType_ATSC@1635017571$kCMClosedCaptionFormatType_CEA608@1664495672$kCMClosedCaptionFormatType_CEA708@1664561208$kCMFormatDescriptionBridgeError_AllocationFailed@-12713$kCMFormatDescriptionBridgeError_IncompatibleFormatDescription@-12716$kCMFormatDescriptionBridgeError_InvalidFormatDescription@-12715$kCMFormatDescriptionBridgeError_InvalidParameter@-12712$kCMFormatDescriptionBridgeError_InvalidSerializedSampleDescription@-12714$kCMFormatDescriptionBridgeError_InvalidSlice@-12719$kCMFormatDescriptionBridgeError_UnsupportedSampleDescriptionFlavor@-12717$kCMFormatDescriptionError_AllocationFailed@-12711$kCMFormatDescriptionError_InvalidParameter@-12710$kCMFormatDescriptionError_ValueNotAvailable@-12718$kCMMPEG2VideoProfile_HDV_1080i50@1751414323$kCMMPEG2VideoProfile_HDV_1080i60@1751414322$kCMMPEG2VideoProfile_HDV_1080p24@1751414326$kCMMPEG2VideoProfile_HDV_1080p25@1751414327$kCMMPEG2VideoProfile_HDV_1080p30@1751414328$kCMMPEG2VideoProfile_HDV_720p24@1751414324$kCMMPEG2VideoProfile_HDV_720p25@1751414325$kCMMPEG2VideoProfile_HDV_720p30@1751414321$kCMMPEG2VideoProfile_HDV_720p50@1751414369$kCMMPEG2VideoProfile_HDV_720p60@1751414329$kCMMPEG2VideoProfile_XDCAM_EX_1080i50_VBR35@2019849827$kCMMPEG2VideoProfile_XDCAM_EX_1080i60_VBR35@2019849826$kCMMPEG2VideoProfile_XDCAM_EX_1080p24_VBR35@2019849828$kCMMPEG2VideoProfile_XDCAM_EX_1080p25_VBR35@2019849829$kCMMPEG2VideoProfile_XDCAM_EX_1080p30_VBR35@2019849830$kCMMPEG2VideoProfile_XDCAM_EX_720p24_VBR35@2019849780$kCMMPEG2VideoProfile_XDCAM_EX_720p25_VBR35@2019849781$kCMMPEG2VideoProfile_XDCAM_EX_720p30_VBR35@2019849777$kCMMPEG2VideoProfile_XDCAM_EX_720p50_VBR35@2019849825$kCMMPEG2VideoProfile_XDCAM_EX_720p60_VBR35@2019849785$kCMMPEG2VideoProfile_XDCAM_HD422_1080i50_CBR50@2019833187$kCMMPEG2VideoProfile_XDCAM_HD422_1080i60_CBR50@2019833186$kCMMPEG2VideoProfile_XDCAM_HD422_1080p24_CBR50@2019833188$kCMMPEG2VideoProfile_XDCAM_HD422_1080p25_CBR50@2019833189$kCMMPEG2VideoProfile_XDCAM_HD422_1080p30_CBR50@2019833190$kCMMPEG2VideoProfile_XDCAM_HD422_540p@2019846194$kCMMPEG2VideoProfile_XDCAM_HD422_720p24_CBR50@2019833140$kCMMPEG2VideoProfile_XDCAM_HD422_720p25_CBR50@2019833141$kCMMPEG2VideoProfile_XDCAM_HD422_720p30_CBR50@2019833137$kCMMPEG2VideoProfile_XDCAM_HD422_720p50_CBR50@2019833185$kCMMPEG2VideoProfile_XDCAM_HD422_720p60_CBR50@2019833145$kCMMPEG2VideoProfile_XDCAM_HD_1080i50_VBR35@2019849779$kCMMPEG2VideoProfile_XDCAM_HD_1080i60_VBR35@2019849778$kCMMPEG2VideoProfile_XDCAM_HD_1080p24_VBR35@2019849782$kCMMPEG2VideoProfile_XDCAM_HD_1080p25_VBR35@2019849783$kCMMPEG2VideoProfile_XDCAM_HD_1080p30_VBR35@2019849784$kCMMPEG2VideoProfile_XDCAM_HD_540p@2019846244$kCMMPEG2VideoProfile_XF@2019981873$kCMMediaType_Audio@1936684398$kCMMediaType_AuxiliaryPicture@1635088502$kCMMediaType_ClosedCaption@1668047728$kCMMediaType_Metadata@1835365473$kCMMediaType_Muxed@1836415096$kCMMediaType_Subtitle@1935832172$kCMMediaType_TaggedBufferGroup@1952606066$kCMMediaType_Text@1952807028$kCMMediaType_TimeCode@1953325924$kCMMediaType_Video@1986618469$kCMMemoryPoolError_AllocationFailed@-15490$kCMMemoryPoolError_InvalidParameter@-15491$kCMMetadataDataTypeRegistryError_AllocationFailed@-16310$kCMMetadataDataTypeRegistryError_BadDataTypeIdentifier@-16312$kCMMetadataDataTypeRegistryError_DataTypeAlreadyRegistered@-16313$kCMMetadataDataTypeRegistryError_MultipleConformingBaseTypes@-16315$kCMMetadataDataTypeRegistryError_RequiredParameterMissing@-16311$kCMMetadataDataTypeRegistryError_RequiresConformingBaseType@-16314$kCMMetadataFormatType_Boxed@1835360888$kCMMetadataFormatType_EMSG@1701671783$kCMMetadataFormatType_ICY@1768126752$kCMMetadataFormatType_ID3@1768174368$kCMMetadataIdentifierError_AllocationFailed@-16300$kCMMetadataIdentifierError_BadIdentifier@-16307$kCMMetadataIdentifierError_BadKey@-16302$kCMMetadataIdentifierError_BadKeyLength@-16303$kCMMetadataIdentifierError_BadKeySpace@-16306$kCMMetadataIdentifierError_BadKeyType@-16304$kCMMetadataIdentifierError_BadNumberKey@-16305$kCMMetadataIdentifierError_NoKeyValueAvailable@-16308$kCMMetadataIdentifierError_RequiredParameterMissing@-16301$kCMMuxedStreamType_DV@1685463072$kCMMuxedStreamType_EmbeddedDeviceScreenRecording@1769173536$kCMMuxedStreamType_MPEG1System@1836069235$kCMMuxedStreamType_MPEG2Program@1836069488$kCMMuxedStreamType_MPEG2Transport@1836069492$kCMPackingType_None@1852796517$kCMPackingType_OverUnder@1870030194$kCMPackingType_SideBySide@1936286821$kCMPersistentTrackID_Invalid@0$kCMPixelFormat_16BE555@16$kCMPixelFormat_16BE565@1110783541$kCMPixelFormat_16LE555@1278555445$kCMPixelFormat_16LE5551@892679473$kCMPixelFormat_16LE565@1278555701$kCMPixelFormat_24RGB@24$kCMPixelFormat_32ARGB@32$kCMPixelFormat_32BGRA@1111970369$kCMPixelFormat_422YpCbCr10@1983000880$kCMPixelFormat_422YpCbCr16@1983000886$kCMPixelFormat_422YpCbCr8@846624121$kCMPixelFormat_422YpCbCr8_yuvs@2037741171$kCMPixelFormat_4444YpCbCrA8@1983131704$kCMPixelFormat_444YpCbCr10@1983131952$kCMPixelFormat_444YpCbCr8@1983066168$kCMPixelFormat_8IndexedGray_WhiteIsZero@40$kCMProjectionType_Equirectangular@1701934441$kCMProjectionType_Fisheye@1718186856$kCMProjectionType_HalfEquirectangular@1751478645$kCMProjectionType_Rectangular@1919247220$kCMSampleBufferError_AllocationFailed@-12730$kCMSampleBufferError_AlreadyHasDataBuffer@-12732$kCMSampleBufferError_ArrayTooSmall@-12737$kCMSampleBufferError_BufferHasNoSampleSizes@-12735$kCMSampleBufferError_BufferHasNoSampleTimingInfo@-12736$kCMSampleBufferError_BufferNotReady@-12733$kCMSampleBufferError_CannotSubdivide@-12739$kCMSampleBufferError_DataCanceled@-16751$kCMSampleBufferError_DataFailed@-16750$kCMSampleBufferError_InvalidEntryCount@-12738$kCMSampleBufferError_InvalidMediaFormat@-12743$kCMSampleBufferError_InvalidMediaTypeForOperation@-12741$kCMSampleBufferError_InvalidSampleData@-12742$kCMSampleBufferError_Invalidated@-12744$kCMSampleBufferError_RequiredParameterMissing@-12731$kCMSampleBufferError_SampleIndexOutOfRange@-12734$kCMSampleBufferError_SampleTimingInfoInvalid@-12740$kCMSampleBufferFlag_AudioBufferList_Assure16ByteAlignment@1$kCMSimpleQueueError_AllocationFailed@-12770$kCMSimpleQueueError_ParameterOutOfRange@-12772$kCMSimpleQueueError_QueueIsFull@-12773$kCMSimpleQueueError_RequiredParameterMissing@-12771$kCMStereoViewInterpretation_AdditionalViews@2$kCMStereoViewInterpretation_Default@0$kCMStereoViewInterpretation_StereoOrderReversed@1$kCMStereoView_LeftEye@1$kCMStereoView_None@0$kCMStereoView_RightEye@2$kCMSubtitleFormatType_3GText@1954034535$kCMSubtitleFormatType_WebVTT@2004251764$kCMSyncError_AllocationFailed@-12754$kCMSyncError_InvalidParameter@-12753$kCMSyncError_MissingRequiredParameter@-12752$kCMSyncError_RateMustBeNonZero@-12755$kCMTagCategory_ChannelID@1986226286$kCMTagCategory_MediaSubType@1836283234$kCMTagCategory_MediaType@1835297121$kCMTagCategory_PackingType@1885430635$kCMTagCategory_PixelFormat@1885960294$kCMTagCategory_ProjectionType@1886547818$kCMTagCategory_StereoView@1702454643$kCMTagCategory_StereoViewInterpretation@1702455664$kCMTagCategory_TrackID@1953653099$kCMTagCategory_Undefined@0$kCMTagCategory_VideoLayerID@1986814329$kCMTagCollectionError_AllocationFailed@-15741$kCMTagCollectionError_ExhaustedBufferSize@-15748$kCMTagCollectionError_InternalError@-15742$kCMTagCollectionError_InvalidTag@-15743$kCMTagCollectionError_InvalidTagCollectionData@-15745$kCMTagCollectionError_InvalidTagCollectionDataVersion@-15747$kCMTagCollectionError_InvalidTagCollectionDictionary@-15744$kCMTagCollectionError_NotYetImplemented@-15749$kCMTagCollectionError_ParamErr@-15740$kCMTagCollectionError_TagNotFound@-15746$kCMTagDataType_Flags@7$kCMTagDataType_Float64@3$kCMTagDataType_Invalid@0$kCMTagDataType_OSType@5$kCMTagDataType_SInt64@2$kCMTagError_AllocationFailed@-15731$kCMTagError_ParamErr@-15730$kCMTaggedBufferGroupError_AllocationFailed@-15781$kCMTaggedBufferGroupError_InternalError@-15782$kCMTaggedBufferGroupError_ParamErr@-15780$kCMTaggedBufferGroupFormatType_TaggedBufferGroup@1952606066$kCMTextDisplayFlag_allSubtitlesForced@2147483648$kCMTextDisplayFlag_continuousKaraoke@2048$kCMTextDisplayFlag_fillTextRegion@262144$kCMTextDisplayFlag_forcedSubtitlesPresent@1073741824$kCMTextDisplayFlag_obeySubtitleFormatting@536870912$kCMTextDisplayFlag_scrollDirectionMask@384$kCMTextDisplayFlag_scrollDirection_bottomToTop@0$kCMTextDisplayFlag_scrollDirection_leftToRight@384$kCMTextDisplayFlag_scrollDirection_rightToLeft@128$kCMTextDisplayFlag_scrollDirection_topToBottom@256$kCMTextDisplayFlag_scrollIn@32$kCMTextDisplayFlag_scrollOut@64$kCMTextDisplayFlag_writeTextVertically@131072$kCMTextFormatType_3GText@1954034535$kCMTextFormatType_QTText@1952807028$kCMTextJustification_bottom_right@-1$kCMTextJustification_centered@1$kCMTextJustification_left_top@0$kCMTimeCodeFlag_24HourMax@2$kCMTimeCodeFlag_DropFrame@1$kCMTimeCodeFlag_NegTimesOK@4$kCMTimeCodeFormatType_Counter32@1668166450$kCMTimeCodeFormatType_Counter64@1668167220$kCMTimeCodeFormatType_TimeCode32@1953325924$kCMTimeCodeFormatType_TimeCode64@1952658996$kCMTimeFlags_HasBeenRounded@2$kCMTimeFlags_ImpliedValueFlagsMask@28$kCMTimeFlags_Indefinite@16$kCMTimeFlags_NegativeInfinity@8$kCMTimeFlags_PositiveInfinity@4$kCMTimeFlags_Valid@1$kCMTimeMaxTimescale@2147483647$kCMTimeRoundingMethod_Default@1$kCMTimeRoundingMethod_QuickTime@4$kCMTimeRoundingMethod_RoundAwayFromZero@3$kCMTimeRoundingMethod_RoundHalfAwayFromZero@1$kCMTimeRoundingMethod_RoundTowardNegativeInfinity@6$kCMTimeRoundingMethod_RoundTowardPositiveInfinity@5$kCMTimeRoundingMethod_RoundTowardZero@2$kCMTimebaseError_AllocationFailed@-12750$kCMTimebaseError_InvalidParameter@-12749$kCMTimebaseError_MissingRequiredParameter@-12748$kCMTimebaseError_ReadOnly@-12757$kCMTimebaseError_TimerIntervalTooShort@-12751$kCMTimebaseVeryLongCFTimeInterval@8073216000.0$kCMVideoCodecType_422YpCbCr8@846624121$kCMVideoCodecType_AV1@1635135537$kCMVideoCodecType_Animation@1919706400$kCMVideoCodecType_AppleProRes422@1634755438$kCMVideoCodecType_AppleProRes422HQ@1634755432$kCMVideoCodecType_AppleProRes422LT@1634755443$kCMVideoCodecType_AppleProRes422Proxy@1634755439$kCMVideoCodecType_AppleProRes4444@1634743400$kCMVideoCodecType_AppleProRes4444XQ@1634743416$kCMVideoCodecType_AppleProResRAW@1634759278$kCMVideoCodecType_AppleProResRAWHQ@1634759272$kCMVideoCodecType_Cinepak@1668704612$kCMVideoCodecType_DVCNTSC@1685480224$kCMVideoCodecType_DVCPAL@1685480304$kCMVideoCodecType_DVCPROHD1080i50@1685481525$kCMVideoCodecType_DVCPROHD1080i60@1685481526$kCMVideoCodecType_DVCPROHD1080p25@1685481522$kCMVideoCodecType_DVCPROHD1080p30@1685481523$kCMVideoCodecType_DVCPROHD720p50@1685481585$kCMVideoCodecType_DVCPROHD720p60@1685481584$kCMVideoCodecType_DVCPro50NTSC@1685468526$kCMVideoCodecType_DVCPro50PAL@1685468528$kCMVideoCodecType_DVCProPAL@1685483632$kCMVideoCodecType_DepthHEVC@1684369512$kCMVideoCodecType_DisparityHEVC@1684632424$kCMVideoCodecType_DolbyVisionHEVC@1685481521$kCMVideoCodecType_H263@1748121139$kCMVideoCodecType_H264@1635148593$kCMVideoCodecType_HEVC@1752589105$kCMVideoCodecType_HEVCWithAlpha@1836415073$kCMVideoCodecType_JPEG@1785750887$kCMVideoCodecType_JPEG_OpenDML@1684890161$kCMVideoCodecType_JPEG_XL@1786276963$kCMVideoCodecType_MPEG1Video@1836069238$kCMVideoCodecType_MPEG2Video@1836069494$kCMVideoCodecType_MPEG4Video@1836070006$kCMVideoCodecType_SorensonVideo@1398165809$kCMVideoCodecType_SorensonVideo3@1398165811$kCMVideoCodecType_VP9@1987063865$"""
misc.update(
    {
        "CMTimeRoundingMethod": NewType("CMTimeRoundingMethod", int),
        "CMTagDataType": NewType("CMTagDataType", int),
        "CMTagCategory": NewType("CMTagCategory", int),
        "CMTaggedBufferGroupError": NewType("CMTaggedBufferGroupError", int),
        "CMTagError": NewType("CMTagError", int),
        "CMStereoViewComponents": NewType("CMStereoViewComponents", int),
        "CMProjectionType": NewType("CMProjectionType", int),
        "CMPackingType": NewType("CMPackingType", int),
        "CMStereoViewInterpretationOptions": NewType(
            "CMStereoViewInterpretationOptions", int
        ),
        "CMTimeFlags": NewType("CMTimeFlags", int),
        "CMTagCollectionError": NewType("CMTagCollectionError", int),
    }
)
misc.update({})
misc.update({})
functions = {
    "CMBlockBufferCreateEmpty": (
        b"i^{__CFAllocator=}II^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimebaseCreateWithMasterTimebase": (
        b"i^{__CFAllocator=}^{OpaqueCMTimebase=}^^{OpaqueCMTimebase=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTagCollectionGetCountOfCategory": (b"q^{OpaqueCMTagCollection=}I",),
    "CMBufferQueueMarkEndOfData": (b"i^{opaqueCMBufferQueue=}",),
    "CMTimeMappingMakeFromDictionary": (
        b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}^{__CFDictionary=}",
    ),
    "CMTagCollectionCreate": (
        b"i^{__CFAllocator=}^{CMTag=IIQ}q^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                3: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMBufferQueueInstallTrigger": (
        b"i^{opaqueCMBufferQueue=}^?^vi{CMTime=qiIq}^^{opaqueCMBufferQueueTriggerToken=}",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMBufferQueueTriggerToken=}"},
                        },
                    },
                    "callable_retained": True,
                },
                5: {"type_modifier": "o"},
            }
        },
    ),
    "CMTaggedBufferGroupCreateCombined": (
        b"i^{__CFAllocator=}^{__CFArray=}^^{OpaqueCMTaggedBufferGroup=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTagGetFlagsValue": (b"Q{CMTag=IIQ}",),
    "CMBufferQueueRemoveTrigger": (
        b"i^{opaqueCMBufferQueue=}^{opaqueCMBufferQueueTriggerToken=}",
    ),
    "CMSampleBufferSetDataBufferFromAudioBufferList": (
        b"i^{opaqueCMSampleBuffer=}^{__CFAllocator=}^{__CFAllocator=}I^{AudioBufferList=I[1{AudioBuffer=II^v}]}",
    ),
    "CMTaggedBufferGroupFormatDescriptionMatchesTaggedBufferGroup": (
        b"Z^{opaqueCMFormatDescription=}^{OpaqueCMTaggedBufferGroup=}",
    ),
    "CMBufferQueueGetTotalSize": (b"Q^{opaqueCMBufferQueue=}",),
    "CMTagCollectionGetCount": (b"q^{OpaqueCMTagCollection=}",),
    "CMSyncGetTime": (b"{CMTime=qiIq}@",),
    "CMMetadataFormatDescriptionGetKeyWithLocalID": (
        b"^{__CFDictionary=}^{opaqueCMFormatDescription=}I",
    ),
    "CMTagIsValid": (b"Z{CMTag=IIQ}",),
    "CMTagGetSInt64Value": (b"q{CMTag=IIQ}",),
    "CMSwapHostEndianTimeCodeDescriptionToBig": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMSwapBigEndianSoundDescriptionToHost": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMBufferQueueGetFirstPresentationTimeStamp": (
        b"{CMTime=qiIq}^{opaqueCMBufferQueue=}",
    ),
    "CMTimeCopyDescription": (
        b"^{__CFString=}^{__CFAllocator=}{CMTime=qiIq}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMBlockBufferGetDataLength": (b"Q^{OpaqueCMBlockBuffer=}",),
    "CMAudioFormatDescriptionCopyAsBigEndianSoundDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{opaqueCMFormatDescription=}^{__CFString=}^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTagHasFloat64Value": (b"Z{CMTag=IIQ}",),
    "CMSwapHostEndianImageDescriptionToBig": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMTimeMappingCopyDescription": (
        b"^{__CFString=}^{__CFAllocator=}{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMVideoFormatDescriptionCreateForImageBuffer": (
        b"i^{__CFAllocator=}^{__CVBuffer=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTagHash": (b"Q{CMTag=IIQ}",),
    "CMSampleBufferHasDataFailed": (
        b"Z^{opaqueCMSampleBuffer=}^i",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CMTimebaseSetAnchorTime": (b"i^{OpaqueCMTimebase=}{CMTime=qiIq}{CMTime=qiIq}",),
    "CMTimebaseRemoveTimer": (b"i^{OpaqueCMTimebase=}^{__CFRunLoopTimer=}",),
    "CMTimeFoldIntoRange": (
        b"{CMTime=qiIq}{CMTime=qiIq}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMFormatDescriptionGetExtension": (
        b"@^{opaqueCMFormatDescription=}^{__CFString=}",
    ),
    "CMBufferQueueGetDuration": (b"{CMTime=qiIq}^{opaqueCMBufferQueue=}",),
    "CMCopyDictionaryOfAttachments": (
        b"^{__CFDictionary=}^{__CFAllocator=}@I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMTaggedBufferGroupGetTagCollectionAtIndex": (
        b"^{OpaqueCMTagCollection=}^{OpaqueCMTaggedBufferGroup=}q",
    ),
    "CMSampleBufferGetSampleSize": (b"Q^{opaqueCMSampleBuffer=}q",),
    "CMSyncConvertTime": (b"{CMTime=qiIq}{CMTime=qiIq}@@",),
    "CMSimpleQueueDequeue": (b"@^{opaqueCMSimpleQueue=}",),
    "CMTimeRangeMakeFromDictionary": (
        b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}^{__CFDictionary=}",
    ),
    "CMSampleBufferCreateReadyWithImageBuffer": (
        b"i^{__CFAllocator=}^{__CVBuffer=}^{opaqueCMFormatDescription=}^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {"type_modifier": "n"},
                4: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMTagMakeWithFloat64Value": (b"{CMTag=IIQ}Id",),
    "CMSampleBufferCreateCopy": (
        b"i^{__CFAllocator=}^{opaqueCMSampleBuffer=}^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTagEqualToTag": (b"Z{CMTag=IIQ}{CMTag=IIQ}",),
    "CMMetadataCreateKeyFromIdentifier": (
        b"i^{__CFAllocator=}^{__CFString=}^@",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTaggedBufferGroupGetCMSampleBufferForTag": (
        b"^{opaqueCMSampleBuffer=}^{OpaqueCMTaggedBufferGroup=}{CMTag=IIQ}^q",
        "",
        {"arguments": {2: {"already_cfretained": True, "type_modifier": "o"}}},
    ),
    "CMSampleBufferCreateReady": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}^{opaqueCMFormatDescription=}qq^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}q^Q^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {"already_cfretained": True, "type_modifier": "o"},
                5: {"c_array_length_in_arg": 4, "type_modifier": "n"},
                7: {"c_array_length_in_arg": 6, "type_modifier": "n"},
            },
        },
    ),
    "CMTimeRangeShow": (b"v{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",),
    "CMBufferQueueDequeueIfDataReadyAndRetain": (
        b"@^{opaqueCMBufferQueue=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMBlockBufferReplaceDataBytes": (
        b"i^v^{OpaqueCMBlockBuffer=}QQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 3, "type_modifier": "n"}}},
    ),
    "CMTagCollectionCountTagsWithFilterFunction": (
        b"q^{OpaqueCMTagCollection=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {0: {"type": b"{CMTag=IIQ}"}, 1: {"type": b"^v"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CMBufferQueueGetMinPresentationTimeStamp": (
        b"{CMTime=qiIq}^{opaqueCMBufferQueue=}",
    ),
    "CMBlockBufferIsEmpty": (b"Z^{OpaqueCMBlockBuffer=}",),
    "CMTextFormatDescriptionCreateFromBigEndianTextDescriptionData": (
        b"i^{__CFAllocator=}^CQ^{__CFString=}I^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                5: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMBufferQueueCopyHead": (
        b"@^{opaqueCMBufferQueue=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMSampleBufferSetDataReady": (b"i^{opaqueCMSampleBuffer=}",),
    "CMMetadataDataTypeRegistryGetDataTypeDescription": (
        b"^{__CFString=}^{__CFString=}",
    ),
    "CMSetAttachment": (b"v@^{__CFString=}@I",),
    "CMTagCollectionAddTagsFromArray": (
        b"i^{OpaqueCMTagCollection=}^{CMTag=IIQ}q",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CMBufferQueueGetHead": (b"@^{opaqueCMBufferQueue=}",),
    "CMBlockBufferAppendBufferReference": (
        b"i^{OpaqueCMBlockBuffer=}^{OpaqueCMBlockBuffer=}QQI",
    ),
    "CMTimebaseGetTime": (b"{CMTime=qiIq}^{OpaqueCMTimebase=}",),
    "CMMetadataDataTypeRegistryGetBaseDataTypes": (b"^{__CFArray=}",),
    "CMVideoFormatDescriptionCreateFromBigEndianImageDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}I^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {4: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSampleBufferSetOutputPresentationTimeStamp": (
        b"i^{opaqueCMSampleBuffer=}{CMTime=qiIq}",
    ),
    "CMBufferQueueInstallTriggerHandlerWithIntegerThreshold": (
        b"i@Iq^^{opaqueCMBufferQueueTriggerToken=}@?",
        "",
        {
            "arguments": {
                3: {"type_modifier": "o"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "^{opaqueCMBufferQueueTriggerToken=}"},
                        },
                    }
                },
            }
        },
    ),
    "CMTimebaseCopySourceTimebase": (
        b"^{OpaqueCMTimebase=}^{OpaqueCMTimebase=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMTextFormatDescriptionGetDefaultTextBox": (
        b"i^{opaqueCMFormatDescription=}Zd^{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"arguments": {3: {"type_modifier": "o"}}},
    ),
    "CMBufferQueueSetValidationHandler": (
        b"i@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "CMSampleBufferSetDataFailed": (b"i^{opaqueCMSampleBuffer=}i",),
    "CMAudioFormatDescriptionEqual": (
        b"Z^{opaqueCMFormatDescription=}^{opaqueCMFormatDescription=}I^I",
        "",
        {"arguments": {3: {"type_modifier": "o"}}},
    ),
    "CMFormatDescriptionEqualIgnoringExtensionKeys": (
        b"Z^{opaqueCMFormatDescription=}^{opaqueCMFormatDescription=}@@",
    ),
    "CMSampleBufferIsValid": (b"Z^{opaqueCMSampleBuffer=}",),
    "CMBufferQueueGetMinDecodeTimeStamp": (b"{CMTime=qiIq}^{opaqueCMBufferQueue=}",),
    "CMTagCollectionCreateExclusiveOr": (
        b"i^{OpaqueCMTagCollection=}^{OpaqueCMTagCollection=}^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSampleBufferGetOutputSampleTimingInfoArray": (
        b"i^{opaqueCMSampleBuffer=}q^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}^q",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": 1, "type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CMAudioSampleBufferCreateReadyWithPacketDescriptions": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}^{opaqueCMFormatDescription=}q{CMTime=qiIq}^{AudioStreamPacketDescription=qII}^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                5: {"c_array_length_in_arg": 3, "type_modifier": "n"},
                6: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMTimebaseSetTimerDispatchSourceNextFireTime": (
        b"i^{OpaqueCMTimebase=}@{CMTime=qiIq}I",
    ),
    "CMVideoFormatDescriptionCreateFromBigEndianImageDescriptionData": (
        b"i^{__CFAllocator=}^CQI^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                5: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMTimeRangeCopyAsDictionary": (
        b"^{__CFDictionary=}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMAudioDeviceClockCreate": (
        b"i^{__CFAllocator=}^{__CFString=}^^{OpaqueCMClock=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeRangeGetUnion": (
        b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMVideoFormatDescriptionCreate": (
        b"i^{__CFAllocator=}Iii^{__CFDictionary=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {5: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeCopyAsDictionary": (
        b"^{__CFDictionary=}{CMTime=qiIq}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMVideoFormatDescriptionCopyAsBigEndianImageDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{opaqueCMFormatDescription=}I^{__CFString=}^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {4: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMFormatDescriptionEqual": (
        b"Z^{opaqueCMFormatDescription=}^{opaqueCMFormatDescription=}",
    ),
    "CMTagHasOSTypeValue": (b"Z{CMTag=IIQ}",),
    "CMTimeMakeWithEpoch": (b"{CMTime=qiIq}qiq",),
    "CMBlockBufferAppendMemoryBlock": (
        b"i^{OpaqueCMBlockBuffer=}^vQ^{__CFAllocator=}^{CMBlockBufferCustomBlockSource=I^?^?^v}QQI",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {"type_modifier": "n"},
            }
        },
    ),
    "CMTagCollectionApplyUntil": (
        b"{CMTag=IIQ}^{OpaqueCMTagCollection=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {0: {"type": b"{CMTag=IIQ}"}, 1: {"type": b"^v"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CMRemoveAllAttachments": (b"v@",),
    "CMTimebaseSetTimerToFireImmediately": (
        b"i^{OpaqueCMTimebase=}^{__CFRunLoopTimer=}",
    ),
    "CMBufferQueueIsAtEndOfData": (b"Z^{opaqueCMBufferQueue=}",),
    "CMTagCopyDescription": (
        b"^{__CFString=}^{__CFAllocator=}{CMTag=IIQ}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMTaggedBufferGroupGetCMSampleBufferForTagCollection": (
        b"^{opaqueCMSampleBuffer=}^{OpaqueCMTaggedBufferGroup=}^{OpaqueCMTagCollection=}^q",
        "",
        {"arguments": {2: {"already_cfretained": True, "type_modifier": "o"}}},
    ),
    "CMMetadataFormatDescriptionCreateFromBigEndianMetadataDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMBufferQueueEnqueue": (b"i^{opaqueCMBufferQueue=}@",),
    "CMBufferQueueGetMaxPresentationTimeStamp": (
        b"{CMTime=qiIq}^{opaqueCMBufferQueue=}",
    ),
    "CMTimebaseCopyMasterClock": (
        b"^{OpaqueCMClock=}^{OpaqueCMTimebase=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMMetadataCreateIdentifierForKeyAndKeySpace": (
        b"i^{__CFAllocator=}@^{__CFString=}^^{__CFString=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMMetadataDataTypeRegistryDataTypeConformsToDataType": (
        b"Z^{__CFString=}^{__CFString=}",
    ),
    "CMMemoryPoolInvalidate": (b"v^{OpaqueCMMemoryPool=}",),
    "CMTimeCodeFormatDescriptionGetFrameQuanta": (b"I^{opaqueCMFormatDescription=}",),
    "CMTagCategoryValueEqualToValue": (b"Z{CMTag=IIQ}{CMTag=IIQ}",),
    "CMTimebaseGetTimeWithTimeScale": (b"{CMTime=qiIq}^{OpaqueCMTimebase=}iI",),
    "CMTimeConvertScale": (b"{CMTime=qiIq}{CMTime=qiIq}iI",),
    "CMClockMakeHostTimeFromSystemUnits": (b"{CMTime=qiIq}Q",),
    "CMAudioSampleBufferCreateWithPacketDescriptionsAndMakeDataReadyHandler": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}Z^{opaqueCMFormatDescription=}q{CMTime=qiIq}^{AudioStreamPacketDescription=qII}^^{opaqueCMSampleBuffer=}@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "^{OpaqueCMSampleBuffer=}"},
                        },
                    }
                },
                6: {"c_array_length_in_arg": 5, "type_modifier": "n"},
                7: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMSampleBufferMakeDataReady": (b"i^{opaqueCMSampleBuffer=}",),
    "CMSampleBufferGetSampleAttachmentsArray": (
        b"^{__CFArray=}^{opaqueCMSampleBuffer=}Z",
    ),
    "CMTimeAdd": (b"{CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}",),
    "CMSampleBufferSetDataBuffer": (
        b"i^{opaqueCMSampleBuffer=}^{OpaqueCMBlockBuffer=}",
    ),
    "CMBlockBufferGetDataPointer": (
        b"i^{OpaqueCMBlockBuffer=}Q^Q^Q^^v",
        "",
        {
            "arguments": {
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
                4: {"c_array_length_in_arg": 2, "type_modifier": "o"},
            }
        },
    ),
    "CMMuxedFormatDescriptionCreate": (
        b"i^{__CFAllocator=}I^{__CFDictionary=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSampleBufferCreateForImageBufferWithMakeDataReadyHandler": (
        b"i^{__CFAllocator=}^{__CVBuffer=}Z^{opaqueCMFormatDescription=}^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}^^{opaqueCMSampleBuffer=}@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {"type_modifier": "n"},
                5: {"already_cfretained": True, "type_modifier": "o"},
                6: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "^{OpaqueCMSampleBuffer=}"},
                        },
                    }
                },
            },
        },
    ),
    "CMClockConvertHostTimeToSystemUnits": (b"Q{CMTime=qiIq}",),
    "CMBlockBufferFillDataBytes": (b"ic^{OpaqueCMBlockBuffer=}QQ",),
    "CMTagHasFlagsValue": (b"Z{CMTag=IIQ}",),
    "CMAudioDeviceClockSetAudioDeviceUID": (b"i^{OpaqueCMClock=}^{__CFString=}",),
    "CMTimebaseCopyUltimateSourceClock": (
        b"^{OpaqueCMClock=}^{OpaqueCMTimebase=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMGetAttachment": (
        b"@@^{__CFString=}^I",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CMTextFormatDescriptionCreateFromBigEndianTextDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}^{__CFString=}I^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {4: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTaggedBufferGroupGetCVPixelBufferForTag": (
        b"^{__CVBuffer=}^{OpaqueCMTaggedBufferGroup=}{CMTag=IIQ}^q",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CMSampleBufferGetDuration": (b"{CMTime=qiIq}^{opaqueCMSampleBuffer=}",),
    "CMMetadataFormatDescriptionCreateWithMetadataSpecifications": (
        b"i^{__CFAllocator=}I^{__CFArray=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeRangeMake": (
        b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTime=qiIq}{CMTime=qiIq}",
    ),
    "CMMemoryPoolGetTypeID": (b"Q",),
    "CMClockGetTypeID": (b"Q",),
    "CMSwapBigEndianMetadataDescriptionToHost": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMTimebaseGetTimeAndRate": (
        b"i^{OpaqueCMTimebase=}^{CMTime=qiIq}^d",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CMMetadataCreateKeySpaceFromIdentifier": (
        b"i^{__CFAllocator=}^{__CFString=}^^{__CFString=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeRangeGetEnd": (b"{CMTime=qiIq}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",),
    "CMTagCollectionCreateCopy": (
        b"i^{OpaqueCMTagCollection=}^{__CFAllocator=}^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSampleBufferInvalidate": (b"i^{opaqueCMSampleBuffer=}",),
    "CMTagHasCategory": (b"Z{CMTag=IIQ}I",),
    "CMTextFormatDescriptionGetJustification": (
        b"i^{opaqueCMFormatDescription=}^z^z",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CMAudioDeviceClockSetAudioDeviceID": (b"i^{OpaqueCMClock=}I",),
    "CMBufferQueueSetValidationCallback": (
        b"i^{opaqueCMBufferQueue=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^{opaqueCMBufferQueue=}"},
                            1: {"type": b"@"},
                            2: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "CMSampleBufferDataIsReady": (b"Z^{opaqueCMSampleBuffer=}",),
    "CMTimeCodeFormatDescriptionGetFrameDuration": (
        b"{CMTime=qiIq}^{opaqueCMFormatDescription=}",
    ),
    "CMSampleBufferCreateForTaggedBufferGroup": (
        b"i^{__CFAllocator=}^{OpaqueCMTaggedBufferGroup=}{CMTime=qiIq}{CMTime=qiIq}^{opaqueCMFormatDescription=}^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {5: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMVideoFormatDescriptionMatchesImageBuffer": (
        b"Z^{opaqueCMFormatDescription=}^{__CVBuffer=}",
    ),
    "CMTimebaseSetTimerNextFireTime": (
        b"i^{OpaqueCMTimebase=}^{__CFRunLoopTimer=}{CMTime=qiIq}I",
    ),
    "CMSwapBigEndianTextDescriptionToHost": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMTagMakeFromDictionary": (b"{CMTag=IIQ}^{__CFDictionary=}",),
    "CMSampleBufferGetDecodeTimeStamp": (b"{CMTime=qiIq}^{opaqueCMSampleBuffer=}",),
    "CMSimpleQueueGetCount": (b"i^{opaqueCMSimpleQueue=}",),
    "CMTimeCodeFormatDescriptionCreateFromBigEndianTimeCodeDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeCompare": (b"i{CMTime=qiIq}{CMTime=qiIq}",),
    "CMBufferQueueInstallTriggerWithIntegerThreshold": (
        b"i^{opaqueCMBufferQueue=}^?^viq^^{opaqueCMBufferQueueTriggerToken=}",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMBufferQueueTriggerToken=}"},
                        },
                    },
                    "callable_retained": True,
                },
                5: {"type_modifier": "o"},
            }
        },
    ),
    "CMTagCollectionAddTagsFromCollection": (
        b"i^{OpaqueCMTagCollection=}^{OpaqueCMTagCollection=}",
    ),
    "CMSampleBufferGetAudioBufferListWithRetainedBlockBuffer": (
        b"i^{opaqueCMSampleBuffer=}^Q^{AudioBufferList=I[1{AudioBuffer=II^v}]}Q^{__CFAllocator=}^{__CFAllocator=}I^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "arguments": {
                1: {"type_modifier": "o"},
                7: {"already_cfretained": True, "type_modifier": "o"},
            }
        },
    ),
    "CMFormatDescriptionGetTypeID": (b"Q",),
    "CMMetadataCreateKeyFromIdentifierAsCFData": (
        b"i^{__CFAllocator=}^{__CFString=}^^{__CFData=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMAudioDeviceClockCreateFromAudioDeviceID": (
        b"i^{__CFAllocator=}I^^{OpaqueCMClock=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimebaseAddTimerDispatchSource": (b"i^{OpaqueCMTimebase=}@",),
    "CMClockGetHostTimeClock": (b"^{OpaqueCMClock=}",),
    "CMTimebaseGetUltimateMasterClock": (b"^{OpaqueCMClock=}^{OpaqueCMTimebase=}",),
    "CMTagCollectionCopyAsDictionary": (
        b"^{__CFDictionary=}^{OpaqueCMTagCollection=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMTimeMultiplyByRatio": (b"{CMTime=qiIq}{CMTime=qiIq}ii",),
    "CMTagMakeWithFlagsValue": (b"{CMTag=IIQ}IQ",),
    "CMAudioSampleBufferCreateWithPacketDescriptions": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}Z^?^v^{opaqueCMFormatDescription=}q{CMTime=qiIq}^{AudioStreamPacketDescription=qII}^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {"c_array_length_in_arg": 6, "type_modifier": "n"},
                9: {"already_cfretained": True, "type_modifier": "o"},
                3: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^{opaqueCMSampleBuffer=}"},
                            1: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                },
            },
        },
    ),
    "CMSampleBufferCallForEachSample": (
        b"i^{opaqueCMSampleBuffer=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^{opaqueCMSampleBuffer=}"},
                            1: {"type": b"q"},
                            2: {"type": b"^v"},
                        },
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CMTimeShow": (b"v{CMTime=qiIq}",),
    "CMTimebaseSetTime": (b"i^{OpaqueCMTimebase=}{CMTime=qiIq}",),
    "CMTagCollectionCopyAsData": (
        b"^{__CFData=}^{OpaqueCMTagCollection=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMTaggedBufferGroupCreate": (
        b"i^{__CFAllocator=}^{__CFArray=}^{__CFArray=}^^{OpaqueCMTaggedBufferGroup=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeMake": (b"{CMTime=qiIq}qi",),
    "CMClockGetTime": (b"{CMTime=qiIq}^{OpaqueCMClock=}",),
    "CMSampleBufferCreateCopyWithNewTiming": (
        b"i^{__CFAllocator=}^{opaqueCMSampleBuffer=}q^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMSampleBufferGetTotalSampleSize": (b"Q^{opaqueCMSampleBuffer=}",),
    "CMTextFormatDescriptionGetDefaultStyle": (
        b"i^{opaqueCMFormatDescription=}^S^Z^Z^Z^d^d",
        "",
        {
            "arguments": {
                1: {"type_modifier": "o"},
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
                4: {"type_modifier": "o"},
                5: {"type_modifier": "o"},
                6: {"c_array_of_fixed_length": 4, "type_modifier": "o"},
            }
        },
    ),
    "CMBlockBufferAccessDataBytes": (
        b"i^{OpaqueCMBlockBuffer=}QQ^v^^v",
        "",
        {
            "arguments": {4: {"type_modifier": "o"}},
            "suggestion": "Use CMBlockBufferCopyDataBytes",
        },
    ),
    "CMAudioFormatDescriptionCreateSummary": (
        b"i^{__CFAllocator=}^{__CFArray=}I^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSwapBigEndianClosedCaptionDescriptionToHost": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMSampleBufferGetPresentationTimeStamp": (
        b"{CMTime=qiIq}^{opaqueCMSampleBuffer=}",
    ),
    "CMTimeRangeContainsTime": (
        b"Z{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTime=qiIq}",
    ),
    "CMTagCollectionRemoveTag": (b"i^{OpaqueCMTagCollection=}{CMTag=IIQ}",),
    "CMTagCollectionCopyDescription": (
        b"^{__CFString=}^{__CFAllocator=}^{OpaqueCMTagCollection=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMTimebaseGetTypeID": (b"Q",),
    "CMMetadataFormatDescriptionCreateFromBigEndianMetadataDescriptionData": (
        b"i^{__CFAllocator=}^CQ^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMTimebaseCreateWithSourceTimebase": (
        b"i^{__CFAllocator=}^{OpaqueCMTimebase=}^^{OpaqueCMTimebase=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMClosedCaptionFormatDescriptionCreateFromBigEndianClosedCaptionDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeMaximum": (b"{CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}",),
    "CMTimeCodeFormatDescriptionCreate": (
        b"i^{__CFAllocator=}I{CMTime=qiIq}II^{__CFDictionary=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {6: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimebaseCreateWithSourceClock": (
        b"i^{__CFAllocator=}^{OpaqueCMClock=}^^{OpaqueCMTimebase=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTagCollectionIsEmpty": (b"Z^{OpaqueCMTagCollection=}",),
    "CMTimeRangeEqual": (
        b"Z{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMTimeCodeFormatDescriptionGetTimeCodeFlags": (b"I^{opaqueCMFormatDescription=}",),
    "CMSetAttachments": (b"v@^{__CFDictionary=}I",),
    "CMSampleBufferCreateWithMakeDataReadyHandler": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}Z^{OpaqueCMFormatDescription=}ll^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}l^Q@?@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {"c_array_length_in_arg": 7, "type_modifier": "n"},
                9: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "^{OpaqueCMSampleBuffer=}"},
                        },
                    }
                },
                6: {"c_array_length_in_arg": 5, "type_modifier": "n"},
            },
        },
    ),
    "CMSampleBufferCreate": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}Z^?^v^{opaqueCMFormatDescription=}qq^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}q^Q^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {"c_array_length_in_arg": 7, "type_modifier": "n"},
                11: {"already_cfretained": True, "type_modifier": "o"},
                10: {"c_array_length_in_arg": 9, "type_modifier": "n"},
                3: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^{opaqueCMSampleBuffer=}"},
                            1: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                },
            },
        },
    ),
    "CMTimeCodeFormatDescriptionCopyAsBigEndianTimeCodeDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{opaqueCMFormatDescription=}^{__CFString=}^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSyncGetRelativeRateAndAnchorTime": (
        b"i@@^d^{CMTime=qiIq}^{CMTime=qiIq}",
        "",
        {
            "arguments": {
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
                4: {"type_modifier": "o"},
            }
        },
    ),
    "CMTaggedBufferGroupFormatDescriptionCreateForTaggedBufferGroup": (
        b"i^{__CFAllocator=}^{OpaqueCMTaggedBufferGroup=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeClampToRange": (
        b"{CMTime=qiIq}{CMTime=qiIq}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMTimeMappingMakeEmpty": (
        b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMTimeAbsoluteValue": (b"{CMTime=qiIq}{CMTime=qiIq}",),
    "CMSwapHostEndianTextDescriptionToBig": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMSimpleQueueCreate": (
        b"i^{__CFAllocator=}i^^{opaqueCMSimpleQueue=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSampleBufferGetImageBuffer": (b"^{__CVBuffer=}^{opaqueCMSampleBuffer=}",),
    "CMTagCollectionGetTagsWithCategory": (
        b"i^{OpaqueCMTagCollection=}I^{CMTag=IIQ}q^q",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": (3, 4), "type_modifier": "o"},
                4: {"type_modifier": "o"},
            }
        },
    ),
    "CMBufferQueueGetBufferCount": (b"q^{opaqueCMBufferQueue=}",),
    "CMTimeMappingMake": (
        b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMSampleBufferCopyPCMDataIntoAudioBufferList": (
        b"i^{opaqueCMSampleBuffer=}ii^{AudioBufferList=I[1{AudioBuffer=II^v}]}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMAudioFormatDescriptionGetMostCompatibleFormat": (
        b"^{AudioFormatListItem={AudioStreamBasicDescription=dIIIIIIII}I}^{opaqueCMFormatDescription=}",
        "",
        {"retval": {"deref_result_pointer": True}},
    ),
    "CMTextFormatDescriptionGetFontName": (
        b"i^{opaqueCMFormatDescription=}S^^{__CFString=}",
        "",
        {"arguments": {2: {"already_cfretained": True, "type_modifier": "o"}}},
    ),
    "CMTimeRangeCopyDescription": (
        b"^{__CFString=}^{__CFAllocator=}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMMetadataFormatDescriptionCreateWithKeys": (
        b"i^{__CFAllocator=}I^{__CFArray=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSampleBufferGetAudioStreamPacketDescriptions": (
        b"i^{opaqueCMSampleBuffer=}Q^{AudioStreamPacketDescription=qII}^Q",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": 1, "type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CMTagCategoryEqualToTagCategory": (b"Z{CMTag=IIQ}{CMTag=IIQ}",),
    "CMBufferQueueGetFirstDecodeTimeStamp": (b"{CMTime=qiIq}^{opaqueCMBufferQueue=}",),
    "CMSwapHostEndianClosedCaptionDescriptionToBig": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMTagMakeWithOSTypeValue": (b"{CMTag=IIQ}II",),
    "CMTimebaseGetMaster": (b"@^{OpaqueCMTimebase=}",),
    "CMSimpleQueueEnqueue": (b"i^{opaqueCMSimpleQueue=}@",),
    "CMTaggedBufferGroupGetCount": (b"q^{OpaqueCMTaggedBufferGroup=}",),
    "CMAudioFormatDescriptionGetRichestDecodableFormat": (
        b"^{AudioFormatListItem={AudioStreamBasicDescription=dIIIIIIII}I}^{opaqueCMFormatDescription=}",
        "",
        {"retval": {"deref_result_pointer": True}},
    ),
    "CMSyncGetRelativeRate": (b"d@@",),
    "CMTimebaseSetSourceTimebase": (b"i^{OpaqueCMTimebase=}^{OpaqueCMTimebase=}",),
    "CMSampleBufferGetDataBuffer": (
        b"^{OpaqueCMBlockBuffer=}^{opaqueCMSampleBuffer=}",
    ),
    "CMMetadataFormatDescriptionCopyAsBigEndianMetadataDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{opaqueCMFormatDescription=}^{__CFString=}^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimebaseCopyUltimateMasterClock": (
        b"^{OpaqueCMClock=}^{OpaqueCMTimebase=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMBufferQueueDequeueAndRetain": (
        b"@^{opaqueCMBufferQueue=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMTimebaseRemoveTimerDispatchSource": (b"i^{OpaqueCMTimebase=}@",),
    "CMTimeGetSeconds": (b"d{CMTime=qiIq}",),
    "CMTimebaseGetEffectiveRate": (b"d^{OpaqueCMTimebase=}",),
    "CMTagCollectionCreateMutableCopy": (
        b"i^{OpaqueCMTagCollection=}^{__CFAllocator=}^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMMemoryPoolCreate": (
        b"^{OpaqueCMMemoryPool=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMSwapBigEndianImageDescriptionToHost": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMSampleBufferGetOutputPresentationTimeStamp": (
        b"{CMTime=qiIq}^{opaqueCMSampleBuffer=}",
    ),
    "CMMemoryPoolGetAllocator": (b"^{__CFAllocator=}^{OpaqueCMMemoryPool=}",),
    "CMSampleBufferCallBlockForEachSample": (
        b"i^{opaqueCMSampleBuffer=}@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMSampleBuffer=}"},
                            2: {"type": b"q"},
                        },
                    }
                }
            }
        },
    ),
    "CMBlockBufferCreateContiguous": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}^{__CFAllocator=}^{CMBlockBufferCustomBlockSource=I^?^?^v}QQI^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {"type_modifier": "n"},
                7: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMTimeCodeFormatDescriptionCreateFromBigEndianTimeCodeDescriptionData": (
        b"i^{__CFAllocator=}^CQ^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMTimebaseSetRateAndAnchorTime": (
        b"i^{OpaqueCMTimebase=}d{CMTime=qiIq}{CMTime=qiIq}",
    ),
    "CMMetadataDataTypeRegistryGetBaseDataTypeForConformingDataType": (
        b"^{__CFString=}^{__CFString=}",
    ),
    "CMSyncMightDrift": (b"Z@@",),
    "CMTagCollectionContainsTag": (b"Z^{OpaqueCMTagCollection=}{CMTag=IIQ}",),
    "CMVideoFormatDescriptionGetExtensionKeysCommonWithImageBuffers": (
        b"^{__CFArray=}",
    ),
    "CMTagCollectionGetTags": (
        b"i^{OpaqueCMTagCollection=}^{CMTag=IIQ}q^q",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": (2, 3), "type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CMTaggedBufferGroupGetTypeID": (b"Q",),
    "CMClockGetAnchorTime": (
        b"i^{OpaqueCMClock=}^{CMTime=qiIq}^{CMTime=qiIq}",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CMTimeMappingShow": (
        b"v{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}",
    ),
    "CMTimeRangeGetIntersection": (
        b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMTagCollectionRemoveAllTags": (b"i^{OpaqueCMTagCollection=}",),
    "CMTimeMapDurationFromRangeToRange": (
        b"{CMTime=qiIq}{CMTime=qiIq}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMTimebaseCopySourceClock": (
        b"^{OpaqueCMClock=}^{OpaqueCMTimebase=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMSampleBufferGetTaggedBufferGroup": (
        b"^{OpaqueCMTaggedBufferGroup=}^{opaqueCMSampleBuffer=}",
    ),
    "CMTaggedBufferGroupGetCVPixelBufferAtIndex": (
        b"^{__CVBuffer=}^{OpaqueCMTaggedBufferGroup=}q",
    ),
    "CMMemoryPoolFlush": (b"v^{OpaqueCMMemoryPool=}",),
    "CMTagCollectionApply": (
        b"v^{OpaqueCMTagCollection=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"{CMTag=IIQ}"}, 1: {"type": b"^v"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CMAudioFormatDescriptionGetFormatList": (
        b"^{AudioFormatListItem={AudioStreamBasicDescription=dIIIIIIII}I}^{opaqueCMFormatDescription=}^Q",
        "",
        {
            "retval": {"c_array_length_in_arg": 1},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "CMSampleBufferGetFormatDescription": (
        b"^{opaqueCMFormatDescription=}^{opaqueCMSampleBuffer=}",
    ),
    "CMVideoFormatDescriptionCopyTagCollectionArray": (
        b"i^{opaqueCMFormatDescription=}^^{__CFArray=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTagGetOSTypeValue": (b"I{CMTag=IIQ}",),
    "CMTagGetValueDataType": (b"I{CMTag=IIQ}",),
    "CMMetadataDataTypeRegistryGetConformingDataTypes": (
        b"^{__CFArray=}^{__CFString=}",
    ),
    "CMAudioFormatDescriptionCreateFromBigEndianSoundDescriptionData": (
        b"i^{__CFAllocator=}^CQ^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMSampleBufferGetOutputDuration": (b"{CMTime=qiIq}^{opaqueCMSampleBuffer=}",),
    "CMTimebaseSetMasterClock": (b"i^{OpaqueCMTimebase=}^{OpaqueCMClock=}",),
    "CMTagCollectionCreateFromDictionary": (
        b"i^{__CFDictionary=}^{__CFAllocator=}^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTextFormatDescriptionGetDisplayFlags": (
        b"i^{opaqueCMFormatDescription=}^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CMTimebaseAddTimer": (
        b"i^{OpaqueCMTimebase=}^{__CFRunLoopTimer=}^{__CFRunLoop=}",
    ),
    "CMClosedCaptionFormatDescriptionCopyAsBigEndianClosedCaptionDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{opaqueCMFormatDescription=}^{__CFString=}^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSampleBufferCreateForImageBuffer": (
        b"i^{__CFAllocator=}^{__CVBuffer=}Z^?^v^{opaqueCMFormatDescription=}^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^{opaqueCMSampleBuffer=}"},
                            1: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                },
                6: {"type_modifier": "n"},
                7: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMTimeMultiplyByFloat64": (b"{CMTime=qiIq}{CMTime=qiIq}d",),
    "CMSimpleQueueGetHead": (b"@^{opaqueCMSimpleQueue=}",),
    "CMTimeMultiply": (b"{CMTime=qiIq}{CMTime=qiIq}i",),
    "CMVideoFormatDescriptionGetCleanAperture": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}^{opaqueCMFormatDescription=}Z",
    ),
    "CMTimeMinimum": (b"{CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}",),
    "CMAudioFormatDescriptionGetChannelLayout": (
        b"^{AudioChannelLayout=III[1{AudioChannelDescription=II[3f]}]}^{opaqueCMFormatDescription=}^Q",
        "",
        {
            "retval": {"c_array_length_in_arg": 1},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "CMSimpleQueueGetTypeID": (b"Q",),
    "CMPropagateAttachments": (b"v@@",),
    "CMTimebaseGetRate": (b"d^{OpaqueCMTimebase=}",),
    "CMTagCompare": (b"q{CMTag=IIQ}{CMTag=IIQ}",),
    "CMFormatDescriptionCreate": (
        b"i^{__CFAllocator=}II^{__CFDictionary=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {4: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMBufferQueueIsEmpty": (b"Z^{opaqueCMBufferQueue=}",),
    "CMAudioFormatDescriptionGetStreamBasicDescription": (
        b"^{AudioStreamBasicDescription=dIIIIIIII}^{opaqueCMFormatDescription=}",
        "",
        {"retval": {"deref_result_pointer": True}},
    ),
    "CMTimebaseGetMasterClock": (b"^{OpaqueCMClock=}^{OpaqueCMTimebase=}",),
    "CMClockMightDrift": (b"Z^{OpaqueCMClock=}^{OpaqueCMClock=}",),
    "CMTimebaseSetRate": (b"i^{OpaqueCMTimebase=}d",),
    "CMTagCollectionCreateDifference": (
        b"i^{OpaqueCMTagCollection=}^{OpaqueCMTagCollection=}^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimebaseCreateWithMasterClock": (
        b"i^{__CFAllocator=}^{OpaqueCMClock=}^^{OpaqueCMTimebase=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimeRangeFromTimeToTime": (
        b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTime=qiIq}{CMTime=qiIq}",
    ),
    "CMMetadataFormatDescriptionGetIdentifiers": (
        b"^{__CFArray=}^{opaqueCMFormatDescription=}",
    ),
    "CMClosedCaptionFormatDescriptionCreateFromBigEndianClosedCaptionDescriptionData": (
        b"i^{__CFAllocator=}^CQ^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMSampleBufferGetSampleTimingInfoArray": (
        b"i^{opaqueCMSampleBuffer=}q^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}^q",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": 1, "type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CMBufferQueueTestTrigger": (
        b"Z^{opaqueCMBufferQueue=}^{opaqueCMBufferQueueTriggerToken=}",
    ),
    "CMFormatDescriptionGetMediaType": (b"I^{opaqueCMFormatDescription=}",),
    "CMMetadataDataTypeRegistryRegisterDataType": (
        b"i^{__CFString=}^{__CFString=}^{__CFArray=}",
    ),
    "CMBufferQueueGetTypeID": (b"Q",),
    "CMBufferQueueCallForEachBuffer": (
        b"i^{opaqueCMBufferQueue=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {0: {"type": b"@"}, 1: {"type": b"^v"}},
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "CMTagHasSInt64Value": (b"Z{CMTag=IIQ}",),
    "CMSwapHostEndianSoundDescriptionToBig": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMTagCollectionAddTag": (b"i^{OpaqueCMTagCollection=}{CMTag=IIQ}",),
    "CMSampleBufferTrackDataReadiness": (
        b"i^{opaqueCMSampleBuffer=}^{opaqueCMSampleBuffer=}",
    ),
    "CMMetadataDataTypeRegistryDataTypeIsBaseDataType": (b"Z^{__CFString=}",),
    "CMAudioFormatDescriptionCreate": (
        b"i^{__CFAllocator=}^{AudioStreamBasicDescription=dIIIIIIII}Q^{AudioChannelLayout=III[1{AudioChannelDescription=II[3f]}]}Q^v^{__CFDictionary=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"type_modifier": "n"},
                3: {"type_modifier": "n"},
                5: {"c_array_length_in_arg": 4, "type_modifier": "n"},
                7: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMSwapHostEndianMetadataDescriptionToBig": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMSampleBufferGetNumSamples": (b"q^{opaqueCMSampleBuffer=}",),
    "CMTagGetFloat64Value": (b"d{CMTag=IIQ}",),
    "CMSampleBufferCopySampleBufferForRange": (
        b"i^{__CFAllocator=}^{opaqueCMSampleBuffer=}{CFRange=qq}^^{opaqueCMSampleBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimebaseGetMasterTimebase": (b"^{OpaqueCMTimebase=}^{OpaqueCMTimebase=}",),
    "CMSimpleQueueGetCapacity": (b"i^{opaqueCMSimpleQueue=}",),
    "CMTimebaseSetSourceClock": (b"i^{OpaqueCMTimebase=}^{OpaqueCMClock=}",),
    "CMTimeMappingCopyAsDictionary": (
        b"^{__CFDictionary=}{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMFormatDescriptionGetMediaSubType": (b"I^{opaqueCMFormatDescription=}",),
    "CMVideoFormatDescriptionGetPresentationDimensions": (
        b"{CGSize=dd}^{opaqueCMFormatDescription=}ZZ",
    ),
    "CMTagCollectionGetTagsWithFilterFunction": (
        b"i^{OpaqueCMTagCollection=}^{CMTag=IIQ}q^q^?^v",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": (2, 3), "type_modifier": "o"},
                3: {"type_modifier": "o"},
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {0: {"type": b"{CMTag=IIQ}"}, 1: {"type": b"^v"}},
                    },
                    "callable_retained": False,
                },
            }
        },
    ),
    "CMTagCollectionCreateIntersection": (
        b"i^{OpaqueCMTagCollection=}^{OpaqueCMTagCollection=}^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMBufferQueueGetEndPresentationTimeStamp": (
        b"{CMTime=qiIq}^{opaqueCMBufferQueue=}",
    ),
    "CMAudioDeviceClockGetAudioDevice": (
        b"i^{OpaqueCMClock=}^^{__CFString=}^I^Z",
        "",
        {
            "arguments": {
                1: {"type_modifier": "o"},
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CMTagCollectionCreateMutable": (
        b"i^{__CFAllocator=}q^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMMetadataFormatDescriptionCreateWithMetadataFormatDescriptionAndMetadataSpecifications": (
        b"i^{__CFAllocator=}^{opaqueCMFormatDescription=}^{__CFArray=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSampleBufferGetSampleTimingInfo": (
        b"i^{opaqueCMSampleBuffer=}q^{CMSampleTimingInfo={CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CMBlockBufferGetTypeID": (b"Q",),
    "CMTagCollectionCreateUnion": (
        b"i^{OpaqueCMTagCollection=}^{OpaqueCMTagCollection=}^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMDoesBigEndianSoundDescriptionRequireLegacyCBRSampleTableLayout": (
        b"Z^{OpaqueCMBlockBuffer=}^{__CFString=}",
    ),
    "CMTimebaseCopyMaster": (
        b"@^{OpaqueCMTimebase=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMAudioFormatDescriptionGetMagicCookie": (
        b"^v^{opaqueCMFormatDescription=}^Q",
        "",
        {
            "retval": {"c_array_length_in_arg": 1},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "CMFormatDescriptionGetExtensions": (
        b"^{__CFDictionary=}^{opaqueCMFormatDescription=}",
    ),
    "CMSampleBufferGetTypeID": (b"Q",),
    "CMBlockBufferCreateWithMemoryBlock": (
        b"i^{__CFAllocator=}^vQ^{__CFAllocator=}^{CMBlockBufferCustomBlockSource=I^?^?^v}QQI^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {"already_cfretained": True, "type_modifier": "o"},
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {"type_modifier": "n"},
            },
        },
    ),
    "CMTimeMakeFromDictionary": (b"{CMTime=qiIq}^{__CFDictionary=}",),
    "CMSampleBufferSetInvalidateHandler": (
        b"i^{opaqueCMSampleBuffer=}@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "^{opaqueCMSampleBuffer=}"},
                        },
                    }
                }
            }
        },
    ),
    "CMTaggedBufferGroupGetNumberOfMatchesForTagCollection": (
        b"q^{OpaqueCMTaggedBufferGroup=}^{OpaqueCMTagCollection=}",
    ),
    "CMTagMakeWithSInt64Value": (b"{CMTag=IIQ}Iq",),
    "CMBufferQueueInstallTriggerHandler": (
        b"i@I{CMTime=qiIq}^^{opaqueCMBufferQueueTriggerToken=}@?",
        "",
        {
            "arguments": {
                3: {"type_modifier": "o"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "^{opaqueCMBufferQueueTriggerToken=}"},
                        },
                    }
                },
            }
        },
    ),
    "CMTagCollectionContainsSpecifiedTags": (
        b"Z^{OpaqueCMTagCollection=}^{CMTag=IIQ}q",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CMAudioClockCreate": (
        b"i^{__CFAllocator=}^^{OpaqueCMClock=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMBlockBufferIsRangeContiguous": (b"Z^{OpaqueCMBlockBuffer=}QQ",),
    "CMTimeRangeContainsTimeRange": (
        b"Z{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMTagCollectionGetTypeID": (b"Q",),
    "CMTimebaseCopySource": (
        b"@^{OpaqueCMTimebase=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMBufferQueueReset": (b"i^{opaqueCMBufferQueue=}",),
    "CMTimeMakeWithSeconds": (b"{CMTime=qiIq}di",),
    "CMTextFormatDescriptionCopyAsBigEndianTextDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{opaqueCMFormatDescription=}^{__CFString=}^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTagCollectionCopyTagsOfCategories": (
        b"i^{__CFAllocator=}^{OpaqueCMTagCollection=}^Iq^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
                4: {"already_cfretained": True, "type_modifier": "o"},
            },
        },
    ),
    "CMTimeMapTimeFromRangeToRange": (
        b"{CMTime=qiIq}{CMTime=qiIq}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
    ),
    "CMBufferQueueContainsEndOfData": (b"Z^{opaqueCMBufferQueue=}",),
    "CMTaggedBufferGroupGetCVPixelBufferForTagCollection": (
        b"^{__CVBuffer=}^{OpaqueCMTaggedBufferGroup=}^{OpaqueCMTagCollection=}^q",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CMTimebaseNotificationBarrier": (b"i^{OpaqueCMTimebase=}",),
    "CMTimebaseSetMasterTimebase": (b"i^{OpaqueCMTimebase=}^{OpaqueCMTimebase=}",),
    "CMVideoFormatDescriptionGetDimensions": (
        b"{CMVideoDimensions=ii}^{opaqueCMFormatDescription=}",
    ),
    "CMBlockBufferAssureBlockMemory": (b"i^{OpaqueCMBlockBuffer=}",),
    "CMClockInvalidate": (b"v^{OpaqueCMClock=}",),
    "CMTagCollectionContainsCategory": (b"Z^{OpaqueCMTagCollection=}I",),
    "CMMetadataDataTypeRegistryDataTypeIsRegistered": (b"Z^{__CFString=}",),
    "CMTaggedBufferGroupGetCMSampleBufferAtIndex": (
        b"^{opaqueCMSampleBuffer=}^{OpaqueCMTaggedBufferGroup=}q",
    ),
    "CMTagGetCategory": (b"I{CMTag=IIQ}",),
    "CMBlockBufferCreateWithBufferReference": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}QQI^^{OpaqueCMBlockBuffer=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {5: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMAudioFormatDescriptionCreateFromBigEndianSoundDescriptionBlockBuffer": (
        b"i^{__CFAllocator=}^{OpaqueCMBlockBuffer=}^{__CFString=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMSwapBigEndianTimeCodeDescriptionToHost": (
        b"i^CQ",
        "",
        {"arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "N"}}},
    ),
    "CMTimeSubtract": (b"{CMTime=qiIq}{CMTime=qiIq}{CMTime=qiIq}",),
    "CMMetadataFormatDescriptionCreateByMergingMetadataFormatDescriptions": (
        b"i^{__CFAllocator=}^{opaqueCMFormatDescription=}^{opaqueCMFormatDescription=}^^{opaqueCMFormatDescription=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "CMTimebaseCopyMasterTimebase": (
        b"^{OpaqueCMTimebase=}^{OpaqueCMTimebase=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMSimpleQueueReset": (b"i^{opaqueCMSimpleQueue=}",),
    "CMSampleBufferGetOutputDecodeTimeStamp": (
        b"{CMTime=qiIq}^{opaqueCMSampleBuffer=}",
    ),
    "CMSampleBufferSetInvalidateCallback": (
        b"i^{opaqueCMSampleBuffer=}^?Q",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{opaqueCMSampleBuffer=}"},
                            1: {"type": b"Q"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "CMBufferQueueResetWithCallback": (
        b"i^{opaqueCMBufferQueue=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"@"}, 1: {"type": b"^v"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CMBlockBufferCopyDataBytes": (
        b"i^{OpaqueCMBlockBuffer=}QQ^v",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"c_array_length_in_arg": 2, "type_modifier": "o"}},
        },
    ),
    "CMTagGetValue": (b"Q{CMTag=IIQ}",),
    "CMSampleBufferGetSampleSizeArray": (
        b"i^{opaqueCMSampleBuffer=}q^Q^q",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": 1, "type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CMTagCollectionRemoveAllTagsOfCategory": (b"i^{OpaqueCMTagCollection=}I",),
    "CMTimebaseSetTimerDispatchSourceToFireImmediately": (b"i^{OpaqueCMTimebase=}@",),
    "CMRemoveAttachment": (b"v@^{__CFString=}",),
    "CMTagCopyAsDictionary": (
        b"^{__CFDictionary=}{CMTag=IIQ}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CMTagCollectionContainsTagsOfCollection": (
        b"Z^{OpaqueCMTagCollection=}^{OpaqueCMTagCollection=}",
    ),
    "CMTagCollectionCreateFromData": (
        b"i^{__CFData=}^{__CFAllocator=}^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
}
aliases = {
    "CMSubtitleFormatDescriptionGetFormatType": "CMFormatDescriptionGetMediaSubType",
    "COREMEDIA_DECLARE_BRIDGED_TYPES": "COREMEDIA_TRUE",
    "CMVideoFormatDescriptionGetCodecType": "CMFormatDescriptionGetMediaSubType",
    "COREMEDIA_DECLARE_NULLABILITY_BEGIN_END": "COREMEDIA_TRUE",
    "kCMFormatDescriptionExtension_YCbCrMatrix": "kCVImageBufferYCbCrMatrixKey",
    "kCMFormatDescriptionExtension_FieldCount": "kCVImageBufferFieldCountKey",
    "CM_RETURNS_NOT_RETAINED_PARAMETER": "CF_RETURNS_NOT_RETAINED",
    "kCMFormatDescriptionExtension_GammaLevel": "kCVImageBufferGammaLevelKey",
    "kCMFormatDescriptionChromaLocation_Bottom": "kCVImageBufferChromaLocation_Bottom",
    "kCMFormatDescriptionKey_CleanApertureVerticalOffset": "kCVImageBufferCleanApertureVerticalOffsetKey",
    "CM_RETURNS_RETAINED": "CF_RETURNS_RETAINED",
    "kCMFormatDescriptionYCbCrMatrix_SMPTE_240M_1995": "kCVImageBufferYCbCrMatrix_SMPTE_240M_1995",
    "kCMFormatDescriptionExtension_ColorPrimaries": "kCVImageBufferColorPrimariesKey",
    "kCMFormatDescriptionYCbCrMatrix_ITU_R_601_4": "kCVImageBufferYCbCrMatrix_ITU_R_601_4",
    "kCMFormatDescriptionColorPrimaries_SMPTE_C": "kCVImageBufferColorPrimaries_SMPTE_C",
    "COREMEDIA_DECLARE_RELEASES_ARGUMENT": "COREMEDIA_TRUE",
    "CM_NULLABLE": "__nullable",
    "COREMEDIA_DECLARE_RETURNS_NOT_RETAINED_ON_PARAMETERS": "COREMEDIA_TRUE",
    "kCMFormatDescriptionChromaLocation_Left": "kCVImageBufferChromaLocation_Left",
    "kCMFormatDescriptionTransferFunction_UseGamma": "kCVImageBufferTransferFunction_UseGamma",
    "kCMTimeRoundingMethod_Default": "kCMTimeRoundingMethod_RoundHalfAwayFromZero",
    "kCMFormatDescriptionKey_PixelAspectRatioVerticalSpacing": "kCVImageBufferPixelAspectRatioVerticalSpacingKey",
    "CM_NONNULL": "__nonnull",
    "kCMFormatDescriptionKey_PixelAspectRatioHorizontalSpacing": "kCVImageBufferPixelAspectRatioHorizontalSpacingKey",
    "kCMVideoCodecType_422YpCbCr8": "kCMPixelFormat_422YpCbCr8",
    "kCMFormatDescriptionExtension_VerbatimImageDescription": "kCMFormatDescriptionExtension_VerbatimSampleDescription",
    "kCMFormatDescriptionExtension_ChromaLocationTopField": "kCVImageBufferChromaLocationTopFieldKey",
    "kCMFormatDescriptionExtension_PixelAspectRatio": "kCVImageBufferPixelAspectRatioKey",
    "COREMEDIA_CMBASECLASS_VERSION_IS_POINTER_ALIGNED": selAorI(
        "COREMEDIA_TRUE", "COREMEDIA_FALSE"
    ),
    "CM_RELEASES_ARGUMENT": "CF_RELEASES_ARGUMENT",
    "COREMEDIA_DECLARE_RETURNS_RETAINED_BLOCK": "COREMEDIA_TRUE",
    "kCMFormatDescriptionTransferFunction_SMPTE_240M_1995": "kCVImageBufferTransferFunction_SMPTE_240M_1995",
    "kCMFormatDescriptionExtension_ChromaLocationBottomField": "kCVImageBufferChromaLocationBottomFieldKey",
    "kCMFormatDescriptionExtension_TransferFunction": "kCVImageBufferTransferFunctionKey",
    "kCMTimebaseFarFutureCFAbsoluteTime": "kCMTimebaseVeryLongCFTimeInterval",
    "CM_RETURNS_RETAINED_PARAMETER": "CF_RETURNS_RETAINED",
    "kCMFormatDescriptionKey_CleanApertureHorizontalOffset": "kCVImageBufferCleanApertureHorizontalOffsetKey",
    "kCMFormatDescriptionTransferFunction_ITU_R_709_2": "kCVImageBufferTransferFunction_ITU_R_709_2",
    "kCMFormatDescriptionColorPrimaries_EBU_3213": "kCVImageBufferColorPrimaries_EBU_3213",
    "COREMEDIA_DECLARE_NULLABILITY": "COREMEDIA_TRUE",
    "CMMutableTagCollectionRef": "CMTagCollectionRef",
    "kCMFormatDescriptionKey_CleanApertureWidth": "kCVImageBufferCleanApertureWidthKey",
    "CM_RETURNS_RETAINED_BLOCK": "DISPATCH_RETURNS_RETAINED_BLOCK",
    "kCMFormatDescriptionExtension_FieldDetail": "kCVImageBufferFieldDetailKey",
    "kCMFormatDescriptionFieldDetail_SpatialFirstLineLate": "kCVImageBufferFieldDetailSpatialFirstLineLate",
    "COREMEDIA_DECLARE_RETURNS_RETAINED": "COREMEDIA_TRUE",
    "kCMFormatDescriptionColorPrimaries_ITU_R_709_2": "kCVImageBufferColorPrimaries_ITU_R_709_2",
    "COREMEDIA_USE_ALIGNED_CMBASECLASS_VERSION": "COREMEDIA_TRUE",
    "kCMFormatDescriptionChromaLocation_DV420": "kCVImageBufferChromaLocation_DV420",
    "COREMEDIA_DECLARE_RETURNS_RETAINED_ON_PARAMETERS": "COREMEDIA_TRUE",
    "kCMFormatDescriptionChromaLocation_TopLeft": "kCVImageBufferChromaLocation_TopLeft",
    "kCMFormatDescriptionFieldDetail_SpatialFirstLineEarly": "kCVImageBufferFieldDetailSpatialFirstLineEarly",
    "kCMFormatDescriptionFieldDetail_TemporalBottomFirst": "kCVImageBufferFieldDetailTemporalBottomFirst",
    "kCMFormatDescriptionExtension_CleanAperture": "kCVImageBufferCleanApertureKey",
    "kCMFormatDescriptionYCbCrMatrix_ITU_R_709_2": "kCVImageBufferYCbCrMatrix_ITU_R_709_2",
    "COREMEDIA_USE_DERIVED_ENUMS_FOR_CONSTANTS": "COREMEDIA_TRUE",
    "kCMFormatDescriptionKey_CleanApertureHeight": "kCVImageBufferCleanApertureHeightKey",
    "kCMFormatDescriptionFieldDetail_TemporalTopFirst": "kCVImageBufferFieldDetailTemporalTopFirst",
    "kCMFormatDescriptionChromaLocation_Center": "kCVImageBufferChromaLocation_Center",
    "CMITEMCOUNT_MAX": "INTPTR_MAX",
    "kCMFormatDescriptionChromaLocation_BottomLeft": "kCVImageBufferChromaLocation_BottomLeft",
    "kCMFormatDescriptionChromaLocation_Top": "kCVImageBufferChromaLocation_Top",
}
cftypes = [
    ("CMBufferQueueRef", b"^{opaqueCMBufferQueue=}", "CMBufferQueueGetTypeID", None),
    ("CMMemoryPoolRef", b"^{OpaqueCMMemoryPool=}", "CMMemoryPoolGetTypeID", None),
    ("CMTimebaseRef", b"^{OpaqueCMTimebase=}", "CMTimebaseGetTypeID", None),
    (
        "CMFormatDescriptionRef",
        b"^{opaqueCMFormatDescription=}",
        "CMFormatDescriptionGetTypeID",
        None,
    ),
    (
        "CMTagCollectionRef",
        b"^{OpaqueCMTagCollection=}",
        "CMTagCollectionGetTypeID",
        None,
    ),
    (
        "CMTaggedBufferGroupRef",
        b"^{OpaqueCMTaggedBufferGroup=}",
        "CMTaggedBufferGroupGetTypeID",
        None,
    ),
    ("CMSimpleQueueRef", b"^{opaqueCMSimpleQueue=}", "CMSimpleQueueGetTypeID", None),
    ("CMClockRef", b"^{OpaqueCMClock=}", "CMClockGetTypeID", None),
    ("CMBlockBufferRef", b"^{OpaqueCMBlockBuffer=}", "CMBlockBufferGetTypeID", None),
    ("CMSimpleQueueef", b"^{opaqueCMSimpleQueue}", "CMSimpleQueueetTypeID", None),
    ("CMSampleBufferRef", b"^{opaqueCMSampleBuffer=}", "CMSampleBufferGetTypeID", None),
    (
        "CMSampleBufferrRef",
        b"^{opaqueCMSampleBufferr=}",
        "CMSampleBufferrGetTypeID",
        None,
    ),
]
misc.update(
    {
        "CMBufferQueueTriggerToken": objc.createOpaquePointerType(
            "CMBufferQueueTriggerToken", b"^{opaqueCMBufferQueueTriggerToken=}"
        )
    }
)
expressions = {
    "kCMTimebaseVeryLongCFTimeInterval": "(CFTimeInterval)(256.0 * 365.0 * 24.0 * 60.0 * 60.0)"
}

# END OF FILE
