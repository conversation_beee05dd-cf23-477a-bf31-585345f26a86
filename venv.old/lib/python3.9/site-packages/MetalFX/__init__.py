"""
Python mapping for the MetalFX framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import Metal
    import objc
    from . import _metadata, _MetalFX

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="MetalFX",
        frameworkIdentifier="com.apple.MetalFX",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/MetalFX.framework"
        ),
        globals_dict=globals(),
        inline_list=None,
        parents=(
            _MetalFX,
            Metal,
        ),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    del sys.modules["MetalFX._metadata"]


globals().pop("_setup")()
