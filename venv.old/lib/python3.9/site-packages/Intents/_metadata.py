# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:13:09 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$INAnswerCallIntentIdentifier$INCancelWorkoutIntentIdentifier$INCarChargingConnectorTypeCCS1$INCarChargingConnectorTypeCCS2$INCarChargingConnectorTypeCHAdeMO$INCarChargingConnectorTypeGBTAC$INCarChargingConnectorTypeGBTDC$INCarChargingConnectorTypeJ1772$INCarChargingConnectorTypeMennekes$INCarChargingConnectorTypeNACSAC$INCarChargingConnectorTypeNACSDC$INCarChargingConnectorTypeTesla$INEndWorkoutIntentIdentifier$INGetRideStatusIntentIdentifier$INHangUpCallIntentIdentifier$INIntentErrorDomain$INListRideOptionsIntentIdentifier$INPauseWorkoutIntentIdentifier$INPersonHandleLabelHome$INPersonHandleLabelHomeFax$INPersonHandleLabelMain$INPersonHandleLabelMobile$INPersonHandleLabelOther$INPersonHandleLabelPager$INPersonHandleLabelSchool$INPersonHandleLabelWork$INPersonHandleLabelWorkFax$INPersonHandleLabeliPhone$INPersonRelationshipAssistant$INPersonRelationshipBrother$INPersonRelationshipChild$INPersonRelationshipDaughter$INPersonRelationshipFather$INPersonRelationshipFriend$INPersonRelationshipManager$INPersonRelationshipMother$INPersonRelationshipParent$INPersonRelationshipPartner$INPersonRelationshipSister$INPersonRelationshipSon$INPersonRelationshipSpouse$INRequestPaymentIntentIdentifier$INRequestRideIntentIdentifier$INResumeWorkoutIntentIdentifier$INSaveProfileInCarIntentIdentifier$INSearchCallHistoryIntentIdentifier$INSearchForMessagesIntentIdentifier$INSearchForPhotosIntentIdentifier$INSendMessageIntentIdentifier$INSendPaymentIntentIdentifier$INSetAudioSourceInCarIntentIdentifier$INSetClimateSettingsInCarIntentIdentifier$INSetDefrosterSettingsInCarIntentIdentifier$INSetMessageAttributeIntentIdentifier$INSetProfileInCarIntentIdentifier$INSetRadioStationIntentIdentifier$INSetSeatSettingsInCarIntentIdentifier$INStartAudioCallIntentIdentifier$INStartCallIntentIdentifier$INStartPhotoPlaybackIntentIdentifier$INStartVideoCallIntentIdentifier$INStartWorkoutIntentIdentifier$INWorkoutNameIdentifierCrosstraining$INWorkoutNameIdentifierCycle$INWorkoutNameIdentifierDance$INWorkoutNameIdentifierElliptical$INWorkoutNameIdentifierExercise$INWorkoutNameIdentifierHighIntensityIntervalTraining$INWorkoutNameIdentifierHike$INWorkoutNameIdentifierIndoorcycle$INWorkoutNameIdentifierIndoorrun$INWorkoutNameIdentifierIndoorwalk$INWorkoutNameIdentifierMove$INWorkoutNameIdentifierOther$INWorkoutNameIdentifierRower$INWorkoutNameIdentifierRun$INWorkoutNameIdentifierSit$INWorkoutNameIdentifierStairs$INWorkoutNameIdentifierStand$INWorkoutNameIdentifierSteps$INWorkoutNameIdentifierSwim$INWorkoutNameIdentifierWalk$INWorkoutNameIdentifierYoga$IntentsVersionNumber@d$"""
enums = """$INAccountTypeChecking@1$INAccountTypeCredit@2$INAccountTypeDebit@3$INAccountTypeInvestment@4$INAccountTypeMortgage@5$INAccountTypePrepaid@6$INAccountTypeSaving@7$INAccountTypeUnknown@0$INActivateCarSignalIntentResponseCodeFailure@4$INActivateCarSignalIntentResponseCodeFailureRequiringAppLaunch@5$INActivateCarSignalIntentResponseCodeInProgress@2$INActivateCarSignalIntentResponseCodeReady@1$INActivateCarSignalIntentResponseCodeSuccess@3$INActivateCarSignalIntentResponseCodeUnspecified@0$INAddMediaIntentResponseCodeFailure@5$INAddMediaIntentResponseCodeFailureRequiringAppLaunch@6$INAddMediaIntentResponseCodeHandleInApp@4$INAddMediaIntentResponseCodeInProgress@2$INAddMediaIntentResponseCodeReady@1$INAddMediaIntentResponseCodeSuccess@3$INAddMediaIntentResponseCodeUnspecified@0$INAddMediaMediaDestinationUnsupportedReasonPlaylistNameNotFound@1$INAddMediaMediaDestinationUnsupportedReasonPlaylistNotEditable@2$INAddMediaMediaItemUnsupportedReasonCellularDataSettings@5$INAddMediaMediaItemUnsupportedReasonExplicitContentSettings@4$INAddMediaMediaItemUnsupportedReasonLoginRequired@1$INAddMediaMediaItemUnsupportedReasonRegionRestriction@8$INAddMediaMediaItemUnsupportedReasonRestrictedContent@6$INAddMediaMediaItemUnsupportedReasonServiceUnavailable@7$INAddMediaMediaItemUnsupportedReasonSubscriptionRequired@2$INAddMediaMediaItemUnsupportedReasonUnsupportedMediaType@3$INAddTasksIntentResponseCodeFailure@4$INAddTasksIntentResponseCodeFailureRequiringAppLaunch@5$INAddTasksIntentResponseCodeInProgress@2$INAddTasksIntentResponseCodeReady@1$INAddTasksIntentResponseCodeSuccess@3$INAddTasksIntentResponseCodeUnspecified@0$INAddTasksTargetTaskListConfirmationReasonListShouldBeCreated@1$INAddTasksTemporalEventTriggerUnsupportedReasonInvalidRecurrence@2$INAddTasksTemporalEventTriggerUnsupportedReasonTimeInPast@1$INAmountTypeAmountDue@2$INAmountTypeCurrentBalance@3$INAmountTypeMaximumTransferAmount@4$INAmountTypeMinimumDue@1$INAmountTypeMinimumTransferAmount@5$INAmountTypeStatementBalance@6$INAmountTypeUnknown@0$INAnswerCallIntentResponseCodeContinueInApp@2$INAnswerCallIntentResponseCodeFailure@5$INAnswerCallIntentResponseCodeFailureRequiringAppLaunch@6$INAnswerCallIntentResponseCodeInProgress@3$INAnswerCallIntentResponseCodeReady@1$INAnswerCallIntentResponseCodeSuccess@4$INAnswerCallIntentResponseCodeUnspecified@0$INAppendToNoteIntentResponseCodeFailure@4$INAppendToNoteIntentResponseCodeFailureCannotUpdatePasswordProtectedNote@6$INAppendToNoteIntentResponseCodeFailureRequiringAppLaunch@5$INAppendToNoteIntentResponseCodeInProgress@2$INAppendToNoteIntentResponseCodeReady@1$INAppendToNoteIntentResponseCodeSuccess@3$INAppendToNoteIntentResponseCodeUnspecified@0$INBalanceTypeMiles@3$INBalanceTypeMoney@1$INBalanceTypePoints@2$INBalanceTypeUnknown@0$INBillTypeAutoInsurance@1$INBillTypeCable@2$INBillTypeCarLease@3$INBillTypeCarLoan@4$INBillTypeCreditCard@5$INBillTypeElectricity@6$INBillTypeGarbageAndRecycling@8$INBillTypeGas@7$INBillTypeHealthInsurance@9$INBillTypeHomeInsurance@10$INBillTypeInternet@11$INBillTypeLifeInsurance@12$INBillTypeMortgage@13$INBillTypeMusicStreaming@14$INBillTypePhone@15$INBillTypeRent@16$INBillTypeSewer@17$INBillTypeStudentLoan@18$INBillTypeTrafficTicket@19$INBillTypeTuition@20$INBillTypeUnknown@0$INBillTypeUtilities@21$INBillTypeWater@22$INBookRestaurantReservationIntentCodeDenied@1$INBookRestaurantReservationIntentCodeFailure@2$INBookRestaurantReservationIntentCodeFailureRequiringAppLaunch@3$INBookRestaurantReservationIntentCodeFailureRequiringAppLaunchMustVerifyCredentials@4$INBookRestaurantReservationIntentCodeFailureRequiringAppLaunchServiceTemporarilyUnavailable@5$INBookRestaurantReservationIntentCodeSuccess@0$INCallAudioRouteBluetoothAudioRoute@2$INCallAudioRouteSpeakerphoneAudioRoute@1$INCallAudioRouteUnknown@0$INCallCapabilityAudioCall@1$INCallCapabilityOptionAudioCall@1$INCallCapabilityOptionVideoCall@2$INCallCapabilityUnknown@0$INCallCapabilityVideoCall@2$INCallDestinationTypeCallBack@5$INCallDestinationTypeEmergency@2$INCallDestinationTypeEmergencyDestination@2$INCallDestinationTypeNormal@1$INCallDestinationTypeNormalDestination@1$INCallDestinationTypeRedial@4$INCallDestinationTypeRedialDestination@4$INCallDestinationTypeUnknown@0$INCallDestinationTypeVoicemail@3$INCallDestinationTypeVoicemailDestination@3$INCallRecordTypeInProgress@7$INCallRecordTypeLatest@4$INCallRecordTypeMissed@2$INCallRecordTypeOnHold@8$INCallRecordTypeOptionInProgress@64$INCallRecordTypeOptionLatest@8$INCallRecordTypeOptionMissed@2$INCallRecordTypeOptionOnHold@128$INCallRecordTypeOptionOutgoing@1$INCallRecordTypeOptionReceived@4$INCallRecordTypeOptionRinging@32$INCallRecordTypeOptionVoicemail@16$INCallRecordTypeOutgoing@1$INCallRecordTypeReceived@3$INCallRecordTypeRinging@6$INCallRecordTypeUnknown@0$INCallRecordTypeVoicemail@5$INCancelRideIntentResponseCodeFailure@3$INCancelRideIntentResponseCodeReady@1$INCancelRideIntentResponseCodeSuccess@2$INCancelRideIntentResponseCodeUnspecified@0$INCancelWorkoutIntentResponseCodeContinueInApp@2$INCancelWorkoutIntentResponseCodeFailure@3$INCancelWorkoutIntentResponseCodeFailureNoMatchingWorkout@5$INCancelWorkoutIntentResponseCodeFailureRequiringAppLaunch@4$INCancelWorkoutIntentResponseCodeHandleInApp@6$INCancelWorkoutIntentResponseCodeReady@1$INCancelWorkoutIntentResponseCodeSuccess@7$INCancelWorkoutIntentResponseCodeUnspecified@0$INCarAirCirculationModeFreshAir@1$INCarAirCirculationModeRecirculateAir@2$INCarAirCirculationModeUnknown@0$INCarAudioSourceAUX@5$INCarAudioSourceBluetooth@4$INCarAudioSourceCarPlay@1$INCarAudioSourceHardDrive@9$INCarAudioSourceMemoryCard@7$INCarAudioSourceOpticalDrive@8$INCarAudioSourceRadio@3$INCarAudioSourceUSB@6$INCarAudioSourceUnknown@0$INCarAudioSourceiPod@2$INCarDefrosterAll@3$INCarDefrosterFront@1$INCarDefrosterRear@2$INCarDefrosterUnknown@0$INCarSeatAll@12$INCarSeatDriver@1$INCarSeatFront@5$INCarSeatFrontLeft@3$INCarSeatFrontRight@4$INCarSeatPassenger@2$INCarSeatRear@8$INCarSeatRearLeft@6$INCarSeatRearRight@7$INCarSeatThirdRow@11$INCarSeatThirdRowLeft@9$INCarSeatThirdRowRight@10$INCarSeatUnknown@0$INCarSignalOptionAudible@1$INCarSignalOptionVisible@2$INChargingConnectorTypeOptionCCS1@4$INChargingConnectorTypeOptionCCS2@8$INChargingConnectorTypeOptionCHAdeMO@16$INChargingConnectorTypeOptionGBTAC@32$INChargingConnectorTypeOptionGBTDC@64$INChargingConnectorTypeOptionJ1772@2$INChargingConnectorTypeOptionMennekes@256$INChargingConnectorTypeOptionNone@1$INChargingConnectorTypeOptionTesla@128$INConditionalOperatorAll@0$INConditionalOperatorAny@1$INConditionalOperatorNone@2$INCreateNoteIntentResponseCodeFailure@4$INCreateNoteIntentResponseCodeFailureRequiringAppLaunch@5$INCreateNoteIntentResponseCodeInProgress@2$INCreateNoteIntentResponseCodeReady@1$INCreateNoteIntentResponseCodeSuccess@3$INCreateNoteIntentResponseCodeUnspecified@0$INCreateTaskListIntentResponseCodeFailure@4$INCreateTaskListIntentResponseCodeFailureRequiringAppLaunch@5$INCreateTaskListIntentResponseCodeInProgress@2$INCreateTaskListIntentResponseCodeReady@1$INCreateTaskListIntentResponseCodeSuccess@3$INCreateTaskListIntentResponseCodeUnspecified@0$INDailyRoutineSituationActiveWorkout@8$INDailyRoutineSituationCommute@6$INDailyRoutineSituationEvening@1$INDailyRoutineSituationGym@5$INDailyRoutineSituationHeadphonesConnected@7$INDailyRoutineSituationHome@2$INDailyRoutineSituationMorning@0$INDailyRoutineSituationPhysicalActivityIncomplete@9$INDailyRoutineSituationSchool@4$INDailyRoutineSituationWork@3$INDateSearchTypeByCreatedDate@3$INDateSearchTypeByDueDate@1$INDateSearchTypeByModifiedDate@2$INDateSearchTypeUnknown@0$INDayOfWeekOptionFriday@16$INDayOfWeekOptionMonday@1$INDayOfWeekOptionSaturday@32$INDayOfWeekOptionSunday@64$INDayOfWeekOptionThursday@8$INDayOfWeekOptionTuesday@2$INDayOfWeekOptionWednesday@4$INDeleteTasksIntentResponseCodeFailure@4$INDeleteTasksIntentResponseCodeFailureRequiringAppLaunch@5$INDeleteTasksIntentResponseCodeInProgress@2$INDeleteTasksIntentResponseCodeReady@1$INDeleteTasksIntentResponseCodeSuccess@3$INDeleteTasksIntentResponseCodeUnspecified@0$INDeleteTasksTaskListUnsupportedReasonNoTaskListFound@1$INDeleteTasksTaskUnsupportedReasonNoTasksFound@1$INDeleteTasksTaskUnsupportedReasonNoTasksInApp@2$INEditMessageIntentResponseCodeFailure@4$INEditMessageIntentResponseCodeFailureMessageNotFound@6$INEditMessageIntentResponseCodeFailureMessageServiceNotAvailable@10$INEditMessageIntentResponseCodeFailureMessageTypeUnsupported@8$INEditMessageIntentResponseCodeFailurePastEditTimeLimit@7$INEditMessageIntentResponseCodeFailureRequiringAppLaunch@5$INEditMessageIntentResponseCodeFailureRequiringInAppAuthentication@11$INEditMessageIntentResponseCodeFailureUnsupportedOnService@9$INEditMessageIntentResponseCodeInProgress@2$INEditMessageIntentResponseCodeReady@1$INEditMessageIntentResponseCodeSuccess@3$INEditMessageIntentResponseCodeUnspecified@0$INEndWorkoutIntentResponseCodeContinueInApp@2$INEndWorkoutIntentResponseCodeFailure@3$INEndWorkoutIntentResponseCodeFailureNoMatchingWorkout@5$INEndWorkoutIntentResponseCodeFailureRequiringAppLaunch@4$INEndWorkoutIntentResponseCodeHandleInApp@6$INEndWorkoutIntentResponseCodeReady@1$INEndWorkoutIntentResponseCodeSuccess@7$INEndWorkoutIntentResponseCodeUnspecified@0$INFocusStatusAuthorizationStatusAuthorized@3$INFocusStatusAuthorizationStatusDenied@2$INFocusStatusAuthorizationStatusNotDetermined@0$INFocusStatusAuthorizationStatusRestricted@1$INGetAvailableRestaurantReservationBookingDefaultsIntentResponseCodeFailure@1$INGetAvailableRestaurantReservationBookingDefaultsIntentResponseCodeSuccess@0$INGetAvailableRestaurantReservationBookingDefaultsIntentResponseCodeUnspecified@2$INGetAvailableRestaurantReservationBookingsIntentCodeFailure@1$INGetAvailableRestaurantReservationBookingsIntentCodeFailureRequestUnsatisfiable@2$INGetAvailableRestaurantReservationBookingsIntentCodeFailureRequestUnspecified@3$INGetAvailableRestaurantReservationBookingsIntentCodeSuccess@0$INGetCarLockStatusIntentResponseCodeFailure@4$INGetCarLockStatusIntentResponseCodeFailureRequiringAppLaunch@5$INGetCarLockStatusIntentResponseCodeInProgress@2$INGetCarLockStatusIntentResponseCodeReady@1$INGetCarLockStatusIntentResponseCodeSuccess@3$INGetCarLockStatusIntentResponseCodeUnspecified@0$INGetCarPowerLevelStatusIntentResponseCodeFailure@4$INGetCarPowerLevelStatusIntentResponseCodeFailureRequiringAppLaunch@5$INGetCarPowerLevelStatusIntentResponseCodeInProgress@2$INGetCarPowerLevelStatusIntentResponseCodeReady@1$INGetCarPowerLevelStatusIntentResponseCodeSuccess@3$INGetCarPowerLevelStatusIntentResponseCodeUnspecified@0$INGetReservationDetailsIntentResponseCodeFailure@4$INGetReservationDetailsIntentResponseCodeFailureRequiringAppLaunch@5$INGetReservationDetailsIntentResponseCodeInProgress@2$INGetReservationDetailsIntentResponseCodeReady@1$INGetReservationDetailsIntentResponseCodeSuccess@3$INGetReservationDetailsIntentResponseCodeUnspecified@0$INGetRestaurantGuestIntentResponseCodeFailure@1$INGetRestaurantGuestIntentResponseCodeSuccess@0$INGetRideStatusIntentResponseCodeFailure@4$INGetRideStatusIntentResponseCodeFailureRequiringAppLaunch@5$INGetRideStatusIntentResponseCodeFailureRequiringAppLaunchMustVerifyCredentials@6$INGetRideStatusIntentResponseCodeFailureRequiringAppLaunchServiceTemporarilyUnavailable@7$INGetRideStatusIntentResponseCodeInProgress@2$INGetRideStatusIntentResponseCodeReady@1$INGetRideStatusIntentResponseCodeSuccess@3$INGetRideStatusIntentResponseCodeUnspecified@0$INGetUserCurrentRestaurantReservationBookingsIntentResponseCodeFailure@1$INGetUserCurrentRestaurantReservationBookingsIntentResponseCodeFailureRequestUnsatisfiable@2$INGetUserCurrentRestaurantReservationBookingsIntentResponseCodeSuccess@0$INGetUserCurrentRestaurantReservationBookingsIntentResponseCodeUnspecified@3$INGetVisualCodeIntentResponseCodeContinueInApp@2$INGetVisualCodeIntentResponseCodeFailure@5$INGetVisualCodeIntentResponseCodeFailureAppConfigurationRequired@7$INGetVisualCodeIntentResponseCodeFailureRequiringAppLaunch@6$INGetVisualCodeIntentResponseCodeInProgress@3$INGetVisualCodeIntentResponseCodeReady@1$INGetVisualCodeIntentResponseCodeSuccess@4$INGetVisualCodeIntentResponseCodeUnspecified@0$INHangUpCallIntentResponseCodeFailure@4$INHangUpCallIntentResponseCodeFailureNoCallToHangUp@6$INHangUpCallIntentResponseCodeFailureRequiringAppLaunch@5$INHangUpCallIntentResponseCodeInProgress@2$INHangUpCallIntentResponseCodeReady@1$INHangUpCallIntentResponseCodeSuccess@3$INHangUpCallIntentResponseCodeUnspecified@0$INIntentErrorDecodingGeneric@9000$INIntentErrorDeletingAllInteractions@1902$INIntentErrorDeletingInteractionWithGroupIdentifier@1904$INIntentErrorDeletingInteractionWithIdentifiers@1903$INIntentErrorDonatingInteraction@1901$INIntentErrorEncodingFailed@8001$INIntentErrorEncodingGeneric@8000$INIntentErrorExtensionBringUpFailed@5001$INIntentErrorExtensionLaunchingTimeout@5000$INIntentErrorImageGeneric@6000$INIntentErrorImageLoadingFailed@6003$INIntentErrorImageNoServiceAvailable@6001$INIntentErrorImageProxyInvalid@6006$INIntentErrorImageProxyLoop@6005$INIntentErrorImageProxyTimeout@6007$INIntentErrorImageRetrievalFailed@6004$INIntentErrorImageScalingFailed@6009$INIntentErrorImageServiceFailure@6008$INIntentErrorImageStorageFailed@6002$INIntentErrorIntentSupportedByMultipleExtension@2001$INIntentErrorInteractionOperationNotSupported@1900$INIntentErrorInvalidIntentName@2004$INIntentErrorInvalidUserVocabularyFileLocation@4000$INIntentErrorMissingInformation@3002$INIntentErrorNoAppAvailable@2005$INIntentErrorNoAppIntent@10001$INIntentErrorNoHandlerProvidedForIntent@2003$INIntentErrorPermissionDenied@6010$INIntentErrorRequestTimedOut@3001$INIntentErrorRestrictedIntentsNotSupportedByExtension@2002$INIntentErrorUnableToCreateAppIntentRepresentation@10000$INIntentErrorVoiceShortcutCreationFailed@7000$INIntentErrorVoiceShortcutDeleteFailed@7002$INIntentErrorVoiceShortcutGetFailed@7001$INIntentHandlingStatusDeferredToApplication@5$INIntentHandlingStatusFailure@4$INIntentHandlingStatusInProgress@2$INIntentHandlingStatusReady@1$INIntentHandlingStatusSuccess@3$INIntentHandlingStatusUnspecified@0$INIntentHandlingStatusUserConfirmationRequired@6$INInteractionDirectionIncoming@2$INInteractionDirectionOutgoing@1$INInteractionDirectionUnspecified@0$INListCarsIntentResponseCodeFailure@4$INListCarsIntentResponseCodeFailureRequiringAppLaunch@5$INListCarsIntentResponseCodeInProgress@2$INListCarsIntentResponseCodeReady@1$INListCarsIntentResponseCodeSuccess@3$INListCarsIntentResponseCodeUnspecified@0$INListRideOptionsIntentResponseCodeFailure@4$INListRideOptionsIntentResponseCodeFailurePreviousRideNeedsFeedback@10$INListRideOptionsIntentResponseCodeFailureRequiringAppLaunch@5$INListRideOptionsIntentResponseCodeFailureRequiringAppLaunchMustVerifyCredentials@6$INListRideOptionsIntentResponseCodeFailureRequiringAppLaunchNoServiceInArea@7$INListRideOptionsIntentResponseCodeFailureRequiringAppLaunchPreviousRideNeedsCompletion@9$INListRideOptionsIntentResponseCodeFailureRequiringAppLaunchServiceTemporarilyUnavailable@8$INListRideOptionsIntentResponseCodeInProgress@2$INListRideOptionsIntentResponseCodeReady@1$INListRideOptionsIntentResponseCodeSuccess@3$INListRideOptionsIntentResponseCodeUnspecified@0$INLocationSearchTypeByLocationTrigger@1$INLocationSearchTypeUnknown@0$INMediaAffinityTypeDislike@2$INMediaAffinityTypeLike@1$INMediaAffinityTypeUnknown@0$INMediaDestinationTypeLibrary@1$INMediaDestinationTypePlaylist@2$INMediaDestinationTypeUnknown@0$INMediaItemTypeAlbum@2$INMediaItemTypeAlgorithmicRadioStation@19$INMediaItemTypeArtist@3$INMediaItemTypeAudioBook@10$INMediaItemTypeGenre@4$INMediaItemTypeMovie@11$INMediaItemTypeMusic@18$INMediaItemTypeMusicStation@9$INMediaItemTypeMusicVideo@14$INMediaItemTypeNews@20$INMediaItemTypePlaylist@5$INMediaItemTypePodcastEpisode@7$INMediaItemTypePodcastPlaylist@8$INMediaItemTypePodcastShow@6$INMediaItemTypePodcastStation@15$INMediaItemTypeRadioStation@16$INMediaItemTypeSong@1$INMediaItemTypeStation@17$INMediaItemTypeTVShow@12$INMediaItemTypeTVShowEpisode@13$INMediaItemTypeUnknown@0$INMediaReferenceCurrentlyPlaying@1$INMediaReferenceMy@2$INMediaReferenceUnknown@0$INMediaSortOrderBest@3$INMediaSortOrderNewest@1$INMediaSortOrderOldest@2$INMediaSortOrderPopular@5$INMediaSortOrderRecommended@8$INMediaSortOrderTrending@7$INMediaSortOrderUnknown@0$INMediaSortOrderUnpopular@6$INMediaSortOrderWorst@4$INMediaUserContextSubscriptionStatusNotSubscribed@1$INMediaUserContextSubscriptionStatusSubscribed@2$INMediaUserContextSubscriptionStatusUnknown@0$INMessageAttributeFlagged@3$INMessageAttributeOptionFlagged@4$INMessageAttributeOptionPlayed@16$INMessageAttributeOptionRead@1$INMessageAttributeOptionUnflagged@8$INMessageAttributeOptionUnread@2$INMessageAttributePlayed@5$INMessageAttributeRead@1$INMessageAttributeUnflagged@4$INMessageAttributeUnknown@0$INMessageAttributeUnread@2$INMessageReactionTypeEmoji@2$INMessageReactionTypeEmojiReaction@1$INMessageReactionTypeGeneric@3$INMessageReactionTypeUnknown@0$INMessageTypeActivitySnippet@23$INMessageTypeAnimoji@22$INMessageTypeAudio@2$INMessageTypeDigitalTouch@3$INMessageTypeFile@24$INMessageTypeHandwriting@4$INMessageTypeLink@25$INMessageTypeMediaAddressCard@14$INMessageTypeMediaAnimatedImage@27$INMessageTypeMediaAudio@18$INMessageTypeMediaCalendar@12$INMessageTypeMediaImage@15$INMessageTypeMediaLocation@13$INMessageTypeMediaPass@17$INMessageTypeMediaVideo@16$INMessageTypePaymentNote@21$INMessageTypePaymentRequest@20$INMessageTypePaymentSent@19$INMessageTypeReaction@26$INMessageTypeSticker@5$INMessageTypeTapbackDisliked@7$INMessageTypeTapbackEmphasized@8$INMessageTypeTapbackLaughed@11$INMessageTypeTapbackLiked@6$INMessageTypeTapbackLoved@9$INMessageTypeTapbackQuestioned@10$INMessageTypeText@1$INMessageTypeThirdPartyAttachment@28$INMessageTypeUnspecified@0$INNoteContentTypeImage@2$INNoteContentTypeText@1$INNoteContentTypeUnknown@0$INNotebookItemTypeNote@1$INNotebookItemTypeTask@3$INNotebookItemTypeTaskList@2$INNotebookItemTypeUnknown@0$INOutgoingMessageTypeOutgoingMessageAudio@2$INOutgoingMessageTypeOutgoingMessageText@1$INOutgoingMessageTypeUnknown@0$INPauseWorkoutIntentResponseCodeContinueInApp@2$INPauseWorkoutIntentResponseCodeFailure@3$INPauseWorkoutIntentResponseCodeFailureNoMatchingWorkout@5$INPauseWorkoutIntentResponseCodeFailureRequiringAppLaunch@4$INPauseWorkoutIntentResponseCodeHandleInApp@6$INPauseWorkoutIntentResponseCodeReady@1$INPauseWorkoutIntentResponseCodeSuccess@7$INPauseWorkoutIntentResponseCodeUnspecified@0$INPayBillIntentResponseCodeFailure@4$INPayBillIntentResponseCodeFailureCredentialsUnverified@6$INPayBillIntentResponseCodeFailureInsufficientFunds@7$INPayBillIntentResponseCodeFailureRequiringAppLaunch@5$INPayBillIntentResponseCodeInProgress@2$INPayBillIntentResponseCodeReady@1$INPayBillIntentResponseCodeSuccess@3$INPayBillIntentResponseCodeUnspecified@0$INPaymentMethodTypeApplePay@8$INPaymentMethodTypeBrokerage@3$INPaymentMethodTypeChecking@1$INPaymentMethodTypeCredit@5$INPaymentMethodTypeDebit@4$INPaymentMethodTypePrepaid@6$INPaymentMethodTypeSavings@2$INPaymentMethodTypeStore@7$INPaymentMethodTypeUnknown@0$INPaymentStatusCanceled@3$INPaymentStatusCompleted@2$INPaymentStatusFailed@4$INPaymentStatusPending@1$INPaymentStatusUnknown@0$INPaymentStatusUnpaid@5$INPersonHandleTypeEmailAddress@1$INPersonHandleTypePhoneNumber@2$INPersonHandleTypeUnknown@0$INPersonSuggestionTypeInstantMessageAddress@2$INPersonSuggestionTypeNone@0$INPersonSuggestionTypeSocialProfile@1$INPhotoAttributeOptionBouncePhoto@134217728$INPhotoAttributeOptionBurstPhoto@1024$INPhotoAttributeOptionChromeFilter@131072$INPhotoAttributeOptionFadeFilter@4194304$INPhotoAttributeOptionFavorite@64$INPhotoAttributeOptionFlash@8$INPhotoAttributeOptionFrontFacingCamera@256$INPhotoAttributeOptionGIF@4$INPhotoAttributeOptionHDRPhoto@2048$INPhotoAttributeOptionInstantFilter@262144$INPhotoAttributeOptionLandscapeOrientation@16$INPhotoAttributeOptionLivePhoto@33554432$INPhotoAttributeOptionLongExposurePhoto@268435456$INPhotoAttributeOptionLoopPhoto@67108864$INPhotoAttributeOptionMonoFilter@2097152$INPhotoAttributeOptionNoirFilter@65536$INPhotoAttributeOptionPanoramaPhoto@8192$INPhotoAttributeOptionPhoto@1$INPhotoAttributeOptionPortraitOrientation@32$INPhotoAttributeOptionPortraitPhoto@16777216$INPhotoAttributeOptionProcessFilter@8388608$INPhotoAttributeOptionScreenshot@512$INPhotoAttributeOptionSelfie@128$INPhotoAttributeOptionSlowMotionVideo@32768$INPhotoAttributeOptionSquarePhoto@4096$INPhotoAttributeOptionTimeLapseVideo@16384$INPhotoAttributeOptionTonalFilter@524288$INPhotoAttributeOptionTransferFilter@1048576$INPhotoAttributeOptionVideo@2$INPlayMediaIntentResponseCodeContinueInApp@2$INPlayMediaIntentResponseCodeFailure@6$INPlayMediaIntentResponseCodeFailureMaxStreamLimitReached@11$INPlayMediaIntentResponseCodeFailureNoUnplayedContent@9$INPlayMediaIntentResponseCodeFailureRequiringAppLaunch@7$INPlayMediaIntentResponseCodeFailureRestrictedContent@10$INPlayMediaIntentResponseCodeFailureUnknownMediaType@8$INPlayMediaIntentResponseCodeHandleInApp@5$INPlayMediaIntentResponseCodeInProgress@3$INPlayMediaIntentResponseCodeReady@1$INPlayMediaIntentResponseCodeSuccess@4$INPlayMediaIntentResponseCodeUnspecified@0$INPlayMediaMediaItemUnsupportedReasonCellularDataSettings@5$INPlayMediaMediaItemUnsupportedReasonExplicitContentSettings@4$INPlayMediaMediaItemUnsupportedReasonLoginRequired@1$INPlayMediaMediaItemUnsupportedReasonRegionRestriction@8$INPlayMediaMediaItemUnsupportedReasonRestrictedContent@6$INPlayMediaMediaItemUnsupportedReasonServiceUnavailable@7$INPlayMediaMediaItemUnsupportedReasonSubscriptionRequired@2$INPlayMediaMediaItemUnsupportedReasonUnsupportedMediaType@3$INPlayMediaPlaybackSpeedUnsupportedReasonAboveMaximum@2$INPlayMediaPlaybackSpeedUnsupportedReasonBelowMinimum@1$INPlaybackQueueLocationLater@3$INPlaybackQueueLocationNext@2$INPlaybackQueueLocationNow@1$INPlaybackQueueLocationUnknown@0$INPlaybackRepeatModeAll@2$INPlaybackRepeatModeNone@1$INPlaybackRepeatModeOne@3$INPlaybackRepeatModeUnknown@0$INRadioTypeAM@1$INRadioTypeDAB@5$INRadioTypeFM@2$INRadioTypeHD@3$INRadioTypeSatellite@4$INRadioTypeUnknown@0$INRecurrenceFrequencyDaily@3$INRecurrenceFrequencyHourly@2$INRecurrenceFrequencyMinute@1$INRecurrenceFrequencyMonthly@5$INRecurrenceFrequencyUnknown@0$INRecurrenceFrequencyWeekly@4$INRecurrenceFrequencyYearly@6$INRelativeReferenceNext@1$INRelativeReferencePrevious@2$INRelativeReferenceUnknown@0$INRelativeSettingHigher@3$INRelativeSettingHighest@4$INRelativeSettingLower@2$INRelativeSettingLowest@1$INRelativeSettingUnknown@0$INRelevantShortcutRoleAction@0$INRelevantShortcutRoleInformation@1$INRequestPaymentCurrencyAmountUnsupportedReasonPaymentsAmountAboveMaximum@2$INRequestPaymentCurrencyAmountUnsupportedReasonPaymentsAmountBelowMinimum@1$INRequestPaymentCurrencyAmountUnsupportedReasonPaymentsCurrencyUnsupported@3$INRequestPaymentIntentResponseCodeFailure@4$INRequestPaymentIntentResponseCodeFailureCredentialsUnverified@6$INRequestPaymentIntentResponseCodeFailureNoBankAccount@10$INRequestPaymentIntentResponseCodeFailureNotEligible@11$INRequestPaymentIntentResponseCodeFailurePaymentsAmountAboveMaximum@8$INRequestPaymentIntentResponseCodeFailurePaymentsAmountBelowMinimum@7$INRequestPaymentIntentResponseCodeFailurePaymentsCurrencyUnsupported@9$INRequestPaymentIntentResponseCodeFailureRequiringAppLaunch@5$INRequestPaymentIntentResponseCodeFailureTermsAndConditionsAcceptanceRequired@12$INRequestPaymentIntentResponseCodeInProgress@2$INRequestPaymentIntentResponseCodeReady@1$INRequestPaymentIntentResponseCodeSuccess@3$INRequestPaymentIntentResponseCodeUnspecified@0$INRequestPaymentPayerUnsupportedReasonCredentialsUnverified@1$INRequestPaymentPayerUnsupportedReasonNoAccount@2$INRequestPaymentPayerUnsupportedReasonNoValidHandle@3$INRequestRideIntentResponseCodeFailure@4$INRequestRideIntentResponseCodeFailureRequiringAppLaunch@5$INRequestRideIntentResponseCodeFailureRequiringAppLaunchMustVerifyCredentials@6$INRequestRideIntentResponseCodeFailureRequiringAppLaunchNoServiceInArea@7$INRequestRideIntentResponseCodeFailureRequiringAppLaunchPreviousRideNeedsCompletion@9$INRequestRideIntentResponseCodeFailureRequiringAppLaunchRideScheduledTooFar@10$INRequestRideIntentResponseCodeFailureRequiringAppLaunchServiceTemporarilyUnavailable@8$INRequestRideIntentResponseCodeInProgress@2$INRequestRideIntentResponseCodeReady@1$INRequestRideIntentResponseCodeSuccess@3$INRequestRideIntentResponseCodeUnspecified@0$INReservationActionTypeCheckIn@1$INReservationActionTypeUnknown@0$INReservationStatusCanceled@1$INReservationStatusConfirmed@4$INReservationStatusHold@3$INReservationStatusPending@2$INReservationStatusUnknown@0$INRestaurantReservationUserBookingStatusConfirmed@1$INRestaurantReservationUserBookingStatusDenied@2$INRestaurantReservationUserBookingStatusPending@0$INResumeWorkoutIntentResponseCodeContinueInApp@2$INResumeWorkoutIntentResponseCodeFailure@3$INResumeWorkoutIntentResponseCodeFailureNoMatchingWorkout@5$INResumeWorkoutIntentResponseCodeFailureRequiringAppLaunch@4$INResumeWorkoutIntentResponseCodeHandleInApp@6$INResumeWorkoutIntentResponseCodeReady@1$INResumeWorkoutIntentResponseCodeSuccess@7$INResumeWorkoutIntentResponseCodeUnspecified@0$INRideFeedbackTypeOptionRate@1$INRideFeedbackTypeOptionTip@2$INRidePhaseApproachingPickup@5$INRidePhaseCompleted@4$INRidePhaseConfirmed@2$INRidePhaseOngoing@3$INRidePhasePickup@6$INRidePhaseReceived@1$INRidePhaseUnknown@0$INSaveProfileInCarIntentResponseCodeFailure@4$INSaveProfileInCarIntentResponseCodeFailureRequiringAppLaunch@5$INSaveProfileInCarIntentResponseCodeInProgress@2$INSaveProfileInCarIntentResponseCodeReady@1$INSaveProfileInCarIntentResponseCodeSuccess@3$INSaveProfileInCarIntentResponseCodeUnspecified@0$INSearchCallHistoryIntentResponseCodeContinueInApp@2$INSearchCallHistoryIntentResponseCodeFailure@3$INSearchCallHistoryIntentResponseCodeFailureAppConfigurationRequired@5$INSearchCallHistoryIntentResponseCodeFailureRequiringAppLaunch@4$INSearchCallHistoryIntentResponseCodeInProgress@6$INSearchCallHistoryIntentResponseCodeReady@1$INSearchCallHistoryIntentResponseCodeSuccess@7$INSearchCallHistoryIntentResponseCodeUnspecified@0$INSearchForAccountsIntentResponseCodeFailure@4$INSearchForAccountsIntentResponseCodeFailureAccountNotFound@7$INSearchForAccountsIntentResponseCodeFailureCredentialsUnverified@6$INSearchForAccountsIntentResponseCodeFailureNotEligible@9$INSearchForAccountsIntentResponseCodeFailureRequiringAppLaunch@5$INSearchForAccountsIntentResponseCodeFailureTermsAndConditionsAcceptanceRequired@8$INSearchForAccountsIntentResponseCodeInProgress@2$INSearchForAccountsIntentResponseCodeReady@1$INSearchForAccountsIntentResponseCodeSuccess@3$INSearchForAccountsIntentResponseCodeUnspecified@0$INSearchForBillsIntentResponseCodeFailure@4$INSearchForBillsIntentResponseCodeFailureBillNotFound@7$INSearchForBillsIntentResponseCodeFailureCredentialsUnverified@6$INSearchForBillsIntentResponseCodeFailureRequiringAppLaunch@5$INSearchForBillsIntentResponseCodeInProgress@2$INSearchForBillsIntentResponseCodeReady@1$INSearchForBillsIntentResponseCodeSuccess@3$INSearchForBillsIntentResponseCodeUnspecified@0$INSearchForMediaIntentResponseCodeContinueInApp@2$INSearchForMediaIntentResponseCodeFailure@5$INSearchForMediaIntentResponseCodeFailureRequiringAppLaunch@6$INSearchForMediaIntentResponseCodeInProgress@3$INSearchForMediaIntentResponseCodeReady@1$INSearchForMediaIntentResponseCodeSuccess@4$INSearchForMediaIntentResponseCodeUnspecified@0$INSearchForMediaMediaItemUnsupportedReasonCellularDataSettings@5$INSearchForMediaMediaItemUnsupportedReasonExplicitContentSettings@4$INSearchForMediaMediaItemUnsupportedReasonLoginRequired@1$INSearchForMediaMediaItemUnsupportedReasonRegionRestriction@8$INSearchForMediaMediaItemUnsupportedReasonRestrictedContent@6$INSearchForMediaMediaItemUnsupportedReasonServiceUnavailable@7$INSearchForMediaMediaItemUnsupportedReasonSubscriptionRequired@2$INSearchForMediaMediaItemUnsupportedReasonUnsupportedMediaType@3$INSearchForMessagesIntentResponseCodeFailure@4$INSearchForMessagesIntentResponseCodeFailureMessageServiceNotAvailable@6$INSearchForMessagesIntentResponseCodeFailureMessageTooManyResults@7$INSearchForMessagesIntentResponseCodeFailureRequiringAppLaunch@5$INSearchForMessagesIntentResponseCodeFailureRequiringInAppAuthentication@8$INSearchForMessagesIntentResponseCodeInProgress@2$INSearchForMessagesIntentResponseCodeReady@1$INSearchForMessagesIntentResponseCodeSuccess@3$INSearchForMessagesIntentResponseCodeUnspecified@0$INSearchForNotebookItemsIntentResponseCodeFailure@4$INSearchForNotebookItemsIntentResponseCodeFailureRequiringAppLaunch@5$INSearchForNotebookItemsIntentResponseCodeInProgress@2$INSearchForNotebookItemsIntentResponseCodeReady@1$INSearchForNotebookItemsIntentResponseCodeSuccess@3$INSearchForNotebookItemsIntentResponseCodeUnspecified@0$INSearchForPhotosIntentResponseCodeContinueInApp@2$INSearchForPhotosIntentResponseCodeFailure@3$INSearchForPhotosIntentResponseCodeFailureAppConfigurationRequired@5$INSearchForPhotosIntentResponseCodeFailureRequiringAppLaunch@4$INSearchForPhotosIntentResponseCodeReady@1$INSearchForPhotosIntentResponseCodeUnspecified@0$INSendMessageIntentResponseCodeFailure@4$INSendMessageIntentResponseCodeFailureMessageAttachmentError@7$INSendMessageIntentResponseCodeFailureMessageLocationNotAllowed@8$INSendMessageIntentResponseCodeFailureMessageServiceNotAvailable@6$INSendMessageIntentResponseCodeFailureRequiringAppLaunch@5$INSendMessageIntentResponseCodeFailureRequiringInAppAuthentication@7$INSendMessageIntentResponseCodeInProgress@2$INSendMessageIntentResponseCodeReady@1$INSendMessageIntentResponseCodeSuccess@3$INSendMessageIntentResponseCodeUnspecified@0$INSendMessageRecipientUnsupportedReasonMessagingServiceNotEnabledForRecipient@3$INSendMessageRecipientUnsupportedReasonNoAccount@1$INSendMessageRecipientUnsupportedReasonNoHandleForLabel@6$INSendMessageRecipientUnsupportedReasonNoValidHandle@4$INSendMessageRecipientUnsupportedReasonOffline@2$INSendMessageRecipientUnsupportedReasonRequestedHandleInvalid@5$INSendMessageRecipientUnsupportedReasonRequiringInAppAuthentication@7$INSendPaymentCurrencyAmountUnsupportedReasonPaymentsAmountAboveMaximum@2$INSendPaymentCurrencyAmountUnsupportedReasonPaymentsAmountBelowMinimum@1$INSendPaymentCurrencyAmountUnsupportedReasonPaymentsCurrencyUnsupported@3$INSendPaymentIntentResponseCodeFailure@4$INSendPaymentIntentResponseCodeFailureCredentialsUnverified@6$INSendPaymentIntentResponseCodeFailureInsufficientFunds@10$INSendPaymentIntentResponseCodeFailureNoBankAccount@11$INSendPaymentIntentResponseCodeFailureNotEligible@12$INSendPaymentIntentResponseCodeFailurePaymentsAmountAboveMaximum@8$INSendPaymentIntentResponseCodeFailurePaymentsAmountBelowMinimum@7$INSendPaymentIntentResponseCodeFailurePaymentsCurrencyUnsupported@9$INSendPaymentIntentResponseCodeFailureRequiringAppLaunch@5$INSendPaymentIntentResponseCodeFailureTermsAndConditionsAcceptanceRequired@13$INSendPaymentIntentResponseCodeInProgress@2$INSendPaymentIntentResponseCodeReady@1$INSendPaymentIntentResponseCodeSuccess@3$INSendPaymentIntentResponseCodeUnspecified@0$INSendPaymentPayeeUnsupportedReasonCredentialsUnverified@1$INSendPaymentPayeeUnsupportedReasonInsufficientFunds@2$INSendPaymentPayeeUnsupportedReasonNoAccount@3$INSendPaymentPayeeUnsupportedReasonNoValidHandle@4$INSendRideFeedbackIntentResponseCodeFailure@3$INSendRideFeedbackIntentResponseCodeReady@1$INSendRideFeedbackIntentResponseCodeSuccess@2$INSendRideFeedbackIntentResponseCodeUnspecified@0$INSetAudioSourceInCarIntentResponseCodeFailure@4$INSetAudioSourceInCarIntentResponseCodeFailureRequiringAppLaunch@5$INSetAudioSourceInCarIntentResponseCodeInProgress@2$INSetAudioSourceInCarIntentResponseCodeReady@1$INSetAudioSourceInCarIntentResponseCodeSuccess@3$INSetAudioSourceInCarIntentResponseCodeUnspecified@0$INSetCarLockStatusIntentResponseCodeFailure@4$INSetCarLockStatusIntentResponseCodeFailureRequiringAppLaunch@5$INSetCarLockStatusIntentResponseCodeInProgress@2$INSetCarLockStatusIntentResponseCodeReady@1$INSetCarLockStatusIntentResponseCodeSuccess@3$INSetCarLockStatusIntentResponseCodeUnspecified@0$INSetClimateSettingsInCarIntentResponseCodeFailure@4$INSetClimateSettingsInCarIntentResponseCodeFailureRequiringAppLaunch@5$INSetClimateSettingsInCarIntentResponseCodeInProgress@2$INSetClimateSettingsInCarIntentResponseCodeReady@1$INSetClimateSettingsInCarIntentResponseCodeSuccess@3$INSetClimateSettingsInCarIntentResponseCodeUnspecified@0$INSetDefrosterSettingsInCarIntentResponseCodeFailure@4$INSetDefrosterSettingsInCarIntentResponseCodeFailureRequiringAppLaunch@5$INSetDefrosterSettingsInCarIntentResponseCodeInProgress@2$INSetDefrosterSettingsInCarIntentResponseCodeReady@1$INSetDefrosterSettingsInCarIntentResponseCodeSuccess@3$INSetDefrosterSettingsInCarIntentResponseCodeUnspecified@0$INSetMessageAttributeIntentResponseCodeFailure@4$INSetMessageAttributeIntentResponseCodeFailureMessageAttributeNotSet@7$INSetMessageAttributeIntentResponseCodeFailureMessageNotFound@6$INSetMessageAttributeIntentResponseCodeFailureRequiringAppLaunch@5$INSetMessageAttributeIntentResponseCodeInProgress@2$INSetMessageAttributeIntentResponseCodeReady@1$INSetMessageAttributeIntentResponseCodeSuccess@3$INSetMessageAttributeIntentResponseCodeUnspecified@0$INSetProfileInCarIntentResponseCodeFailure@4$INSetProfileInCarIntentResponseCodeFailureRequiringAppLaunch@5$INSetProfileInCarIntentResponseCodeInProgress@2$INSetProfileInCarIntentResponseCodeReady@1$INSetProfileInCarIntentResponseCodeSuccess@3$INSetProfileInCarIntentResponseCodeUnspecified@0$INSetRadioStationIntentResponseCodeFailure@4$INSetRadioStationIntentResponseCodeFailureNotSubscribed@6$INSetRadioStationIntentResponseCodeFailureRequiringAppLaunch@5$INSetRadioStationIntentResponseCodeInProgress@2$INSetRadioStationIntentResponseCodeReady@1$INSetRadioStationIntentResponseCodeSuccess@3$INSetRadioStationIntentResponseCodeUnspecified@0$INSetSeatSettingsInCarIntentResponseCodeFailure@4$INSetSeatSettingsInCarIntentResponseCodeFailureRequiringAppLaunch@5$INSetSeatSettingsInCarIntentResponseCodeInProgress@2$INSetSeatSettingsInCarIntentResponseCodeReady@1$INSetSeatSettingsInCarIntentResponseCodeSuccess@3$INSetSeatSettingsInCarIntentResponseCodeUnspecified@0$INSetTaskAttributeIntentResponseCodeFailure@4$INSetTaskAttributeIntentResponseCodeFailureRequiringAppLaunch@5$INSetTaskAttributeIntentResponseCodeInProgress@2$INSetTaskAttributeIntentResponseCodeReady@1$INSetTaskAttributeIntentResponseCodeSuccess@3$INSetTaskAttributeIntentResponseCodeUnspecified@0$INSetTaskAttributeTemporalEventTriggerUnsupportedReasonInvalidRecurrence@2$INSetTaskAttributeTemporalEventTriggerUnsupportedReasonTimeInPast@1$INShareFocusStatusIntentResponseCodeFailure@4$INShareFocusStatusIntentResponseCodeFailureRequiringAppLaunch@5$INShareFocusStatusIntentResponseCodeInProgress@2$INShareFocusStatusIntentResponseCodeReady@1$INShareFocusStatusIntentResponseCodeSuccess@3$INShareFocusStatusIntentResponseCodeUnspecified@0$INShortcutAvailabilityOptionSleepJournaling@2$INShortcutAvailabilityOptionSleepMindfulness@1$INShortcutAvailabilityOptionSleepMusic@4$INShortcutAvailabilityOptionSleepPodcasts@8$INShortcutAvailabilityOptionSleepPrepareForTomorrow@32$INShortcutAvailabilityOptionSleepReading@16$INShortcutAvailabilityOptionSleepWrapUpYourDay@32$INShortcutAvailabilityOptionSleepYogaAndStretching@64$INSiriAuthorizationStatusAuthorized@3$INSiriAuthorizationStatusDenied@2$INSiriAuthorizationStatusNotDetermined@0$INSiriAuthorizationStatusRestricted@1$INSnoozeTasksIntentResponseCodeFailure@4$INSnoozeTasksIntentResponseCodeFailureRequiringAppLaunch@5$INSnoozeTasksIntentResponseCodeInProgress@2$INSnoozeTasksIntentResponseCodeReady@1$INSnoozeTasksIntentResponseCodeSuccess@3$INSnoozeTasksIntentResponseCodeUnspecified@0$INSnoozeTasksTaskUnsupportedReasonNoTasksFound@1$INSortTypeAsIs@1$INSortTypeByDate@2$INSortTypeUnknown@0$INSpatialEventArrive@1$INSpatialEventDepart@2$INSpatialEventUnknown@0$INStartAudioCallIntentResponseCodeContinueInApp@2$INStartAudioCallIntentResponseCodeFailure@3$INStartAudioCallIntentResponseCodeFailureAppConfigurationRequired@5$INStartAudioCallIntentResponseCodeFailureCallingServiceNotAvailable@6$INStartAudioCallIntentResponseCodeFailureContactNotSupportedByApp@7$INStartAudioCallIntentResponseCodeFailureNoValidNumber@8$INStartAudioCallIntentResponseCodeFailureRequiringAppLaunch@4$INStartAudioCallIntentResponseCodeReady@1$INStartAudioCallIntentResponseCodeUnspecified@0$INStartCallCallCapabilityUnsupportedReasonCameraNotAccessible@3$INStartCallCallCapabilityUnsupportedReasonMicrophoneNotAccessible@2$INStartCallCallCapabilityUnsupportedReasonVideoCallUnsupported@1$INStartCallCallRecordToCallBackUnsupportedReasonNoMatchingCall@1$INStartCallContactUnsupportedReasonInvalidHandle@4$INStartCallContactUnsupportedReasonMultipleContactsUnsupported@2$INStartCallContactUnsupportedReasonNoCallHistoryForRedial@6$INStartCallContactUnsupportedReasonNoContactFound@1$INStartCallContactUnsupportedReasonNoHandleForLabel@3$INStartCallContactUnsupportedReasonNoUsableHandleForRedial@7$INStartCallContactUnsupportedReasonRequiringInAppAuthentication@8$INStartCallContactUnsupportedReasonUnsupportedMmiUssd@5$INStartCallIntentResponseCodeContinueInApp@2$INStartCallIntentResponseCodeFailure@4$INStartCallIntentResponseCodeFailureAirplaneModeEnabled@8$INStartCallIntentResponseCodeFailureAppConfigurationRequired@10$INStartCallIntentResponseCodeFailureCallInProgress@11$INStartCallIntentResponseCodeFailureCallRinging@12$INStartCallIntentResponseCodeFailureCallingServiceNotAvailable@6$INStartCallIntentResponseCodeFailureContactNotSupportedByApp@7$INStartCallIntentResponseCodeFailureRequiringAppLaunch@5$INStartCallIntentResponseCodeFailureRequiringInAppAuthentication@13$INStartCallIntentResponseCodeFailureUnableToHandOff@9$INStartCallIntentResponseCodeReady@1$INStartCallIntentResponseCodeUnspecified@0$INStartCallIntentResponseCodeUserConfirmationRequired@3$INStartPhotoPlaybackIntentResponseCodeContinueInApp@2$INStartPhotoPlaybackIntentResponseCodeFailure@3$INStartPhotoPlaybackIntentResponseCodeFailureAppConfigurationRequired@5$INStartPhotoPlaybackIntentResponseCodeFailureRequiringAppLaunch@4$INStartPhotoPlaybackIntentResponseCodeReady@1$INStartPhotoPlaybackIntentResponseCodeUnspecified@0$INStartVideoCallIntentResponseCodeContinueInApp@2$INStartVideoCallIntentResponseCodeFailure@3$INStartVideoCallIntentResponseCodeFailureAppConfigurationRequired@5$INStartVideoCallIntentResponseCodeFailureCallingServiceNotAvailable@6$INStartVideoCallIntentResponseCodeFailureContactNotSupportedByApp@7$INStartVideoCallIntentResponseCodeFailureInvalidNumber@8$INStartVideoCallIntentResponseCodeFailureRequiringAppLaunch@4$INStartVideoCallIntentResponseCodeReady@1$INStartVideoCallIntentResponseCodeUnspecified@0$INStartWorkoutIntentResponseCodeContinueInApp@2$INStartWorkoutIntentResponseCodeFailure@3$INStartWorkoutIntentResponseCodeFailureNoMatchingWorkout@6$INStartWorkoutIntentResponseCodeFailureOngoingWorkout@5$INStartWorkoutIntentResponseCodeFailureRequiringAppLaunch@4$INStartWorkoutIntentResponseCodeHandleInApp@7$INStartWorkoutIntentResponseCodeReady@1$INStartWorkoutIntentResponseCodeSuccess@8$INStartWorkoutIntentResponseCodeUnspecified@0$INStickerTypeEmoji@1$INStickerTypeGeneric@2$INStickerTypeUnknown@0$INTaskPriorityFlagged@2$INTaskPriorityNotFlagged@1$INTaskPriorityUnknown@0$INTaskStatusCompleted@2$INTaskStatusNotCompleted@1$INTaskStatusUnknown@0$INTaskTypeCompletable@2$INTaskTypeNotCompletable@1$INTaskTypeUnknown@0$INTemporalEventTriggerTypeOptionNotScheduled@1$INTemporalEventTriggerTypeOptionScheduledNonRecurring@2$INTemporalEventTriggerTypeOptionScheduledRecurring@4$INTicketedEventCategoryMovie@1$INTicketedEventCategoryUnknown@0$INTransferMoneyIntentResponseCodeFailure@4$INTransferMoneyIntentResponseCodeFailureCredentialsUnverified@6$INTransferMoneyIntentResponseCodeFailureInsufficientFunds@7$INTransferMoneyIntentResponseCodeFailureRequiringAppLaunch@5$INTransferMoneyIntentResponseCodeInProgress@2$INTransferMoneyIntentResponseCodeReady@1$INTransferMoneyIntentResponseCodeSuccess@3$INTransferMoneyIntentResponseCodeUnspecified@0$INUnsendMessagesIntentResponseCodeFailure@4$INUnsendMessagesIntentResponseCodeFailureMessageNotFound@6$INUnsendMessagesIntentResponseCodeFailureMessageServiceNotAvailable@10$INUnsendMessagesIntentResponseCodeFailureMessageTypeUnsupported@8$INUnsendMessagesIntentResponseCodeFailurePastUnsendTimeLimit@7$INUnsendMessagesIntentResponseCodeFailureRequiringAppLaunch@5$INUnsendMessagesIntentResponseCodeFailureRequiringInAppAuthentication@11$INUnsendMessagesIntentResponseCodeFailureUnsupportedOnService@9$INUnsendMessagesIntentResponseCodeInProgress@2$INUnsendMessagesIntentResponseCodeReady@1$INUnsendMessagesIntentResponseCodeSuccess@3$INUnsendMessagesIntentResponseCodeUnspecified@0$INUpcomingMediaPredictionModeDefault@0$INUpcomingMediaPredictionModeOnlyPredictSuggestedIntents@1$INUpdateMediaAffinityIntentResponseCodeFailure@4$INUpdateMediaAffinityIntentResponseCodeFailureRequiringAppLaunch@5$INUpdateMediaAffinityIntentResponseCodeInProgress@2$INUpdateMediaAffinityIntentResponseCodeReady@1$INUpdateMediaAffinityIntentResponseCodeSuccess@3$INUpdateMediaAffinityIntentResponseCodeUnspecified@0$INUpdateMediaAffinityMediaItemUnsupportedReasonCellularDataSettings@5$INUpdateMediaAffinityMediaItemUnsupportedReasonExplicitContentSettings@4$INUpdateMediaAffinityMediaItemUnsupportedReasonLoginRequired@1$INUpdateMediaAffinityMediaItemUnsupportedReasonRegionRestriction@8$INUpdateMediaAffinityMediaItemUnsupportedReasonRestrictedContent@6$INUpdateMediaAffinityMediaItemUnsupportedReasonServiceUnavailable@7$INUpdateMediaAffinityMediaItemUnsupportedReasonSubscriptionRequired@2$INUpdateMediaAffinityMediaItemUnsupportedReasonUnsupportedMediaType@3$INVisualCodeTypeBus@5$INVisualCodeTypeContact@1$INVisualCodeTypeRequestPayment@2$INVisualCodeTypeSendPayment@3$INVisualCodeTypeSubway@6$INVisualCodeTypeTransit@4$INVisualCodeTypeUnknown@0$INVocabularyStringTypeCarName@301$INVocabularyStringTypeCarProfileName@300$INVocabularyStringTypeContactGroupName@2$INVocabularyStringTypeContactName@1$INVocabularyStringTypeMediaAudiobookAuthorName@703$INVocabularyStringTypeMediaAudiobookTitle@702$INVocabularyStringTypeMediaMusicArtistName@701$INVocabularyStringTypeMediaPlaylistTitle@700$INVocabularyStringTypeMediaShowTitle@704$INVocabularyStringTypeNotebookItemGroupName@501$INVocabularyStringTypeNotebookItemTitle@500$INVocabularyStringTypePaymentsAccountNickname@401$INVocabularyStringTypePaymentsOrganizationName@400$INVocabularyStringTypePhotoAlbumName@101$INVocabularyStringTypePhotoTag@100$INVocabularyStringTypeWorkoutActivityName@200$INWorkoutGoalUnitTypeFoot@3$INWorkoutGoalUnitTypeHour@8$INWorkoutGoalUnitTypeInch@1$INWorkoutGoalUnitTypeJoule@9$INWorkoutGoalUnitTypeKiloCalorie@10$INWorkoutGoalUnitTypeMeter@2$INWorkoutGoalUnitTypeMile@4$INWorkoutGoalUnitTypeMinute@7$INWorkoutGoalUnitTypeSecond@6$INWorkoutGoalUnitTypeUnknown@0$INWorkoutGoalUnitTypeYard@5$INWorkoutLocationTypeIndoor@2$INWorkoutLocationTypeOutdoor@1$INWorkoutLocationTypeUnknown@0$"""
misc.update(
    {
        "INListCarsIntentResponseCode": NewType("INListCarsIntentResponseCode", int),
        "INReservationActionType": NewType("INReservationActionType", int),
        "INSetAudioSourceInCarIntentResponseCode": NewType(
            "INSetAudioSourceInCarIntentResponseCode", int
        ),
        "INCallCapability": NewType("INCallCapability", int),
        "INIntentHandlingStatus": NewType("INIntentHandlingStatus", int),
        "INPlaybackQueueLocation": NewType("INPlaybackQueueLocation", int),
        "INSendMessageRecipientUnsupportedReason": NewType(
            "INSendMessageRecipientUnsupportedReason", int
        ),
        "INSpatialEvent": NewType("INSpatialEvent", int),
        "INSiriAuthorizationStatus": NewType("INSiriAuthorizationStatus", int),
        "INMediaDestinationType": NewType("INMediaDestinationType", int),
        "INAddTasksIntentResponseCode": NewType("INAddTasksIntentResponseCode", int),
        "INAddMediaMediaDestinationUnsupportedReason": NewType(
            "INAddMediaMediaDestinationUnsupportedReason", int
        ),
        "INInteractionDirection": NewType("INInteractionDirection", int),
        "INMediaSortOrder": NewType("INMediaSortOrder", int),
        "INWorkoutLocationType": NewType("INWorkoutLocationType", int),
        "INNoteContentType": NewType("INNoteContentType", int),
        "INResumeWorkoutIntentResponseCode": NewType(
            "INResumeWorkoutIntentResponseCode", int
        ),
        "INMessageType": NewType("INMessageType", int),
        "INPlayMediaPlaybackSpeedUnsupportedReason": NewType(
            "INPlayMediaPlaybackSpeedUnsupportedReason", int
        ),
        "INGetRideStatusIntentResponseCode": NewType(
            "INGetRideStatusIntentResponseCode", int
        ),
        "INStartCallCallRecordToCallBackUnsupportedReason": NewType(
            "INStartCallCallRecordToCallBackUnsupportedReason", int
        ),
        "INHangUpCallIntentResponseCode": NewType(
            "INHangUpCallIntentResponseCode", int
        ),
        "INSearchCallHistoryIntentResponseCode": NewType(
            "INSearchCallHistoryIntentResponseCode", int
        ),
        "INCreateNoteIntentResponseCode": NewType(
            "INCreateNoteIntentResponseCode", int
        ),
        "INMediaUserContextSubscriptionStatus": NewType(
            "INMediaUserContextSubscriptionStatus", int
        ),
        "INPersonSuggestionType": NewType("INPersonSuggestionType", int),
        "INPaymentMethodType": NewType("INPaymentMethodType", int),
        "INTransferMoneyIntentResponseCode": NewType(
            "INTransferMoneyIntentResponseCode", int
        ),
        "INGetCarPowerLevelStatusIntentResponseCode": NewType(
            "INGetCarPowerLevelStatusIntentResponseCode", int
        ),
        "INSendRideFeedbackIntentResponseCode": NewType(
            "INSendRideFeedbackIntentResponseCode", int
        ),
        "INCallRecordTypeOptions": NewType("INCallRecordTypeOptions", int),
        "INTicketedEventCategory": NewType("INTicketedEventCategory", int),
        "INUpdateMediaAffinityIntentResponseCode": NewType(
            "INUpdateMediaAffinityIntentResponseCode", int
        ),
        "INStartCallIntentResponseCode": NewType("INStartCallIntentResponseCode", int),
        "INTaskType": NewType("INTaskType", int),
        "INPlayMediaMediaItemUnsupportedReason": NewType(
            "INPlayMediaMediaItemUnsupportedReason", int
        ),
        "INMessageAttributeOptions": NewType("INMessageAttributeOptions", int),
        "INStartVideoCallIntentResponseCode": NewType(
            "INStartVideoCallIntentResponseCode", int
        ),
        "INCancelWorkoutIntentResponseCode": NewType(
            "INCancelWorkoutIntentResponseCode", int
        ),
        "INVocabularyStringType": NewType("INVocabularyStringType", int),
        "INSearchForAccountsIntentResponseCode": NewType(
            "INSearchForAccountsIntentResponseCode", int
        ),
        "INFocusStatusAuthorizationStatus": NewType(
            "INFocusStatusAuthorizationStatus", int
        ),
        "INMessageAttribute": NewType("INMessageAttribute", int),
        "INSearchForNotebookItemsIntentResponseCode": NewType(
            "INSearchForNotebookItemsIntentResponseCode", int
        ),
        "INCarAirCirculationMode": NewType("INCarAirCirculationMode", int),
        "INSendPaymentIntentResponseCode": NewType(
            "INSendPaymentIntentResponseCode", int
        ),
        "INCallDestinationType": NewType("INCallDestinationType", int),
        "INBookRestaurantReservationIntentCode": NewType(
            "INBookRestaurantReservationIntentCode", int
        ),
        "INAddMediaIntentResponseCode": NewType("INAddMediaIntentResponseCode", int),
        "INUnsendMessagesIntentResponseCode": NewType(
            "INUnsendMessagesIntentResponseCode", int
        ),
        "INSetSeatSettingsInCarIntentResponseCode": NewType(
            "INSetSeatSettingsInCarIntentResponseCode", int
        ),
        "INTaskStatus": NewType("INTaskStatus", int),
        "INRelevantShortcutRole": NewType("INRelevantShortcutRole", int),
        "INDailyRoutineSituation": NewType("INDailyRoutineSituation", int),
        "INStartWorkoutIntentResponseCode": NewType(
            "INStartWorkoutIntentResponseCode", int
        ),
        "INSearchForPhotosIntentResponseCode": NewType(
            "INSearchForPhotosIntentResponseCode", int
        ),
        "INRequestRideIntentResponseCode": NewType(
            "INRequestRideIntentResponseCode", int
        ),
        "INPaymentStatus": NewType("INPaymentStatus", int),
        "INRecurrenceFrequency": NewType("INRecurrenceFrequency", int),
        "INAddMediaMediaItemUnsupportedReason": NewType(
            "INAddMediaMediaItemUnsupportedReason", int
        ),
        "INGetCarLockStatusIntentResponseCode": NewType(
            "INGetCarLockStatusIntentResponseCode", int
        ),
        "INAddTasksTargetTaskListConfirmationReason": NewType(
            "INAddTasksTargetTaskListConfirmationReason", int
        ),
        "INCarDefroster": NewType("INCarDefroster", int),
        "INSearchForMediaIntentResponseCode": NewType(
            "INSearchForMediaIntentResponseCode", int
        ),
        "INOutgoingMessageType": NewType("INOutgoingMessageType", int),
        "INDayOfWeekOptions": NewType("INDayOfWeekOptions", int),
        "INBalanceType": NewType("INBalanceType", int),
        "INRidePhase": NewType("INRidePhase", int),
        "INNotebookItemType": NewType("INNotebookItemType", int),
        "INUpcomingMediaPredictionMode": NewType("INUpcomingMediaPredictionMode", int),
        "INDeleteTasksTaskUnsupportedReason": NewType(
            "INDeleteTasksTaskUnsupportedReason", int
        ),
        "INPhotoAttributeOptions": NewType("INPhotoAttributeOptions", int),
        "INPersonHandleType": NewType("INPersonHandleType", int),
        "INSnoozeTasksIntentResponseCode": NewType(
            "INSnoozeTasksIntentResponseCode", int
        ),
        "INSetTaskAttributeTemporalEventTriggerUnsupportedReason": NewType(
            "INSetTaskAttributeTemporalEventTriggerUnsupportedReason", int
        ),
        "INCallRecordType": NewType("INCallRecordType", int),
        "INCarSignalOptions": NewType("INCarSignalOptions", int),
        "INGetAvailableRestaurantReservationBookingDefaultsIntentResponseCode": NewType(
            "INGetAvailableRestaurantReservationBookingDefaultsIntentResponseCode", int
        ),
        "INAnswerCallIntentResponseCode": NewType(
            "INAnswerCallIntentResponseCode", int
        ),
        "INSearchForMessagesIntentResponseCode": NewType(
            "INSearchForMessagesIntentResponseCode", int
        ),
        "INCarAudioSource": NewType("INCarAudioSource", int),
        "INAccountType": NewType("INAccountType", int),
        "INBillType": NewType("INBillType", int),
        "INGetRestaurantGuestIntentResponseCode": NewType(
            "INGetRestaurantGuestIntentResponseCode", int
        ),
        "INMediaAffinityType": NewType("INMediaAffinityType", int),
        "INGetAvailableRestaurantReservationBookingsIntentCode": NewType(
            "INGetAvailableRestaurantReservationBookingsIntentCode", int
        ),
        "INAddTasksTemporalEventTriggerUnsupportedReason": NewType(
            "INAddTasksTemporalEventTriggerUnsupportedReason", int
        ),
        "INGetUserCurrentRestaurantReservationBookingsIntentResponseCode": NewType(
            "INGetUserCurrentRestaurantReservationBookingsIntentResponseCode", int
        ),
        "INRadioType": NewType("INRadioType", int),
        "INGetReservationDetailsIntentResponseCode": NewType(
            "INGetReservationDetailsIntentResponseCode", int
        ),
        "INCallAudioRoute": NewType("INCallAudioRoute", int),
        "INConditionalOperator": NewType("INConditionalOperator", int),
        "INSendPaymentCurrencyAmountUnsupportedReason": NewType(
            "INSendPaymentCurrencyAmountUnsupportedReason", int
        ),
        "INSaveProfileInCarIntentResponseCode": NewType(
            "INSaveProfileInCarIntentResponseCode", int
        ),
        "INCallCapabilityOptions": NewType("INCallCapabilityOptions", int),
        "INReservationStatus": NewType("INReservationStatus", int),
        "INSetTaskAttributeIntentResponseCode": NewType(
            "INSetTaskAttributeIntentResponseCode", int
        ),
        "INDeleteTasksTaskListUnsupportedReason": NewType(
            "INDeleteTasksTaskListUnsupportedReason", int
        ),
        "INRideFeedbackTypeOptions": NewType("INRideFeedbackTypeOptions", int),
        "INRelativeSetting": NewType("INRelativeSetting", int),
        "INRelativeReference": NewType("INRelativeReference", int),
        "INSetRadioStationIntentResponseCode": NewType(
            "INSetRadioStationIntentResponseCode", int
        ),
        "INSendPaymentPayeeUnsupportedReason": NewType(
            "INSendPaymentPayeeUnsupportedReason", int
        ),
        "INSetClimateSettingsInCarIntentResponseCode": NewType(
            "INSetClimateSettingsInCarIntentResponseCode", int
        ),
        "INStartPhotoPlaybackIntentResponseCode": NewType(
            "INStartPhotoPlaybackIntentResponseCode", int
        ),
        "INPauseWorkoutIntentResponseCode": NewType(
            "INPauseWorkoutIntentResponseCode", int
        ),
        "INSearchForBillsIntentResponseCode": NewType(
            "INSearchForBillsIntentResponseCode", int
        ),
        "INWorkoutGoalUnitType": NewType("INWorkoutGoalUnitType", int),
        "INStartCallContactUnsupportedReason": NewType(
            "INStartCallContactUnsupportedReason", int
        ),
        "INPayBillIntentResponseCode": NewType("INPayBillIntentResponseCode", int),
        "INMediaReference": NewType("INMediaReference", int),
        "INRestaurantReservationUserBookingStatus": NewType(
            "INRestaurantReservationUserBookingStatus", int
        ),
        "INSetMessageAttributeIntentResponseCode": NewType(
            "INSetMessageAttributeIntentResponseCode", int
        ),
        "INListRideOptionsIntentResponseCode": NewType(
            "INListRideOptionsIntentResponseCode", int
        ),
        "INTaskPriority": NewType("INTaskPriority", int),
        "INEditMessageIntentResponseCode": NewType(
            "INEditMessageIntentResponseCode", int
        ),
        "INDateSearchType": NewType("INDateSearchType", int),
        "INStartCallCallCapabilityUnsupportedReason": NewType(
            "INStartCallCallCapabilityUnsupportedReason", int
        ),
        "INMediaItemType": NewType("INMediaItemType", int),
        "INCreateTaskListIntentResponseCode": NewType(
            "INCreateTaskListIntentResponseCode", int
        ),
        "INCancelRideIntentResponseCode": NewType(
            "INCancelRideIntentResponseCode", int
        ),
        "INAppendToNoteIntentResponseCode": NewType(
            "INAppendToNoteIntentResponseCode", int
        ),
        "INIntentErrorCode": NewType("INIntentErrorCode", int),
        "INSetDefrosterSettingsInCarIntentResponseCode": NewType(
            "INSetDefrosterSettingsInCarIntentResponseCode", int
        ),
        "INUpdateMediaAffinityMediaItemUnsupportedReason": NewType(
            "INUpdateMediaAffinityMediaItemUnsupportedReason", int
        ),
        "INStartAudioCallIntentResponseCode": NewType(
            "INStartAudioCallIntentResponseCode", int
        ),
        "INMessageReactionType": NewType("INMessageReactionType", int),
        "INRequestPaymentCurrencyAmountUnsupportedReason": NewType(
            "INRequestPaymentCurrencyAmountUnsupportedReason", int
        ),
        "INSetCarLockStatusIntentResponseCode": NewType(
            "INSetCarLockStatusIntentResponseCode", int
        ),
        "INSortType": NewType("INSortType", int),
        "INRequestPaymentPayerUnsupportedReason": NewType(
            "INRequestPaymentPayerUnsupportedReason", int
        ),
        "INTemporalEventTriggerTypeOptions": NewType(
            "INTemporalEventTriggerTypeOptions", int
        ),
        "INEndWorkoutIntentResponseCode": NewType(
            "INEndWorkoutIntentResponseCode", int
        ),
        "INActivateCarSignalIntentResponseCode": NewType(
            "INActivateCarSignalIntentResponseCode", int
        ),
        "INPlayMediaIntentResponseCode": NewType("INPlayMediaIntentResponseCode", int),
        "INSearchForMediaMediaItemUnsupportedReason": NewType(
            "INSearchForMediaMediaItemUnsupportedReason", int
        ),
        "INSetProfileInCarIntentResponseCode": NewType(
            "INSetProfileInCarIntentResponseCode", int
        ),
        "INGetVisualCodeIntentResponseCode": NewType(
            "INGetVisualCodeIntentResponseCode", int
        ),
        "INLocationSearchType": NewType("INLocationSearchType", int),
        "INCarSeat": NewType("INCarSeat", int),
        "INShortcutAvailabilityOptions": NewType("INShortcutAvailabilityOptions", int),
        "INSnoozeTasksTaskUnsupportedReason": NewType(
            "INSnoozeTasksTaskUnsupportedReason", int
        ),
        "INDeleteTasksIntentResponseCode": NewType(
            "INDeleteTasksIntentResponseCode", int
        ),
        "INVisualCodeType": NewType("INVisualCodeType", int),
        "INPlaybackRepeatMode": NewType("INPlaybackRepeatMode", int),
        "INShareFocusStatusIntentResponseCode": NewType(
            "INShareFocusStatusIntentResponseCode", int
        ),
        "INRequestPaymentIntentResponseCode": NewType(
            "INRequestPaymentIntentResponseCode", int
        ),
        "INStickerType": NewType("INStickerType", int),
        "INAmountType": NewType("INAmountType", int),
        "INSendMessageIntentResponseCode": NewType(
            "INSendMessageIntentResponseCode", int
        ),
    }
)
misc.update(
    {
        "INPersonRelationship": NewType("INPersonRelationship", str),
        "INPersonHandleLabel": NewType("INPersonHandleLabel", str),
    }
)
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"INBooleanResolutionResult",
        b"successWithResolvedValue:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"INFile", b"removedOnCompletion", {"retval": {"type": b"Z"}})
    r(b"INFile", b"setRemovedOnCompletion:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"INFocusStatusCenter",
        b"requestAuthorizationWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"INInteraction",
        b"deleteAllInteractionsWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"INInteraction",
        b"deleteInteractionsWithGroupIdentifier:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"INInteraction",
        b"deleteInteractionsWithIdentifiers:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"INInteraction",
        b"deleteInteractionsWithIdentifiers:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"INInteraction",
        b"donateInteractionWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"INObjectCollection",
        b"setUsesIndexedCollation:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"INObjectCollection", b"usesIndexedCollation", {"retval": {"type": b"Z"}})
    r(b"INParameter", b"isEqualToParameter:", {"retval": {"type": "Z"}})
    r(
        b"INPerson",
        b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:isContactSuggestion:suggestionType:",
        {"arguments": {8: {"type": b"Z"}}},
    )
    r(
        b"INPerson",
        b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:isMe:",
        {"arguments": {8: {"type": "Z"}}},
    )
    r(
        b"INPerson",
        b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:isMe:suggestionType:",
        {"arguments": {8: {"type": b"Z"}}},
    )
    r(b"INPerson", b"isContactSuggestion", {"retval": {"type": "Z"}})
    r(b"INPerson", b"isMe", {"retval": {"type": b"Z"}})
    r(
        b"INPreferences",
        b"requestSiriAuthorization:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"INRelevantShortcutStore",
        b"setRelevantShortcuts:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"emailAddressEditable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"emailAddressFieldShouldBeDisplayed",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"nameEditable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"nameFieldFirstNameOptional",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"nameFieldLastNameOptional",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"nameFieldShouldBeDisplayed",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"phoneNumberEditable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"phoneNumberFieldShouldBeDisplayed",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"setEmailAddressEditable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"setEmailAddressFieldShouldBeDisplayed:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"setNameEditable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"setNameFieldFirstNameOptional:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"setNameFieldLastNameOptional:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"setNameFieldShouldBeDisplayed:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"setPhoneNumberEditable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantGuestDisplayPreferences",
        b"setPhoneNumberFieldShouldBeDisplayed:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantReservationBooking",
        b"isBookingAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantReservationBooking",
        b"requiresEmailAddress",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantReservationBooking",
        b"requiresManualRequest",
        {"retval": {"type": b"Z"}},
    )
    r(b"INRestaurantReservationBooking", b"requiresName", {"retval": {"type": b"Z"}})
    r(
        b"INRestaurantReservationBooking",
        b"requiresPhoneNumber",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INRestaurantReservationBooking",
        b"setBookingAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantReservationBooking",
        b"setRequiresEmailAddress:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantReservationBooking",
        b"setRequiresManualRequest:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantReservationBooking",
        b"setRequiresName:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INRestaurantReservationBooking",
        b"setRequiresPhoneNumber:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"INRideCompletionStatus", b"isCanceled", {"retval": {"type": b"Z"}})
    r(b"INRideCompletionStatus", b"isCompleted", {"retval": {"type": b"Z"}})
    r(b"INRideCompletionStatus", b"isMissedPickup", {"retval": {"type": b"Z"}})
    r(b"INRideCompletionStatus", b"isOutstanding", {"retval": {"type": b"Z"}})
    r(
        b"INSendMessageIntentDonationMetadata",
        b"isReplyToCurrentUser",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INSendMessageIntentDonationMetadata",
        b"mentionsCurrentUser",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INSendMessageIntentDonationMetadata",
        b"notifyRecipientAnyway",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"INSendMessageIntentDonationMetadata",
        b"setMentionsCurrentUser:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INSendMessageIntentDonationMetadata",
        b"setNotifyRecipientAnyway:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INSendMessageIntentDonationMetadata",
        b"setReplyToCurrentUser:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"INVoiceShortcutCenter",
        b"getAllVoiceShortcutsWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"INVoiceShortcutCenter",
        b"getVoiceShortcutWithIdentifier:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSObject",
        b"alternativeSpeakableMatches",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"confirmActivateCarSignal:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmAddMedia:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmAddTasks:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmAnswerCall:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmAppendToNote:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmBookRestaurantReservation:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmCancelRide:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmCancelWorkout:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmCreateNote:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmCreateTaskList:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmDeleteTasks:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmEditMessage:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmEndWorkout:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmGetAvailableRestaurantReservationBookingDefaults:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmGetAvailableRestaurantReservationBookings:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmGetCarLockStatus:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmGetCarPowerLevelStatus:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmGetRestaurantGuest:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmGetRideStatus:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmGetUserCurrentRestaurantReservationBookings:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmGetVisualCode:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmHangUpCall:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmListCars:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmListRideOptions:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmPauseWorkout:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmPayBill:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmPlayMedia:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmRequestPayment:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmRequestRide:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmResumeWorkout:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSaveProfileInCar:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSearchCallHistory:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSearchForAccounts:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSearchForBills:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSearchForMedia:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSearchForMessages:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSearchForNotebookItems:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSearchForPhotos:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSendMessage:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSendPayment:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSendRideFeedback:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetAudioSourceInCar:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetCarLockStatus:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetClimateSettingsInCar:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetDefrosterSettingsInCar:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetMessageAttribute:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetProfileInCar:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetRadioStation:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetSeatSettingsInCar:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSetTaskAttribute:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmShareFocusStatus:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmSnoozeTasks:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmStartAudioCall:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmStartCall:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmStartPhotoPlayback:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmStartVideoCall:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmStartWorkout:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmTransferMoney:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmUnsendMessages:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"confirmUpdateMediaAffinity:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"getCarPowerLevelStatusResponseDidUpdate:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"getRideStatusResponseDidUpdate:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"handleActivateCarSignal:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleAddMedia:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleAddTasks:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleAnswerCall:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleAppendToNote:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleBookRestaurantReservation:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleCancelRide:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleCancelWorkout:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleCreateNote:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleCreateTaskList:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleDeleteTasks:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleEditMessage:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleEndWorkout:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleGetAvailableRestaurantReservationBookingDefaults:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleGetAvailableRestaurantReservationBookings:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleGetCarLockStatus:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleGetCarPowerLevelStatus:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleGetRestaurantGuest:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleGetRideStatus:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleGetUserCurrentRestaurantReservationBookings:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleGetVisualCode:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleHangUpCall:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleListCars:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleListRideOptions:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handlePauseWorkout:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handlePayBill:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handlePlayMedia:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleRequestPayment:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleRequestRide:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleResumeWorkout:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSaveProfileInCar:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSearchCallHistory:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSearchForAccounts:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSearchForBills:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSearchForMedia:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSearchForMessages:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSearchForNotebookItems:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSearchForPhotos:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSendMessage:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSendPayment:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSendRideFeedback:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetAudioSourceInCar:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetCarLockStatus:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetClimateSettingsInCar:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetDefrosterSettingsInCar:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetMessageAttribute:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetProfileInCar:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetRadioStation:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetSeatSettingsInCar:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSetTaskAttribute:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleShareFocusStatus:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleSnoozeTasks:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleStartAudioCall:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleStartCall:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleStartPhotoPlayback:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleStartVideoCall:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleStartWorkout:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleTransferMoney:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleUnsendMessages:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleUpdateMediaAffinity:completion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handlerForIntent:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"identifier", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"pronunciationHint", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"resolveAccountNicknameForSearchForAccounts:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveAccountTypeForSearchForAccounts:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveAffinityTypeForUpdateMediaAffinity:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveAirCirculationModeForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveAlbumNameForSearchForPhotos:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveAlbumNameForStartPhotoPlayback:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveAttributeForSetMessageAttribute:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveAttributesForSearchForMessages:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveAudioSourceForSetAudioSourceInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveBillPayeeForPayBill:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveBillPayeeForSearchForBills:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveBillTypeForPayBill:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveBillTypeForSearchForBills:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveBookingDateComponentsForBookRestaurantReservation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCallCapabilitiesForSearchCallHistory:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"resolveCallCapabilityForStartCall:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCallRecordToCallBackForStartCall:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCallTypeForSearchCallHistory:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCallTypesForSearchCallHistory:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCarNameForActivateCarSignal:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCarNameForGetCarLockStatus:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCarNameForGetCarPowerLevelStatus:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCarNameForSetCarLockStatus:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCarNameForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCarNameForSetDefrosterSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCarNameForSetProfileInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCarNameForSetSeatSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveChannelForSetRadioStation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveClimateZoneForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveContactsForStartAudioCall:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveContactsForStartCall:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveContactsForStartVideoCall:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveContentForAppendToNote:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveContentForCreateNote:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveContentForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveContentForSendMessage:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCurrencyAmountForRequestPayment:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCurrencyAmountForRequestPayment:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCurrencyAmountForSendPayment:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveCurrencyAmountForSendPayment:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDateCreatedForSearchCallHistory:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDateCreatedForSearchForPhotos:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDateCreatedForStartPhotoPlayback:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDateSearchTypeForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDateTimeForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDateTimeRangeForSearchForMessages:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDefaultProfileForSetProfileInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDefrosterForSetDefrosterSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDestinationTypeForStartAudioCall:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDestinationTypeForStartCall:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDropOffLocationForListRideOptions:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDropOffLocationForRequestRide:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDueDateForPayBill:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveDueDateRangeForSearchForBills:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEditedContentForEditMessage:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEnableAirConditionerForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEnableAutoModeForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEnableClimateControlForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEnableCoolingForSetSeatSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEnableFanForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEnableForSetDefrosterSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEnableHeatingForSetSeatSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveEnableMassageForSetSeatSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveFanSpeedIndexForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveFanSpeedPercentageForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveFrequencyForSetRadioStation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveFromAccountForPayBill:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveFromAccountForTransferMoney:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveGoalValueForStartWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveGroupNameForCreateNote:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveGroupNameForCreateTaskList:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveGroupNameForSendMessage:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveGroupNamesForSearchForMessages:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveGuestForBookRestaurantReservation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveGuestProvidedSpecialRequestTextForBookRestaurantReservation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveIdentifiersForSearchForMessages:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"resolveIsOpenEndedForStartWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveItemTypeForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveLevelForSetSeatSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveLocationCreatedForSearchForPhotos:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveLocationCreatedForStartPhotoPlayback:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveLocationForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveLocationSearchTypeForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveLockedForSetCarLockStatus:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveMediaDestinationForAddMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveMediaItemsForAddMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveMediaItemsForPlayMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveMediaItemsForSearchForMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveMediaItemsForUpdateMediaAffinity:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveNextTriggerTimeForSnoozeTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveNoteForRequestPayment:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveNoteForSendPayment:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveNotificationIdentifiersForSearchForMessages:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"resolveOrganizationNameForSearchForAccounts:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveOutgoingMessageTypeForSendMessage:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePartySizeForBookRestaurantReservation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePartySizeForGetAvailableRestaurantReservationBookings:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePartySizeForRequestRide:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePayeeForSendPayment:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePayeeForSendPayment:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePayerForRequestPayment:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePayerForRequestPayment:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePaymentDateRangeForSearchForBills:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePeopleInPhotoForSearchForPhotos:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePeopleInPhotoForStartPhotoPlayback:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePickupLocationForListRideOptions:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePickupLocationForRequestRide:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePlayShuffledForPlayMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePlaybackQueueLocationForPlayMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePlaybackRepeatModeForPlayMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePlaybackSpeedForPlayMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePreferredBookingDateComponentsForGetAvailableRestaurantReservationBookings:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePresetNumberForSetRadioStation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePriorityForAddTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolvePriorityForSetTaskAttribute:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveProfileNameForSaveProfileInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveProfileNameForSetProfileInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveProfileNumberForSaveProfileInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveProfileNumberForSetProfileInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRadioTypeForSetRadioStation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRecipientForSearchCallHistory:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRecipientsForSearchForMessages:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRecipientsForSendMessage:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRecipientsForSendMessage:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRelativeAudioSourceReferenceForSetAudioSourceInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRelativeFanSpeedSettingForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRelativeLevelSettingForSetSeatSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRelativeTemperatureSettingForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRequestedBalanceTypeForSearchForAccounts:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRestaurantForBookRestaurantReservation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRestaurantForGetAvailableRestaurantReservationBookingDefaults:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRestaurantForGetAvailableRestaurantReservationBookings:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRestaurantForGetUserCurrentRestaurantReservationBookings:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveResumePlaybackForPlayMedia:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveRideOptionNameForRequestRide:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveScheduledPickupTimeForRequestRide:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveSearchTermsForSearchForMessages:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"resolveSearchTermsForSearchForPhotos:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveSeatForSetSeatSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveSenderForSendMessage:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"resolveSendersForSearchForMessages:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveServiceNameForSendMessage:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"resolveSignalsForActivateCarSignal:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveSpatialEventTriggerForAddTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveSpatialEventTriggerForSetTaskAttribute:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveSpeakableGroupNameForSendMessage:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveSpeakableGroupNamesForSearchForMessages:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveStationNameForSetRadioStation:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveStatusForSearchForBills:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveStatusForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveStatusForSetTaskAttribute:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTargetNoteForAppendToNote:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTargetTaskForSetTaskAttribute:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTargetTaskListForAddTasks:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTargetTaskListForAddTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTaskListForDeleteTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTaskPriorityForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTaskTitleForSetTaskAttribute:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTaskTitlesForAddTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTaskTitlesForCreateTaskList:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTasksForDeleteTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTasksForSnoozeTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTemperatureForSetClimateSettingsInCar:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTemporalEventTriggerForAddTasks:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTemporalEventTriggerForAddTasks:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTemporalEventTriggerForSetTaskAttribute:completion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTemporalEventTriggerForSetTaskAttribute:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTemporalEventTriggerTypesForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTitleForCreateNote:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTitleForCreateTaskList:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTitleForSearchForNotebookItems:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveToAccountForTransferMoney:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTransactionAmountForPayBill:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTransactionAmountForTransferMoney:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTransactionNoteForPayBill:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTransactionNoteForTransferMoney:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTransactionScheduledDateForPayBill:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveTransactionScheduledDateForTransferMoney:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveUnseenForSearchCallHistory:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveVisualCodeTypeForGetVisualCode:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveWorkoutGoalUnitTypeForStartWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveWorkoutLocationTypeForStartWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveWorkoutNameForCancelWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveWorkoutNameForEndWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveWorkoutNameForPauseWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveWorkoutNameForResumeWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"resolveWorkoutNameForStartWorkout:withCompletion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"spokenPhrase", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"startSendingUpdatesForGetCarPowerLevelStatus:toObserver:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"startSendingUpdatesForGetRideStatus:toObserver:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"stopSendingUpdatesForGetCarPowerLevelStatus:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"stopSendingUpdatesForGetRideStatus:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"vocabularyIdentifier",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSString", b"deferredLocalizedIntentsStringWithFormat:", {"variadic": True})
    r(
        b"NSString",
        b"deferredLocalizedIntentsStringWithFormat:fromTable:",
        {"variadic": True},
    )
    r(b"null", b"deferredLocalizedIntentsStringWithFormat:", {"variadic": True})
    r(
        b"null",
        b"deferredLocalizedIntentsStringWithFormat:fromTable:",
        {"variadic": True},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "INActivateCarSignalIntent", b"initWithCarName:signals:"
)
objc.registerNewKeywordsFromSelector(
    "INActivateCarSignalIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INAddMediaIntent", b"initWithMediaItems:mediaSearch:mediaDestination:"
)
objc.registerNewKeywordsFromSelector(
    "INAddMediaIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INAddMediaMediaDestinationResolutionResult",
    b"initWithMediaDestinationResolutionResult:",
)
objc.registerNewKeywordsFromSelector(
    "INAddMediaMediaItemResolutionResult", b"initWithMediaItemResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INAddTasksIntent",
    b"initWithTargetTaskList:taskTitles:spatialEventTrigger:temporalEventTrigger:",
)
objc.registerNewKeywordsFromSelector(
    "INAddTasksIntent",
    b"initWithTargetTaskList:taskTitles:spatialEventTrigger:temporalEventTrigger:priority:",
)
objc.registerNewKeywordsFromSelector(
    "INAddTasksIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INAddTasksTargetTaskListResolutionResult", b"initWithTaskListResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INAddTasksTemporalEventTriggerResolutionResult",
    b"initWithTemporalEventTriggerResolutionResult:",
)
objc.registerNewKeywordsFromSelector(
    "INAggregatedMessageReaction", b"initWithReactionType:emoji:senders:reactionCount:"
)
objc.registerNewKeywordsFromSelector("INAirline", b"initWithName:iataCode:icaoCode:")
objc.registerNewKeywordsFromSelector("INAirport", b"initWithName:iataCode:icaoCode:")
objc.registerNewKeywordsFromSelector("INAirportGate", b"initWithAirport:terminal:gate:")
objc.registerNewKeywordsFromSelector(
    "INAnswerCallIntent", b"initWithAudioRoute:callIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "INAnswerCallIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INAppendToNoteIntent", b"initWithTargetNote:content:"
)
objc.registerNewKeywordsFromSelector(
    "INAppendToNoteIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector("INBalanceAmount", b"initWithAmount:balanceType:")
objc.registerNewKeywordsFromSelector("INBalanceAmount", b"initWithAmount:currencyCode:")
objc.registerNewKeywordsFromSelector(
    "INBillDetails",
    b"initWithBillType:paymentStatus:billPayee:amountDue:minimumDue:lateFee:dueDate:paymentDate:",
)
objc.registerNewKeywordsFromSelector(
    "INBillPayee", b"initWithNickname:number:organizationName:"
)
objc.registerNewKeywordsFromSelector(
    "INBoatReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:URL:reservedSeat:boatTrip:",
)
objc.registerNewKeywordsFromSelector(
    "INBoatTrip",
    b"initWithProvider:boatName:boatNumber:tripDuration:departureBoatTerminalLocation:arrivalBoatTerminalLocation:",
)
objc.registerNewKeywordsFromSelector(
    "INBookRestaurantReservationIntent",
    b"initWithRestaurant:bookingDateComponents:partySize:bookingIdentifier:guest:selectedOffer:guestProvidedSpecialRequestText:",
)
objc.registerNewKeywordsFromSelector(
    "INBookRestaurantReservationIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INBusReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:URL:reservedSeat:busTrip:",
)
objc.registerNewKeywordsFromSelector(
    "INBusTrip",
    b"initWithProvider:busName:busNumber:tripDuration:departureBusStopLocation:departurePlatform:arrivalBusStopLocation:arrivalPlatform:",
)
objc.registerNewKeywordsFromSelector("INCallGroup", b"initWithGroupName:groupId:")
objc.registerNewKeywordsFromSelector(
    "INCallRecord",
    b"initWithIdentifier:dateCreated:callRecordType:callCapability:callDuration:unseen:",
)
objc.registerNewKeywordsFromSelector(
    "INCallRecord",
    b"initWithIdentifier:dateCreated:callRecordType:callCapability:callDuration:unseen:numberOfCalls:",
)
objc.registerNewKeywordsFromSelector(
    "INCallRecord",
    b"initWithIdentifier:dateCreated:callRecordType:callCapability:callDuration:unseen:participants:numberOfCalls:isCallerIdBlocked:",
)
objc.registerNewKeywordsFromSelector(
    "INCallRecord",
    b"initWithIdentifier:dateCreated:caller:callRecordType:callCapability:callDuration:unseen:",
)
objc.registerNewKeywordsFromSelector(
    "INCallRecord",
    b"initWithIdentifier:dateCreated:caller:callRecordType:callCapability:callDuration:unseen:numberOfCalls:",
)
objc.registerNewKeywordsFromSelector(
    "INCallRecordFilter", b"initWithParticipants:callTypes:callCapability:"
)
objc.registerNewKeywordsFromSelector("INCancelRideIntent", b"initWithRideIdentifier:")
objc.registerNewKeywordsFromSelector(
    "INCancelRideIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector("INCancelWorkoutIntent", b"initWithWorkoutName:")
objc.registerNewKeywordsFromSelector(
    "INCancelWorkoutIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INCar",
    b"initWithCarIdentifier:displayName:year:make:model:color:headUnit:supportedChargingConnectors:",
)
objc.registerNewKeywordsFromSelector(
    "INCarHeadUnit", b"initWithBluetoothIdentifier:iAP2Identifier:"
)
objc.registerNewKeywordsFromSelector("INColor", b"initWithRed:green:blue:")
objc.registerNewKeywordsFromSelector(
    "INCreateNoteIntent", b"initWithTitle:content:groupName:"
)
objc.registerNewKeywordsFromSelector(
    "INCreateNoteIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INCreateTaskListIntent", b"initWithTitle:taskTitles:groupName:"
)
objc.registerNewKeywordsFromSelector(
    "INCreateTaskListIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INCurrencyAmount", b"initWithAmount:currencyCode:"
)
objc.registerNewKeywordsFromSelector(
    "INDailyRoutineRelevanceProvider", b"initWithSituation:"
)
objc.registerNewKeywordsFromSelector(
    "INDateComponentsRange", b"initWithEKRecurrenceRule:"
)
objc.registerNewKeywordsFromSelector(
    "INDateComponentsRange", b"initWithStartDateComponents:endDateComponents:"
)
objc.registerNewKeywordsFromSelector(
    "INDateComponentsRange",
    b"initWithStartDateComponents:endDateComponents:recurrenceRule:",
)
objc.registerNewKeywordsFromSelector(
    "INDateRelevanceProvider", b"initWithStartDate:endDate:"
)
objc.registerNewKeywordsFromSelector("INDefaultCardTemplate", b"initWithTitle:")
objc.registerNewKeywordsFromSelector(
    "INDeleteTasksIntent", b"initWithTaskList:tasks:all:"
)
objc.registerNewKeywordsFromSelector(
    "INDeleteTasksIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INDeleteTasksTaskListResolutionResult", b"initWithTaskListResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INDeleteTasksTaskResolutionResult", b"initWithTaskResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INEditMessageIntent", b"initWithMessageIdentifier:editedContent:"
)
objc.registerNewKeywordsFromSelector(
    "INEditMessageIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector("INEndWorkoutIntent", b"initWithWorkoutName:")
objc.registerNewKeywordsFromSelector(
    "INEndWorkoutIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INFlight",
    b"initWithAirline:flightNumber:boardingTime:flightDuration:departureAirportGate:arrivalAirportGate:",
)
objc.registerNewKeywordsFromSelector(
    "INFlightReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:URL:reservedSeat:flight:",
)
objc.registerNewKeywordsFromSelector(
    "INFlightReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:reservedSeat:flight:",
)
objc.registerNewKeywordsFromSelector("INFocusStatus", b"initWithIsFocused:")
objc.registerNewKeywordsFromSelector(
    "INGetAvailableRestaurantReservationBookingDefaultsIntent", b"initWithRestaurant:"
)
objc.registerNewKeywordsFromSelector(
    "INGetAvailableRestaurantReservationBookingDefaultsIntentResponse",
    b"initWithDefaultPartySize:defaultBookingDate:code:userActivity:",
)
objc.registerNewKeywordsFromSelector(
    "INGetAvailableRestaurantReservationBookingsIntent",
    b"initWithRestaurant:partySize:preferredBookingDateComponents:maximumNumberOfResults:earliestBookingDateForResults:latestBookingDateForResults:",
)
objc.registerNewKeywordsFromSelector(
    "INGetAvailableRestaurantReservationBookingsIntentResponse",
    b"initWithAvailableBookings:code:userActivity:",
)
objc.registerNewKeywordsFromSelector("INGetCarLockStatusIntent", b"initWithCarName:")
objc.registerNewKeywordsFromSelector(
    "INGetCarLockStatusIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INGetCarPowerLevelStatusIntent", b"initWithCarName:"
)
objc.registerNewKeywordsFromSelector(
    "INGetCarPowerLevelStatusIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INGetReservationDetailsIntent",
    b"initWithReservationContainerReference:reservationItemReferences:",
)
objc.registerNewKeywordsFromSelector(
    "INGetReservationDetailsIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INGetRestaurantGuestIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INGetRideStatusIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INGetUserCurrentRestaurantReservationBookingsIntent",
    b"initWithRestaurant:reservationIdentifier:maximumNumberOfResults:earliestBookingDateForResults:",
)
objc.registerNewKeywordsFromSelector(
    "INGetUserCurrentRestaurantReservationBookingsIntentResponse",
    b"initWithUserCurrentBookings:code:userActivity:",
)
objc.registerNewKeywordsFromSelector(
    "INGetVisualCodeIntent", b"initWithVisualCodeType:"
)
objc.registerNewKeywordsFromSelector(
    "INGetVisualCodeIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector("INHangUpCallIntent", b"initWithCallIdentifier:")
objc.registerNewKeywordsFromSelector(
    "INHangUpCallIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INHeadUnit", b"initWithBluetoothIdentifier:iap2Identifier:"
)
objc.registerNewKeywordsFromSelector("INImageNoteContent", b"initWithImage:")
objc.registerNewKeywordsFromSelector("INInteraction", b"initWithIntent:response:")
objc.registerNewKeywordsFromSelector(
    "INListCarsIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INListRideOptionsIntent", b"initWithPickupLocation:dropOffLocation:"
)
objc.registerNewKeywordsFromSelector(
    "INListRideOptionsIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector("INLocationRelevanceProvider", b"initWithRegion:")
objc.registerNewKeywordsFromSelector(
    "INLodgingReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:URL:lodgingBusinessLocation:reservationDuration:numberOfAdults:numberOfChildren:",
)
objc.registerNewKeywordsFromSelector(
    "INLodgingReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:lodgingBusinessLocation:reservationDuration:numberOfAdults:numberOfChildren:",
)
objc.registerNewKeywordsFromSelector(
    "INMediaItem", b"initWithIdentifier:title:type:artwork:"
)
objc.registerNewKeywordsFromSelector(
    "INMediaItem", b"initWithIdentifier:title:type:artwork:artist:"
)
objc.registerNewKeywordsFromSelector(
    "INMediaSearch",
    b"initWithMediaType:sortOrder:mediaName:artistName:albumName:genreNames:moodNames:activityNames:releaseDate:reference:mediaIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "INMediaSearch",
    b"initWithMediaType:sortOrder:mediaName:artistName:albumName:genreNames:moodNames:releaseDate:reference:mediaIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage", b"initWithIdentifier:content:dateSent:sender:recipients:"
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:groupName:messageType:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:groupName:messageType:serviceName:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:groupName:messageType:serviceName:attachmentFiles:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:groupName:messageType:serviceName:audioMessageFile:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:groupName:serviceName:linkMetadata:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:groupName:serviceName:messageType:numberOfAttachments:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:groupName:serviceName:messageType:referencedMessage:reaction:aggregatedReactions:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:groupName:serviceName:messageType:referencedMessage:sticker:reaction:aggregatedReactions:",
)
objc.registerNewKeywordsFromSelector(
    "INMessage",
    b"initWithIdentifier:conversationIdentifier:content:dateSent:sender:recipients:messageType:",
)
objc.registerNewKeywordsFromSelector(
    "INMessageLinkMetadata", b"initWithSiteName:summary:title:openGraphType:linkURL:"
)
objc.registerNewKeywordsFromSelector(
    "INMessageReaction", b"initWithReactionType:reactionDescription:emoji:"
)
objc.registerNewKeywordsFromSelector(
    "INNote",
    b"initWithTitle:contents:groupName:createdDateComponents:modifiedDateComponents:identifier:",
)
objc.registerNewKeywordsFromSelector("INObject", b"initWithIdentifier:displayString:")
objc.registerNewKeywordsFromSelector(
    "INObject", b"initWithIdentifier:displayString:pronunciationHint:"
)
objc.registerNewKeywordsFromSelector(
    "INObject",
    b"initWithIdentifier:displayString:pronunciationHint:subtitleString:displayImage:",
)
objc.registerNewKeywordsFromSelector(
    "INObject", b"initWithIdentifier:displayString:subtitleString:displayImage:"
)
objc.registerNewKeywordsFromSelector("INObjectCollection", b"initWithItems:")
objc.registerNewKeywordsFromSelector("INObjectCollection", b"initWithSections:")
objc.registerNewKeywordsFromSelector("INObjectSection", b"initWithTitle:items:")
objc.registerNewKeywordsFromSelector("INPauseWorkoutIntent", b"initWithWorkoutName:")
objc.registerNewKeywordsFromSelector(
    "INPauseWorkoutIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INPayBillIntent",
    b"initWithBillPayee:fromAccount:transactionAmount:transactionScheduledDate:transactionNote:billType:dueDate:",
)
objc.registerNewKeywordsFromSelector(
    "INPayBillIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INPaymentAccount", b"initWithNickname:number:accountType:organizationName:"
)
objc.registerNewKeywordsFromSelector(
    "INPaymentAccount",
    b"initWithNickname:number:accountType:organizationName:balance:secondaryBalance:",
)
objc.registerNewKeywordsFromSelector("INPaymentAmount", b"initWithAmountType:amount:")
objc.registerNewKeywordsFromSelector(
    "INPaymentMethod", b"initWithType:name:identificationHint:icon:"
)
objc.registerNewKeywordsFromSelector(
    "INPaymentRecord", b"initWithPayee:payer:currencyAmount:paymentMethod:note:status:"
)
objc.registerNewKeywordsFromSelector(
    "INPaymentRecord",
    b"initWithPayee:payer:currencyAmount:paymentMethod:note:status:feeAmount:",
)
objc.registerNewKeywordsFromSelector(
    "INPerson", b"initWithHandle:displayName:contactIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "INPerson", b"initWithHandle:nameComponents:contactIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "INPerson", b"initWithHandle:nameComponents:displayName:image:contactIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "INPerson",
    b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "INPerson",
    b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:aliases:suggestionType:",
)
objc.registerNewKeywordsFromSelector(
    "INPerson",
    b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:isContactSuggestion:suggestionType:",
)
objc.registerNewKeywordsFromSelector(
    "INPerson",
    b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:isMe:",
)
objc.registerNewKeywordsFromSelector(
    "INPerson",
    b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:isMe:suggestionType:",
)
objc.registerNewKeywordsFromSelector(
    "INPerson",
    b"initWithPersonHandle:nameComponents:displayName:image:contactIdentifier:customIdentifier:relationship:",
)
objc.registerNewKeywordsFromSelector("INPersonHandle", b"initWithValue:type:")
objc.registerNewKeywordsFromSelector("INPersonHandle", b"initWithValue:type:label:")
objc.registerNewKeywordsFromSelector(
    "INPlayMediaIntent",
    b"initWithMediaItems:mediaContainer:playShuffled:playbackRepeatMode:resumePlayback:",
)
objc.registerNewKeywordsFromSelector(
    "INPlayMediaIntent",
    b"initWithMediaItems:mediaContainer:playShuffled:playbackRepeatMode:resumePlayback:playbackQueueLocation:playbackSpeed:mediaSearch:",
)
objc.registerNewKeywordsFromSelector(
    "INPlayMediaIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INPlayMediaMediaItemResolutionResult", b"initWithMediaItemResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INPlayMediaPlaybackSpeedResolutionResult", b"initWithDoubleResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INPriceRange", b"initWithMaximumPrice:currencyCode:"
)
objc.registerNewKeywordsFromSelector(
    "INPriceRange", b"initWithMinimumPrice:currencyCode:"
)
objc.registerNewKeywordsFromSelector("INPriceRange", b"initWithPrice:currencyCode:")
objc.registerNewKeywordsFromSelector(
    "INPriceRange", b"initWithRangeBetweenPrice:andPrice:currencyCode:"
)
objc.registerNewKeywordsFromSelector("INRecurrenceRule", b"initWithInterval:frequency:")
objc.registerNewKeywordsFromSelector(
    "INRecurrenceRule", b"initWithInterval:frequency:weeklyRecurrenceDays:"
)
objc.registerNewKeywordsFromSelector("INRelevantShortcut", b"initWithShortcut:")
objc.registerNewKeywordsFromSelector(
    "INRentalCar", b"initWithRentalCompanyName:type:make:model:rentalCarDescription:"
)
objc.registerNewKeywordsFromSelector(
    "INRentalCarReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:URL:rentalCar:rentalDuration:pickupLocation:dropOffLocation:",
)
objc.registerNewKeywordsFromSelector(
    "INRentalCarReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:rentalCar:rentalDuration:pickupLocation:dropOffLocation:",
)
objc.registerNewKeywordsFromSelector(
    "INRequestPaymentCurrencyAmountResolutionResult",
    b"initWithCurrencyAmountResolutionResult:",
)
objc.registerNewKeywordsFromSelector(
    "INRequestPaymentIntent", b"initWithPayer:currencyAmount:note:"
)
objc.registerNewKeywordsFromSelector(
    "INRequestPaymentIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INRequestPaymentPayerResolutionResult", b"initWithPersonResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INRequestRideIntent",
    b"initWithPickupLocation:dropOffLocation:rideOptionName:partySize:paymentMethod:",
)
objc.registerNewKeywordsFromSelector(
    "INRequestRideIntent",
    b"initWithPickupLocation:dropOffLocation:rideOptionName:partySize:paymentMethod:scheduledPickupTime:",
)
objc.registerNewKeywordsFromSelector(
    "INRequestRideIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INReservationAction", b"initWithType:validDuration:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INRestaurant", b"initWithLocation:name:vendorIdentifier:restaurantIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "INRestaurantGuest", b"initWithNameComponents:phoneNumber:emailAddress:"
)
objc.registerNewKeywordsFromSelector(
    "INRestaurantReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:URL:reservationDuration:partySize:restaurantLocation:",
)
objc.registerNewKeywordsFromSelector(
    "INRestaurantReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:reservationDuration:partySize:restaurantLocation:",
)
objc.registerNewKeywordsFromSelector(
    "INRestaurantReservationBooking",
    b"initWithRestaurant:bookingDate:partySize:bookingIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "INRestaurantReservationUserBooking",
    b"initWithRestaurant:bookingDate:partySize:bookingIdentifier:guest:status:dateStatusModified:",
)
objc.registerNewKeywordsFromSelector("INResumeWorkoutIntent", b"initWithWorkoutName:")
objc.registerNewKeywordsFromSelector(
    "INResumeWorkoutIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INRideDriver", b"initWithHandle:displayName:image:rating:phoneNumber:"
)
objc.registerNewKeywordsFromSelector(
    "INRideDriver", b"initWithHandle:nameComponents:image:rating:phoneNumber:"
)
objc.registerNewKeywordsFromSelector(
    "INRideDriver",
    b"initWithPersonHandle:nameComponents:displayName:image:rating:phoneNumber:",
)
objc.registerNewKeywordsFromSelector(
    "INRideDriver", b"initWithPhoneNumber:nameComponents:displayName:image:rating:"
)
objc.registerNewKeywordsFromSelector(
    "INRideFareLineItem", b"initWithTitle:price:currencyCode:"
)
objc.registerNewKeywordsFromSelector("INRideOption", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "INRideOption", b"initWithName:estimatedPickupDate:"
)
objc.registerNewKeywordsFromSelector(
    "INRidePartySizeOption", b"initWithPartySizeRange:sizeDescription:priceRange:"
)
objc.registerNewKeywordsFromSelector(
    "INSaveProfileInCarIntent", b"initWithProfileNumber:profileLabel:"
)
objc.registerNewKeywordsFromSelector(
    "INSaveProfileInCarIntent", b"initWithProfileNumber:profileName:"
)
objc.registerNewKeywordsFromSelector(
    "INSaveProfileInCarIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchCallHistoryIntent",
    b"initWithCallType:dateCreated:recipient:callCapabilities:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchCallHistoryIntent",
    b"initWithDateCreated:recipient:callCapabilities:callTypes:unseen:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchCallHistoryIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchForAccountsIntent",
    b"initWithAccountNickname:accountType:organizationName:requestedBalanceType:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForAccountsIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchForBillsIntent",
    b"initWithBillPayee:paymentDateRange:billType:status:dueDateRange:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForBillsIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchForMediaIntent", b"initWithMediaItems:mediaSearch:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchForMediaIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchForMediaMediaItemResolutionResult", b"initWithMediaItemResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchForMessagesIntent",
    b"initWithRecipients:senders:searchTerms:attributes:dateTimeRange:identifiers:notificationIdentifiers:groupNames:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForMessagesIntent",
    b"initWithRecipients:senders:searchTerms:attributes:dateTimeRange:identifiers:notificationIdentifiers:speakableGroupNames:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForMessagesIntent",
    b"initWithRecipients:senders:searchTerms:attributes:dateTimeRange:identifiers:notificationIdentifiers:speakableGroupNames:conversationIdentifiers:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForMessagesIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchForNotebookItemsIntent",
    b"initWithTitle:content:itemType:status:location:locationSearchType:dateTime:dateSearchType:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForNotebookItemsIntent",
    b"initWithTitle:content:itemType:status:location:locationSearchType:dateTime:dateSearchType:notebookItemIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForNotebookItemsIntent",
    b"initWithTitle:content:itemType:status:location:locationSearchType:dateTime:dateSearchType:temporalEventTriggerTypes:taskPriority:notebookItemIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForNotebookItemsIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSearchForPhotosIntent",
    b"initWithDateCreated:locationCreated:albumName:searchTerms:includedAttributes:excludedAttributes:peopleInPhoto:",
)
objc.registerNewKeywordsFromSelector(
    "INSearchForPhotosIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSeat", b"initWithSeatSection:seatRow:seatNumber:seatingType:"
)
objc.registerNewKeywordsFromSelector(
    "INSendMessageIntent", b"initWithRecipients:content:groupName:serviceName:sender:"
)
objc.registerNewKeywordsFromSelector(
    "INSendMessageIntent",
    b"initWithRecipients:content:speakableGroupName:conversationIdentifier:serviceName:sender:",
)
objc.registerNewKeywordsFromSelector(
    "INSendMessageIntent",
    b"initWithRecipients:outgoingMessageType:content:speakableGroupName:conversationIdentifier:serviceName:sender:",
)
objc.registerNewKeywordsFromSelector(
    "INSendMessageIntent",
    b"initWithRecipients:outgoingMessageType:content:speakableGroupName:conversationIdentifier:serviceName:sender:attachments:",
)
objc.registerNewKeywordsFromSelector(
    "INSendMessageIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSendMessageRecipientResolutionResult", b"initWithPersonResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INSendPaymentCurrencyAmountResolutionResult",
    b"initWithCurrencyAmountResolutionResult:",
)
objc.registerNewKeywordsFromSelector(
    "INSendPaymentIntent", b"initWithPayee:currencyAmount:note:"
)
objc.registerNewKeywordsFromSelector(
    "INSendPaymentIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSendPaymentPayeeResolutionResult", b"initWithPersonResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INSendRideFeedbackIntent", b"initWithRideIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "INSendRideFeedbackIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetAudioSourceInCarIntent", b"initWithAudioSource:relativeAudioSourceReference:"
)
objc.registerNewKeywordsFromSelector(
    "INSetAudioSourceInCarIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetCarLockStatusIntent", b"initWithLocked:carName:"
)
objc.registerNewKeywordsFromSelector(
    "INSetCarLockStatusIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetClimateSettingsInCarIntent",
    b"initWithEnableFan:enableAirConditioner:enableClimateControl:enableAutoMode:airCirculationMode:fanSpeedIndex:fanSpeedPercentage:relativeFanSpeedSetting:temperature:relativeTemperatureSetting:climateZone:",
)
objc.registerNewKeywordsFromSelector(
    "INSetClimateSettingsInCarIntent",
    b"initWithEnableFan:enableAirConditioner:enableClimateControl:enableAutoMode:airCirculationMode:fanSpeedIndex:fanSpeedPercentage:relativeFanSpeedSetting:temperature:relativeTemperatureSetting:climateZone:carName:",
)
objc.registerNewKeywordsFromSelector(
    "INSetClimateSettingsInCarIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetDefrosterSettingsInCarIntent", b"initWithEnable:defroster:"
)
objc.registerNewKeywordsFromSelector(
    "INSetDefrosterSettingsInCarIntent", b"initWithEnable:defroster:carName:"
)
objc.registerNewKeywordsFromSelector(
    "INSetDefrosterSettingsInCarIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetMessageAttributeIntent", b"initWithIdentifiers:attribute:"
)
objc.registerNewKeywordsFromSelector(
    "INSetMessageAttributeIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetProfileInCarIntent", b"initWithProfileNumber:profileLabel:defaultProfile:"
)
objc.registerNewKeywordsFromSelector(
    "INSetProfileInCarIntent", b"initWithProfileNumber:profileName:defaultProfile:"
)
objc.registerNewKeywordsFromSelector(
    "INSetProfileInCarIntent",
    b"initWithProfileNumber:profileName:defaultProfile:carName:",
)
objc.registerNewKeywordsFromSelector(
    "INSetProfileInCarIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetRadioStationIntent",
    b"initWithRadioType:frequency:stationName:channel:presetNumber:",
)
objc.registerNewKeywordsFromSelector(
    "INSetRadioStationIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetSeatSettingsInCarIntent",
    b"initWithEnableHeating:enableCooling:enableMassage:seat:level:relativeLevelSetting:",
)
objc.registerNewKeywordsFromSelector(
    "INSetSeatSettingsInCarIntent",
    b"initWithEnableHeating:enableCooling:enableMassage:seat:level:relativeLevelSetting:carName:",
)
objc.registerNewKeywordsFromSelector(
    "INSetSeatSettingsInCarIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetTaskAttributeIntent",
    b"initWithTargetTask:status:spatialEventTrigger:temporalEventTrigger:",
)
objc.registerNewKeywordsFromSelector(
    "INSetTaskAttributeIntent",
    b"initWithTargetTask:taskTitle:status:priority:spatialEventTrigger:temporalEventTrigger:",
)
objc.registerNewKeywordsFromSelector(
    "INSetTaskAttributeIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSetTaskAttributeTemporalEventTriggerResolutionResult",
    b"initWithTemporalEventTriggerResolutionResult:",
)
objc.registerNewKeywordsFromSelector(
    "INShareFocusStatusIntent", b"initWithFocusStatus:"
)
objc.registerNewKeywordsFromSelector(
    "INShareFocusStatusIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector("INShortcut", b"initWithIntent:")
objc.registerNewKeywordsFromSelector("INShortcut", b"initWithUserActivity:")
objc.registerNewKeywordsFromSelector(
    "INSnoozeTasksIntent", b"initWithTasks:nextTriggerTime:all:"
)
objc.registerNewKeywordsFromSelector(
    "INSnoozeTasksIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INSnoozeTasksTaskResolutionResult", b"initWithTaskResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INSpatialEventTrigger", b"initWithPlacemark:event:"
)
objc.registerNewKeywordsFromSelector(
    "INSpeakableString", b"initWithIdentifier:spokenPhrase:pronunciationHint:"
)
objc.registerNewKeywordsFromSelector("INSpeakableString", b"initWithSpokenPhrase:")
objc.registerNewKeywordsFromSelector(
    "INSpeakableString", b"initWithVocabularyIdentifier:spokenPhrase:pronunciationHint:"
)
objc.registerNewKeywordsFromSelector("INStartAudioCallIntent", b"initWithContacts:")
objc.registerNewKeywordsFromSelector(
    "INStartAudioCallIntent", b"initWithDestinationType:contacts:"
)
objc.registerNewKeywordsFromSelector(
    "INStartAudioCallIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INStartCallCallCapabilityResolutionResult",
    b"initWithCallCapabilityResolutionResult:",
)
objc.registerNewKeywordsFromSelector(
    "INStartCallCallRecordToCallBackResolutionResult",
    b"initWithCallRecordResolutionResult:",
)
objc.registerNewKeywordsFromSelector(
    "INStartCallContactResolutionResult", b"initWithPersonResolutionResult:"
)
objc.registerNewKeywordsFromSelector(
    "INStartCallIntent", b"initWithAudioRoute:destinationType:contacts:callCapability:"
)
objc.registerNewKeywordsFromSelector(
    "INStartCallIntent",
    b"initWithAudioRoute:destinationType:contacts:recordTypeForRedialing:callCapability:",
)
objc.registerNewKeywordsFromSelector(
    "INStartCallIntent",
    b"initWithCallRecordFilter:callRecordToCallBack:audioRoute:destinationType:contacts:callCapability:",
)
objc.registerNewKeywordsFromSelector(
    "INStartCallIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INStartPhotoPlaybackIntent",
    b"initWithDateCreated:locationCreated:albumName:searchTerms:includedAttributes:excludedAttributes:peopleInPhoto:",
)
objc.registerNewKeywordsFromSelector(
    "INStartPhotoPlaybackIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector("INStartVideoCallIntent", b"initWithContacts:")
objc.registerNewKeywordsFromSelector(
    "INStartVideoCallIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INStartWorkoutIntent",
    b"initWithWorkoutName:goalValue:workoutGoalUnitType:workoutLocationType:isOpenEnded:",
)
objc.registerNewKeywordsFromSelector(
    "INStartWorkoutIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector("INSticker", b"initWithType:emoji:")
objc.registerNewKeywordsFromSelector(
    "INTask",
    b"initWithTitle:status:taskType:spatialEventTrigger:temporalEventTrigger:createdDateComponents:modifiedDateComponents:identifier:",
)
objc.registerNewKeywordsFromSelector(
    "INTask",
    b"initWithTitle:status:taskType:spatialEventTrigger:temporalEventTrigger:createdDateComponents:modifiedDateComponents:identifier:priority:",
)
objc.registerNewKeywordsFromSelector(
    "INTaskList",
    b"initWithTitle:tasks:groupName:createdDateComponents:modifiedDateComponents:identifier:",
)
objc.registerNewKeywordsFromSelector(
    "INTemporalEventTrigger", b"initWithDateComponentsRange:"
)
objc.registerNewKeywordsFromSelector(
    "INTermsAndConditions",
    b"initWithLocalizedTermsAndConditionsText:privacyPolicyURL:termsAndConditionsURL:",
)
objc.registerNewKeywordsFromSelector("INTextNoteContent", b"initWithText:")
objc.registerNewKeywordsFromSelector(
    "INTicketedEvent", b"initWithCategory:name:eventDuration:location:"
)
objc.registerNewKeywordsFromSelector(
    "INTicketedEventReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:URL:reservedSeat:event:",
)
objc.registerNewKeywordsFromSelector(
    "INTicketedEventReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:reservedSeat:event:",
)
objc.registerNewKeywordsFromSelector(
    "INTrainReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:URL:reservedSeat:trainTrip:",
)
objc.registerNewKeywordsFromSelector(
    "INTrainReservation",
    b"initWithItemReference:reservationNumber:bookingTime:reservationStatus:reservationHolderName:actions:reservedSeat:trainTrip:",
)
objc.registerNewKeywordsFromSelector(
    "INTrainTrip",
    b"initWithProvider:trainName:trainNumber:tripDuration:departureStationLocation:departurePlatform:arrivalStationLocation:arrivalPlatform:",
)
objc.registerNewKeywordsFromSelector(
    "INTransferMoneyIntent",
    b"initWithFromAccount:toAccount:transactionAmount:transactionScheduledDate:transactionNote:",
)
objc.registerNewKeywordsFromSelector(
    "INTransferMoneyIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INUnsendMessagesIntent", b"initWithMessageIdentifiers:"
)
objc.registerNewKeywordsFromSelector(
    "INUnsendMessagesIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INUpdateMediaAffinityIntent", b"initWithMediaItems:mediaSearch:affinityType:"
)
objc.registerNewKeywordsFromSelector(
    "INUpdateMediaAffinityIntentResponse", b"initWithCode:userActivity:"
)
objc.registerNewKeywordsFromSelector(
    "INUpdateMediaAffinityMediaItemResolutionResult",
    b"initWithMediaItemResolutionResult:",
)
expressions = {}

# END OF FILE
