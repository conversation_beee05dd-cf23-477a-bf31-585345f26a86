# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 12:36:01 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$HKCategoryTypeIdentifierAbdominalCramps$HKCategoryTypeIdentifierAcne$HKCategoryTypeIdentifierAppetiteChanges$HKCategoryTypeIdentifierAppleStandHour$HKCategoryTypeIdentifierAppleWalkingSteadinessEvent$HKCategoryTypeIdentifierAudioExposureEvent$HKCategoryTypeIdentifierBladderIncontinence$HKCategoryTypeIdentifierBleedingAfterPregnancy$HKCategoryTypeIdentifierBleedingDuringPregnancy$HKCategoryTypeIdentifierBloating$HKCategoryTypeIdentifierBreastPain$HKCategoryTypeIdentifierCervicalMucusQuality$HKCategoryTypeIdentifierChestTightnessOrPain$HKCategoryTypeIdentifierChills$HKCategoryTypeIdentifierConstipation$HKCategoryTypeIdentifierContraceptive$HKCategoryTypeIdentifierCoughing$HKCategoryTypeIdentifierDiarrhea$HKCategoryTypeIdentifierDizziness$HKCategoryTypeIdentifierDrySkin$HKCategoryTypeIdentifierEnvironmentalAudioExposureEvent$HKCategoryTypeIdentifierFainting$HKCategoryTypeIdentifierFatigue$HKCategoryTypeIdentifierFever$HKCategoryTypeIdentifierGeneralizedBodyAche$HKCategoryTypeIdentifierHairLoss$HKCategoryTypeIdentifierHandwashingEvent$HKCategoryTypeIdentifierHeadache$HKCategoryTypeIdentifierHeadphoneAudioExposureEvent$HKCategoryTypeIdentifierHeartburn$HKCategoryTypeIdentifierHighHeartRateEvent$HKCategoryTypeIdentifierHotFlashes$HKCategoryTypeIdentifierInfrequentMenstrualCycles$HKCategoryTypeIdentifierIntermenstrualBleeding$HKCategoryTypeIdentifierIrregularHeartRhythmEvent$HKCategoryTypeIdentifierIrregularMenstrualCycles$HKCategoryTypeIdentifierLactation$HKCategoryTypeIdentifierLossOfSmell$HKCategoryTypeIdentifierLossOfTaste$HKCategoryTypeIdentifierLowCardioFitnessEvent$HKCategoryTypeIdentifierLowHeartRateEvent$HKCategoryTypeIdentifierLowerBackPain$HKCategoryTypeIdentifierMemoryLapse$HKCategoryTypeIdentifierMenstrualFlow$HKCategoryTypeIdentifierMindfulSession$HKCategoryTypeIdentifierMoodChanges$HKCategoryTypeIdentifierNausea$HKCategoryTypeIdentifierNightSweats$HKCategoryTypeIdentifierOvulationTestResult$HKCategoryTypeIdentifierPelvicPain$HKCategoryTypeIdentifierPersistentIntermenstrualBleeding$HKCategoryTypeIdentifierPregnancy$HKCategoryTypeIdentifierPregnancyTestResult$HKCategoryTypeIdentifierProgesteroneTestResult$HKCategoryTypeIdentifierProlongedMenstrualPeriods$HKCategoryTypeIdentifierRapidPoundingOrFlutteringHeartbeat$HKCategoryTypeIdentifierRunnyNose$HKCategoryTypeIdentifierSexualActivity$HKCategoryTypeIdentifierShortnessOfBreath$HKCategoryTypeIdentifierSinusCongestion$HKCategoryTypeIdentifierSkippedHeartbeat$HKCategoryTypeIdentifierSleepAnalysis$HKCategoryTypeIdentifierSleepApneaEvent$HKCategoryTypeIdentifierSleepChanges$HKCategoryTypeIdentifierSoreThroat$HKCategoryTypeIdentifierToothbrushingEvent$HKCategoryTypeIdentifierVaginalDryness$HKCategoryTypeIdentifierVomiting$HKCategoryTypeIdentifierWheezing$HKCharacteristicTypeIdentifierActivityMoveMode$HKCharacteristicTypeIdentifierBiologicalSex$HKCharacteristicTypeIdentifierBloodType$HKCharacteristicTypeIdentifierDateOfBirth$HKCharacteristicTypeIdentifierFitzpatrickSkinType$HKCharacteristicTypeIdentifierWheelchairUse$HKClinicalTypeIdentifierAllergyRecord$HKClinicalTypeIdentifierClinicalNoteRecord$HKClinicalTypeIdentifierConditionRecord$HKClinicalTypeIdentifierCoverageRecord$HKClinicalTypeIdentifierImmunizationRecord$HKClinicalTypeIdentifierLabResultRecord$HKClinicalTypeIdentifierMedicationRecord$HKClinicalTypeIdentifierProcedureRecord$HKClinicalTypeIdentifierVitalSignRecord$HKCorrelationTypeIdentifierBloodPressure$HKCorrelationTypeIdentifierFood$HKDataTypeIdentifierHeartbeatSeries$HKDataTypeIdentifierStateOfMind$HKDetailedCDAValidationErrorKey$HKDevicePropertyKeyFirmwareVersion$HKDevicePropertyKeyHardwareVersion$HKDevicePropertyKeyLocalIdentifier$HKDevicePropertyKeyManufacturer$HKDevicePropertyKeyModel$HKDevicePropertyKeyName$HKDevicePropertyKeySoftwareVersion$HKDevicePropertyKeyUDIDeviceIdentifier$HKDocumentTypeIdentifierCDA$HKErrorDomain$HKFHIRReleaseDSTU2$HKFHIRReleaseR4$HKFHIRReleaseUnknown$HKFHIRResourceTypeAllergyIntolerance$HKFHIRResourceTypeCondition$HKFHIRResourceTypeCoverage$HKFHIRResourceTypeDiagnosticReport$HKFHIRResourceTypeDocumentReference$HKFHIRResourceTypeImmunization$HKFHIRResourceTypeMedicationDispense$HKFHIRResourceTypeMedicationOrder$HKFHIRResourceTypeMedicationRequest$HKFHIRResourceTypeMedicationStatement$HKFHIRResourceTypeObservation$HKFHIRResourceTypeProcedure$HKMetadataKeyActivityType$HKMetadataKeyAlgorithmVersion$HKMetadataKeyAlpineSlopeGrade$HKMetadataKeyAppleDeviceCalibrated$HKMetadataKeyAppleECGAlgorithmVersion$HKMetadataKeyAppleFitnessPlusCatalogIdentifier$HKMetadataKeyAppleFitnessPlusSession$HKMetadataKeyAudioExposureDuration$HKMetadataKeyAudioExposureLevel$HKMetadataKeyAverageMETs$HKMetadataKeyAverageSpeed$HKMetadataKeyBarometricPressure$HKMetadataKeyBloodGlucoseMealTime$HKMetadataKeyBodyTemperatureSensorLocation$HKMetadataKeyCoachedWorkout$HKMetadataKeyCrossTrainerDistance$HKMetadataKeyCyclingFunctionalThresholdPowerTestType$HKMetadataKeyDateOfEarliestDataUsedForEstimate$HKMetadataKeyDeviceManufacturerName$HKMetadataKeyDeviceName$HKMetadataKeyDevicePlacementSide$HKMetadataKeyDeviceSerialNumber$HKMetadataKeyDigitalSignature$HKMetadataKeyElevationAscended$HKMetadataKeyElevationDescended$HKMetadataKeyExternalUUID$HKMetadataKeyFitnessMachineDuration$HKMetadataKeyFoodType$HKMetadataKeyGlassesPrescriptionDescription$HKMetadataKeyGroupFitness$HKMetadataKeyHeadphoneGain$HKMetadataKeyHeartRateEventThreshold$HKMetadataKeyHeartRateMotionContext$HKMetadataKeyHeartRateRecoveryActivityDuration$HKMetadataKeyHeartRateRecoveryActivityType$HKMetadataKeyHeartRateRecoveryMaxObservedRecoveryHeartRate$HKMetadataKeyHeartRateRecoveryTestType$HKMetadataKeyHeartRateSensorLocation$HKMetadataKeyIndoorBikeDistance$HKMetadataKeyIndoorWorkout$HKMetadataKeyInsulinDeliveryReason$HKMetadataKeyLapLength$HKMetadataKeyLowCardioFitnessEventThreshold$HKMetadataKeyMaximumLightIntensity$HKMetadataKeyMaximumSpeed$HKMetadataKeyMenstrualCycleStart$HKMetadataKeyPhysicalEffortEstimationType$HKMetadataKeyQuantityClampedToLowerBound$HKMetadataKeyQuantityClampedToUpperBound$HKMetadataKeyReferenceRangeLowerLimit$HKMetadataKeyReferenceRangeUpperLimit$HKMetadataKeySWOLFScore$HKMetadataKeySessionEstimate$HKMetadataKeySexualActivityProtectionUsed$HKMetadataKeySwimmingLocationType$HKMetadataKeySwimmingStrokeStyle$HKMetadataKeySyncIdentifier$HKMetadataKeySyncVersion$HKMetadataKeyTimeZone$HKMetadataKeyUDIDeviceIdentifier$HKMetadataKeyUDIProductionIdentifier$HKMetadataKeyUserMotionContext$HKMetadataKeyVO2MaxTestType$HKMetadataKeyVO2MaxValue$HKMetadataKeyWasTakenInLab$HKMetadataKeyWasUserEntered$HKMetadataKeyWaterSalinity$HKMetadataKeyWeatherCondition$HKMetadataKeyWeatherHumidity$HKMetadataKeyWeatherTemperature$HKMetadataKeyWorkoutBrandName$HKPredicateKeyPathAverage$HKPredicateKeyPathAverageHeartRate$HKPredicateKeyPathCDAAuthorName$HKPredicateKeyPathCDACustodianName$HKPredicateKeyPathCDAPatientName$HKPredicateKeyPathCDATitle$HKPredicateKeyPathCategoryValue$HKPredicateKeyPathClinicalRecordFHIRResourceIdentifier$HKPredicateKeyPathClinicalRecordFHIRResourceType$HKPredicateKeyPathCorrelation$HKPredicateKeyPathCount$HKPredicateKeyPathDateComponents$HKPredicateKeyPathDevice$HKPredicateKeyPathECGClassification$HKPredicateKeyPathECGSymptomsStatus$HKPredicateKeyPathEndDate$HKPredicateKeyPathMax$HKPredicateKeyPathMetadata$HKPredicateKeyPathMin$HKPredicateKeyPathMostRecent$HKPredicateKeyPathMostRecentDuration$HKPredicateKeyPathMostRecentEndDate$HKPredicateKeyPathMostRecentStartDate$HKPredicateKeyPathQuantity$HKPredicateKeyPathSource$HKPredicateKeyPathSourceRevision$HKPredicateKeyPathStartDate$HKPredicateKeyPathSum$HKPredicateKeyPathUUID$HKPredicateKeyPathWorkout$HKPredicateKeyPathWorkoutActivity$HKPredicateKeyPathWorkoutActivityAverageQuantity$HKPredicateKeyPathWorkoutActivityDuration$HKPredicateKeyPathWorkoutActivityEndDate$HKPredicateKeyPathWorkoutActivityMaximumQuantity$HKPredicateKeyPathWorkoutActivityMinimumQuantity$HKPredicateKeyPathWorkoutActivityStartDate$HKPredicateKeyPathWorkoutActivitySumQuantity$HKPredicateKeyPathWorkoutActivityType$HKPredicateKeyPathWorkoutAverageQuantity$HKPredicateKeyPathWorkoutDuration$HKPredicateKeyPathWorkoutEffortRelationship$HKPredicateKeyPathWorkoutMaximumQuantity$HKPredicateKeyPathWorkoutMinimumQuantity$HKPredicateKeyPathWorkoutSumQuantity$HKPredicateKeyPathWorkoutTotalDistance$HKPredicateKeyPathWorkoutTotalEnergyBurned$HKPredicateKeyPathWorkoutTotalFlightsClimbed$HKPredicateKeyPathWorkoutTotalSwimmingStrokeCount$HKPredicateKeyPathWorkoutType$HKQuantityTypeIdentifierActiveEnergyBurned$HKQuantityTypeIdentifierAppleExerciseTime$HKQuantityTypeIdentifierAppleMoveTime$HKQuantityTypeIdentifierAppleSleepingBreathingDisturbances$HKQuantityTypeIdentifierAppleSleepingWristTemperature$HKQuantityTypeIdentifierAppleStandTime$HKQuantityTypeIdentifierAppleWalkingSteadiness$HKQuantityTypeIdentifierAtrialFibrillationBurden$HKQuantityTypeIdentifierBasalBodyTemperature$HKQuantityTypeIdentifierBasalEnergyBurned$HKQuantityTypeIdentifierBloodAlcoholContent$HKQuantityTypeIdentifierBloodGlucose$HKQuantityTypeIdentifierBloodPressureDiastolic$HKQuantityTypeIdentifierBloodPressureSystolic$HKQuantityTypeIdentifierBodyFatPercentage$HKQuantityTypeIdentifierBodyMass$HKQuantityTypeIdentifierBodyMassIndex$HKQuantityTypeIdentifierBodyTemperature$HKQuantityTypeIdentifierCrossCountrySkiingSpeed$HKQuantityTypeIdentifierCyclingCadence$HKQuantityTypeIdentifierCyclingFunctionalThresholdPower$HKQuantityTypeIdentifierCyclingPower$HKQuantityTypeIdentifierCyclingSpeed$HKQuantityTypeIdentifierDietaryBiotin$HKQuantityTypeIdentifierDietaryCaffeine$HKQuantityTypeIdentifierDietaryCalcium$HKQuantityTypeIdentifierDietaryCarbohydrates$HKQuantityTypeIdentifierDietaryChloride$HKQuantityTypeIdentifierDietaryCholesterol$HKQuantityTypeIdentifierDietaryChromium$HKQuantityTypeIdentifierDietaryCopper$HKQuantityTypeIdentifierDietaryEnergyConsumed$HKQuantityTypeIdentifierDietaryFatMonounsaturated$HKQuantityTypeIdentifierDietaryFatPolyunsaturated$HKQuantityTypeIdentifierDietaryFatSaturated$HKQuantityTypeIdentifierDietaryFatTotal$HKQuantityTypeIdentifierDietaryFiber$HKQuantityTypeIdentifierDietaryFolate$HKQuantityTypeIdentifierDietaryIodine$HKQuantityTypeIdentifierDietaryIron$HKQuantityTypeIdentifierDietaryMagnesium$HKQuantityTypeIdentifierDietaryManganese$HKQuantityTypeIdentifierDietaryMolybdenum$HKQuantityTypeIdentifierDietaryNiacin$HKQuantityTypeIdentifierDietaryPantothenicAcid$HKQuantityTypeIdentifierDietaryPhosphorus$HKQuantityTypeIdentifierDietaryPotassium$HKQuantityTypeIdentifierDietaryProtein$HKQuantityTypeIdentifierDietaryRiboflavin$HKQuantityTypeIdentifierDietarySelenium$HKQuantityTypeIdentifierDietarySodium$HKQuantityTypeIdentifierDietarySugar$HKQuantityTypeIdentifierDietaryThiamin$HKQuantityTypeIdentifierDietaryVitaminA$HKQuantityTypeIdentifierDietaryVitaminB12$HKQuantityTypeIdentifierDietaryVitaminB6$HKQuantityTypeIdentifierDietaryVitaminC$HKQuantityTypeIdentifierDietaryVitaminD$HKQuantityTypeIdentifierDietaryVitaminE$HKQuantityTypeIdentifierDietaryVitaminK$HKQuantityTypeIdentifierDietaryWater$HKQuantityTypeIdentifierDietaryZinc$HKQuantityTypeIdentifierDistanceCrossCountrySkiing$HKQuantityTypeIdentifierDistanceCycling$HKQuantityTypeIdentifierDistanceDownhillSnowSports$HKQuantityTypeIdentifierDistancePaddleSports$HKQuantityTypeIdentifierDistanceRowing$HKQuantityTypeIdentifierDistanceSkatingSports$HKQuantityTypeIdentifierDistanceSwimming$HKQuantityTypeIdentifierDistanceWalkingRunning$HKQuantityTypeIdentifierDistanceWheelchair$HKQuantityTypeIdentifierElectrodermalActivity$HKQuantityTypeIdentifierEnvironmentalAudioExposure$HKQuantityTypeIdentifierEnvironmentalSoundReduction$HKQuantityTypeIdentifierEstimatedWorkoutEffortScore$HKQuantityTypeIdentifierFlightsClimbed$HKQuantityTypeIdentifierForcedExpiratoryVolume1$HKQuantityTypeIdentifierForcedVitalCapacity$HKQuantityTypeIdentifierHeadphoneAudioExposure$HKQuantityTypeIdentifierHeartRate$HKQuantityTypeIdentifierHeartRateRecoveryOneMinute$HKQuantityTypeIdentifierHeartRateVariabilitySDNN$HKQuantityTypeIdentifierHeight$HKQuantityTypeIdentifierInhalerUsage$HKQuantityTypeIdentifierInsulinDelivery$HKQuantityTypeIdentifierLeanBodyMass$HKQuantityTypeIdentifierNikeFuel$HKQuantityTypeIdentifierNumberOfAlcoholicBeverages$HKQuantityTypeIdentifierNumberOfTimesFallen$HKQuantityTypeIdentifierOxygenSaturation$HKQuantityTypeIdentifierPaddleSportsSpeed$HKQuantityTypeIdentifierPeakExpiratoryFlowRate$HKQuantityTypeIdentifierPeripheralPerfusionIndex$HKQuantityTypeIdentifierPhysicalEffort$HKQuantityTypeIdentifierPushCount$HKQuantityTypeIdentifierRespiratoryRate$HKQuantityTypeIdentifierRestingHeartRate$HKQuantityTypeIdentifierRowingSpeed$HKQuantityTypeIdentifierRunningGroundContactTime$HKQuantityTypeIdentifierRunningPower$HKQuantityTypeIdentifierRunningSpeed$HKQuantityTypeIdentifierRunningStrideLength$HKQuantityTypeIdentifierRunningVerticalOscillation$HKQuantityTypeIdentifierSixMinuteWalkTestDistance$HKQuantityTypeIdentifierStairAscentSpeed$HKQuantityTypeIdentifierStairDescentSpeed$HKQuantityTypeIdentifierStepCount$HKQuantityTypeIdentifierSwimmingStrokeCount$HKQuantityTypeIdentifierTimeInDaylight$HKQuantityTypeIdentifierUVExposure$HKQuantityTypeIdentifierUnderwaterDepth$HKQuantityTypeIdentifierVO2Max$HKQuantityTypeIdentifierWaistCircumference$HKQuantityTypeIdentifierWalkingAsymmetryPercentage$HKQuantityTypeIdentifierWalkingDoubleSupportPercentage$HKQuantityTypeIdentifierWalkingHeartRateAverage$HKQuantityTypeIdentifierWalkingSpeed$HKQuantityTypeIdentifierWalkingStepLength$HKQuantityTypeIdentifierWaterTemperature$HKQuantityTypeIdentifierWorkoutEffortScore$HKSampleSortIdentifierEndDate$HKSampleSortIdentifierStartDate$HKScoredAssessmentTypeIdentifierGAD7$HKScoredAssessmentTypeIdentifierPHQ9$HKSourceRevisionAnyOperatingSystem@{NSOperatingSystemVersion=qqq}$HKSourceRevisionAnyProductType$HKSourceRevisionAnyVersion$HKUserPreferencesDidChangeNotification$HKVerifiableClinicalRecordCredentialTypeCOVID19$HKVerifiableClinicalRecordCredentialTypeImmunization$HKVerifiableClinicalRecordCredentialTypeLaboratory$HKVerifiableClinicalRecordCredentialTypeRecovery$HKVerifiableClinicalRecordSourceTypeEUDigitalCOVIDCertificate$HKVerifiableClinicalRecordSourceTypeSMARTHealthCard$HKVisionPrescriptionTypeIdentifier$HKWorkoutRouteTypeIdentifier$HKWorkoutSortIdentifierDuration$HKWorkoutSortIdentifierTotalDistance$HKWorkoutSortIdentifierTotalEnergyBurned$HKWorkoutSortIdentifierTotalFlightsClimbed$HKWorkoutSortIdentifierTotalSwimmingStrokeCount$HKWorkoutTypeIdentifier$"""
enums = """$HKActivityMoveModeActiveEnergy@1$HKActivityMoveModeAppleMoveTime@2$HKAnchoredObjectQueryNoAnchor@0$HKAppleECGAlgorithmVersion1@1$HKAppleECGAlgorithmVersion2@2$HKAppleSleepingBreathingDisturbancesClassificationElevated@1$HKAppleSleepingBreathingDisturbancesClassificationNotElevated@0$HKAppleWalkingSteadinessClassificationLow@2$HKAppleWalkingSteadinessClassificationOK@1$HKAppleWalkingSteadinessClassificationVeryLow@3$HKAudiogramConductionTypeAir@0$HKAudiogramSensitivityTestSideLeft@0$HKAudiogramSensitivityTestSideRight@1$HKAuthorizationRequestStatusShouldRequest@1$HKAuthorizationRequestStatusUnknown@0$HKAuthorizationRequestStatusUnnecessary@2$HKAuthorizationStatusNotDetermined@0$HKAuthorizationStatusSharingAuthorized@2$HKAuthorizationStatusSharingDenied@1$HKBiologicalSexFemale@1$HKBiologicalSexMale@2$HKBiologicalSexNotSet@0$HKBiologicalSexOther@3$HKBloodGlucoseMealTimePostprandial@2$HKBloodGlucoseMealTimePreprandial@1$HKBloodTypeABNegative@6$HKBloodTypeABPositive@5$HKBloodTypeANegative@2$HKBloodTypeAPositive@1$HKBloodTypeBNegative@4$HKBloodTypeBPositive@3$HKBloodTypeNotSet@0$HKBloodTypeONegative@8$HKBloodTypeOPositive@7$HKBodyTemperatureSensorLocationArmpit@1$HKBodyTemperatureSensorLocationBody@2$HKBodyTemperatureSensorLocationEar@3$HKBodyTemperatureSensorLocationEarDrum@9$HKBodyTemperatureSensorLocationFinger@4$HKBodyTemperatureSensorLocationForehead@11$HKBodyTemperatureSensorLocationGastroIntestinal@5$HKBodyTemperatureSensorLocationMouth@6$HKBodyTemperatureSensorLocationOther@0$HKBodyTemperatureSensorLocationRectum@7$HKBodyTemperatureSensorLocationTemporalArtery@10$HKBodyTemperatureSensorLocationToe@8$HKCategoryValueAppetiteChangesDecreased@2$HKCategoryValueAppetiteChangesIncreased@3$HKCategoryValueAppetiteChangesNoChange@1$HKCategoryValueAppetiteChangesUnspecified@0$HKCategoryValueAppleStandHourIdle@1$HKCategoryValueAppleStandHourStood@0$HKCategoryValueAppleWalkingSteadinessEventInitialLow@1$HKCategoryValueAppleWalkingSteadinessEventInitialVeryLow@2$HKCategoryValueAppleWalkingSteadinessEventRepeatLow@3$HKCategoryValueAppleWalkingSteadinessEventRepeatVeryLow@4$HKCategoryValueAudioExposureEventLoudEnvironment@1$HKCategoryValueCervicalMucusQualityCreamy@3$HKCategoryValueCervicalMucusQualityDry@1$HKCategoryValueCervicalMucusQualityEggWhite@5$HKCategoryValueCervicalMucusQualitySticky@2$HKCategoryValueCervicalMucusQualityWatery@4$HKCategoryValueContraceptiveImplant@2$HKCategoryValueContraceptiveInjection@3$HKCategoryValueContraceptiveIntrauterineDevice@4$HKCategoryValueContraceptiveIntravaginalRing@5$HKCategoryValueContraceptiveOral@6$HKCategoryValueContraceptivePatch@7$HKCategoryValueContraceptiveUnspecified@1$HKCategoryValueEnvironmentalAudioExposureEventMomentaryLimit@1$HKCategoryValueHeadphoneAudioExposureEventSevenDayLimit@1$HKCategoryValueLowCardioFitnessEventLowFitness@1$HKCategoryValueMenstrualFlowHeavy@4$HKCategoryValueMenstrualFlowLight@2$HKCategoryValueMenstrualFlowMedium@3$HKCategoryValueMenstrualFlowNone@5$HKCategoryValueMenstrualFlowUnspecified@1$HKCategoryValueNotApplicable@0$HKCategoryValueOvulationTestResultEstrogenSurge@4$HKCategoryValueOvulationTestResultIndeterminate@3$HKCategoryValueOvulationTestResultLuteinizingHormoneSurge@2$HKCategoryValueOvulationTestResultNegative@1$HKCategoryValueOvulationTestResultPositive@2$HKCategoryValuePregnancyTestResultIndeterminate@3$HKCategoryValuePregnancyTestResultNegative@1$HKCategoryValuePregnancyTestResultPositive@2$HKCategoryValuePresenceNotPresent@1$HKCategoryValuePresencePresent@0$HKCategoryValueProgesteroneTestResultIndeterminate@3$HKCategoryValueProgesteroneTestResultNegative@1$HKCategoryValueProgesteroneTestResultPositive@2$HKCategoryValueSeverityMild@2$HKCategoryValueSeverityModerate@3$HKCategoryValueSeverityNotPresent@1$HKCategoryValueSeveritySevere@4$HKCategoryValueSeverityUnspecified@0$HKCategoryValueSleepAnalysisAsleep@1$HKCategoryValueSleepAnalysisAsleepCore@3$HKCategoryValueSleepAnalysisAsleepDeep@4$HKCategoryValueSleepAnalysisAsleepREM@5$HKCategoryValueSleepAnalysisAsleepUnspecified@1$HKCategoryValueSleepAnalysisAwake@2$HKCategoryValueSleepAnalysisInBed@0$HKCategoryValueVaginalBleedingHeavy@4$HKCategoryValueVaginalBleedingLight@2$HKCategoryValueVaginalBleedingMedium@3$HKCategoryValueVaginalBleedingNone@5$HKCategoryValueVaginalBleedingUnspecified@1$HKCyclingFunctionalThresholdPowerTestTypeMaxExercise20Minute@2$HKCyclingFunctionalThresholdPowerTestTypeMaxExercise60Minute@1$HKCyclingFunctionalThresholdPowerTestTypePredictionExercise@4$HKCyclingFunctionalThresholdPowerTestTypeRampTest@3$HKDevicePlacementSideCentral@3$HKDevicePlacementSideLeft@1$HKDevicePlacementSideRight@2$HKDevicePlacementSideUnknown@0$HKElectrocardiogramClassificationAtrialFibrillation@2$HKElectrocardiogramClassificationInconclusiveHighHeartRate@4$HKElectrocardiogramClassificationInconclusiveLowHeartRate@3$HKElectrocardiogramClassificationInconclusiveOther@6$HKElectrocardiogramClassificationInconclusivePoorReading@5$HKElectrocardiogramClassificationNotSet@0$HKElectrocardiogramClassificationSinusRhythm@1$HKElectrocardiogramClassificationUnrecognized@100$HKElectrocardiogramLeadAppleWatchSimilarToLeadI@1$HKElectrocardiogramSymptomsStatusNone@1$HKElectrocardiogramSymptomsStatusNotSet@0$HKElectrocardiogramSymptomsStatusPresent@2$HKErrorAnotherWorkoutSessionStarted@8$HKErrorAuthorizationDenied@4$HKErrorAuthorizationNotDetermined@5$HKErrorBackgroundWorkoutSessionNotAllowed@14$HKErrorDataSizeExceeded@13$HKErrorDatabaseInaccessible@6$HKErrorHealthDataRestricted@2$HKErrorHealthDataUnavailable@1$HKErrorInvalidArgument@3$HKErrorNoData@11$HKErrorNotPermissibleForGuestUserMode@15$HKErrorRequiredAuthorizationDenied@10$HKErrorUserCanceled@7$HKErrorUserExitedWorkoutSession@9$HKErrorWorkoutActivityNotAllowed@12$HKFitzpatrickSkinTypeI@1$HKFitzpatrickSkinTypeII@2$HKFitzpatrickSkinTypeIII@3$HKFitzpatrickSkinTypeIV@4$HKFitzpatrickSkinTypeNotSet@0$HKFitzpatrickSkinTypeV@5$HKFitzpatrickSkinTypeVI@6$HKGAD7AssessmentAnswerMoreThanHalfTheDays@2$HKGAD7AssessmentAnswerNearlyEveryDay@3$HKGAD7AssessmentAnswerNotAtAll@0$HKGAD7AssessmentAnswerSeveralDays@1$HKGAD7AssessmentRiskMild@2$HKGAD7AssessmentRiskModerate@3$HKGAD7AssessmentRiskNoneToMinimal@1$HKGAD7AssessmentRiskSevere@4$HKHeartRateMotionContextActive@2$HKHeartRateMotionContextNotSet@0$HKHeartRateMotionContextSedentary@1$HKHeartRateRecoveryTestTypeMaxExercise@1$HKHeartRateRecoveryTestTypePredictionNonExercise@3$HKHeartRateRecoveryTestTypePredictionSubMaxExercise@2$HKHeartRateSensorLocationChest@1$HKHeartRateSensorLocationEarLobe@5$HKHeartRateSensorLocationFinger@3$HKHeartRateSensorLocationFoot@6$HKHeartRateSensorLocationHand@4$HKHeartRateSensorLocationOther@0$HKHeartRateSensorLocationWrist@2$HKInsulinDeliveryReasonBasal@1$HKInsulinDeliveryReasonBolus@2$HKMetricPrefixCenti@5$HKMetricPrefixDeca@7$HKMetricPrefixDeci@6$HKMetricPrefixFemto@13$HKMetricPrefixGiga@11$HKMetricPrefixHecto@8$HKMetricPrefixKilo@9$HKMetricPrefixMega@10$HKMetricPrefixMicro@3$HKMetricPrefixMilli@4$HKMetricPrefixNano@2$HKMetricPrefixNone@0$HKMetricPrefixPico@1$HKMetricPrefixTera@12$HKNoError@0$HKObjectQueryNoLimit@0$HKPHQ9AssessmentAnswerMoreThanHalfTheDays@2$HKPHQ9AssessmentAnswerNearlyEveryDay@3$HKPHQ9AssessmentAnswerNotAtAll@0$HKPHQ9AssessmentAnswerPreferNotToAnswer@4$HKPHQ9AssessmentAnswerSeveralDays@1$HKPHQ9AssessmentRiskMild@2$HKPHQ9AssessmentRiskModerate@3$HKPHQ9AssessmentRiskModeratelySevere@4$HKPHQ9AssessmentRiskNoneToMinimal@1$HKPHQ9AssessmentRiskSevere@5$HKPhysicalEffortEstimationTypeActivityLookup@1$HKPhysicalEffortEstimationTypeDeviceSensed@2$HKPrismBaseDown@2$HKPrismBaseIn@3$HKPrismBaseNone@0$HKPrismBaseOut@4$HKPrismBaseUp@1$HKQuantityAggregationStyleCumulative@0$HKQuantityAggregationStyleDiscrete@1$HKQuantityAggregationStyleDiscreteArithmetic@1$HKQuantityAggregationStyleDiscreteEquivalentContinuousLevel@3$HKQuantityAggregationStyleDiscreteTemporallyWeighted@2$HKQueryOptionNone@0$HKQueryOptionStrictEndDate@2$HKQueryOptionStrictStartDate@1$HKStateOfMindAssociationCommunity@1$HKStateOfMindAssociationCurrentEvents@2$HKStateOfMindAssociationDating@3$HKStateOfMindAssociationEducation@4$HKStateOfMindAssociationFamily@5$HKStateOfMindAssociationFitness@6$HKStateOfMindAssociationFriends@7$HKStateOfMindAssociationHealth@8$HKStateOfMindAssociationHobbies@9$HKStateOfMindAssociationIdentity@10$HKStateOfMindAssociationMoney@11$HKStateOfMindAssociationPartner@12$HKStateOfMindAssociationSelfCare@13$HKStateOfMindAssociationSpirituality@14$HKStateOfMindAssociationTasks@15$HKStateOfMindAssociationTravel@16$HKStateOfMindAssociationWeather@18$HKStateOfMindAssociationWork@17$HKStateOfMindKindDailyMood@2$HKStateOfMindKindMomentaryEmotion@1$HKStateOfMindLabelAmazed@1$HKStateOfMindLabelAmused@2$HKStateOfMindLabelAngry@3$HKStateOfMindLabelAnnoyed@32$HKStateOfMindLabelAnxious@4$HKStateOfMindLabelAshamed@5$HKStateOfMindLabelBrave@6$HKStateOfMindLabelCalm@7$HKStateOfMindLabelConfident@33$HKStateOfMindLabelContent@8$HKStateOfMindLabelDisappointed@9$HKStateOfMindLabelDiscouraged@10$HKStateOfMindLabelDisgusted@11$HKStateOfMindLabelDrained@34$HKStateOfMindLabelEmbarrassed@12$HKStateOfMindLabelExcited@13$HKStateOfMindLabelFrustrated@14$HKStateOfMindLabelGrateful@15$HKStateOfMindLabelGuilty@16$HKStateOfMindLabelHappy@17$HKStateOfMindLabelHopeful@35$HKStateOfMindLabelHopeless@18$HKStateOfMindLabelIndifferent@36$HKStateOfMindLabelIrritated@19$HKStateOfMindLabelJealous@20$HKStateOfMindLabelJoyful@21$HKStateOfMindLabelLonely@22$HKStateOfMindLabelOverwhelmed@37$HKStateOfMindLabelPassionate@23$HKStateOfMindLabelPeaceful@24$HKStateOfMindLabelProud@25$HKStateOfMindLabelRelieved@26$HKStateOfMindLabelSad@27$HKStateOfMindLabelSatisfied@38$HKStateOfMindLabelScared@28$HKStateOfMindLabelStressed@29$HKStateOfMindLabelSurprised@30$HKStateOfMindLabelWorried@31$HKStateOfMindValenceClassificationNeutral@4$HKStateOfMindValenceClassificationPleasant@6$HKStateOfMindValenceClassificationSlightlyPleasant@5$HKStateOfMindValenceClassificationSlightlyUnpleasant@3$HKStateOfMindValenceClassificationUnpleasant@2$HKStateOfMindValenceClassificationVeryPleasant@7$HKStateOfMindValenceClassificationVeryUnpleasant@1$HKStatisticsOptionCumulativeSum@16$HKStatisticsOptionDiscreteAverage@2$HKStatisticsOptionDiscreteMax@8$HKStatisticsOptionDiscreteMin@4$HKStatisticsOptionDiscreteMostRecent@32$HKStatisticsOptionDuration@64$HKStatisticsOptionMostRecent@32$HKStatisticsOptionNone@0$HKStatisticsOptionSeparateBySource@1$HKSwimmingStrokeStyleBackstroke@3$HKSwimmingStrokeStyleBreaststroke@4$HKSwimmingStrokeStyleButterfly@5$HKSwimmingStrokeStyleFreestyle@2$HKSwimmingStrokeStyleKickboard@6$HKSwimmingStrokeStyleMixed@1$HKSwimmingStrokeStyleUnknown@0$HKUnknownError@0$HKUpdateFrequencyDaily@3$HKUpdateFrequencyHourly@2$HKUpdateFrequencyImmediate@1$HKUpdateFrequencyWeekly@4$HKUserMotionContextActive@2$HKUserMotionContextNotSet@0$HKUserMotionContextStationary@1$HKVO2MaxTestTypeMaxExercise@1$HKVO2MaxTestTypePredictionNonExercise@3$HKVO2MaxTestTypePredictionSubMaxExercise@2$HKVisionEyeLeft@1$HKVisionEyeRight@2$HKVisionPrescriptionTypeContacts@2$HKVisionPrescriptionTypeGlasses@1$HKWaterSalinityFreshWater@1$HKWaterSalinitySaltWater@2$HKWeatherConditionBlustery@9$HKWeatherConditionClear@1$HKWeatherConditionCloudy@5$HKWeatherConditionDrizzle@21$HKWeatherConditionDust@11$HKWeatherConditionFair@2$HKWeatherConditionFoggy@6$HKWeatherConditionFreezingDrizzle@15$HKWeatherConditionFreezingRain@16$HKWeatherConditionHail@13$HKWeatherConditionHaze@7$HKWeatherConditionHurricane@26$HKWeatherConditionMixedRainAndHail@17$HKWeatherConditionMixedRainAndSleet@19$HKWeatherConditionMixedRainAndSnow@18$HKWeatherConditionMixedSnowAndSleet@20$HKWeatherConditionMostlyCloudy@4$HKWeatherConditionNone@0$HKWeatherConditionPartlyCloudy@3$HKWeatherConditionScatteredShowers@22$HKWeatherConditionShowers@23$HKWeatherConditionSleet@14$HKWeatherConditionSmoky@10$HKWeatherConditionSnow@12$HKWeatherConditionThunderstorms@24$HKWeatherConditionTornado@27$HKWeatherConditionTropicalStorm@25$HKWeatherConditionWindy@8$HKWheelchairUseNo@1$HKWheelchairUseNotSet@0$HKWheelchairUseYes@2$HKWorkoutActivityTypeAmericanFootball@1$HKWorkoutActivityTypeArchery@2$HKWorkoutActivityTypeAustralianFootball@3$HKWorkoutActivityTypeBadminton@4$HKWorkoutActivityTypeBarre@58$HKWorkoutActivityTypeBaseball@5$HKWorkoutActivityTypeBasketball@6$HKWorkoutActivityTypeBowling@7$HKWorkoutActivityTypeBoxing@8$HKWorkoutActivityTypeCardioDance@77$HKWorkoutActivityTypeClimbing@9$HKWorkoutActivityTypeCooldown@80$HKWorkoutActivityTypeCoreTraining@59$HKWorkoutActivityTypeCricket@10$HKWorkoutActivityTypeCrossCountrySkiing@60$HKWorkoutActivityTypeCrossTraining@11$HKWorkoutActivityTypeCurling@12$HKWorkoutActivityTypeCycling@13$HKWorkoutActivityTypeDance@14$HKWorkoutActivityTypeDanceInspiredTraining@15$HKWorkoutActivityTypeDiscSports@75$HKWorkoutActivityTypeDownhillSkiing@61$HKWorkoutActivityTypeElliptical@16$HKWorkoutActivityTypeEquestrianSports@17$HKWorkoutActivityTypeFencing@18$HKWorkoutActivityTypeFishing@19$HKWorkoutActivityTypeFitnessGaming@76$HKWorkoutActivityTypeFlexibility@62$HKWorkoutActivityTypeFunctionalStrengthTraining@20$HKWorkoutActivityTypeGolf@21$HKWorkoutActivityTypeGymnastics@22$HKWorkoutActivityTypeHandCycling@74$HKWorkoutActivityTypeHandball@23$HKWorkoutActivityTypeHighIntensityIntervalTraining@63$HKWorkoutActivityTypeHiking@24$HKWorkoutActivityTypeHockey@25$HKWorkoutActivityTypeHunting@26$HKWorkoutActivityTypeJumpRope@64$HKWorkoutActivityTypeKickboxing@65$HKWorkoutActivityTypeLacrosse@27$HKWorkoutActivityTypeMartialArts@28$HKWorkoutActivityTypeMindAndBody@29$HKWorkoutActivityTypeMixedCardio@73$HKWorkoutActivityTypeMixedMetabolicCardioTraining@30$HKWorkoutActivityTypeOther@3000$HKWorkoutActivityTypePaddleSports@31$HKWorkoutActivityTypePickleball@79$HKWorkoutActivityTypePilates@66$HKWorkoutActivityTypePlay@32$HKWorkoutActivityTypePreparationAndRecovery@33$HKWorkoutActivityTypeRacquetball@34$HKWorkoutActivityTypeRowing@35$HKWorkoutActivityTypeRugby@36$HKWorkoutActivityTypeRunning@37$HKWorkoutActivityTypeSailing@38$HKWorkoutActivityTypeSkatingSports@39$HKWorkoutActivityTypeSnowSports@40$HKWorkoutActivityTypeSnowboarding@67$HKWorkoutActivityTypeSoccer@41$HKWorkoutActivityTypeSocialDance@78$HKWorkoutActivityTypeSoftball@42$HKWorkoutActivityTypeSquash@43$HKWorkoutActivityTypeStairClimbing@44$HKWorkoutActivityTypeStairs@68$HKWorkoutActivityTypeStepTraining@69$HKWorkoutActivityTypeSurfingSports@45$HKWorkoutActivityTypeSwimBikeRun@82$HKWorkoutActivityTypeSwimming@46$HKWorkoutActivityTypeTableTennis@47$HKWorkoutActivityTypeTaiChi@72$HKWorkoutActivityTypeTennis@48$HKWorkoutActivityTypeTrackAndField@49$HKWorkoutActivityTypeTraditionalStrengthTraining@50$HKWorkoutActivityTypeTransition@83$HKWorkoutActivityTypeUnderwaterDiving@84$HKWorkoutActivityTypeVolleyball@51$HKWorkoutActivityTypeWalking@52$HKWorkoutActivityTypeWaterFitness@53$HKWorkoutActivityTypeWaterPolo@54$HKWorkoutActivityTypeWaterSports@55$HKWorkoutActivityTypeWheelchairRunPace@71$HKWorkoutActivityTypeWheelchairWalkPace@70$HKWorkoutActivityTypeWrestling@56$HKWorkoutActivityTypeYoga@57$HKWorkoutEffortRelationshipQueryOptionsDefault@0$HKWorkoutEffortRelationshipQueryOptionsMostRelevant@1$HKWorkoutEventTypeLap@3$HKWorkoutEventTypeMarker@4$HKWorkoutEventTypeMotionPaused@5$HKWorkoutEventTypeMotionResumed@6$HKWorkoutEventTypePause@1$HKWorkoutEventTypePauseOrResumeRequest@8$HKWorkoutEventTypeResume@2$HKWorkoutEventTypeSegment@7$HKWorkoutSessionLocationTypeIndoor@2$HKWorkoutSessionLocationTypeOutdoor@3$HKWorkoutSessionLocationTypeUnknown@1$HKWorkoutSessionStateEnded@3$HKWorkoutSessionStateNotStarted@1$HKWorkoutSessionStatePaused@4$HKWorkoutSessionStatePrepared@5$HKWorkoutSessionStateRunning@2$HKWorkoutSessionStateStopped@6$HKWorkoutSessionTypeMirrored@1$HKWorkoutSessionTypePrimary@0$HKWorkoutSwimmingLocationTypeOpenWater@2$HKWorkoutSwimmingLocationTypePool@1$HKWorkoutSwimmingLocationTypeUnknown@0$"""
misc.update(
    {
        "HKCategoryValuePresence": NewType("HKCategoryValuePresence", int),
        "HKStatisticsOptions": NewType("HKStatisticsOptions", int),
        "HKWheelchairUse": NewType("HKWheelchairUse", int),
        "HKCategoryValueLowCardioFitnessEvent": NewType(
            "HKCategoryValueLowCardioFitnessEvent", int
        ),
        "HKCategoryValueVaginalBleeding": NewType(
            "HKCategoryValueVaginalBleeding", int
        ),
        "HKCategoryValueAppleWalkingSteadinessEvent": NewType(
            "HKCategoryValueAppleWalkingSteadinessEvent", int
        ),
        "HKInsulinDeliveryReason": NewType("HKInsulinDeliveryReason", int),
        "HKCategoryValueAppetiteChanges": NewType(
            "HKCategoryValueAppetiteChanges", int
        ),
        "HKCategoryValueEnvironmentalAudioExposureEvent": NewType(
            "HKCategoryValueEnvironmentalAudioExposureEvent", int
        ),
        "HKWorkoutSessionState": NewType("HKWorkoutSessionState", int),
        "HKDevicePlacementSide": NewType("HKDevicePlacementSide", int),
        "HKUpdateFrequency": NewType("HKUpdateFrequency", int),
        "HKCategoryValueSleepAnalysis": NewType("HKCategoryValueSleepAnalysis", int),
        "HKPHQ9AssessmentAnswer": NewType("HKPHQ9AssessmentAnswer", int),
        "HKBodyTemperatureSensorLocation": NewType(
            "HKBodyTemperatureSensorLocation", int
        ),
        "HKCategoryValueSeverity": NewType("HKCategoryValueSeverity", int),
        "HKHeartRateRecoveryTestType": NewType("HKHeartRateRecoveryTestType", int),
        "HKStateOfMindAssociation": NewType("HKStateOfMindAssociation", int),
        "HKElectrocardiogramClassification": NewType(
            "HKElectrocardiogramClassification", int
        ),
        "HKCategoryValuePregnancyTestResult": NewType(
            "HKCategoryValuePregnancyTestResult", int
        ),
        "HKElectrocardiogramSymptomsStatus": NewType(
            "HKElectrocardiogramSymptomsStatus", int
        ),
        "HKCategoryValueOvulationTestResult": NewType(
            "HKCategoryValueOvulationTestResult", int
        ),
        "HKWeatherCondition": NewType("HKWeatherCondition", int),
        "HKCategoryValueMenstrualFlow": NewType("HKCategoryValueMenstrualFlow", int),
        "HKAudiogramSensitivityTestSide": NewType(
            "HKAudiogramSensitivityTestSide", int
        ),
        "HKPhysicalEffortEstimationType": NewType(
            "HKPhysicalEffortEstimationType", int
        ),
        "HKUserMotionContext": NewType("HKUserMotionContext", int),
        "HKWorkoutSessionType": NewType("HKWorkoutSessionType", int),
        "HKCategoryValueHeadphoneAudioExposureEvent": NewType(
            "HKCategoryValueHeadphoneAudioExposureEvent", int
        ),
        "HKBloodType": NewType("HKBloodType", int),
        "HKBloodGlucoseMealTime": NewType("HKBloodGlucoseMealTime", int),
        "HKWorkoutSessionLocationType": NewType("HKWorkoutSessionLocationType", int),
        "HKQueryOptions": NewType("HKQueryOptions", int),
        "HKCategoryValueProgesteroneTestResult": NewType(
            "HKCategoryValueProgesteroneTestResult", int
        ),
        "HKWorkoutActivityType": NewType("HKWorkoutActivityType", int),
        "HKStateOfMindKind": NewType("HKStateOfMindKind", int),
        "HKCategoryValueAudioExposureEvent": NewType(
            "HKCategoryValueAudioExposureEvent", int
        ),
        "HKHeartRateMotionContext": NewType("HKHeartRateMotionContext", int),
        "HKAppleECGAlgorithmVersion": NewType("HKAppleECGAlgorithmVersion", int),
        "HKActivityMoveMode": NewType("HKActivityMoveMode", int),
        "HKFitzpatrickSkinType": NewType("HKFitzpatrickSkinType", int),
        "HKSwimmingStrokeStyle": NewType("HKSwimmingStrokeStyle", int),
        "HKAppleWalkingSteadinessClassification": NewType(
            "HKAppleWalkingSteadinessClassification", int
        ),
        "HKHeartRateSensorLocation": NewType("HKHeartRateSensorLocation", int),
        "HKStateOfMindValenceClassification": NewType(
            "HKStateOfMindValenceClassification", int
        ),
        "HKWorkoutEffortRelationshipQueryOptions": NewType(
            "HKWorkoutEffortRelationshipQueryOptions", int
        ),
        "HKCategoryValue": NewType("HKCategoryValue", int),
        "HKGAD7AssessmentAnswer": NewType("HKGAD7AssessmentAnswer", int),
        "HKPrismBase": NewType("HKPrismBase", int),
        "HKMetricPrefix": NewType("HKMetricPrefix", int),
        "HKPHQ9AssessmentRisk": NewType("HKPHQ9AssessmentRisk", int),
        "HKAuthorizationRequestStatus": NewType("HKAuthorizationRequestStatus", int),
        "HKVisionPrescriptionType": NewType("HKVisionPrescriptionType", int),
        "HKCyclingFunctionalThresholdPowerTestType": NewType(
            "HKCyclingFunctionalThresholdPowerTestType", int
        ),
        "HKWorkoutEventType": NewType("HKWorkoutEventType", int),
        "HKErrorCode": NewType("HKErrorCode", int),
        "HKAppleSleepingBreathingDisturbancesClassification": NewType(
            "HKAppleSleepingBreathingDisturbancesClassification", int
        ),
        "HKStateOfMindLabel": NewType("HKStateOfMindLabel", int),
        "HKWaterSalinity": NewType("HKWaterSalinity", int),
        "HKCategoryValueContraceptive": NewType("HKCategoryValueContraceptive", int),
        "HKVisionEye": NewType("HKVisionEye", int),
        "HKBiologicalSex": NewType("HKBiologicalSex", int),
        "HKQuantityAggregationStyle": NewType("HKQuantityAggregationStyle", int),
        "HKCategoryValueAppleStandHour": NewType("HKCategoryValueAppleStandHour", int),
        "HKCategoryValueCervicalMucusQuality": NewType(
            "HKCategoryValueCervicalMucusQuality", int
        ),
        "HKVO2MaxTestType": NewType("HKVO2MaxTestType", int),
        "HKWorkoutSwimmingLocationType": NewType("HKWorkoutSwimmingLocationType", int),
        "HKAuthorizationStatus": NewType("HKAuthorizationStatus", int),
        "HKGAD7AssessmentRisk": NewType("HKGAD7AssessmentRisk", int),
        "HKAudiogramConductionType": NewType("HKAudiogramConductionType", int),
        "HKElectrocardiogramLead": NewType("HKElectrocardiogramLead", int),
    }
)
misc.update(
    {
        "HKDocumentTypeIdentifier": NewType("HKDocumentTypeIdentifier", str),
        "HKScoredAssessmentTypeIdentifier": NewType(
            "HKScoredAssessmentTypeIdentifier", str
        ),
        "HKCorrelationTypeIdentifier": NewType("HKCorrelationTypeIdentifier", str),
        "HKFHIRResourceType": NewType("HKFHIRResourceType", str),
        "HKVerifiableClinicalRecordSourceType": NewType(
            "HKVerifiableClinicalRecordSourceType", str
        ),
        "HKCategoryTypeIdentifier": NewType("HKCategoryTypeIdentifier", str),
        "HKFHIRRelease": NewType("HKFHIRRelease", str),
        "HKCharacteristicTypeIdentifier": NewType(
            "HKCharacteristicTypeIdentifier", str
        ),
        "HKVerifiableClinicalRecordCredentialType": NewType(
            "HKVerifiableClinicalRecordCredentialType", str
        ),
        "HKQuantityTypeIdentifier": NewType("HKQuantityTypeIdentifier", str),
        "HKAudiogramConductionType": NewType("HKAudiogramConductionType", int),
        "HKClinicalTypeIdentifier": NewType("HKClinicalTypeIdentifier", str),
    }
)
misc.update({"HKUnitMolarMassBloodGlucose": 180.15588000005408})
functions = {
    "HKAppleWalkingSteadinessMaximumQuantityForClassification": (b"@q",),
    "HKCategoryValueSleepAnalysisAsleepValues": (b"@",),
    "HKMaximumScoreForPHQ9AssessmentRisk": (b"qq",),
    "HKMinimumScoreForPHQ9AssessmentRisk": (b"qq",),
    "HKAppleSleepingBreathingDisturbancesMinimumQuantityForClassification": (b"@q",),
    "HKStateOfMindValenceClassificationForValence": (b"@d",),
    "HKAppleWalkingSteadinessMinimumQuantityForClassification": (b"@q",),
    "HKMaximumScoreForGAD7AssessmentRisk": (b"qq",),
    "HKAppleSleepingBreathingDisturbancesClassificationForQuantity": (b"@@",),
    "HKAppleWalkingSteadinessClassificationForQuantity": (
        b"Z@^q^@",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "HKMinimumScoreForGAD7AssessmentRisk": (b"qq",),
}
aliases = {
    "HKCategoryValueSleepAnalysisAsleep": "HKCategoryValueSleepAnalysisAsleepUnspecified",
    "HKStatisticsOptionDiscreteMostRecent": "HKStatisticsOptionMostRecent",
    "HKCategoryValueOvulationTestResultPositive": "HKCategoryValueOvulationTestResultLuteinizingHormoneSurge",
    "HKQuantityAggregationStyleDiscrete": "HKQuantityAggregationStyleDiscreteArithmetic",
    "HKNoError": "HKUnknownError",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"HKActivitySummary", b"isPaused", {"retval": {"type": b"Z"}})
    r(b"HKActivitySummary", b"setPaused:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"HKActivitySummaryQuery",
        b"initWithPredicate:resultsHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKActivitySummaryQuery",
        b"setUpdateHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKActivitySummaryQuery",
        b"updateHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"HKAnchoredObjectQuery",
        b"initWithQueryDescriptors:anchor:limit:resultsHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                            5: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAnchoredObjectQuery",
        b"initWithType:predicate:anchor:limit:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Q"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAnchoredObjectQuery",
        b"initWithType:predicate:anchor:limit:resultsHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                            5: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAnchoredObjectQuery",
        b"setUpdateHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                            5: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAnchoredObjectQuery",
        b"updateHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                        4: {"type": b"@"},
                        5: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"HKAttachmentStore",
        b"addAttachmentToObject:name:contentType:URL:metadata:completion:",
        {
            "arguments": {
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAttachmentStore",
        b"getAttachmentsForObject:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAttachmentStore",
        b"getDataForAttachment:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAttachmentStore",
        b"removeAttachment:fromObject:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAttachmentStore",
        b"streamDataForAttachment:dataHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKAudiogramSensitivityPoint",
        b"sensitivityPointWithFrequency:leftEarSensitivity:rightEarSensitivity:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"HKAudiogramSensitivityPoint",
        b"sensitivityPointWithFrequency:tests:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"HKAudiogramSensitivityPointClampingRange",
        b"clampingRangeWithLowerBound:upperBound:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"HKAudiogramSensitivityPointClampingRange",
        b"sensitivityPointWithFrequency:tests:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"HKAudiogramSensitivityTest",
        b"initWithSensitivity:type:masked:side:clampingRange:error:",
        {"arguments": {4: {"type": b"Z"}, 7: {"type_modifier": b"o"}}},
    )
    r(b"HKAudiogramSensitivityTest", b"masked", {"retval": {"type": b"Z"}})
    r(
        b"HKCDADocumentSample",
        b"CDADocumentSampleWithData:startDate:endDate:metadata:validationError:",
        {"arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"HKCorrelationQuery",
        b"initWithType:predicate:samplePredicates:completion:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"HKDocumentQuery", b"includeDocumentData", {"retval": {"type": b"Z"}})
    r(
        b"HKDocumentQuery",
        b"initWithDocumentType:predicate:limit:sortDescriptors:includeDocumentData:resultsHandler:",
        {
            "arguments": {
                6: {"type": b"Z"},
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                            4: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"HKElectrocardiogramQuery",
        b"initWithElectrocardiogram:dataHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKFHIRVersion",
        b"versionFromVersionString:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"HKHealthStore",
        b"activityMoveModeWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"HKHealthStore",
        b"addSamples:toWorkout:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"biologicalSexWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"HKHealthStore",
        b"bloodTypeWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"HKHealthStore",
        b"dateOfBirthComponentsWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"HKHealthStore",
        b"dateOfBirthWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"HKHealthStore",
        b"deleteObject:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"deleteObjects:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"deleteObjectsOfType:predicate:withCompletion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"Q"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"disableAllBackgroundDeliveryWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"disableBackgroundDeliveryForType:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"enableBackgroundDeliveryForType:frequency:withCompletion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"fitzpatrickSkinTypeWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"HKHealthStore",
        b"getRequestStatusForAuthorizationToShareTypes:readTypes:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"handleAuthorizationForExtensionWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"HKHealthStore", b"isHealthDataAvailable", {"retval": {"type": b"Z"}})
    r(
        b"HKHealthStore",
        b"preferredUnitsForQuantityTypes:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"recalibrateEstimatesForSampleType:atDate:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"recoverActiveWorkoutSessionWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"relateWorkoutEffortSample:withWorkout:activity:completion:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"requestAuthorizationToShareTypes:readTypes:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"requestPerObjectReadAuthorizationForType:predicate:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"saveObject:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"saveObjects:withCompletion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"setWorkoutSessionMirroringStartHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"splitTotalEnergy:startDate:endDate:resultsHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"startWatchAppWithWorkoutConfiguration:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"HKHealthStore", b"supportsHealthRecords", {"retval": {"type": b"Z"}})
    r(
        b"HKHealthStore",
        b"unrelateWorkoutEffortSample:fromWorkout:activity:completion:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHealthStore",
        b"wheelchairUseWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"HKHealthStore",
        b"workoutSessionMirroringStartHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"HKHeartbeatSeriesBuilder",
        b"addHeartbeatWithTimeIntervalSinceSeriesStartDate:precededByGap:completion:",
        {
            "arguments": {
                3: {"type": b"Z"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"HKHeartbeatSeriesBuilder",
        b"addMetadata:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHeartbeatSeriesBuilder",
        b"finishSeriesWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKHeartbeatSeriesQuery",
        b"initWithHeartbeatSeries:dataHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"d"},
                            3: {"type": b"Z"},
                            4: {"type": b"Z"},
                            5: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKLiveWorkoutBuilder",
        b"setShouldCollectWorkoutEvents:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"HKLiveWorkoutBuilder",
        b"shouldCollectWorkoutEvents",
        {"retval": {"type": b"Z"}},
    )
    r(b"HKObjectType", b"requiresPerObjectAuthorization", {"retval": {"type": b"Z"}})
    r(
        b"HKObserverQuery",
        b"initWithQueryDescriptors:updateHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {0: {"type": "^v"}},
                                },
                                "type": b"@?",
                            },
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKObserverQuery",
        b"initWithSampleType:predicate:updateHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {0: {"type": "^v"}},
                                },
                                "type": b"@?",
                            },
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"HKQuantity", b"isCompatibleWithUnit:", {"retval": {"type": b"Z"}})
    r(
        b"HKQuantitySeriesSampleBuilder",
        b"finishSeriesWithMetadata:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKQuantitySeriesSampleBuilder",
        b"finishSeriesWithMetadata:endDate:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKQuantitySeriesSampleBuilder",
        b"insertQuantity:date:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"HKQuantitySeriesSampleBuilder",
        b"insertQuantity:dateInterval:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"HKQuantitySeriesSampleQuery", b"includeSample", {"retval": {"type": b"Z"}})
    r(
        b"HKQuantitySeriesSampleQuery",
        b"initWithQuantityType:predicate:quantityHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                            5: {"type": b"Z"},
                            6: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKQuantitySeriesSampleQuery",
        b"initWithSample:quantityHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"Z"},
                            5: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKQuantitySeriesSampleQuery",
        b"orderByQuantitySampleStartDate",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"HKQuantitySeriesSampleQuery",
        b"setIncludeSample:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"HKQuantitySeriesSampleQuery",
        b"setOrderByQuantitySampleStartDate:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"HKQuantityType", b"isCompatibleWithUnit:", {"retval": {"type": b"Z"}})
    r(b"HKSample", b"hasUndeterminedDuration", {"retval": {"type": b"Z"}})
    r(
        b"HKSampleQuery",
        b"initWithQueryDescriptors:limit:resultsHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKSampleQuery",
        b"initWithQueryDescriptors:limit:sortDescriptors:resultsHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKSampleQuery",
        b"initWithSampleType:predicate:limit:sortDescriptors:resultsHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"HKSampleType", b"allowsRecalibrationForEstimates", {"retval": {"type": b"Z"}})
    r(b"HKSampleType", b"isMaximumDurationRestricted", {"retval": {"type": b"Z"}})
    r(b"HKSampleType", b"isMinimumDurationRestricted", {"retval": {"type": b"Z"}})
    r(
        b"HKSourceQuery",
        b"initWithSampleType:samplePredicate:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKSourceRevision",
        b"initWithSource:version:productType:operatingSystemVersion:",
        {"arguments": {5: {"type": b"{NSOperatingSystemVersion=qqq}"}}},
    )
    r(
        b"HKSourceRevision",
        b"operatingSystemVersion",
        {"retval": {"type": b"{NSOperatingSystemVersion=qqq}"}},
    )
    r(
        b"HKStatisticsCollection",
        b"enumerateStatisticsFromDate:toDate:withBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKStatisticsCollectionQuery",
        b"initialResultsHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"HKStatisticsCollectionQuery",
        b"setInitialResultsHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKStatisticsCollectionQuery",
        b"setStatisticsUpdateHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKStatisticsCollectionQuery",
        b"statisticsUpdateHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                        4: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"HKStatisticsQuery",
        b"initWithQuantityType:quantitySamplePredicate:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"HKUnit", b"isNull", {"retval": {"type": b"Z"}})
    r(
        b"HKVerifiableClinicalRecordQuery",
        b"initWithRecordTypes:predicate:resultsHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKVerifiableClinicalRecordQuery",
        b"initWithRecordTypes:sourceTypes:predicate:resultsHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"addMetadata:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"addSamples:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"addWorkoutActivity:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"addWorkoutEvents:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"beginCollectionWithStartDate:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"endCollectionWithEndDate:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"finishWorkoutWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"updateActivityWithUUID:addMedatata:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutBuilder",
        b"updateActivityWithUUID:endDate:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutEffortRelationshipQuery",
        b"initWithPredicate:anchor:options:resultsHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutRouteBuilder",
        b"addMetadata:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutRouteBuilder",
        b"finishRouteWithWorkout:metadata:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutRouteBuilder",
        b"insertRouteData:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutRouteQuery",
        b"initWithRoute:dataHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutRouteQuery",
        b"initWithRoute:dateInterval:dataHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutSession",
        b"initWithConfiguration:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"HKWorkoutSession",
        b"initWithHealthStore:configuration:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"HKWorkoutSession",
        b"sendDataToRemoteWorkoutSession:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutSession",
        b"startMirroringToCompanionDeviceWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"HKWorkoutSession",
        b"stopMirroringToCompanionDeviceWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSObject",
        b"workoutBuilder:didBeginActivity:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"workoutBuilder:didCollectDataOfTypes:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"workoutBuilder:didEndActivity:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"workoutBuilderDidCollectEvent:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"workoutSession:didBeginActivityWithConfiguration:date:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"workoutSession:didChangeToState:fromState:date:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"q"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"workoutSession:didDisconnectFromRemoteDeviceWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"workoutSession:didEndActivityWithConfiguration:date:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"workoutSession:didFailWithError:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"workoutSession:didGenerateEvent:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"workoutSession:didReceiveDataFromRemoteWorkoutSession:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "HKActivitySummaryQuery", b"initWithPredicate:resultsHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKAnchoredObjectQuery", b"initWithQueryDescriptors:anchor:limit:resultsHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKAnchoredObjectQuery", b"initWithType:predicate:anchor:limit:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKAnchoredObjectQuery", b"initWithType:predicate:anchor:limit:resultsHandler:"
)
objc.registerNewKeywordsFromSelector("HKAttachmentStore", b"initWithHealthStore:")
objc.registerNewKeywordsFromSelector(
    "HKAudiogramSensitivityTest",
    b"initWithSensitivity:type:masked:side:clampingRange:error:",
)
objc.registerNewKeywordsFromSelector(
    "HKContactsLensSpecification",
    b"initWithSphere:cylinder:axis:addPower:baseCurve:diameter:",
)
objc.registerNewKeywordsFromSelector(
    "HKCorrelationQuery", b"initWithType:predicate:samplePredicates:completion:"
)
objc.registerNewKeywordsFromSelector(
    "HKDevice",
    b"initWithName:manufacturer:model:hardwareVersion:firmwareVersion:softwareVersion:localIdentifier:UDIDeviceIdentifier:",
)
objc.registerNewKeywordsFromSelector(
    "HKDocumentQuery",
    b"initWithDocumentType:predicate:limit:sortDescriptors:includeDocumentData:resultsHandler:",
)
objc.registerNewKeywordsFromSelector(
    "HKElectrocardiogramQuery", b"initWithElectrocardiogram:dataHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKGlassesLensSpecification",
    b"initWithSphere:cylinder:axis:addPower:vertexDistance:prism:farPupillaryDistance:nearPupillaryDistance:",
)
objc.registerNewKeywordsFromSelector(
    "HKHeartbeatSeriesBuilder", b"initWithHealthStore:device:startDate:"
)
objc.registerNewKeywordsFromSelector(
    "HKHeartbeatSeriesQuery", b"initWithHeartbeatSeries:dataHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKLiveWorkoutBuilder", b"initWithHealthStore:configuration:device:"
)
objc.registerNewKeywordsFromSelector(
    "HKLiveWorkoutDataSource", b"initWithHealthStore:workoutConfiguration:"
)
objc.registerNewKeywordsFromSelector(
    "HKObserverQuery", b"initWithQueryDescriptors:updateHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKObserverQuery", b"initWithSampleType:predicate:updateHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKQuantitySeriesSampleBuilder",
    b"initWithHealthStore:quantityType:startDate:device:",
)
objc.registerNewKeywordsFromSelector(
    "HKQuantitySeriesSampleQuery", b"initWithQuantityType:predicate:quantityHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKQuantitySeriesSampleQuery", b"initWithSample:quantityHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKQueryDescriptor", b"initWithSampleType:predicate:"
)
objc.registerNewKeywordsFromSelector(
    "HKSampleQuery", b"initWithQueryDescriptors:limit:resultsHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKSampleQuery", b"initWithQueryDescriptors:limit:sortDescriptors:resultsHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKSampleQuery",
    b"initWithSampleType:predicate:limit:sortDescriptors:resultsHandler:",
)
objc.registerNewKeywordsFromSelector(
    "HKSourceQuery", b"initWithSampleType:samplePredicate:completionHandler:"
)
objc.registerNewKeywordsFromSelector("HKSourceRevision", b"initWithSource:version:")
objc.registerNewKeywordsFromSelector(
    "HKSourceRevision", b"initWithSource:version:productType:operatingSystemVersion:"
)
objc.registerNewKeywordsFromSelector(
    "HKStatisticsCollectionQuery",
    b"initWithQuantityType:quantitySamplePredicate:options:anchorDate:intervalComponents:",
)
objc.registerNewKeywordsFromSelector(
    "HKStatisticsQuery",
    b"initWithQuantityType:quantitySamplePredicate:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "HKVerifiableClinicalRecordQuery", b"initWithRecordTypes:predicate:resultsHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKVerifiableClinicalRecordQuery",
    b"initWithRecordTypes:sourceTypes:predicate:resultsHandler:",
)
objc.registerNewKeywordsFromSelector("HKVisionPrism", b"initWithAmount:angle:eye:")
objc.registerNewKeywordsFromSelector(
    "HKVisionPrism",
    b"initWithVerticalAmount:verticalBase:horizontalAmount:horizontalBase:eye:",
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutActivity", b"initWithWorkoutConfiguration:startDate:endDate:metadata:"
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutBuilder", b"initWithHealthStore:configuration:device:"
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutEffortRelationshipQuery",
    b"initWithPredicate:anchor:options:resultsHandler:",
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutRouteBuilder", b"initWithHealthStore:device:"
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutRouteQuery", b"initWithRoute:dataHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutRouteQuery", b"initWithRoute:dateInterval:dataHandler:"
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutSession", b"initWithActivityType:locationType:"
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutSession", b"initWithConfiguration:error:"
)
objc.registerNewKeywordsFromSelector(
    "HKWorkoutSession", b"initWithHealthStore:configuration:error:"
)
expressions = {}

# END OF FILE
