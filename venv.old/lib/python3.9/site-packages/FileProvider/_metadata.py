# This file is generated by objective.metadata
#
# Last update: Sat Mar  8 10:37:18 2025
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "NSFileProviderTypeAndCreator": objc.createStructType(
            "FileProvider.NSFileProviderTypeAndCreator",
            b"{NSFileProviderTypeAndCreator=II}",
            ["type", "creator"],
        )
    }
)
constants = """$NSFileProviderDomainDidChange$NSFileProviderErrorCausedByErrorsKey$NSFileProviderErrorCollidingItemKey$NSFileProviderErrorDomain$NSFileProviderErrorItemKey$NSFileProviderErrorNonExistentItemIdentifierKey$NSFileProviderFavoriteRankUnranked@Q$NSFileProviderInitialPageSortedByDate$NSFileProviderInitialPageSortedByName$NSFileProviderMaterializedSetDidChange$NSFileProviderPendingSetDidChange$NSFileProviderRootContainerItemIdentifier$NSFileProviderTrashContainerItemIdentifier$NSFileProviderWorkingSetContainerItemIdentifier$"""
enums = """$NSFileProviderContentPolicyDownloadEagerlyAndKeepDownloaded@3$NSFileProviderContentPolicyDownloadLazily@1$NSFileProviderContentPolicyDownloadLazilyAndEvictOnRemoteUpdate@2$NSFileProviderContentPolicyInherited@0$NSFileProviderCreateItemDeletionConflicted@2$NSFileProviderCreateItemMayAlreadyExist@1$NSFileProviderDeleteItemRecursive@1$NSFileProviderDesktop@1$NSFileProviderDocuments@2$NSFileProviderDomainRemovalModePreserveDirtyUserData@1$NSFileProviderDomainRemovalModePreserveDownloadedUserData@2$NSFileProviderDomainRemovalModeRemoveAll@0$NSFileProviderDomainTestingModeAlwaysEnabled@1$NSFileProviderDomainTestingModeInteractive@2$NSFileProviderErrorApplicationExtensionNotFound@-2014$NSFileProviderErrorCannotSynchronize@-2005$NSFileProviderErrorDeletionRejected@-1006$NSFileProviderErrorDirectoryNotEmpty@-1007$NSFileProviderErrorDomainDisabled@-2011$NSFileProviderErrorExcludedFromSync@-2010$NSFileProviderErrorFilenameCollision@-1001$NSFileProviderErrorInsufficientQuota@-1003$NSFileProviderErrorNewerExtensionVersionFound@-2004$NSFileProviderErrorNoSuchItem@-1005$NSFileProviderErrorNonEvictable@-2008$NSFileProviderErrorNonEvictableChildren@-2006$NSFileProviderErrorNotAuthenticated@-1000$NSFileProviderErrorOlderExtensionVersionRunning@-2003$NSFileProviderErrorPageExpired@-1002$NSFileProviderErrorProviderDomainNotFound@-2013$NSFileProviderErrorProviderDomainTemporarilyUnavailable@-2012$NSFileProviderErrorProviderNotFound@-2001$NSFileProviderErrorProviderTranslocated@-2002$NSFileProviderErrorServerUnreachable@-1004$NSFileProviderErrorSyncAnchorExpired@-1002$NSFileProviderErrorUnsyncedEdits@-2007$NSFileProviderErrorVersionNoLongerAvailable@-2009$NSFileProviderFetchContentsOptionsStrictVersioning@1$NSFileProviderFileSystemHidden@8$NSFileProviderFileSystemPathExtensionHidden@16$NSFileProviderFileSystemUserExecutable@1$NSFileProviderFileSystemUserReadable@2$NSFileProviderFileSystemUserWritable@4$NSFileProviderItemCapabilitiesAllowsAddingSubItems@2$NSFileProviderItemCapabilitiesAllowsAll@63$NSFileProviderItemCapabilitiesAllowsContentEnumerating@1$NSFileProviderItemCapabilitiesAllowsDeleting@32$NSFileProviderItemCapabilitiesAllowsEvicting@64$NSFileProviderItemCapabilitiesAllowsExcludingFromSync@128$NSFileProviderItemCapabilitiesAllowsReading@1$NSFileProviderItemCapabilitiesAllowsRenaming@8$NSFileProviderItemCapabilitiesAllowsReparenting@4$NSFileProviderItemCapabilitiesAllowsTrashing@16$NSFileProviderItemCapabilitiesAllowsWriting@2$NSFileProviderItemContentModificationDate@128$NSFileProviderItemContents@1$NSFileProviderItemCreationDate@64$NSFileProviderItemExtendedAttributes@512$NSFileProviderItemFavoriteRank@32$NSFileProviderItemFileSystemFlags@256$NSFileProviderItemFilename@2$NSFileProviderItemLastUsedDate@8$NSFileProviderItemParentItemIdentifier@4$NSFileProviderItemTagData@16$NSFileProviderItemTypeAndCreator@1024$NSFileProviderManagerDisconnectionOptionsTemporary@1$NSFileProviderMaterializationFlagsKnownSparseRanges@1$NSFileProviderModifyItemMayAlreadyExist@1$NSFileProviderTestingOperationSideDisk@0$NSFileProviderTestingOperationSideFileProvider@1$NSFileProviderTestingOperationTypeChildrenEnumeration@6$NSFileProviderTestingOperationTypeCollisionResolution@7$NSFileProviderTestingOperationTypeContentFetch@5$NSFileProviderTestingOperationTypeCreation@2$NSFileProviderTestingOperationTypeDeletion@4$NSFileProviderTestingOperationTypeIngestion@0$NSFileProviderTestingOperationTypeLookup@1$NSFileProviderTestingOperationTypeModification@3$NSFileProviderVolumeUnsupportedReasonNetwork@16$NSFileProviderVolumeUnsupportedReasonNonAPFS@2$NSFileProviderVolumeUnsupportedReasonNonEncrypted@4$NSFileProviderVolumeUnsupportedReasonNone@0$NSFileProviderVolumeUnsupportedReasonQuarantined@32$NSFileProviderVolumeUnsupportedReasonReadOnly@8$NSFileProviderVolumeUnsupportedReasonUnknown@1$"""
misc.update(
    {
        "NSFileProviderManagerDisconnectionOptions": NewType(
            "NSFileProviderManagerDisconnectionOptions", int
        ),
        "NSFileProviderFileSystemFlags": NewType("NSFileProviderFileSystemFlags", int),
        "NSFileProviderMaterializationFlags": NewType(
            "NSFileProviderMaterializationFlags", int
        ),
        "NSFileProviderCreateItemOptions": NewType(
            "NSFileProviderCreateItemOptions", int
        ),
        "NSFileProviderKnownFolders": NewType("NSFileProviderKnownFolders", int),
        "NSFileProviderModifyItemOptions": NewType(
            "NSFileProviderModifyItemOptions", int
        ),
        "NSFileProviderErrorCode": NewType("NSFileProviderErrorCode", int),
        "NSFileProviderDomainTestingModes": NewType(
            "NSFileProviderDomainTestingModes", int
        ),
        "NSFileProviderDomainRemovalMode": NewType(
            "NSFileProviderDomainRemovalMode", int
        ),
        "NSFileProviderVolumeUnsupportedReason": NewType(
            "NSFileProviderVolumeUnsupportedReason", int
        ),
        "NSFileProviderDeleteItemOptions": NewType(
            "NSFileProviderDeleteItemOptions", int
        ),
        "NSFileProviderItemFields": NewType("NSFileProviderItemFields", int),
        "NSFileProviderTestingOperationType": NewType(
            "NSFileProviderTestingOperationType", int
        ),
        "NSFileProviderTestingOperationSide": NewType(
            "NSFileProviderTestingOperationSide", int
        ),
        "NSFileProviderFetchContentsOptions": NewType(
            "NSFileProviderFetchContentsOptions", int
        ),
        "NSFileProviderContentPolicy": NewType("NSFileProviderContentPolicy", int),
        "NSFileProviderItemCapabilities": NewType(
            "NSFileProviderItemCapabilities", int
        ),
    }
)
misc.update(
    {
        "NSFileProviderSyncAnchor": NewType(
            "NSFileProviderSyncAnchor", objc.lookUpClass("NSData")
        ),
        "NSFileProviderItemDecorationIdentifier": NewType(
            "NSFileProviderItemDecorationIdentifier", str
        ),
        "NSFileProviderPage": NewType("NSFileProviderPage", objc.lookUpClass("NSData")),
    }
)
misc.update({})
aliases = {
    "NSFileProviderItemCapabilitiesAllowsContentEnumerating": "NSFileProviderItemCapabilitiesAllowsReading",
    "NSFileProviderItemCapabilitiesAllowsAddingSubItems": "NSFileProviderItemCapabilitiesAllowsWriting",
    "NSFileProviderErrorPageExpired": "NSFileProviderErrorSyncAnchorExpired",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"NSFileProviderDomain", b"isDisconnected", {"retval": {"type": b"Z"}})
    r(b"NSFileProviderDomain", b"isHidden", {"retval": {"type": b"Z"}})
    r(b"NSFileProviderDomain", b"isReplicated", {"retval": {"type": b"Z"}})
    r(b"NSFileProviderDomain", b"setHidden:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSFileProviderDomain",
        b"setSupportsSyncingTrash:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSFileProviderDomain", b"supportsSyncingTrash", {"retval": {"type": b"Z"}})
    r(b"NSFileProviderDomain", b"userEnabled", {"retval": {"type": b"Z"}})
    r(
        b"NSFileProviderExtension",
        b"createDirectoryWithName:inParentItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"createItemBasedOnTemplate:fields:contents:options:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"deleteItemWithIdentifier:baseVersion:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"deleteItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"enumeratorForContainerItemIdentifier:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderExtension",
        b"enumeratorForSearchQuery:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderExtension",
        b"fetchContentsForItemWithIdentifier:version:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"fetchContentsForItemWithIdentifier:version:usingExistingContentsAtURL:existingVersion:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"fetchThumbnailsForItemIdentifiers:requestedSize:perThumbnailCompletionHandler:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                },
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"importDidFinishWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"importDocumentAtURL:toParentItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"itemChanged:baseVersion:changedFields:contents:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"itemForIdentifier:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderExtension",
        b"materializedItemsDidChangeWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"performActionWithIdentifier:onItemsWithIdentifiers:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"providePlaceholderAtURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"renameItemWithIdentifier:toName:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"reparentItemWithIdentifier:toParentItemWithIdentifier:newName:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"setFavoriteRank:forItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"setLastUsedDate:forItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"setTagData:forItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"startProvidingItemAtURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"supportedServiceSourcesForItemIdentifier:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderExtension",
        b"trashItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"untrashItemWithIdentifier:toParentItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderExtension",
        b"writePlaceholderAtURL:withMetadata:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderKnownFolderLocations",
        b"setShouldCreateBinaryCompatibilitySymlink:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSFileProviderKnownFolderLocations",
        b"shouldCreateBinaryCompatibilitySymlink",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSFileProviderManager",
        b"addDomain:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"checkDomainsCanBeStored:onVolumeAtURL:unsupportedReason:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": "^Z", "type_modifier": b"o"},
                4: {"type_modifier": b"o"},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSFileProviderManager",
        b"claimKnownFolders:localizedReason:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"disconnectWithReason:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"evictItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"getDomainsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"getIdentifierForUserVisibleFileAtURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"getServiceWithName:itemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"getUserVisibleURLForItemIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"importDomain:fromDirectoryAtURL:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"listAvailableTestingOperationsWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderManager",
        b"lookupRequestingApplicationIdentifier:reason:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"reconnectWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"registerURLSessionTask:forItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"reimportItemsBelowItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"releaseKnownFolders:localizedReason:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"removeAllDomainsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"removeDomain:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"removeDomain:mode:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"requestDiagnosticCollectionForItemWithIdentifier:errorReason:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"requestDownloadForItemWithIdentifier:requestedRange:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"requestModificationOfFields:forItemWithIdentifier:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"runTestingOperations:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderManager",
        b"setDownloadPolicy:forItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"signalEnumeratorForContainerItemIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"signalErrorResolved:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"stateDirectoryURLWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderManager",
        b"temporaryDirectoryURLWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileProviderManager",
        b"waitForChangesOnItemsBelowItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"waitForStabilizationWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderManager",
        b"writePlaceholderAtURL:withMetadata:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSFileProviderRequest", b"isFileViewerRequest", {"retval": {"type": "Z"}})
    r(b"NSFileProviderRequest", b"isSystemRequest", {"retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"asChildrenEnumeration",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"asCollisionResolution",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"asContentFetch", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"asCreation", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"asDeletion", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"asIngestion", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"asLookup", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"asModification", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"capabilities", {"required": False, "retval": {"type": b"Q"}})
    r(b"NSObject", b"changedFields", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"childItemCount", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"contentModificationDate",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"contentPolicy", {"required": False, "retval": {"type": b"q"}})
    r(b"NSObject", b"contentType", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"createItemBasedOnTemplate:fields:contents:options:request:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                            4: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"Z"},
                            4: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"creationDate", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"currentSyncAnchorWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(b"NSObject", b"decorations", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"deleteItemWithIdentifier:baseVersion:options:request:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"didDeleteItemsWithIdentifiers:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"didEnumerateItems:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"didUpdateItems:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"documentSize", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"domainVersion", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"downloadingError", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"enumerateChangesForObserver:fromSyncAnchor:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"enumerateItemsForObserver:startingAtPage:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"enumeratorForContainerItemIdentifier:request:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(b"NSObject", b"extendedAttributes", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"favoriteRank", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"fetchContentsForItemWithIdentifier:version:request:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"fetchContentsForItemWithIdentifier:version:usingExistingContentsAtURL:existingVersion:request:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"fetchContentsForItemWithIdentifier:version:usingExistingContentsAtURL:request:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"fetchPartialContentsForItemWithIdentifier:version:request:minimalRange:aligningTo:options:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"{_NSRange=QQ}"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
                8: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"{_NSRange=QQ}"},
                            4: {"type": b"Q"},
                            5: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"fetchThumbnailsForItemIdentifiers:requestedSize:perThumbnailCompletionHandler:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{CGSize=dd}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"fileSystemFlags", {"required": False, "retval": {"type": "Q"}})
    r(b"NSObject", b"filename", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"finishEnumeratingChangesUpToSyncAnchor:moreComing:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"finishEnumeratingUpToPage:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"finishEnumeratingWithError:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"getKnownFolderLocations:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"importDidFinishWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"initWithDomain:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"invalidate", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"isDownloaded", {"required": False, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isDownloading", {"required": False, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isExcludedFromSync", {"retval": {"type": "Z"}})
    r(b"NSObject", b"isHidden", {"retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"isInteractionSuppressedForIdentifier:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isMaximumSizeReached",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"isMostRecentVersionDownloaded",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(b"NSObject", b"isPathExtensionHidden", {"retval": {"type": "Z"}})
    r(b"NSObject", b"isRestricted", {"required": False, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isShared", {"required": False, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"isSharedByCurrentUser",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(b"NSObject", b"isTrashed", {"required": False, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isUploaded", {"required": False, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isUploading", {"required": False, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isUserReadable", {"retval": {"type": "Z"}})
    r(b"NSObject", b"isUserWritable", {"retval": {"type": "Z"}})
    r(b"NSObject", b"item", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"itemForIdentifier:request:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(b"NSObject", b"itemIdentifier", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"itemVersion", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"lastUsedDate", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"makeListenerEndpointAndReturnError:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"materializedItemsDidChangeWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"modifyItem:baseVersion:changedFields:contents:options:request:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"@"},
                8: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"Z"},
                            4: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"mostRecentEditorNameComponents",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"ownerNameComponents",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"parentItemIdentifier",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"pendingItemsDidChangeWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"performActionWithIdentifier:onItemsWithIdentifiers:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"refreshInterval", {"required": True, "retval": {"type": "d"}})
    r(b"NSObject", b"renamedItem", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"serviceName", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"setInteractionSuppressed:forIdentifier:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Z"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"shouldConnectExternalDomainWithCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(b"NSObject", b"side", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"sourceItem", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"sourceItemIdentifier",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"suggestedBatchSize", {"required": False, "retval": {"type": "Q"}})
    r(b"NSObject", b"suggestedPageSize", {"required": False, "retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"supportedServiceSourcesForItemIdentifier:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"symlinkTargetPath", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"tagData", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"targetItemBaseVersion",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"targetItemIdentifier",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"targetSide", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"type", {"required": True, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"typeAndCreator",
        {"required": False, "retval": {"type": b"{NSFileProviderTypeAndCreator=II}"}},
    )
    r(b"NSObject", b"typeIdentifier", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"uploadingError", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"userInfo", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"versionIdentifier", {"required": False, "retval": {"type": b"@"}})
    r(
        b"null",
        b"createDirectoryWithName:inParentItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"deleteItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"enumeratorForContainerItemIdentifier:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"null",
        b"fetchThumbnailsForItemIdentifiers:requestedSize:perThumbnailCompletionHandler:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                },
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"null",
        b"importDocumentAtURL:toParentItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"renameItemWithIdentifier:toName:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"reparentItemWithIdentifier:toParentItemWithIdentifier:newName:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"setFavoriteRank:forItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"setLastUsedDate:forItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"setTagData:forItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"supportedServiceSourcesForItemIdentifier:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"null",
        b"trashItemWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"untrashItemWithIdentifier:toParentItemIdentifier:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"writePlaceholderAtURL:withMetadata:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "NSFileProviderDomain", b"initWithDisplayName:userInfo:volumeURL:"
)
objc.registerNewKeywordsFromSelector(
    "NSFileProviderDomain", b"initWithIdentifier:displayName:"
)
objc.registerNewKeywordsFromSelector(
    "NSFileProviderDomain",
    b"initWithIdentifier:displayName:pathRelativeToDocumentStorage:",
)
objc.registerNewKeywordsFromSelector(
    "NSFileProviderItemVersion", b"initWithContentVersion:metadataVersion:"
)
objc.registerNewKeywordsFromSelector(
    "NSFileProviderKnownFolderLocation", b"initWithExistingItemIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "NSFileProviderKnownFolderLocation", b"initWithParentItemIdentifier:filename:"
)
expressions = {}

# END OF FILE
