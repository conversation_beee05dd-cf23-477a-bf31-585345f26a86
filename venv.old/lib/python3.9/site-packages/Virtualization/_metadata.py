# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:21:52 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$VZErrorDomain$"""
enums = """$VZDiskImageCachingModeAutomatic@0$VZDiskImageCachingModeCached@2$VZDiskImageCachingModeUncached@1$VZDiskImageSynchronizationModeFsync@2$VZDiskImageSynchronizationModeFull@1$VZDiskImageSynchronizationModeNone@3$VZDiskSynchronizationModeFull@0$VZDiskSynchronizationModeNone@1$VZEFIVariableStoreInitializationOptionAllowOverwrite@1$VZErrorDeviceAlreadyAttached@30002$VZErrorDeviceInitializationFailure@30003$VZErrorDeviceNotFound@30004$VZErrorInstallationFailed@10007$VZErrorInstallationRequiresUpdate@10006$VZErrorInternal@1$VZErrorInvalidDiskImage@5$VZErrorInvalidRestoreImage@10005$VZErrorInvalidRestoreImageCatalog@10002$VZErrorInvalidVirtualMachineConfiguration@2$VZErrorInvalidVirtualMachineState@3$VZErrorInvalidVirtualMachineStateTransition@4$VZErrorNetworkBlockDeviceDisconnected@20002$VZErrorNetworkBlockDeviceNegotiationFailed@20001$VZErrorNetworkError@7$VZErrorNoSupportedRestoreImagesInCatalog@10003$VZErrorNotSupported@10$VZErrorOperationCancelled@9$VZErrorOutOfDiskSpace@8$VZErrorRestore@12$VZErrorRestoreImageCatalogLoadFailed@10001$VZErrorRestoreImageLoadFailed@10004$VZErrorSave@11$VZErrorUSBControllerNotFound@30001$VZErrorVirtualMachineLimitExceeded@6$VZLinuxRosettaAvailabilityInstalled@2$VZLinuxRosettaAvailabilityNotInstalled@1$VZLinuxRosettaAvailabilityNotSupported@0$VZMacAuxiliaryStorageInitializationOptionAllowOverwrite@1$VZVirtualMachineStateError@3$VZVirtualMachineStatePaused@2$VZVirtualMachineStatePausing@5$VZVirtualMachineStateRestoring@9$VZVirtualMachineStateResuming@6$VZVirtualMachineStateRunning@1$VZVirtualMachineStateSaving@8$VZVirtualMachineStateStarting@4$VZVirtualMachineStateStopped@0$VZVirtualMachineStateStopping@7$"""
misc.update(
    {
        "VZVirtualMachineState": NewType("VZVirtualMachineState", int),
        "VZDiskImageCachingMode": NewType("VZDiskImageCachingMode", int),
        "VZLinuxRosettaAvailability": NewType("VZLinuxRosettaAvailability", int),
        "VZDiskImageSynchronizationMode": NewType(
            "VZDiskImageSynchronizationMode", int
        ),
        "VZErrorCode": NewType("VZErrorCode", int),
        "VZMacAuxiliaryStorageInitializationOptions": NewType(
            "VZMacAuxiliaryStorageInitializationOptions", int
        ),
        "VZDiskSynchronizationMode": NewType("VZDiskSynchronizationMode", int),
        "VZEFIVariableStoreInitializationOptions": NewType(
            "VZEFIVariableStoreInitializationOptions", int
        ),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"NSObject",
        b"attachment:didEncounterError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"attachmentWasConnected:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"consoleDevice:didClosePort:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"consoleDevice:didOpenPort:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"displayDidBeginReconfiguration:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"displayDidEndReconfiguration:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"guestDidStopVirtualMachine:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"listener:shouldAcceptNewConnection:fromSocketDevice:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"setUuid:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"usbController", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"uuid", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"virtualMachine:didStopWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"virtualMachine:networkDevice:attachmentWasDisconnectedWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"VZDiskBlockDeviceStorageDeviceAttachment",
        b"initWithFileHandle:readOnly:synchronizationMode:error:",
        {"arguments": {3: {"type": b"Z"}, 5: {"type_modifier": b"o"}}},
    )
    r(
        b"VZDiskBlockDeviceStorageDeviceAttachment",
        b"isReadOnly",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VZDiskImageStorageDeviceAttachment",
        b"initWithURL:readOnly:cachingMode:synchronizationMode:error:",
        {"arguments": {3: {"type": b"Z"}, 6: {"type_modifier": b"o"}}},
    )
    r(
        b"VZDiskImageStorageDeviceAttachment",
        b"initWithURL:readOnly:error:",
        {"arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}}},
    )
    r(b"VZDiskImageStorageDeviceAttachment", b"isReadOnly", {"retval": {"type": "Z"}})
    r(
        b"VZEFIVariableStore",
        b"initCreatingVariableStoreAtURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"VZFileSerialPortAttachment", b"append", {"retval": {"type": b"Z"}})
    r(
        b"VZFileSerialPortAttachment",
        b"initWithURL:append:error:",
        {"arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}}},
    )
    r(
        b"VZGenericPlatformConfiguration",
        b"isNestedVirtualizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VZGenericPlatformConfiguration",
        b"isNestedVirtualizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VZGenericPlatformConfiguration",
        b"setNestedVirtualizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VZGraphicsDisplay",
        b"reconfigureWithConfiguration:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VZGraphicsDisplay",
        b"reconfigureWithSizeInPixels:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VZLinuxRosettaAbstractSocketCachingOptions",
        b"initWithName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VZLinuxRosettaDirectoryShare",
        b"initWithError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VZLinuxRosettaDirectoryShare",
        b"installRosettaWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZLinuxRosettaUnixSocketCachingOptions",
        b"initWithPath:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"VZMACAddress", b"isBroadcastAddress", {"retval": {"type": b"Z"}})
    r(b"VZMACAddress", b"isLocallyAdministeredAddress", {"retval": {"type": b"Z"}})
    r(b"VZMACAddress", b"isMulticastAddress", {"retval": {"type": b"Z"}})
    r(b"VZMACAddress", b"isUnicastAddress", {"retval": {"type": b"Z"}})
    r(b"VZMACAddress", b"isUniversallyAdministeredAddress", {"retval": {"type": b"Z"}})
    r(
        b"VZMacAuxiliaryStorage",
        b"initCreatingStorageAtURL:hardwareModel:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(b"VZMacHardwareModel", b"isSupported", {"retval": {"type": b"Z"}})
    r(
        b"VZMacOSInstaller",
        b"installWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZMacOSRestoreImage",
        b"fetchLatestSupportedWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"VZMacOSRestoreImage", b"isSupported", {"retval": {"type": b"Z"}})
    r(
        b"VZMacOSRestoreImage",
        b"loadFileURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VZMacOSRestoreImage",
        b"operatingSystemVersion",
        {"retval": {"type": b"{_NSOperatingSystemVersion=qqq}"}},
    )
    r(
        b"VZMacOSVirtualMachineStartOptions",
        b"setStartUpFromMacOSRecovery:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VZMacOSVirtualMachineStartOptions",
        b"startUpFromMacOSRecovery",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VZMultipleDirectoryShare",
        b"validateName:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VZNetworkBlockDeviceStorageDeviceAttachment",
        b"initWithURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VZNetworkBlockDeviceStorageDeviceAttachment",
        b"initWithURL:timeout:forcedReadOnly:synchronizationMode:error:",
        {"arguments": {4: {"type": b"Z"}, 6: {"type_modifier": b"o"}}},
    )
    r(
        b"VZNetworkBlockDeviceStorageDeviceAttachment",
        b"isForcedReadOnly",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VZNetworkBlockDeviceStorageDeviceAttachment",
        b"validateURL:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VZSharedDirectory",
        b"initWithURL:readOnly:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"VZSharedDirectory", b"isReadOnly", {"retval": {"type": b"Z"}})
    r(
        b"VZSpiceAgentPortAttachment",
        b"setSharesClipboard:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"VZSpiceAgentPortAttachment", b"sharesClipboard", {"retval": {"type": b"Z"}})
    r(
        b"VZUSBController",
        b"attachDevice:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZUSBController",
        b"detachDevice:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZVirtioBlockDeviceConfiguration",
        b"validateBlockDeviceIdentifier:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"VZVirtioConsolePortConfiguration", b"isConsole", {"retval": {"type": b"Z"}})
    r(
        b"VZVirtioConsolePortConfiguration",
        b"setIsConsole:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VZVirtioFileSystemDeviceConfiguration",
        b"validateTag:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VZVirtioSocketDevice",
        b"connectToPort:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"VZVirtualMachine", b"canPause", {"retval": {"type": b"Z"}})
    r(b"VZVirtualMachine", b"canRequestStop", {"retval": {"type": b"Z"}})
    r(b"VZVirtualMachine", b"canResume", {"retval": {"type": b"Z"}})
    r(b"VZVirtualMachine", b"canStart", {"retval": {"type": b"Z"}})
    r(b"VZVirtualMachine", b"canStop", {"retval": {"type": b"Z"}})
    r(b"VZVirtualMachine", b"isSupported", {"retval": {"type": b"Z"}})
    r(
        b"VZVirtualMachine",
        b"pauseWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZVirtualMachine",
        b"requestStopWithError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VZVirtualMachine",
        b"restoreMachineStateFromURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZVirtualMachine",
        b"resumeWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZVirtualMachine",
        b"saveMachineStateToURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZVirtualMachine",
        b"startWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZVirtualMachine",
        b"startWithOptions:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZVirtualMachine",
        b"stopWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"VZVirtualMachineConfiguration",
        b"validateSaveRestoreSupportWithError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VZVirtualMachineConfiguration",
        b"validateWithError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VZVirtualMachineView",
        b"automaticallyReconfiguresDisplay",
        {"retval": {"type": b"Z"}},
    )
    r(b"VZVirtualMachineView", b"capturesSystemKeys", {"retval": {"type": b"Z"}})
    r(
        b"VZVirtualMachineView",
        b"setAutomaticallyReconfiguresDisplay:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VZVirtualMachineView",
        b"setCapturesSystemKeys:",
        {"arguments": {2: {"type": b"Z"}}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "VZBridgedNetworkDeviceAttachment", b"initWithInterface:"
)
objc.registerNewKeywordsFromSelector(
    "VZDiskBlockDeviceStorageDeviceAttachment",
    b"initWithFileHandle:readOnly:synchronizationMode:error:",
)
objc.registerNewKeywordsFromSelector(
    "VZDiskImageStorageDeviceAttachment",
    b"initWithURL:readOnly:cachingMode:synchronizationMode:error:",
)
objc.registerNewKeywordsFromSelector(
    "VZDiskImageStorageDeviceAttachment", b"initWithURL:readOnly:error:"
)
objc.registerNewKeywordsFromSelector(
    "VZEFIVariableStore", b"initCreatingVariableStoreAtURL:options:error:"
)
objc.registerNewKeywordsFromSelector("VZEFIVariableStore", b"initWithURL:")
objc.registerNewKeywordsFromSelector(
    "VZFileHandleNetworkDeviceAttachment", b"initWithFileHandle:"
)
objc.registerNewKeywordsFromSelector(
    "VZFileHandleSerialPortAttachment",
    b"initWithFileHandleForReading:fileHandleForWriting:",
)
objc.registerNewKeywordsFromSelector(
    "VZFileSerialPortAttachment", b"initWithURL:append:error:"
)
objc.registerNewKeywordsFromSelector(
    "VZGenericMachineIdentifier", b"initWithDataRepresentation:"
)
objc.registerNewKeywordsFromSelector("VZLinuxBootLoader", b"initWithKernelURL:")
objc.registerNewKeywordsFromSelector(
    "VZLinuxRosettaAbstractSocketCachingOptions", b"initWithName:error:"
)
objc.registerNewKeywordsFromSelector("VZLinuxRosettaDirectoryShare", b"initWithError:")
objc.registerNewKeywordsFromSelector(
    "VZLinuxRosettaUnixSocketCachingOptions", b"initWithPath:error:"
)
objc.registerNewKeywordsFromSelector("VZMACAddress", b"initWithEthernetAddress:")
objc.registerNewKeywordsFromSelector("VZMACAddress", b"initWithString:")
objc.registerNewKeywordsFromSelector(
    "VZMacAuxiliaryStorage", b"initCreatingStorageAtURL:hardwareModel:options:error:"
)
objc.registerNewKeywordsFromSelector("VZMacAuxiliaryStorage", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("VZMacAuxiliaryStorage", b"initWithURL:")
objc.registerNewKeywordsFromSelector(
    "VZMacGraphicsDisplayConfiguration", b"initForScreen:sizeInPoints:"
)
objc.registerNewKeywordsFromSelector(
    "VZMacGraphicsDisplayConfiguration",
    b"initWithWidthInPixels:heightInPixels:pixelsPerInch:",
)
objc.registerNewKeywordsFromSelector(
    "VZMacHardwareModel", b"initWithDataRepresentation:"
)
objc.registerNewKeywordsFromSelector(
    "VZMacMachineIdentifier", b"initWithDataRepresentation:"
)
objc.registerNewKeywordsFromSelector(
    "VZMacOSInstaller", b"initWithVirtualMachine:restoreImageURL:"
)
objc.registerNewKeywordsFromSelector(
    "VZMultipleDirectoryShare", b"initWithDirectories:"
)
objc.registerNewKeywordsFromSelector(
    "VZNVMExpressControllerDeviceConfiguration", b"initWithAttachment:"
)
objc.registerNewKeywordsFromSelector(
    "VZNetworkBlockDeviceStorageDeviceAttachment", b"initWithURL:error:"
)
objc.registerNewKeywordsFromSelector(
    "VZNetworkBlockDeviceStorageDeviceAttachment",
    b"initWithURL:timeout:forcedReadOnly:synchronizationMode:error:",
)
objc.registerNewKeywordsFromSelector("VZSharedDirectory", b"initWithURL:readOnly:")
objc.registerNewKeywordsFromSelector("VZSingleDirectoryShare", b"initWithDirectory:")
objc.registerNewKeywordsFromSelector(
    "VZUSBMassStorageDevice", b"initWithConfiguration:"
)
objc.registerNewKeywordsFromSelector(
    "VZUSBMassStorageDeviceConfiguration", b"initWithAttachment:"
)
objc.registerNewKeywordsFromSelector(
    "VZVirtioBlockDeviceConfiguration", b"initWithAttachment:"
)
objc.registerNewKeywordsFromSelector(
    "VZVirtioFileSystemDeviceConfiguration", b"initWithTag:"
)
objc.registerNewKeywordsFromSelector(
    "VZVirtioGraphicsScanoutConfiguration", b"initWithWidthInPixels:heightInPixels:"
)
objc.registerNewKeywordsFromSelector("VZVirtualMachine", b"initWithConfiguration:")
objc.registerNewKeywordsFromSelector(
    "VZVirtualMachine", b"initWithConfiguration:queue:"
)
expressions = {}

# END OF FILE
