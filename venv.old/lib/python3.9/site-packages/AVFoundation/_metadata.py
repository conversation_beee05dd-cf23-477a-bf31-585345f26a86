# This file is generated by objective.metadata
#
# Last update: Mon May  5 09:40:47 2025
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "AVAudio3DPoint": objc.createStructType(
            "AVFoundation.AVAudio3DPoint", b"{AVAudio3DPoint=fff}", ["x", "y", "z"]
        ),
        "AVAudioConverterPrimeInfo": objc.createStructType(
            "AVFoundation.AVAudioConverterPrimeInfo",
            b"{AVAudioConverterPrimeInfo=II}",
            ["leadingFrames", "trailingFrames"],
        ),
        "AVSampleCursorSyncInfo": objc.createStructType(
            "AVFoundation.AVSampleCursorSyncInfo",
            b"{AVSampleCursorSyncInfo=ZZZ}",
            ["sampleIsFullSync", "sampleIsPartialSync", "sampleIsDroppable"],
        ),
        "AVCaptureWhiteBalanceChromaticityValues": objc.createStructType(
            "AVFoundation.AVCaptureWhiteBalanceChromaticityValues",
            b"{AVCaptureWhiteBalanceChromaticityValues=ff}",
            ["x", "y"],
        ),
        "AVAudio3DVectorOrientation": objc.createStructType(
            "AVFoundation.AVAudio3DVectorOrientation",
            b"{AVAudio3DVectorOrientation={AVAudio3DPoint=fff}{AVAudio3DPoint=fff}}",
            ["forward", "up"],
        ),
        "AVCaptionDimension": objc.createStructType(
            "AVFoundation.AVCaptionDimension",
            b"{AVCaptionDimension=dq}",
            ["value", "units"],
        ),
        "AVPixelAspectRatio": objc.createStructType(
            "AVFoundation.AVPixelAspectRatio",
            b"{AVPixelAspectRatio=qq}",
            ["horizontalSpacing", "verticalSpacing"],
        ),
        "AVCaptureWhiteBalanceTemperatureAndTintValues": objc.createStructType(
            "AVFoundation.AVCaptureWhiteBalanceTemperatureAndTintValues",
            b"{AVCaptureWhiteBalanceTemperatureAndTintValues=ff}",
            ["temperature", "tint"],
        ),
        "AVAudio3DAngularOrientation": objc.createStructType(
            "AVFoundation.AVAudio3DAngularOrientation",
            b"{AVAudio3DAngularOrientation=fff}",
            ["yaw", "pitch", "roll"],
        ),
        "AVAudioVoiceProcessingOtherAudioDuckingConfiguration": objc.createStructType(
            "AVFoundation.AVAudioVoiceProcessingOtherAudioDuckingConfiguration",
            b"{_AVAudioVoiceProcessingOtherAudioDuckingConfiguration=Zq}",
            ["enableAdvancedDucking", "duckingLevel"],
        ),
        "AVSampleCursorStorageRange": objc.createStructType(
            "AVFoundation.AVSampleCursorStorageRange",
            b"{AVSampleCursorStorageRange=qq}",
            ["offset", "length"],
        ),
        "AVSampleCursorDependencyInfo": objc.createStructType(
            "AVFoundation.AVSampleCursorDependencyInfo",
            b"{AVSampleCursorDependencyInfo=ZZZZZZ}",
            [
                "sampleIndicatesWhetherItHasDependentSamples",
                "sampleHasDependentSamples",
                "sampleIndicatesWhetherItDependsOnOthers",
                "sampleDependsOnOthers",
                "sampleIndicatesWhetherItHasRedundantCoding",
                "sampleHasRedundantCoding",
            ],
        ),
        "AVCaptionPoint": objc.createStructType(
            "AVFoundation.AVCaptionPoint",
            b"{AVCaptionPoint={AVCaptionDimension=dq}{AVCaptionDimension=dq}}",
            ["x", "y"],
        ),
        "AVBeatRange": objc.createStructType(
            "AVFoundation.AVBeatRange", b"{_AVBeatRange=dd}", ["start", "length"]
        ),
        "AVSampleCursorChunkInfo": objc.createStructType(
            "AVFoundation.AVSampleCursorChunkInfo",
            b"{AVSampleCursorChunkInfo=qZZZ}",
            [
                "chunkSampleCount",
                "chunkHasUniformSampleSizes",
                "chunkHasUniformSampleDurations",
                "chunkHasUniformFormatDescriptions",
            ],
        ),
        "AVEdgeWidths": objc.createStructType(
            "AVFoundation.AVEdgeWidths",
            b"{AVEdgeWidths=dddd}",
            ["left", "top", "right", "bottom"],
        ),
        "AVSampleCursorAudioDependencyInfo": objc.createStructType(
            "AVFoundation.AVSampleCursorAudioDependencyInfo",
            b"{AVSampleCursorAudioDependencyInfo=Zq}",
            ["audioSampleIsIndependentlyDecodable", "audioSamplePacketRefreshCount"],
        ),
        "AVCaptureWhiteBalanceGains": objc.createStructType(
            "AVFoundation.AVCaptureWhiteBalanceGains",
            b"{AVCaptureWhiteBalanceGains=fff}",
            ["redGain", "greenGain", "blueGain"],
        ),
        "AVCaptionSize": objc.createStructType(
            "AVFoundation.AVCaptionSize",
            b"{AVCaptionSize={AVCaptionDimension=dq}{AVCaptionDimension=dq}}",
            ["width", "height"],
        ),
    }
)
constants = """$AVAssetChapterMetadataGroupsDidChangeNotification$AVAssetContainsFragmentsDidChangeNotification$AVAssetDownloadTaskMediaSelectionKey$AVAssetDownloadTaskMediaSelectionPrefersMultichannelKey$AVAssetDownloadTaskMinimumRequiredMediaBitrateKey$AVAssetDownloadTaskMinimumRequiredPresentationSizeKey$AVAssetDownloadTaskPrefersHDRKey$AVAssetDownloadTaskPrefersLosslessAudioKey$AVAssetDownloadedAssetEvictionPriorityDefault$AVAssetDownloadedAssetEvictionPriorityImportant$AVAssetDurationDidChangeNotification$AVAssetExportPreset1280x720$AVAssetExportPreset1920x1080$AVAssetExportPreset3840x2160$AVAssetExportPreset640x480$AVAssetExportPreset960x540$AVAssetExportPresetAppleM4A$AVAssetExportPresetAppleM4V1080pHD$AVAssetExportPresetAppleM4V480pSD$AVAssetExportPresetAppleM4V720pHD$AVAssetExportPresetAppleM4VAppleTV$AVAssetExportPresetAppleM4VCellular$AVAssetExportPresetAppleM4VWiFi$AVAssetExportPresetAppleM4ViPod$AVAssetExportPresetAppleProRes422LPCM$AVAssetExportPresetAppleProRes4444LPCM$AVAssetExportPresetHEVC1920x1080$AVAssetExportPresetHEVC1920x1080WithAlpha$AVAssetExportPresetHEVC3840x2160$AVAssetExportPresetHEVC3840x2160WithAlpha$AVAssetExportPresetHEVC7680x4320$AVAssetExportPresetHEVCHighestQuality$AVAssetExportPresetHEVCHighestQualityWithAlpha$AVAssetExportPresetHighestQuality$AVAssetExportPresetLowQuality$AVAssetExportPresetMVHEVC1440x1440$AVAssetExportPresetMVHEVC960x960$AVAssetExportPresetMediumQuality$AVAssetExportPresetPassthrough$AVAssetImageGeneratorApertureModeCleanAperture$AVAssetImageGeneratorApertureModeEncodedPixels$AVAssetImageGeneratorApertureModeProductionAperture$AVAssetImageGeneratorDynamicRangePolicyForceSDR$AVAssetImageGeneratorDynamicRangePolicyMatchSource$AVAssetMediaSelectionGroupsDidChangeNotification$AVAssetPlaybackConfigurationOptionSpatialVideo$AVAssetPlaybackConfigurationOptionStereoMultiviewVideo$AVAssetPlaybackConfigurationOptionStereoVideo$AVAssetResourceLoadingRequestStreamingContentKeyRequestRequiresPersistentKey$AVAssetTrackSegmentsDidChangeNotification$AVAssetTrackTimeRangeDidChangeNotification$AVAssetTrackTrackAssociationsDidChangeNotification$AVAssetWasDefragmentedNotification$AVAssetWriterInputMediaDataLocationBeforeMainMediaDataNotInterleaved$AVAssetWriterInputMediaDataLocationInterleavedWithMainMediaData$AVAudioApplicationInputMuteStateChangeNotification$AVAudioApplicationMuteStateKey$AVAudioBitRateStrategy_Constant$AVAudioBitRateStrategy_LongTermAverage$AVAudioBitRateStrategy_Variable$AVAudioBitRateStrategy_VariableConstrained$AVAudioEngineConfigurationChangeNotification$AVAudioFileTypeKey$AVAudioSequencerInfoDictionaryKeyAlbum$AVAudioSequencerInfoDictionaryKeyApproximateDurationInSeconds$AVAudioSequencerInfoDictionaryKeyArtist$AVAudioSequencerInfoDictionaryKeyChannelLayout$AVAudioSequencerInfoDictionaryKeyComments$AVAudioSequencerInfoDictionaryKeyComposer$AVAudioSequencerInfoDictionaryKeyCopyright$AVAudioSequencerInfoDictionaryKeyEncodingApplication$AVAudioSequencerInfoDictionaryKeyGenre$AVAudioSequencerInfoDictionaryKeyISRC$AVAudioSequencerInfoDictionaryKeyKeySignature$AVAudioSequencerInfoDictionaryKeyLyricist$AVAudioSequencerInfoDictionaryKeyNominalBitRate$AVAudioSequencerInfoDictionaryKeyRecordedDate$AVAudioSequencerInfoDictionaryKeySourceBitDepth$AVAudioSequencerInfoDictionaryKeySourceEncoder$AVAudioSequencerInfoDictionaryKeySubTitle$AVAudioSequencerInfoDictionaryKeyTempo$AVAudioSequencerInfoDictionaryKeyTimeSignature$AVAudioSequencerInfoDictionaryKeyTitle$AVAudioSequencerInfoDictionaryKeyTrackNumber$AVAudioSequencerInfoDictionaryKeyYear$AVAudioSessionCategoryAmbient$AVAudioSessionCategoryAudioProcessing$AVAudioSessionCategoryMultiRoute$AVAudioSessionCategoryPlayAndRecord$AVAudioSessionCategoryPlayback$AVAudioSessionCategoryRecord$AVAudioSessionCategorySoloAmbient$AVAudioSessionInterruptionNotification$AVAudioSessionInterruptionOptionKey$AVAudioSessionInterruptionTypeKey$AVAudioSessionInterruptionWasSuspendedKey$AVAudioSessionLocationLower$AVAudioSessionLocationUpper$AVAudioSessionMediaServicesWereLostNotification$AVAudioSessionMediaServicesWereResetNotification$AVAudioSessionModeDefault$AVAudioSessionModeGameChat$AVAudioSessionModeMeasurement$AVAudioSessionModeMoviePlayback$AVAudioSessionModeSpokenAudio$AVAudioSessionModeVideoChat$AVAudioSessionModeVideoRecording$AVAudioSessionModeVoiceChat$AVAudioSessionModeVoicePrompt$AVAudioSessionOrientationBack$AVAudioSessionOrientationBottom$AVAudioSessionOrientationFront$AVAudioSessionOrientationLeft$AVAudioSessionOrientationRight$AVAudioSessionOrientationTop$AVAudioSessionPolarPatternCardioid$AVAudioSessionPolarPatternOmnidirectional$AVAudioSessionPolarPatternStereo$AVAudioSessionPolarPatternSubcardioid$AVAudioSessionPortAVB$AVAudioSessionPortAirPlay$AVAudioSessionPortBluetoothA2DP$AVAudioSessionPortBluetoothHFP$AVAudioSessionPortBluetoothLE$AVAudioSessionPortBuiltInMic$AVAudioSessionPortBuiltInReceiver$AVAudioSessionPortBuiltInSpeaker$AVAudioSessionPortCarAudio$AVAudioSessionPortDisplayPort$AVAudioSessionPortFireWire$AVAudioSessionPortHDMI$AVAudioSessionPortHeadphones$AVAudioSessionPortHeadsetMic$AVAudioSessionPortLineIn$AVAudioSessionPortLineOut$AVAudioSessionPortPCI$AVAudioSessionPortThunderbolt$AVAudioSessionPortUSBAudio$AVAudioSessionPortVirtual$AVAudioSessionRouteChangeNotification$AVAudioSessionRouteChangePreviousRouteKey$AVAudioSessionRouteChangeReasonKey$AVAudioSessionSilenceSecondaryAudioHintNotification$AVAudioSessionSilenceSecondaryAudioHintTypeKey$AVAudioTimePitchAlgorithmLowQualityZeroLatency$AVAudioTimePitchAlgorithmSpectral$AVAudioTimePitchAlgorithmTimeDomain$AVAudioTimePitchAlgorithmVarispeed$AVAudioUnitComponentManagerRegistrationsChangedNotification$AVAudioUnitComponentTagsDidChangeNotification$AVAudioUnitManufacturerNameApple$AVAudioUnitTypeEffect$AVAudioUnitTypeFormatConverter$AVAudioUnitTypeGenerator$AVAudioUnitTypeMIDIProcessor$AVAudioUnitTypeMixer$AVAudioUnitTypeMusicDevice$AVAudioUnitTypeMusicEffect$AVAudioUnitTypeOfflineEffect$AVAudioUnitTypeOutput$AVAudioUnitTypePanner$AVCaptionConversionAdjustmentTypeTimeRange$AVCaptionConversionWarningTypeExcessMediaData$AVCaptionMediaSubTypeKey$AVCaptionMediaTypeKey$AVCaptionTimeCodeFrameDurationKey$AVCaptionUseDropFrameTimeCodeKey$AVCaptureDeviceSubjectAreaDidChangeNotification$AVCaptureDeviceTypeBuiltInDualCamera$AVCaptureDeviceTypeBuiltInDualWideCamera$AVCaptureDeviceTypeBuiltInDuoCamera$AVCaptureDeviceTypeBuiltInLiDARDepthCamera$AVCaptureDeviceTypeBuiltInMicrophone$AVCaptureDeviceTypeBuiltInTelephotoCamera$AVCaptureDeviceTypeBuiltInTripleCamera$AVCaptureDeviceTypeBuiltInTrueDepthCamera$AVCaptureDeviceTypeBuiltInUltraWideCamera$AVCaptureDeviceTypeBuiltInWideAngleCamera$AVCaptureDeviceTypeContinuityCamera$AVCaptureDeviceTypeContinuityCamera,$AVCaptureDeviceTypeDeskViewCamera$AVCaptureDeviceTypeExternal$AVCaptureDeviceTypeExternalUnknown$AVCaptureDeviceTypeMicrophone$AVCaptureDeviceWasConnectedNotification$AVCaptureDeviceWasDisconnectedNotification$AVCaptureExposureDurationCurrent@{CMTime=qiIq}$AVCaptureExposureTargetBiasCurrent@f$AVCaptureISOCurrent@f$AVCaptureInputPortFormatDescriptionDidChangeNotification$AVCaptureLensPositionCurrent@f$AVCaptureMaxAvailableTorchLevel@f$AVCaptureReactionType@I$AVCaptureReactionTypeBalloons$AVCaptureReactionTypeConfetti$AVCaptureReactionTypeFireworks$AVCaptureReactionTypeHeart$AVCaptureReactionTypeLasers$AVCaptureReactionTypeRain$AVCaptureReactionTypeThumbsDown$AVCaptureReactionTypeThumbsUp$AVCaptureSessionDidStartRunningNotification$AVCaptureSessionDidStopRunningNotification$AVCaptureSessionErrorKey$AVCaptureSessionInterruptionEndedNotification$AVCaptureSessionInterruptionReasonKey$AVCaptureSessionInterruptionSystemPressureStateKey$AVCaptureSessionPreset1280x720$AVCaptureSessionPreset1920x1080$AVCaptureSessionPreset320x240$AVCaptureSessionPreset352x288$AVCaptureSessionPreset3840x2160$AVCaptureSessionPreset640x480$AVCaptureSessionPreset960x540$AVCaptureSessionPresetHigh$AVCaptureSessionPresetInputPriority$AVCaptureSessionPresetLow$AVCaptureSessionPresetMedium$AVCaptureSessionPresetPhoto$AVCaptureSessionPresetiFrame1280x720$AVCaptureSessionPresetiFrame960x540$AVCaptureSessionRuntimeErrorNotification$AVCaptureSessionWasInterruptedNotification$AVCaptureSystemPressureLevelCritical$AVCaptureSystemPressureLevelFair$AVCaptureSystemPressureLevelNominal$AVCaptureSystemPressureLevelSerious$AVCaptureSystemPressureLevelShutdown$AVCaptureWhiteBalanceGainsCurrent@{AVCaptureWhiteBalanceGains=fff}$AVChannelLayoutKey$AVContentKeyRequestProtocolVersionsKey$AVContentKeyRequestRequiresValidationDataInSecureTokenKey$AVContentKeyRequestRetryReasonReceivedObsoleteContentKey$AVContentKeyRequestRetryReasonReceivedResponseWithExpiredLease$AVContentKeyRequestRetryReasonTimedOut$AVContentKeySessionServerPlaybackContextOptionProtocolVersions$AVContentKeySessionServerPlaybackContextOptionServerChallenge$AVContentKeySystemAuthorizationToken$AVContentKeySystemClearKey$AVContentKeySystemFairPlayStreaming$AVCoordinatedPlaybackSuspensionReasonAudioSessionInterrupted$AVCoordinatedPlaybackSuspensionReasonCoordinatedPlaybackNotPossible$AVCoordinatedPlaybackSuspensionReasonPlayingInterstitial$AVCoordinatedPlaybackSuspensionReasonStallRecovery$AVCoordinatedPlaybackSuspensionReasonUserActionRequired$AVCoordinatedPlaybackSuspensionReasonUserIsChangingCurrentTime$AVCoreAnimationBeginTimeAtZero@d$AVEncoderAudioQualityForVBRKey$AVEncoderAudioQualityKey$AVEncoderBitDepthHintKey$AVEncoderBitRateKey$AVEncoderBitRatePerChannelKey$AVEncoderBitRateStrategyKey$AVErrorDeviceKey$AVErrorDiscontinuityFlagsKey$AVErrorFileSizeKey$AVErrorFileTypeKey$AVErrorMediaSubTypeKey$AVErrorMediaTypeKey$AVErrorPIDKey$AVErrorPersistentTrackIDKey$AVErrorPresentationTimeStampKey$AVErrorRecordingSuccessfullyFinishedKey$AVErrorTimeKey$AVExtendedNoteOnEventDefaultInstrument@I$AVFileType3GPP$AVFileType3GPP2$AVFileTypeAC3$AVFileTypeAHAP$AVFileTypeAIFC$AVFileTypeAIFF$AVFileTypeAMR$AVFileTypeAVCI$AVFileTypeAppleM4A$AVFileTypeAppleM4V$AVFileTypeAppleiTT$AVFileTypeCoreAudioFormat$AVFileTypeDNG$AVFileTypeEnhancedAC3$AVFileTypeHEIC$AVFileTypeHEIF$AVFileTypeJPEG$AVFileTypeMPEG4$AVFileTypeMPEGLayer3$AVFileTypeProfileMPEG4AppleHLS$AVFileTypeProfileMPEG4CMAFCompliant$AVFileTypeQuickTimeMovie$AVFileTypeSCC$AVFileTypeSunAU$AVFileTypeTIFF$AVFileTypeWAVE$AVFormatIDKey$AVFoundationErrorDomain$AVFragmentedMovieContainsMovieFragmentsDidChangeNotification$AVFragmentedMovieDurationDidChangeNotification$AVFragmentedMovieTrackSegmentsDidChangeNotification$AVFragmentedMovieTrackTimeRangeDidChangeNotification$AVFragmentedMovieTrackTotalSampleDataLengthDidChangeNotification$AVFragmentedMovieWasDefragmentedNotification$AVLayerVideoGravityResize$AVLayerVideoGravityResizeAspect$AVLayerVideoGravityResizeAspectFill$AVLinearPCMBitDepthKey$AVLinearPCMIsBigEndianKey$AVLinearPCMIsFloatKey$AVLinearPCMIsNonInterleaved$AVMediaCharacteristicAudible$AVMediaCharacteristicCarriesVideoStereoMetadata$AVMediaCharacteristicContainsAlphaChannel$AVMediaCharacteristicContainsHDRVideo$AVMediaCharacteristicContainsOnlyForcedSubtitles$AVMediaCharacteristicContainsStereoMultiviewVideo$AVMediaCharacteristicDescribesMusicAndSoundForAccessibility$AVMediaCharacteristicDescribesVideoForAccessibility$AVMediaCharacteristicDubbedTranslation$AVMediaCharacteristicEasyToRead$AVMediaCharacteristicEnhancesSpeechIntelligibility$AVMediaCharacteristicFrameBased$AVMediaCharacteristicIndicatesHorizontalFieldOfView$AVMediaCharacteristicIsAuxiliaryContent$AVMediaCharacteristicIsMainProgramContent$AVMediaCharacteristicIsOriginalContent$AVMediaCharacteristicLanguageTranslation$AVMediaCharacteristicLegible$AVMediaCharacteristicTactileMinimal$AVMediaCharacteristicTranscribesSpokenDialogForAccessibility$AVMediaCharacteristicUsesWideGamutColorSpace$AVMediaCharacteristicVisual$AVMediaCharacteristicVoiceOverTranslation$AVMediaTypeAudio$AVMediaTypeAuxiliaryPicture$AVMediaTypeClosedCaption$AVMediaTypeDepthData$AVMediaTypeHaptic$AVMediaTypeMetadata$AVMediaTypeMetadataObject$AVMediaTypeMuxed$AVMediaTypeSubtitle$AVMediaTypeText$AVMediaTypeTimecode$AVMediaTypeVideo$AVMetadata3GPUserDataKeyAlbumAndTrack$AVMetadata3GPUserDataKeyAuthor$AVMetadata3GPUserDataKeyCollection$AVMetadata3GPUserDataKeyCopyright$AVMetadata3GPUserDataKeyDescription$AVMetadata3GPUserDataKeyGenre$AVMetadata3GPUserDataKeyKeywordList$AVMetadata3GPUserDataKeyLocation$AVMetadata3GPUserDataKeyMediaClassification$AVMetadata3GPUserDataKeyMediaRating$AVMetadata3GPUserDataKeyPerformer$AVMetadata3GPUserDataKeyRecordingYear$AVMetadata3GPUserDataKeyThumbnail$AVMetadata3GPUserDataKeyTitle$AVMetadata3GPUserDataKeyUserRating$AVMetadataCommonIdentifierAccessibilityDescription$AVMetadataCommonIdentifierAlbumName$AVMetadataCommonIdentifierArtist$AVMetadataCommonIdentifierArtwork$AVMetadataCommonIdentifierAssetIdentifier$AVMetadataCommonIdentifierAuthor$AVMetadataCommonIdentifierContributor$AVMetadataCommonIdentifierCopyrights$AVMetadataCommonIdentifierCreationDate$AVMetadataCommonIdentifierCreator$AVMetadataCommonIdentifierDescription$AVMetadataCommonIdentifierFormat$AVMetadataCommonIdentifierLanguage$AVMetadataCommonIdentifierLastModifiedDate$AVMetadataCommonIdentifierLocation$AVMetadataCommonIdentifierMake$AVMetadataCommonIdentifierModel$AVMetadataCommonIdentifierPublisher$AVMetadataCommonIdentifierRelation$AVMetadataCommonIdentifierSoftware$AVMetadataCommonIdentifierSource$AVMetadataCommonIdentifierSubject$AVMetadataCommonIdentifierTitle$AVMetadataCommonIdentifierType$AVMetadataCommonKeyAccessibilityDescription$AVMetadataCommonKeyAlbumName$AVMetadataCommonKeyArtist$AVMetadataCommonKeyArtwork$AVMetadataCommonKeyAuthor$AVMetadataCommonKeyContributor$AVMetadataCommonKeyCopyrights$AVMetadataCommonKeyCreationDate$AVMetadataCommonKeyCreator$AVMetadataCommonKeyDescription$AVMetadataCommonKeyFormat$AVMetadataCommonKeyIdentifier$AVMetadataCommonKeyLanguage$AVMetadataCommonKeyLastModifiedDate$AVMetadataCommonKeyLocation$AVMetadataCommonKeyMake$AVMetadataCommonKeyModel$AVMetadataCommonKeyPublisher$AVMetadataCommonKeyRelation$AVMetadataCommonKeySoftware$AVMetadataCommonKeySource$AVMetadataCommonKeySubject$AVMetadataCommonKeyTitle$AVMetadataCommonKeyType$AVMetadataExtraAttributeBaseURIKey$AVMetadataExtraAttributeInfoKey$AVMetadataExtraAttributeValueURIKey$AVMetadataFormatHLSMetadata$AVMetadataFormatID3Metadata$AVMetadataFormatISOUserData$AVMetadataFormatQuickTimeMetadata$AVMetadataFormatQuickTimeUserData$AVMetadataFormatUnknown$AVMetadataFormatiTunesMetadata$AVMetadataID3MetadataKeyAlbumSortOrder$AVMetadataID3MetadataKeyAlbumTitle$AVMetadataID3MetadataKeyAttachedPicture$AVMetadataID3MetadataKeyAudioEncryption$AVMetadataID3MetadataKeyAudioSeekPointIndex$AVMetadataID3MetadataKeyBand$AVMetadataID3MetadataKeyBeatsPerMinute$AVMetadataID3MetadataKeyComments$AVMetadataID3MetadataKeyCommercial$AVMetadataID3MetadataKeyCommercialInformation$AVMetadataID3MetadataKeyCommerical$AVMetadataID3MetadataKeyComposer$AVMetadataID3MetadataKeyConductor$AVMetadataID3MetadataKeyContentGroupDescription$AVMetadataID3MetadataKeyContentType$AVMetadataID3MetadataKeyCopyright$AVMetadataID3MetadataKeyCopyrightInformation$AVMetadataID3MetadataKeyDate$AVMetadataID3MetadataKeyEncodedBy$AVMetadataID3MetadataKeyEncodedWith$AVMetadataID3MetadataKeyEncodingTime$AVMetadataID3MetadataKeyEncryption$AVMetadataID3MetadataKeyEqualization$AVMetadataID3MetadataKeyEqualization2$AVMetadataID3MetadataKeyEventTimingCodes$AVMetadataID3MetadataKeyFileOwner$AVMetadataID3MetadataKeyFileType$AVMetadataID3MetadataKeyGeneralEncapsulatedObject$AVMetadataID3MetadataKeyGroupIdentifier$AVMetadataID3MetadataKeyInitialKey$AVMetadataID3MetadataKeyInternationalStandardRecordingCode$AVMetadataID3MetadataKeyInternetRadioStationName$AVMetadataID3MetadataKeyInternetRadioStationOwner$AVMetadataID3MetadataKeyInvolvedPeopleList_v23$AVMetadataID3MetadataKeyInvolvedPeopleList_v24$AVMetadataID3MetadataKeyLanguage$AVMetadataID3MetadataKeyLeadPerformer$AVMetadataID3MetadataKeyLength$AVMetadataID3MetadataKeyLink$AVMetadataID3MetadataKeyLyricist$AVMetadataID3MetadataKeyMPEGLocationLookupTable$AVMetadataID3MetadataKeyMediaType$AVMetadataID3MetadataKeyModifiedBy$AVMetadataID3MetadataKeyMood$AVMetadataID3MetadataKeyMusicCDIdentifier$AVMetadataID3MetadataKeyMusicianCreditsList$AVMetadataID3MetadataKeyOfficialArtistWebpage$AVMetadataID3MetadataKeyOfficialAudioFileWebpage$AVMetadataID3MetadataKeyOfficialAudioSourceWebpage$AVMetadataID3MetadataKeyOfficialInternetRadioStationHomepage$AVMetadataID3MetadataKeyOfficialPublisherWebpage$AVMetadataID3MetadataKeyOriginalAlbumTitle$AVMetadataID3MetadataKeyOriginalArtist$AVMetadataID3MetadataKeyOriginalFilename$AVMetadataID3MetadataKeyOriginalLyricist$AVMetadataID3MetadataKeyOriginalReleaseTime$AVMetadataID3MetadataKeyOriginalReleaseYear$AVMetadataID3MetadataKeyOwnership$AVMetadataID3MetadataKeyPartOfASet$AVMetadataID3MetadataKeyPayment$AVMetadataID3MetadataKeyPerformerSortOrder$AVMetadataID3MetadataKeyPlayCounter$AVMetadataID3MetadataKeyPlaylistDelay$AVMetadataID3MetadataKeyPopularimeter$AVMetadataID3MetadataKeyPositionSynchronization$AVMetadataID3MetadataKeyPrivate$AVMetadataID3MetadataKeyProducedNotice$AVMetadataID3MetadataKeyPublisher$AVMetadataID3MetadataKeyRecommendedBufferSize$AVMetadataID3MetadataKeyRecordingDates$AVMetadataID3MetadataKeyRecordingTime$AVMetadataID3MetadataKeyRelativeVolumeAdjustment$AVMetadataID3MetadataKeyRelativeVolumeAdjustment2$AVMetadataID3MetadataKeyReleaseTime$AVMetadataID3MetadataKeyReverb$AVMetadataID3MetadataKeySeek$AVMetadataID3MetadataKeySetSubtitle$AVMetadataID3MetadataKeySignature$AVMetadataID3MetadataKeySize$AVMetadataID3MetadataKeySubTitle$AVMetadataID3MetadataKeySynchronizedLyric$AVMetadataID3MetadataKeySynchronizedTempoCodes$AVMetadataID3MetadataKeyTaggingTime$AVMetadataID3MetadataKeyTermsOfUse$AVMetadataID3MetadataKeyTime$AVMetadataID3MetadataKeyTitleDescription$AVMetadataID3MetadataKeyTitleSortOrder$AVMetadataID3MetadataKeyTrackNumber$AVMetadataID3MetadataKeyUniqueFileIdentifier$AVMetadataID3MetadataKeyUnsynchronizedLyric$AVMetadataID3MetadataKeyUserText$AVMetadataID3MetadataKeyUserURL$AVMetadataID3MetadataKeyYear$AVMetadataISOUserDataKeyAccessibilityDescription$AVMetadataISOUserDataKeyCopyright$AVMetadataISOUserDataKeyDate$AVMetadataISOUserDataKeyTaggedCharacteristic$AVMetadataIcyMetadataKeyStreamTitle$AVMetadataIcyMetadataKeyStreamURL$AVMetadataIdentifier3GPUserDataAlbumAndTrack$AVMetadataIdentifier3GPUserDataAuthor$AVMetadataIdentifier3GPUserDataCollection$AVMetadataIdentifier3GPUserDataCopyright$AVMetadataIdentifier3GPUserDataDescription$AVMetadataIdentifier3GPUserDataGenre$AVMetadataIdentifier3GPUserDataKeywordList$AVMetadataIdentifier3GPUserDataLocation$AVMetadataIdentifier3GPUserDataMediaClassification$AVMetadataIdentifier3GPUserDataMediaRating$AVMetadataIdentifier3GPUserDataPerformer$AVMetadataIdentifier3GPUserDataRecordingYear$AVMetadataIdentifier3GPUserDataThumbnail$AVMetadataIdentifier3GPUserDataTitle$AVMetadataIdentifier3GPUserDataUserRating$AVMetadataIdentifierID3MetadataAlbumSortOrder$AVMetadataIdentifierID3MetadataAlbumTitle$AVMetadataIdentifierID3MetadataAttachedPicture$AVMetadataIdentifierID3MetadataAudioEncryption$AVMetadataIdentifierID3MetadataAudioSeekPointIndex$AVMetadataIdentifierID3MetadataBand$AVMetadataIdentifierID3MetadataBeatsPerMinute$AVMetadataIdentifierID3MetadataComments$AVMetadataIdentifierID3MetadataCommercial$AVMetadataIdentifierID3MetadataCommercialInformation$AVMetadataIdentifierID3MetadataCommerical$AVMetadataIdentifierID3MetadataComposer$AVMetadataIdentifierID3MetadataConductor$AVMetadataIdentifierID3MetadataContentGroupDescription$AVMetadataIdentifierID3MetadataContentType$AVMetadataIdentifierID3MetadataCopyright$AVMetadataIdentifierID3MetadataCopyrightInformation$AVMetadataIdentifierID3MetadataDate$AVMetadataIdentifierID3MetadataEncodedBy$AVMetadataIdentifierID3MetadataEncodedWith$AVMetadataIdentifierID3MetadataEncodingTime$AVMetadataIdentifierID3MetadataEncryption$AVMetadataIdentifierID3MetadataEqualization$AVMetadataIdentifierID3MetadataEqualization2$AVMetadataIdentifierID3MetadataEventTimingCodes$AVMetadataIdentifierID3MetadataFileOwner$AVMetadataIdentifierID3MetadataFileType$AVMetadataIdentifierID3MetadataGeneralEncapsulatedObject$AVMetadataIdentifierID3MetadataGroupIdentifier$AVMetadataIdentifierID3MetadataInitialKey$AVMetadataIdentifierID3MetadataInternationalStandardRecordingCode$AVMetadataIdentifierID3MetadataInternetRadioStationName$AVMetadataIdentifierID3MetadataInternetRadioStationOwner$AVMetadataIdentifierID3MetadataInvolvedPeopleList_v23$AVMetadataIdentifierID3MetadataInvolvedPeopleList_v24$AVMetadataIdentifierID3MetadataLanguage$AVMetadataIdentifierID3MetadataLeadPerformer$AVMetadataIdentifierID3MetadataLength$AVMetadataIdentifierID3MetadataLink$AVMetadataIdentifierID3MetadataLyricist$AVMetadataIdentifierID3MetadataMPEGLocationLookupTable$AVMetadataIdentifierID3MetadataMediaType$AVMetadataIdentifierID3MetadataModifiedBy$AVMetadataIdentifierID3MetadataMood$AVMetadataIdentifierID3MetadataMusicCDIdentifier$AVMetadataIdentifierID3MetadataMusicianCreditsList$AVMetadataIdentifierID3MetadataOfficialArtistWebpage$AVMetadataIdentifierID3MetadataOfficialAudioFileWebpage$AVMetadataIdentifierID3MetadataOfficialAudioSourceWebpage$AVMetadataIdentifierID3MetadataOfficialInternetRadioStationHomepage$AVMetadataIdentifierID3MetadataOfficialPublisherWebpage$AVMetadataIdentifierID3MetadataOriginalAlbumTitle$AVMetadataIdentifierID3MetadataOriginalArtist$AVMetadataIdentifierID3MetadataOriginalFilename$AVMetadataIdentifierID3MetadataOriginalLyricist$AVMetadataIdentifierID3MetadataOriginalReleaseTime$AVMetadataIdentifierID3MetadataOriginalReleaseYear$AVMetadataIdentifierID3MetadataOwnership$AVMetadataIdentifierID3MetadataPartOfASet$AVMetadataIdentifierID3MetadataPayment$AVMetadataIdentifierID3MetadataPerformerSortOrder$AVMetadataIdentifierID3MetadataPlayCounter$AVMetadataIdentifierID3MetadataPlaylistDelay$AVMetadataIdentifierID3MetadataPopularimeter$AVMetadataIdentifierID3MetadataPositionSynchronization$AVMetadataIdentifierID3MetadataPrivate$AVMetadataIdentifierID3MetadataProducedNotice$AVMetadataIdentifierID3MetadataPublisher$AVMetadataIdentifierID3MetadataRecommendedBufferSize$AVMetadataIdentifierID3MetadataRecordingDates$AVMetadataIdentifierID3MetadataRecordingTime$AVMetadataIdentifierID3MetadataRelativeVolumeAdjustment$AVMetadataIdentifierID3MetadataRelativeVolumeAdjustment2$AVMetadataIdentifierID3MetadataReleaseTime$AVMetadataIdentifierID3MetadataReverb$AVMetadataIdentifierID3MetadataSeek$AVMetadataIdentifierID3MetadataSetSubtitle$AVMetadataIdentifierID3MetadataSignature$AVMetadataIdentifierID3MetadataSize$AVMetadataIdentifierID3MetadataSubTitle$AVMetadataIdentifierID3MetadataSynchronizedLyric$AVMetadataIdentifierID3MetadataSynchronizedTempoCodes$AVMetadataIdentifierID3MetadataTaggingTime$AVMetadataIdentifierID3MetadataTermsOfUse$AVMetadataIdentifierID3MetadataTime$AVMetadataIdentifierID3MetadataTitleDescription$AVMetadataIdentifierID3MetadataTitleSortOrder$AVMetadataIdentifierID3MetadataTrackNumber$AVMetadataIdentifierID3MetadataUniqueFileIdentifier$AVMetadataIdentifierID3MetadataUnsynchronizedLyric$AVMetadataIdentifierID3MetadataUserText$AVMetadataIdentifierID3MetadataUserURL$AVMetadataIdentifierID3MetadataYear$AVMetadataIdentifierISOUserDataAccessibilityDescription$AVMetadataIdentifierISOUserDataCopyright$AVMetadataIdentifierISOUserDataDate$AVMetadataIdentifierISOUserDataTaggedCharacteristic$AVMetadataIdentifierIcyMetadataStreamTitle$AVMetadataIdentifierIcyMetadataStreamURL$AVMetadataIdentifierQuickTimeMetadataAccessibilityDescription$AVMetadataIdentifierQuickTimeMetadataAlbum$AVMetadataIdentifierQuickTimeMetadataArranger$AVMetadataIdentifierQuickTimeMetadataArtist$AVMetadataIdentifierQuickTimeMetadataArtwork$AVMetadataIdentifierQuickTimeMetadataAuthor$AVMetadataIdentifierQuickTimeMetadataAutoLivePhoto$AVMetadataIdentifierQuickTimeMetadataCameraFrameReadoutTime$AVMetadataIdentifierQuickTimeMetadataCameraIdentifier$AVMetadataIdentifierQuickTimeMetadataCollectionUser$AVMetadataIdentifierQuickTimeMetadataComment$AVMetadataIdentifierQuickTimeMetadataComposer$AVMetadataIdentifierQuickTimeMetadataContentIdentifier$AVMetadataIdentifierQuickTimeMetadataCopyright$AVMetadataIdentifierQuickTimeMetadataCreationDate$AVMetadataIdentifierQuickTimeMetadataCredits$AVMetadataIdentifierQuickTimeMetadataDescription$AVMetadataIdentifierQuickTimeMetadataDetectedCatBody$AVMetadataIdentifierQuickTimeMetadataDetectedDogBody$AVMetadataIdentifierQuickTimeMetadataDetectedFace$AVMetadataIdentifierQuickTimeMetadataDetectedHumanBody$AVMetadataIdentifierQuickTimeMetadataDetectedSalientObject$AVMetadataIdentifierQuickTimeMetadataDirectionFacing$AVMetadataIdentifierQuickTimeMetadataDirectionMotion$AVMetadataIdentifierQuickTimeMetadataDirector$AVMetadataIdentifierQuickTimeMetadataDisplayName$AVMetadataIdentifierQuickTimeMetadataEncodedBy$AVMetadataIdentifierQuickTimeMetadataFullFrameRatePlaybackIntent$AVMetadataIdentifierQuickTimeMetadataGenre$AVMetadataIdentifierQuickTimeMetadataInformation$AVMetadataIdentifierQuickTimeMetadataIsMontage$AVMetadataIdentifierQuickTimeMetadataKeywords$AVMetadataIdentifierQuickTimeMetadataLivePhotoVitalityScore$AVMetadataIdentifierQuickTimeMetadataLivePhotoVitalityScoringVersion$AVMetadataIdentifierQuickTimeMetadataLocationBody$AVMetadataIdentifierQuickTimeMetadataLocationDate$AVMetadataIdentifierQuickTimeMetadataLocationHorizontalAccuracyInMeters$AVMetadataIdentifierQuickTimeMetadataLocationISO6709$AVMetadataIdentifierQuickTimeMetadataLocationName$AVMetadataIdentifierQuickTimeMetadataLocationNote$AVMetadataIdentifierQuickTimeMetadataLocationRole$AVMetadataIdentifierQuickTimeMetadataMake$AVMetadataIdentifierQuickTimeMetadataModel$AVMetadataIdentifierQuickTimeMetadataOriginalArtist$AVMetadataIdentifierQuickTimeMetadataPerformer$AVMetadataIdentifierQuickTimeMetadataPhonogramRights$AVMetadataIdentifierQuickTimeMetadataPreferredAffineTransform$AVMetadataIdentifierQuickTimeMetadataProducer$AVMetadataIdentifierQuickTimeMetadataPublisher$AVMetadataIdentifierQuickTimeMetadataRatingUser$AVMetadataIdentifierQuickTimeMetadataSoftware$AVMetadataIdentifierQuickTimeMetadataSpatialOverCaptureQualityScore$AVMetadataIdentifierQuickTimeMetadataSpatialOverCaptureQualityScoringVersion$AVMetadataIdentifierQuickTimeMetadataTitle$AVMetadataIdentifierQuickTimeMetadataVideoOrientation$AVMetadataIdentifierQuickTimeMetadataYear$AVMetadataIdentifierQuickTimeMetadataiXML$AVMetadataIdentifierQuickTimeUserDataAccessibilityDescription$AVMetadataIdentifierQuickTimeUserDataAlbum$AVMetadataIdentifierQuickTimeUserDataArranger$AVMetadataIdentifierQuickTimeUserDataArtist$AVMetadataIdentifierQuickTimeUserDataAuthor$AVMetadataIdentifierQuickTimeUserDataChapter$AVMetadataIdentifierQuickTimeUserDataComment$AVMetadataIdentifierQuickTimeUserDataComposer$AVMetadataIdentifierQuickTimeUserDataCopyright$AVMetadataIdentifierQuickTimeUserDataCreationDate$AVMetadataIdentifierQuickTimeUserDataCredits$AVMetadataIdentifierQuickTimeUserDataDescription$AVMetadataIdentifierQuickTimeUserDataDirector$AVMetadataIdentifierQuickTimeUserDataDisclaimer$AVMetadataIdentifierQuickTimeUserDataEncodedBy$AVMetadataIdentifierQuickTimeUserDataFullName$AVMetadataIdentifierQuickTimeUserDataGenre$AVMetadataIdentifierQuickTimeUserDataHostComputer$AVMetadataIdentifierQuickTimeUserDataInformation$AVMetadataIdentifierQuickTimeUserDataKeywords$AVMetadataIdentifierQuickTimeUserDataLocationISO6709$AVMetadataIdentifierQuickTimeUserDataMake$AVMetadataIdentifierQuickTimeUserDataModel$AVMetadataIdentifierQuickTimeUserDataOriginalArtist$AVMetadataIdentifierQuickTimeUserDataOriginalFormat$AVMetadataIdentifierQuickTimeUserDataOriginalSource$AVMetadataIdentifierQuickTimeUserDataPerformers$AVMetadataIdentifierQuickTimeUserDataPhonogramRights$AVMetadataIdentifierQuickTimeUserDataProducer$AVMetadataIdentifierQuickTimeUserDataProduct$AVMetadataIdentifierQuickTimeUserDataPublisher$AVMetadataIdentifierQuickTimeUserDataSoftware$AVMetadataIdentifierQuickTimeUserDataSpecialPlaybackRequirements$AVMetadataIdentifierQuickTimeUserDataTaggedCharacteristic$AVMetadataIdentifierQuickTimeUserDataTrack$AVMetadataIdentifierQuickTimeUserDataTrackName$AVMetadataIdentifierQuickTimeUserDataURLLink$AVMetadataIdentifierQuickTimeUserDataWarning$AVMetadataIdentifierQuickTimeUserDataWriter$AVMetadataIdentifieriTunesMetadataAccountKind$AVMetadataIdentifieriTunesMetadataAcknowledgement$AVMetadataIdentifieriTunesMetadataAlbum$AVMetadataIdentifieriTunesMetadataAlbumArtist$AVMetadataIdentifieriTunesMetadataAppleID$AVMetadataIdentifieriTunesMetadataArranger$AVMetadataIdentifieriTunesMetadataArtDirector$AVMetadataIdentifieriTunesMetadataArtist$AVMetadataIdentifieriTunesMetadataArtistID$AVMetadataIdentifieriTunesMetadataAuthor$AVMetadataIdentifieriTunesMetadataBeatsPerMin$AVMetadataIdentifieriTunesMetadataComposer$AVMetadataIdentifieriTunesMetadataConductor$AVMetadataIdentifieriTunesMetadataContentRating$AVMetadataIdentifieriTunesMetadataCopyright$AVMetadataIdentifieriTunesMetadataCoverArt$AVMetadataIdentifieriTunesMetadataCredits$AVMetadataIdentifieriTunesMetadataDescription$AVMetadataIdentifieriTunesMetadataDirector$AVMetadataIdentifieriTunesMetadataDiscCompilation$AVMetadataIdentifieriTunesMetadataDiscNumber$AVMetadataIdentifieriTunesMetadataEQ$AVMetadataIdentifieriTunesMetadataEncodedBy$AVMetadataIdentifieriTunesMetadataEncodingTool$AVMetadataIdentifieriTunesMetadataExecProducer$AVMetadataIdentifieriTunesMetadataGenreID$AVMetadataIdentifieriTunesMetadataGrouping$AVMetadataIdentifieriTunesMetadataLinerNotes$AVMetadataIdentifieriTunesMetadataLyrics$AVMetadataIdentifieriTunesMetadataOnlineExtras$AVMetadataIdentifieriTunesMetadataOriginalArtist$AVMetadataIdentifieriTunesMetadataPerformer$AVMetadataIdentifieriTunesMetadataPhonogramRights$AVMetadataIdentifieriTunesMetadataPlaylistID$AVMetadataIdentifieriTunesMetadataPredefinedGenre$AVMetadataIdentifieriTunesMetadataProducer$AVMetadataIdentifieriTunesMetadataPublisher$AVMetadataIdentifieriTunesMetadataRecordCompany$AVMetadataIdentifieriTunesMetadataReleaseDate$AVMetadataIdentifieriTunesMetadataSoloist$AVMetadataIdentifieriTunesMetadataSongID$AVMetadataIdentifieriTunesMetadataSongName$AVMetadataIdentifieriTunesMetadataSoundEngineer$AVMetadataIdentifieriTunesMetadataThanks$AVMetadataIdentifieriTunesMetadataTrackNumber$AVMetadataIdentifieriTunesMetadataTrackSubTitle$AVMetadataIdentifieriTunesMetadataUserComment$AVMetadataIdentifieriTunesMetadataUserGenre$AVMetadataKeySpaceAudioFile$AVMetadataKeySpaceCommon$AVMetadataKeySpaceHLSDateRange$AVMetadataKeySpaceID3$AVMetadataKeySpaceISOUserData$AVMetadataKeySpaceIcy$AVMetadataKeySpaceQuickTimeMetadata$AVMetadataKeySpaceQuickTimeUserData$AVMetadataKeySpaceiTunes$AVMetadataObjectTypeAztecCode$AVMetadataObjectTypeCatBody$AVMetadataObjectTypeCodabarCode$AVMetadataObjectTypeCode128Code$AVMetadataObjectTypeCode39Code$AVMetadataObjectTypeCode39Mod43Code$AVMetadataObjectTypeCode93Code$AVMetadataObjectTypeDataMatrixCode$AVMetadataObjectTypeDogBody$AVMetadataObjectTypeEAN13Code$AVMetadataObjectTypeEAN8Code$AVMetadataObjectTypeFace$AVMetadataObjectTypeGS1DataBarCode$AVMetadataObjectTypeGS1DataBarExpandedCode$AVMetadataObjectTypeGS1DataBarLimitedCode$AVMetadataObjectTypeHumanBody$AVMetadataObjectTypeHumanFullBody$AVMetadataObjectTypeITF14Code$AVMetadataObjectTypeInterleaved2of5Code$AVMetadataObjectTypeMicroPDF417Code$AVMetadataObjectTypeMicroQRCode$AVMetadataObjectTypePDF417Code$AVMetadataObjectTypeQRCode$AVMetadataObjectTypeSalientObject$AVMetadataObjectTypeUPCECode$AVMetadataQuickTimeMetadataKeyAccessibilityDescription$AVMetadataQuickTimeMetadataKeyAlbum$AVMetadataQuickTimeMetadataKeyArranger$AVMetadataQuickTimeMetadataKeyArtist$AVMetadataQuickTimeMetadataKeyArtwork$AVMetadataQuickTimeMetadataKeyAuthor$AVMetadataQuickTimeMetadataKeyCameraFrameReadoutTime$AVMetadataQuickTimeMetadataKeyCameraIdentifier$AVMetadataQuickTimeMetadataKeyCollectionUser$AVMetadataQuickTimeMetadataKeyComment$AVMetadataQuickTimeMetadataKeyComposer$AVMetadataQuickTimeMetadataKeyContentIdentifier$AVMetadataQuickTimeMetadataKeyCopyright$AVMetadataQuickTimeMetadataKeyCreationDate$AVMetadataQuickTimeMetadataKeyCredits$AVMetadataQuickTimeMetadataKeyDescription$AVMetadataQuickTimeMetadataKeyDirectionFacing$AVMetadataQuickTimeMetadataKeyDirectionMotion$AVMetadataQuickTimeMetadataKeyDirector$AVMetadataQuickTimeMetadataKeyDisplayName$AVMetadataQuickTimeMetadataKeyEncodedBy$AVMetadataQuickTimeMetadataKeyFullFrameRatePlaybackIntent$AVMetadataQuickTimeMetadataKeyGenre$AVMetadataQuickTimeMetadataKeyInformation$AVMetadataQuickTimeMetadataKeyIsMontage$AVMetadataQuickTimeMetadataKeyKeywords$AVMetadataQuickTimeMetadataKeyLocationBody$AVMetadataQuickTimeMetadataKeyLocationDate$AVMetadataQuickTimeMetadataKeyLocationISO6709$AVMetadataQuickTimeMetadataKeyLocationName$AVMetadataQuickTimeMetadataKeyLocationNote$AVMetadataQuickTimeMetadataKeyLocationRole$AVMetadataQuickTimeMetadataKeyMake$AVMetadataQuickTimeMetadataKeyModel$AVMetadataQuickTimeMetadataKeyOriginalArtist$AVMetadataQuickTimeMetadataKeyPerformer$AVMetadataQuickTimeMetadataKeyPhonogramRights$AVMetadataQuickTimeMetadataKeyProducer$AVMetadataQuickTimeMetadataKeyPublisher$AVMetadataQuickTimeMetadataKeyRatingUser$AVMetadataQuickTimeMetadataKeySoftware$AVMetadataQuickTimeMetadataKeyTitle$AVMetadataQuickTimeMetadataKeyYear$AVMetadataQuickTimeMetadataKeyiXML$AVMetadataQuickTimeUserDataKeyAccessibilityDescription$AVMetadataQuickTimeUserDataKeyAlbum$AVMetadataQuickTimeUserDataKeyArranger$AVMetadataQuickTimeUserDataKeyArtist$AVMetadataQuickTimeUserDataKeyAuthor$AVMetadataQuickTimeUserDataKeyChapter$AVMetadataQuickTimeUserDataKeyComment$AVMetadataQuickTimeUserDataKeyComposer$AVMetadataQuickTimeUserDataKeyCopyright$AVMetadataQuickTimeUserDataKeyCreationDate$AVMetadataQuickTimeUserDataKeyCredits$AVMetadataQuickTimeUserDataKeyDescription$AVMetadataQuickTimeUserDataKeyDirector$AVMetadataQuickTimeUserDataKeyDisclaimer$AVMetadataQuickTimeUserDataKeyEncodedBy$AVMetadataQuickTimeUserDataKeyFullName$AVMetadataQuickTimeUserDataKeyGenre$AVMetadataQuickTimeUserDataKeyHostComputer$AVMetadataQuickTimeUserDataKeyInformation$AVMetadataQuickTimeUserDataKeyKeywords$AVMetadataQuickTimeUserDataKeyLocationISO6709$AVMetadataQuickTimeUserDataKeyMake$AVMetadataQuickTimeUserDataKeyModel$AVMetadataQuickTimeUserDataKeyOriginalArtist$AVMetadataQuickTimeUserDataKeyOriginalFormat$AVMetadataQuickTimeUserDataKeyOriginalSource$AVMetadataQuickTimeUserDataKeyPerformers$AVMetadataQuickTimeUserDataKeyPhonogramRights$AVMetadataQuickTimeUserDataKeyProducer$AVMetadataQuickTimeUserDataKeyProduct$AVMetadataQuickTimeUserDataKeyPublisher$AVMetadataQuickTimeUserDataKeySoftware$AVMetadataQuickTimeUserDataKeySpecialPlaybackRequirements$AVMetadataQuickTimeUserDataKeyTaggedCharacteristic$AVMetadataQuickTimeUserDataKeyTrack$AVMetadataQuickTimeUserDataKeyTrackName$AVMetadataQuickTimeUserDataKeyURLLink$AVMetadataQuickTimeUserDataKeyWarning$AVMetadataQuickTimeUserDataKeyWriter$AVMetadataiTunesMetadataKeyAccountKind$AVMetadataiTunesMetadataKeyAcknowledgement$AVMetadataiTunesMetadataKeyAlbum$AVMetadataiTunesMetadataKeyAlbumArtist$AVMetadataiTunesMetadataKeyAppleID$AVMetadataiTunesMetadataKeyArranger$AVMetadataiTunesMetadataKeyArtDirector$AVMetadataiTunesMetadataKeyArtist$AVMetadataiTunesMetadataKeyArtistID$AVMetadataiTunesMetadataKeyAuthor$AVMetadataiTunesMetadataKeyBeatsPerMin$AVMetadataiTunesMetadataKeyComposer$AVMetadataiTunesMetadataKeyConductor$AVMetadataiTunesMetadataKeyContentRating$AVMetadataiTunesMetadataKeyCopyright$AVMetadataiTunesMetadataKeyCoverArt$AVMetadataiTunesMetadataKeyCredits$AVMetadataiTunesMetadataKeyDescription$AVMetadataiTunesMetadataKeyDirector$AVMetadataiTunesMetadataKeyDiscCompilation$AVMetadataiTunesMetadataKeyDiscNumber$AVMetadataiTunesMetadataKeyEQ$AVMetadataiTunesMetadataKeyEncodedBy$AVMetadataiTunesMetadataKeyEncodingTool$AVMetadataiTunesMetadataKeyExecProducer$AVMetadataiTunesMetadataKeyGenreID$AVMetadataiTunesMetadataKeyGrouping$AVMetadataiTunesMetadataKeyLinerNotes$AVMetadataiTunesMetadataKeyLyrics$AVMetadataiTunesMetadataKeyOnlineExtras$AVMetadataiTunesMetadataKeyOriginalArtist$AVMetadataiTunesMetadataKeyPerformer$AVMetadataiTunesMetadataKeyPhonogramRights$AVMetadataiTunesMetadataKeyPlaylistID$AVMetadataiTunesMetadataKeyPredefinedGenre$AVMetadataiTunesMetadataKeyProducer$AVMetadataiTunesMetadataKeyPublisher$AVMetadataiTunesMetadataKeyRecordCompany$AVMetadataiTunesMetadataKeyReleaseDate$AVMetadataiTunesMetadataKeySoloist$AVMetadataiTunesMetadataKeySongID$AVMetadataiTunesMetadataKeySongName$AVMetadataiTunesMetadataKeySoundEngineer$AVMetadataiTunesMetadataKeyThanks$AVMetadataiTunesMetadataKeyTrackNumber$AVMetadataiTunesMetadataKeyTrackSubTitle$AVMetadataiTunesMetadataKeyUserComment$AVMetadataiTunesMetadataKeyUserGenre$AVMovieReferenceRestrictionsKey$AVMovieShouldSupportAliasDataReferencesKey$AVNumberOfChannelsKey$AVOutputSettingsPreset1280x720$AVOutputSettingsPreset1920x1080$AVOutputSettingsPreset3840x2160$AVOutputSettingsPreset640x480$AVOutputSettingsPreset960x540$AVOutputSettingsPresetHEVC1920x1080$AVOutputSettingsPresetHEVC1920x1080WithAlpha$AVOutputSettingsPresetHEVC3840x2160$AVOutputSettingsPresetHEVC3840x2160WithAlpha$AVOutputSettingsPresetHEVC7680x4320$AVOutputSettingsPresetMVHEVC1440x1440$AVOutputSettingsPresetMVHEVC960x960$AVPlaybackCoordinatorOtherParticipantsDidChangeNotification$AVPlaybackCoordinatorSuspensionReasonsDidChangeNotification$AVPlayerAvailableHDRModesDidChangeNotification$AVPlayerEligibleForHDRPlaybackDidChangeNotification$AVPlayerIntegratedTimelineSnapshotsOutOfSyncNotification$AVPlayerIntegratedTimelineSnapshotsOutOfSyncReasonCurrentSegmentChanged$AVPlayerIntegratedTimelineSnapshotsOutOfSyncReasonKey$AVPlayerIntegratedTimelineSnapshotsOutOfSyncReasonLoadedTimeRangesChanged$AVPlayerIntegratedTimelineSnapshotsOutOfSyncReasonSegmentsChanged$AVPlayerInterstitialEventJoinCue$AVPlayerInterstitialEventLeaveCue$AVPlayerInterstitialEventMonitorAssetListResponseStatusDidChangeErrorKey$AVPlayerInterstitialEventMonitorAssetListResponseStatusDidChangeEventKey$AVPlayerInterstitialEventMonitorAssetListResponseStatusDidChangeNotification$AVPlayerInterstitialEventMonitorAssetListResponseStatusDidChangeStatusKey$AVPlayerInterstitialEventMonitorCurrentEventDidChangeNotification$AVPlayerInterstitialEventMonitorEventsDidChangeNotification$AVPlayerInterstitialEventNoCue$AVPlayerInterstitialEventObserverCurrentEventDidChangeNotification$AVPlayerItemDidPlayToEndTimeNotification$AVPlayerItemFailedToPlayToEndTimeErrorKey$AVPlayerItemFailedToPlayToEndTimeNotification$AVPlayerItemLegibleOutputTextStylingResolutionDefault$AVPlayerItemLegibleOutputTextStylingResolutionSourceAndRulesOnly$AVPlayerItemMediaSelectionDidChangeNotification$AVPlayerItemNewAccessLogEntryNotification$AVPlayerItemNewErrorLogEntryNotification$AVPlayerItemPlaybackStalledNotification$AVPlayerItemRecommendedTimeOffsetFromLiveDidChangeNotification$AVPlayerItemTimeJumpedNotification$AVPlayerItemTimeJumpedOriginatingParticipantKey$AVPlayerItemTrackVideoFieldModeDeinterlaceFields$AVPlayerRateDidChangeNotification$AVPlayerRateDidChangeOriginatingParticipantKey$AVPlayerRateDidChangeReasonAppBackgrounded$AVPlayerRateDidChangeReasonAudioSessionInterrupted$AVPlayerRateDidChangeReasonKey$AVPlayerRateDidChangeReasonSetRateCalled$AVPlayerRateDidChangeReasonSetRateFailed$AVPlayerWaitingDuringInterstitialEventReason$AVPlayerWaitingForCoordinatedPlaybackReason$AVPlayerWaitingToMinimizeStallsReason$AVPlayerWaitingWhileEvaluatingBufferingRateReason$AVPlayerWaitingWithNoItemToPlayReason$AVRouteDetectorMultipleRoutesDetectedDidChangeNotification$AVSampleBufferAudioRendererFlushTimeKey$AVSampleBufferAudioRendererOutputConfigurationDidChangeNotification$AVSampleBufferAudioRendererWasFlushedAutomaticallyNotification$AVSampleBufferDisplayLayerFailedToDecodeNotification$AVSampleBufferDisplayLayerFailedToDecodeNotificationErrorKey$AVSampleBufferDisplayLayerOutputObscuredDueToInsufficientExternalProtectionDidChangeNotification$AVSampleBufferDisplayLayerReadyForDisplayDidChangeNotification$AVSampleBufferDisplayLayerRequiresFlushToResumeDecodingDidChangeNotification$AVSampleBufferRenderSynchronizerRateDidChangeNotification$AVSampleBufferVideoRendererDidFailToDecodeNotification$AVSampleBufferVideoRendererDidFailToDecodeNotificationErrorKey$AVSampleBufferVideoRendererRequiresFlushToResumeDecodingDidChangeNotification$AVSampleRateConverterAlgorithmKey$AVSampleRateConverterAlgorithm_Mastering$AVSampleRateConverterAlgorithm_MinimumPhase$AVSampleRateConverterAlgorithm_Normal$AVSampleRateConverterAudioQualityKey$AVSampleRateKey$AVSemanticSegmentationMatteTypeGlasses$AVSemanticSegmentationMatteTypeHair$AVSemanticSegmentationMatteTypeSkin$AVSemanticSegmentationMatteTypeTeeth$AVSpatialCaptureDiscomfortReasonNotEnoughLight$AVSpatialCaptureDiscomfortReasonSubjectTooClose$AVSpeechSynthesisAvailableVoicesDidChangeNotification$AVSpeechSynthesisIPANotationAttribute$AVSpeechSynthesisVoiceIdentifierAlex$AVSpeechUtteranceDefaultSpeechRate@f$AVSpeechUtteranceMaximumSpeechRate@f$AVSpeechUtteranceMinimumSpeechRate@f$AVStreamingKeyDeliveryContentKeyType$AVStreamingKeyDeliveryPersistentContentKeyType$AVTrackAssociationTypeAudioFallback$AVTrackAssociationTypeChapterList$AVTrackAssociationTypeForcedSubtitlesOnly$AVTrackAssociationTypeMetadataReferent$AVTrackAssociationTypeSelectionFollower$AVTrackAssociationTypeTimecode$AVURLAssetAllowsCellularAccessKey$AVURLAssetAllowsConstrainedNetworkAccessKey$AVURLAssetAllowsExpensiveNetworkAccessKey$AVURLAssetHTTPCookiesKey$AVURLAssetHTTPUserAgentKey$AVURLAssetOverrideMIMETypeKey$AVURLAssetPreferPreciseDurationAndTimingKey$AVURLAssetPrimarySessionIdentifierKey$AVURLAssetReferenceRestrictionsKey$AVURLAssetShouldSupportAliasDataReferencesKey$AVURLAssetURLRequestAttributionKey$AVVideoAllowFrameReorderingKey$AVVideoAllowWideColorKey$AVVideoApertureModeCleanAperture$AVVideoApertureModeEncodedPixels$AVVideoApertureModeProductionAperture$AVVideoAppleProRAWBitDepthKey$AVVideoAverageBitRateKey$AVVideoAverageNonDroppableFrameRateKey$AVVideoCleanApertureHeightKey$AVVideoCleanApertureHorizontalOffsetKey$AVVideoCleanApertureKey$AVVideoCleanApertureVerticalOffsetKey$AVVideoCleanApertureWidthKey$AVVideoCodecAppleProRes422$AVVideoCodecAppleProRes4444$AVVideoCodecH264$AVVideoCodecHEVC$AVVideoCodecJPEG$AVVideoCodecKey$AVVideoCodecTypeAppleProRes422$AVVideoCodecTypeAppleProRes422HQ$AVVideoCodecTypeAppleProRes422LT$AVVideoCodecTypeAppleProRes422Proxy$AVVideoCodecTypeAppleProRes4444$AVVideoCodecTypeAppleProRes4444XQ$AVVideoCodecTypeH264$AVVideoCodecTypeHEVC$AVVideoCodecTypeHEVCWithAlpha$AVVideoCodecTypeJPEG$AVVideoCodecTypeJPEGXL$AVVideoColorPrimariesKey$AVVideoColorPrimaries_EBU_3213$AVVideoColorPrimaries_ITU_R_2020$AVVideoColorPrimaries_ITU_R_709_2$AVVideoColorPrimaries_P3_D65$AVVideoColorPrimaries_SMPTE_C$AVVideoColorPropertiesKey$AVVideoCompositionPerFrameHDRDisplayMetadataPolicyGenerate$AVVideoCompositionPerFrameHDRDisplayMetadataPolicyPropagate$AVVideoCompressionPropertiesKey$AVVideoDecompressionPropertiesKey$AVVideoEncoderSpecificationKey$AVVideoExpectedSourceFrameRateKey$AVVideoH264EntropyModeCABAC$AVVideoH264EntropyModeCAVLC$AVVideoH264EntropyModeKey$AVVideoHeightKey$AVVideoMaxKeyFrameIntervalDurationKey$AVVideoMaxKeyFrameIntervalKey$AVVideoPixelAspectRatioHorizontalSpacingKey$AVVideoPixelAspectRatioKey$AVVideoPixelAspectRatioVerticalSpacingKey$AVVideoProfileLevelH264Baseline30$AVVideoProfileLevelH264Baseline31$AVVideoProfileLevelH264Baseline41$AVVideoProfileLevelH264BaselineAutoLevel$AVVideoProfileLevelH264High40$AVVideoProfileLevelH264High41$AVVideoProfileLevelH264HighAutoLevel$AVVideoProfileLevelH264Main30$AVVideoProfileLevelH264Main31$AVVideoProfileLevelH264Main32$AVVideoProfileLevelH264Main41$AVVideoProfileLevelH264MainAutoLevel$AVVideoProfileLevelKey$AVVideoQualityKey$AVVideoRangeHLG$AVVideoRangePQ$AVVideoRangeSDR$AVVideoScalingModeFit$AVVideoScalingModeKey$AVVideoScalingModeResize$AVVideoScalingModeResizeAspect$AVVideoScalingModeResizeAspectFill$AVVideoTransferFunctionKey$AVVideoTransferFunction_IEC_sRGB$AVVideoTransferFunction_ITU_R_2100_HLG$AVVideoTransferFunction_ITU_R_709_2$AVVideoTransferFunction_Linear$AVVideoTransferFunction_SMPTE_240M_1995$AVVideoTransferFunction_SMPTE_ST_2084_PQ$AVVideoWidthKey$AVVideoYCbCrMatrixKey$AVVideoYCbCrMatrix_ITU_R_2020$AVVideoYCbCrMatrix_ITU_R_601_4$AVVideoYCbCrMatrix_ITU_R_709_2$AVVideoYCbCrMatrix_SMPTE_240M_1995$"""
enums = """$AVAUDIOENGINE_HAVE_AUAUDIOUNIT@1$AVAUDIOENGINE_HAVE_MUSICPLAYER@1$AVAUDIOFORMAT_HAVE_CMFORMATDESCRIPTION@1$AVAUDIOIONODE_HAVE_AUDIOUNIT@1$AVAUDIONODE_HAVE_AUAUDIOUNIT@1$AVAUDIOUNITCOMPONENT_HAVE_AUDIOCOMPONENT@1$AVAUDIOUNIT_HAVE_AUDIOUNIT@1$AVAssetExportSessionStatusCancelled@5$AVAssetExportSessionStatusCompleted@3$AVAssetExportSessionStatusExporting@2$AVAssetExportSessionStatusFailed@4$AVAssetExportSessionStatusUnknown@0$AVAssetExportSessionStatusWaiting@1$AVAssetImageGeneratorCancelled@2$AVAssetImageGeneratorFailed@1$AVAssetImageGeneratorSucceeded@0$AVAssetReaderStatusCancelled@4$AVAssetReaderStatusCompleted@2$AVAssetReaderStatusFailed@3$AVAssetReaderStatusReading@1$AVAssetReaderStatusUnknown@0$AVAssetReferenceRestrictionDefaultPolicy@2$AVAssetReferenceRestrictionForbidAll@65535$AVAssetReferenceRestrictionForbidCrossSiteReference@4$AVAssetReferenceRestrictionForbidLocalReferenceToLocal@8$AVAssetReferenceRestrictionForbidLocalReferenceToRemote@2$AVAssetReferenceRestrictionForbidNone@0$AVAssetReferenceRestrictionForbidRemoteReferenceToLocal@1$AVAssetSegmentTypeInitialization@1$AVAssetSegmentTypeSeparable@2$AVAssetTrackGroupOutputHandlingDefaultPolicy@0$AVAssetTrackGroupOutputHandlingNone@0$AVAssetTrackGroupOutputHandlingPreserveAlternateTracks@1$AVAssetWriterStatusCancelled@4$AVAssetWriterStatusCompleted@2$AVAssetWriterStatusFailed@3$AVAssetWriterStatusUnknown@0$AVAssetWriterStatusWriting@1$AVAudio3DMixingPointSourceInHeadModeBypass@1$AVAudio3DMixingPointSourceInHeadModeMono@0$AVAudio3DMixingRenderingAlgorithmAuto@7$AVAudio3DMixingRenderingAlgorithmEqualPowerPanning@0$AVAudio3DMixingRenderingAlgorithmHRTF@2$AVAudio3DMixingRenderingAlgorithmHRTFHQ@6$AVAudio3DMixingRenderingAlgorithmSoundField@3$AVAudio3DMixingRenderingAlgorithmSphericalHead@1$AVAudio3DMixingRenderingAlgorithmStereoPassThrough@5$AVAudio3DMixingSourceModeAmbienceBed@3$AVAudio3DMixingSourceModeBypass@1$AVAudio3DMixingSourceModePointSource@2$AVAudio3DMixingSourceModeSpatializeIfMono@0$AVAudioApplicationMicrophoneInjectionPermissionDenied@1684369017$AVAudioApplicationMicrophoneInjectionPermissionGranted@1735552628$AVAudioApplicationMicrophoneInjectionPermissionServiceDisabled@1936876659$AVAudioApplicationMicrophoneInjectionPermissionUndetermined@1970168948$AVAudioApplicationRecordPermissionDenied@1684369017$AVAudioApplicationRecordPermissionGranted@1735552628$AVAudioApplicationRecordPermissionUndetermined@1970168948$AVAudioConverterInputStatus_EndOfStream@2$AVAudioConverterInputStatus_HaveData@0$AVAudioConverterInputStatus_NoDataNow@1$AVAudioConverterOutputStatus_EndOfStream@2$AVAudioConverterOutputStatus_Error@3$AVAudioConverterOutputStatus_HaveData@0$AVAudioConverterOutputStatus_InputRanDry@1$AVAudioConverterPrimeMethod_None@2$AVAudioConverterPrimeMethod_Normal@1$AVAudioConverterPrimeMethod_Pre@0$AVAudioEngineManualRenderingErrorInitialized@-80801$AVAudioEngineManualRenderingErrorInvalidMode@-80800$AVAudioEngineManualRenderingErrorNotRunning@-80802$AVAudioEngineManualRenderingModeOffline@0$AVAudioEngineManualRenderingModeRealtime@1$AVAudioEngineManualRenderingStatusCannotDoInCurrentContext@2$AVAudioEngineManualRenderingStatusError@-1$AVAudioEngineManualRenderingStatusInsufficientDataFromInputNode@1$AVAudioEngineManualRenderingStatusSuccess@0$AVAudioEnvironmentDistanceAttenuationModelExponential@1$AVAudioEnvironmentDistanceAttenuationModelInverse@2$AVAudioEnvironmentDistanceAttenuationModelLinear@3$AVAudioEnvironmentOutputTypeAuto@0$AVAudioEnvironmentOutputTypeBuiltInSpeakers@2$AVAudioEnvironmentOutputTypeExternalSpeakers@3$AVAudioEnvironmentOutputTypeHeadphones@1$AVAudioOtherFormat@0$AVAudioPCMFormatFloat32@1$AVAudioPCMFormatFloat64@2$AVAudioPCMFormatInt16@3$AVAudioPCMFormatInt32@4$AVAudioPlayerNodeBufferInterrupts@2$AVAudioPlayerNodeBufferInterruptsAtLoop@4$AVAudioPlayerNodeBufferLoops@1$AVAudioPlayerNodeCompletionDataConsumed@0$AVAudioPlayerNodeCompletionDataPlayedBack@2$AVAudioPlayerNodeCompletionDataRendered@1$AVAudioQualityHigh@96$AVAudioQualityLow@32$AVAudioQualityMax@127$AVAudioQualityMedium@64$AVAudioQualityMin@0$AVAudioRoutingArbitrationCategoryPlayAndRecord@1$AVAudioRoutingArbitrationCategoryPlayAndRecordVoice@2$AVAudioRoutingArbitrationCategoryPlayback@0$AVAudioSessionActivationOptionNone@0$AVAudioSessionCategoryOptionAllowAirPlay@64$AVAudioSessionCategoryOptionAllowBluetooth@4$AVAudioSessionCategoryOptionAllowBluetoothA2DP@32$AVAudioSessionCategoryOptionDefaultToSpeaker@8$AVAudioSessionCategoryOptionDuckOthers@2$AVAudioSessionCategoryOptionInterruptSpokenAudioAndMixWithOthers@17$AVAudioSessionCategoryOptionMixWithOthers@1$AVAudioSessionIOTypeAggregated@1$AVAudioSessionIOTypeNotSpecified@0$AVAudioSessionInterruptionFlags_ShouldResume@1$AVAudioSessionInterruptionOptionShouldResume@1$AVAudioSessionInterruptionReasonAppWasSuspended@1$AVAudioSessionInterruptionReasonBuiltInMicMuted@2$AVAudioSessionInterruptionReasonDefault@0$AVAudioSessionInterruptionReasonDeviceUnauthenticated@5$AVAudioSessionInterruptionTypeBegan@1$AVAudioSessionInterruptionTypeEnded@0$AVAudioSessionMicrophoneInjectionModeNone@0$AVAudioSessionMicrophoneInjectionModeSpokenAudio@1$AVAudioSessionPortOverrideNone@0$AVAudioSessionPortOverrideSpeaker@1936747378$AVAudioSessionPromptStyleNone@1852796517$AVAudioSessionPromptStyleNormal@1852992876$AVAudioSessionPromptStyleShort@1936224884$AVAudioSessionRecordPermissionDenied@1684369017$AVAudioSessionRecordPermissionGranted@1735552628$AVAudioSessionRecordPermissionUndetermined@1970168948$AVAudioSessionRenderingModeDolbyAtmos@5$AVAudioSessionRenderingModeDolbyAudio@4$AVAudioSessionRenderingModeMonoStereo@1$AVAudioSessionRenderingModeNotApplicable@0$AVAudioSessionRenderingModeSpatialAudio@3$AVAudioSessionRenderingModeSurround@2$AVAudioSessionRouteChangeReasonCategoryChange@3$AVAudioSessionRouteChangeReasonNewDeviceAvailable@1$AVAudioSessionRouteChangeReasonNoSuitableRouteForCategory@7$AVAudioSessionRouteChangeReasonOldDeviceUnavailable@2$AVAudioSessionRouteChangeReasonOverride@4$AVAudioSessionRouteChangeReasonRouteConfigurationChange@8$AVAudioSessionRouteChangeReasonUnknown@0$AVAudioSessionRouteChangeReasonWakeFromSleep@6$AVAudioSessionRouteSharingPolicyDefault@0$AVAudioSessionRouteSharingPolicyIndependent@2$AVAudioSessionRouteSharingPolicyLongForm@1$AVAudioSessionRouteSharingPolicyLongFormAudio@1$AVAudioSessionRouteSharingPolicyLongFormVideo@3$AVAudioSessionSetActiveFlags_NotifyOthersOnDeactivation@1$AVAudioSessionSetActiveOptionNotifyOthersOnDeactivation@1$AVAudioSessionSilenceSecondaryAudioHintTypeBegin@1$AVAudioSessionSilenceSecondaryAudioHintTypeEnd@0$AVAudioSpatializationFormatMonoAndStereo@3$AVAudioSpatializationFormatMonoStereoAndMultichannel@7$AVAudioSpatializationFormatMultichannel@4$AVAudioSpatializationFormatNone@0$AVAudioStereoOrientationLandscapeLeft@4$AVAudioStereoOrientationLandscapeRight@3$AVAudioStereoOrientationNone@0$AVAudioStereoOrientationPortrait@1$AVAudioStereoOrientationPortraitUpsideDown@2$AVAudioUnitDistortionPresetDrumsBitBrush@0$AVAudioUnitDistortionPresetDrumsBufferBeats@1$AVAudioUnitDistortionPresetDrumsLoFi@2$AVAudioUnitDistortionPresetMultiBrokenSpeaker@3$AVAudioUnitDistortionPresetMultiCellphoneConcert@4$AVAudioUnitDistortionPresetMultiDecimated1@5$AVAudioUnitDistortionPresetMultiDecimated2@6$AVAudioUnitDistortionPresetMultiDecimated3@7$AVAudioUnitDistortionPresetMultiDecimated4@8$AVAudioUnitDistortionPresetMultiDistortedCubed@10$AVAudioUnitDistortionPresetMultiDistortedFunk@9$AVAudioUnitDistortionPresetMultiDistortedSquared@11$AVAudioUnitDistortionPresetMultiEcho1@12$AVAudioUnitDistortionPresetMultiEcho2@13$AVAudioUnitDistortionPresetMultiEchoTight1@14$AVAudioUnitDistortionPresetMultiEchoTight2@15$AVAudioUnitDistortionPresetMultiEverythingIsBroken@16$AVAudioUnitDistortionPresetSpeechAlienChatter@17$AVAudioUnitDistortionPresetSpeechCosmicInterference@18$AVAudioUnitDistortionPresetSpeechGoldenPi@19$AVAudioUnitDistortionPresetSpeechRadioTower@20$AVAudioUnitDistortionPresetSpeechWaves@21$AVAudioUnitEQFilterTypeBandPass@5$AVAudioUnitEQFilterTypeBandStop@6$AVAudioUnitEQFilterTypeHighPass@2$AVAudioUnitEQFilterTypeHighShelf@8$AVAudioUnitEQFilterTypeLowPass@1$AVAudioUnitEQFilterTypeLowShelf@7$AVAudioUnitEQFilterTypeParametric@0$AVAudioUnitEQFilterTypeResonantHighPass@4$AVAudioUnitEQFilterTypeResonantHighShelf@10$AVAudioUnitEQFilterTypeResonantLowPass@3$AVAudioUnitEQFilterTypeResonantLowShelf@9$AVAudioUnitReverbPresetCathedral@8$AVAudioUnitReverbPresetLargeChamber@7$AVAudioUnitReverbPresetLargeHall@4$AVAudioUnitReverbPresetLargeHall2@12$AVAudioUnitReverbPresetLargeRoom@2$AVAudioUnitReverbPresetLargeRoom2@9$AVAudioUnitReverbPresetMediumChamber@6$AVAudioUnitReverbPresetMediumHall@3$AVAudioUnitReverbPresetMediumHall2@10$AVAudioUnitReverbPresetMediumHall3@11$AVAudioUnitReverbPresetMediumRoom@1$AVAudioUnitReverbPresetPlate@5$AVAudioUnitReverbPresetSmallRoom@0$AVAudioVoiceProcessingOtherAudioDuckingLevelDefault@0$AVAudioVoiceProcessingOtherAudioDuckingLevelMax@30$AVAudioVoiceProcessingOtherAudioDuckingLevelMid@20$AVAudioVoiceProcessingOtherAudioDuckingLevelMin@10$AVAudioVoiceProcessingSpeechActivityEnded@1$AVAudioVoiceProcessingSpeechActivityStarted@0$AVAuthorizationStatusAuthorized@3$AVAuthorizationStatusDenied@2$AVAuthorizationStatusNotDetermined@0$AVAuthorizationStatusRestricted@1$AVCaptionAnimationCharacterReveal@1$AVCaptionAnimationNone@0$AVCaptionConversionValidatorStatusCompleted@2$AVCaptionConversionValidatorStatusStopped@3$AVCaptionConversionValidatorStatusUnknown@0$AVCaptionConversionValidatorStatusValidating@1$AVCaptionDecorationLineThrough@2$AVCaptionDecorationNone@0$AVCaptionDecorationOverline@4$AVCaptionDecorationUnderline@1$AVCaptionFontStyleItalic@2$AVCaptionFontStyleNormal@1$AVCaptionFontStyleUnknown@0$AVCaptionFontWeightBold@2$AVCaptionFontWeightNormal@1$AVCaptionFontWeightUnknown@0$AVCaptionRegionDisplayAlignmentAfter@2$AVCaptionRegionDisplayAlignmentBefore@0$AVCaptionRegionDisplayAlignmentCenter@1$AVCaptionRegionScrollNone@0$AVCaptionRegionScrollRollUp@1$AVCaptionRegionWritingModeLeftToRightAndTopToBottom@0$AVCaptionRegionWritingModeTopToBottomAndRightToLeft@2$AVCaptionRubyAlignmentCenter@1$AVCaptionRubyAlignmentDistributeSpaceAround@3$AVCaptionRubyAlignmentDistributeSpaceBetween@2$AVCaptionRubyAlignmentStart@0$AVCaptionRubyPositionAfter@1$AVCaptionRubyPositionBefore@0$AVCaptionTextAlignmentCenter@2$AVCaptionTextAlignmentEnd@1$AVCaptionTextAlignmentLeft@3$AVCaptionTextAlignmentRight@4$AVCaptionTextAlignmentStart@0$AVCaptionTextCombineAll@-1$AVCaptionTextCombineFourDigits@4$AVCaptionTextCombineNone@0$AVCaptionTextCombineOneDigit@1$AVCaptionTextCombineThreeDigits@3$AVCaptionTextCombineTwoDigits@2$AVCaptionUnitsTypeCells@1$AVCaptionUnitsTypePercent@2$AVCaptionUnitsTypeUnspecified@0$AVCaptureAutoFocusRangeRestrictionFar@2$AVCaptureAutoFocusRangeRestrictionNear@1$AVCaptureAutoFocusRangeRestrictionNone@0$AVCaptureAutoFocusSystemContrastDetection@1$AVCaptureAutoFocusSystemNone@0$AVCaptureAutoFocusSystemPhaseDetection@2$AVCaptureCenterStageControlModeApp@1$AVCaptureCenterStageControlModeCooperative@2$AVCaptureCenterStageControlModeUser@0$AVCaptureColorSpace_AppleLog@3$AVCaptureColorSpace_HLG_BT2020@2$AVCaptureColorSpace_P3_D65@1$AVCaptureColorSpace_sRGB@0$AVCaptureDevicePositionBack@1$AVCaptureDevicePositionFront@2$AVCaptureDevicePositionUnspecified@0$AVCaptureDeviceTransportControlsNotPlayingMode@0$AVCaptureDeviceTransportControlsPlayingMode@1$AVCaptureExposureModeAutoExpose@1$AVCaptureExposureModeContinuousAutoExposure@2$AVCaptureExposureModeCustom@3$AVCaptureExposureModeLocked@0$AVCaptureFlashModeAuto@2$AVCaptureFlashModeOff@0$AVCaptureFlashModeOn@1$AVCaptureFocusModeAutoFocus@1$AVCaptureFocusModeContinuousAutoFocus@2$AVCaptureFocusModeLocked@0$AVCaptureLensStabilizationStatusActive@2$AVCaptureLensStabilizationStatusOff@1$AVCaptureLensStabilizationStatusOutOfRange@3$AVCaptureLensStabilizationStatusUnavailable@4$AVCaptureLensStabilizationStatusUnsupported@0$AVCaptureMicrophoneModeStandard@0$AVCaptureMicrophoneModeVoiceIsolation@2$AVCaptureMicrophoneModeWideSpectrum@1$AVCaptureMultichannelAudioModeFirstOrderAmbisonics@2$AVCaptureMultichannelAudioModeNone@0$AVCaptureMultichannelAudioModeStereo@1$AVCaptureOutputDataDroppedReasonDiscontinuity@3$AVCaptureOutputDataDroppedReasonLateData@1$AVCaptureOutputDataDroppedReasonNone@0$AVCaptureOutputDataDroppedReasonOutOfBuffers@2$AVCapturePhotoOutputCaptureReadinessNotReadyMomentarily@2$AVCapturePhotoOutputCaptureReadinessNotReadyWaitingForCapture@3$AVCapturePhotoOutputCaptureReadinessNotReadyWaitingForProcessing@4$AVCapturePhotoOutputCaptureReadinessReady@1$AVCapturePhotoOutputCaptureReadinessSessionNotRunning@0$AVCapturePhotoQualityPrioritizationBalanced@2$AVCapturePhotoQualityPrioritizationQuality@3$AVCapturePhotoQualityPrioritizationSpeed@1$AVCapturePrimaryConstituentDeviceRestrictedSwitchingBehaviorConditionExposureModeChanged@4$AVCapturePrimaryConstituentDeviceRestrictedSwitchingBehaviorConditionFocusModeChanged@2$AVCapturePrimaryConstituentDeviceRestrictedSwitchingBehaviorConditionNone@0$AVCapturePrimaryConstituentDeviceRestrictedSwitchingBehaviorConditionVideoZoomChanged@1$AVCapturePrimaryConstituentDeviceSwitchingBehaviorAuto@1$AVCapturePrimaryConstituentDeviceSwitchingBehaviorLocked@3$AVCapturePrimaryConstituentDeviceSwitchingBehaviorRestricted@2$AVCapturePrimaryConstituentDeviceSwitchingBehaviorUnsupported@0$AVCaptureSessionInterruptionReasonAudioDeviceInUseByAnotherClient@2$AVCaptureSessionInterruptionReasonVideoDeviceInUseByAnotherClient@3$AVCaptureSessionInterruptionReasonVideoDeviceNotAvailableDueToSystemPressure@5$AVCaptureSessionInterruptionReasonVideoDeviceNotAvailableInBackground@1$AVCaptureSessionInterruptionReasonVideoDeviceNotAvailableWithMultipleForegroundApps@4$AVCaptureSystemPressureFactorCameraTemperature@8$AVCaptureSystemPressureFactorDepthModuleTemperature@4$AVCaptureSystemPressureFactorNone@0$AVCaptureSystemPressureFactorPeakPower@2$AVCaptureSystemPressureFactorSystemTemperature@1$AVCaptureSystemUserInterfaceMicrophoneModes@2$AVCaptureSystemUserInterfaceVideoEffects@1$AVCaptureTorchModeAuto@2$AVCaptureTorchModeOff@0$AVCaptureTorchModeOn@1$AVCaptureVideoOrientationLandscapeLeft@4$AVCaptureVideoOrientationLandscapeRight@3$AVCaptureVideoOrientationPortrait@1$AVCaptureVideoOrientationPortraitUpsideDown@2$AVCaptureVideoStabilizationModeAuto@-1$AVCaptureVideoStabilizationModeCinematic@2$AVCaptureVideoStabilizationModeCinematicExtended@3$AVCaptureVideoStabilizationModeCinematicExtendedEnhanced@5$AVCaptureVideoStabilizationModeOff@0$AVCaptureVideoStabilizationModePreviewOptimized@4$AVCaptureVideoStabilizationModeStandard@1$AVCaptureWhiteBalanceModeAutoWhiteBalance@1$AVCaptureWhiteBalanceModeContinuousAutoWhiteBalance@2$AVCaptureWhiteBalanceModeLocked@0$AVContentAuthorizationBusy@4$AVContentAuthorizationCancelled@2$AVContentAuthorizationCompleted@1$AVContentAuthorizationNotAvailable@5$AVContentAuthorizationNotPossible@6$AVContentAuthorizationTimedOut@3$AVContentAuthorizationUnknown@0$AVContentKeyRequestStatusCancelled@4$AVContentKeyRequestStatusFailed@5$AVContentKeyRequestStatusReceivedResponse@1$AVContentKeyRequestStatusRenewed@2$AVContentKeyRequestStatusRequestingResponse@0$AVContentKeyRequestStatusRetried@3$AVDelegatingPlaybackCoordinatorRateChangeOptionPlayImmediately@1$AVDelegatingPlaybackCoordinatorSeekOptionResumeImmediately@1$AVDepthDataAccuracyAbsolute@1$AVDepthDataAccuracyRelative@0$AVDepthDataQualityHigh@1$AVDepthDataQualityLow@0$AVErrorAirPlayControllerRequiresInternet@-11856$AVErrorAirPlayReceiverRequiresInternet@-11857$AVErrorAirPlayReceiverTemporarilyUnavailable@-11882$AVErrorApplicationIsNotAuthorized@-11836$AVErrorApplicationIsNotAuthorizedToUseDevice@-11852$AVErrorCompositionTrackSegmentsNotContiguous@-11824$AVErrorContentIsNotAuthorized@-11835$AVErrorContentIsProtected@-11831$AVErrorContentIsUnavailable@-11863$AVErrorContentKeyRequestCancelled@-11879$AVErrorContentNotUpdated@-11866$AVErrorCreateContentKeyRequestFailed@-11860$AVErrorDecodeFailed@-11821$AVErrorDecoderNotFound@-11833$AVErrorDecoderTemporarilyUnavailable@-11839$AVErrorDeviceAlreadyUsedByAnotherSession@-11804$AVErrorDeviceInUseByAnotherApplication@-11815$AVErrorDeviceLockedForConfigurationByAnotherProcess@-11817$AVErrorDeviceNotConnected@-11814$AVErrorDeviceWasDisconnected@-11808$AVErrorDiskFull@-11807$AVErrorDisplayWasDisabled@-11845$AVErrorEncodeFailed@-11883$AVErrorEncoderNotFound@-11834$AVErrorEncoderTemporarilyUnavailable@-11840$AVErrorExportFailed@-11820$AVErrorExternalPlaybackNotSupportedForAsset@-11870$AVErrorFailedToLoadMediaData@-11849$AVErrorFailedToLoadSampleData@-11881$AVErrorFailedToParse@-11853$AVErrorFileAlreadyExists@-11823$AVErrorFileFailedToParse@-11829$AVErrorFileFormatNotRecognized@-11828$AVErrorFileTypeDoesNotSupportSampleReferences@-11854$AVErrorFormatUnsupported@-11864$AVErrorIncompatibleAsset@-11848$AVErrorIncorrectlyConfigured@-11875$AVErrorInvalidCompositionTrackSegmentDuration@-11825$AVErrorInvalidCompositionTrackSegmentSourceDuration@-11827$AVErrorInvalidCompositionTrackSegmentSourceStartTime@-11826$AVErrorInvalidOutputURLPathExtension@-11843$AVErrorInvalidSampleCursor@-11880$AVErrorInvalidSourceMedia@-11822$AVErrorInvalidVideoComposition@-11841$AVErrorMalformedDepth@-11865$AVErrorMaximumDurationReached@-11810$AVErrorMaximumFileSizeReached@-11811$AVErrorMaximumNumberOfSamplesForFileFormatReached@-11813$AVErrorMaximumStillImageCaptureRequestsExceeded@-11830$AVErrorMediaChanged@-11809$AVErrorMediaDiscontinuity@-11812$AVErrorMediaExtensionConflict@-11887$AVErrorMediaExtensionDisabled@-11886$AVErrorNoCompatibleAlternatesForExternalDisplay@-11868$AVErrorNoDataCaptured@-11805$AVErrorNoImageAtTime@-11832$AVErrorNoLongerPlayable@-11867$AVErrorNoSourceTrack@-11869$AVErrorOperationCancelled@-11878$AVErrorOperationNotAllowed@-11862$AVErrorOperationNotSupportedForAsset@-11838$AVErrorOperationNotSupportedForPreset@-11871$AVErrorOutOfMemory@-11801$AVErrorRecordingAlreadyInProgress@-11859$AVErrorReferenceForbiddenByReferencePolicy@-11842$AVErrorRosettaNotInstalled@-11877$AVErrorSandboxExtensionDenied@-11884$AVErrorScreenCaptureFailed@-11844$AVErrorSegmentStartedWithNonSyncSample@-11876$AVErrorServerIncorrectlyConfigured@-11850$AVErrorSessionConfigurationChanged@-11806$AVErrorSessionHardwareCostOverage@-11872$AVErrorSessionNotRunning@-11803$AVErrorToneMappingFailed@-11885$AVErrorTorchLevelUnavailable@-11846$AVErrorUndecodableMediaData@-11855$AVErrorUnknown@-11800$AVErrorUnsupportedDeviceActiveFormat@-11873$AVErrorUnsupportedOutputSettings@-11861$AVErrorVideoCompositorFailed@-11858$AVExternalContentProtectionStatusInsufficient@2$AVExternalContentProtectionStatusPending@0$AVExternalContentProtectionStatusSufficient@1$AVKeyValueStatusCancelled@4$AVKeyValueStatusFailed@3$AVKeyValueStatusLoaded@2$AVKeyValueStatusLoading@1$AVKeyValueStatusUnknown@0$AVMIDIControlChangeMessageTypeAllNotesOff@123$AVMIDIControlChangeMessageTypeAllSoundOff@120$AVMIDIControlChangeMessageTypeAttackTime@73$AVMIDIControlChangeMessageTypeBalance@8$AVMIDIControlChangeMessageTypeBankSelect@0$AVMIDIControlChangeMessageTypeBreath@2$AVMIDIControlChangeMessageTypeBrightness@74$AVMIDIControlChangeMessageTypeChorusLevel@93$AVMIDIControlChangeMessageTypeDataEntry@6$AVMIDIControlChangeMessageTypeDecayTime@75$AVMIDIControlChangeMessageTypeExpression@11$AVMIDIControlChangeMessageTypeFilterResonance@71$AVMIDIControlChangeMessageTypeFoot@4$AVMIDIControlChangeMessageTypeHold2Pedal@69$AVMIDIControlChangeMessageTypeLegatoPedal@68$AVMIDIControlChangeMessageTypeModWheel@1$AVMIDIControlChangeMessageTypeMonoModeOff@127$AVMIDIControlChangeMessageTypeMonoModeOn@126$AVMIDIControlChangeMessageTypeOmniModeOff@124$AVMIDIControlChangeMessageTypeOmniModeOn@125$AVMIDIControlChangeMessageTypePan@10$AVMIDIControlChangeMessageTypePortamento@65$AVMIDIControlChangeMessageTypePortamentoTime@5$AVMIDIControlChangeMessageTypeRPN_LSB@100$AVMIDIControlChangeMessageTypeRPN_MSB@101$AVMIDIControlChangeMessageTypeReleaseTime@72$AVMIDIControlChangeMessageTypeResetAllControllers@121$AVMIDIControlChangeMessageTypeReverbLevel@91$AVMIDIControlChangeMessageTypeSoft@67$AVMIDIControlChangeMessageTypeSostenuto@66$AVMIDIControlChangeMessageTypeSustain@64$AVMIDIControlChangeMessageTypeVibratoDelay@78$AVMIDIControlChangeMessageTypeVibratoDepth@77$AVMIDIControlChangeMessageTypeVibratoRate@76$AVMIDIControlChangeMessageTypeVolume@7$AVMIDIMetaEventTypeCopyright@2$AVMIDIMetaEventTypeCuePoint@7$AVMIDIMetaEventTypeEndOfTrack@47$AVMIDIMetaEventTypeInstrument@4$AVMIDIMetaEventTypeKeySignature@89$AVMIDIMetaEventTypeLyric@5$AVMIDIMetaEventTypeMarker@6$AVMIDIMetaEventTypeMidiChannel@32$AVMIDIMetaEventTypeMidiPort@33$AVMIDIMetaEventTypeProprietaryEvent@127$AVMIDIMetaEventTypeSequenceNumber@0$AVMIDIMetaEventTypeSmpteOffset@84$AVMIDIMetaEventTypeTempo@81$AVMIDIMetaEventTypeText@1$AVMIDIMetaEventTypeTimeSignature@88$AVMIDIMetaEventTypeTrackName@3$AVMovieWritingAddMovieHeaderToDestination@0$AVMovieWritingTruncateDestinationToMovieHeaderOnly@1$AVMusicSequenceLoadSMF_ChannelsToTracks@1$AVMusicSequenceLoadSMF_PreserveTracks@0$AVMusicTrackLoopCountForever@-1$AVPlayerActionAtItemEndAdvance@0$AVPlayerActionAtItemEndNone@2$AVPlayerActionAtItemEndPause@1$AVPlayerAudiovisualBackgroundPlaybackPolicyAutomatic@1$AVPlayerAudiovisualBackgroundPlaybackPolicyContinuesIfPossible@3$AVPlayerAudiovisualBackgroundPlaybackPolicyPauses@2$AVPlayerHDRModeDolbyVision@4$AVPlayerHDRModeHDR10@2$AVPlayerHDRModeHLG@1$AVPlayerInterstitialEventAssetListResponseStatusAvailable@0$AVPlayerInterstitialEventAssetListResponseStatusCleared@1$AVPlayerInterstitialEventAssetListResponseStatusUnavailable@2$AVPlayerInterstitialEventRestrictionConstrainsSeekingForwardInPrimaryContent@1$AVPlayerInterstitialEventRestrictionDefaultPolicy@0$AVPlayerInterstitialEventRestrictionNone@0$AVPlayerInterstitialEventRestrictionRequiresPlaybackAtPreferredRateForAdvancement@4$AVPlayerInterstitialEventTimelineOccupancyFill@1$AVPlayerInterstitialEventTimelineOccupancySinglePoint@0$AVPlayerItemSegmentTypeInterstitial@1$AVPlayerItemSegmentTypePrimary@0$AVPlayerItemStatusFailed@2$AVPlayerItemStatusReadyToPlay@1$AVPlayerItemStatusUnknown@0$AVPlayerLooperItemOrderingLoopingItemsFollowExistingItems@1$AVPlayerLooperItemOrderingLoopingItemsPrecedeExistingItems@0$AVPlayerLooperStatusCancelled@3$AVPlayerLooperStatusFailed@2$AVPlayerLooperStatusReady@1$AVPlayerLooperStatusUnknown@0$AVPlayerStatusFailed@2$AVPlayerStatusReadyToPlay@1$AVPlayerStatusUnknown@0$AVPlayerTimeControlStatusPaused@0$AVPlayerTimeControlStatusPlaying@2$AVPlayerTimeControlStatusWaitingToPlayAtSpecifiedRate@1$AVQueuedSampleBufferRenderingStatusFailed@2$AVQueuedSampleBufferRenderingStatusRendering@1$AVQueuedSampleBufferRenderingStatusUnknown@0$AVSampleBufferRequestDirectionForward@1$AVSampleBufferRequestDirectionNone@0$AVSampleBufferRequestDirectionReverse@-1$AVSampleBufferRequestModeImmediate@0$AVSampleBufferRequestModeOpportunistic@2$AVSampleBufferRequestModeScheduled@1$AVSpeechBoundaryImmediate@0$AVSpeechBoundaryWord@1$AVSpeechSynthesisMarkerMarkBookmark@4$AVSpeechSynthesisMarkerMarkParagraph@3$AVSpeechSynthesisMarkerMarkPhoneme@0$AVSpeechSynthesisMarkerMarkSentence@2$AVSpeechSynthesisMarkerMarkWord@1$AVSpeechSynthesisPersonalVoiceAuthorizationStatusAuthorized@3$AVSpeechSynthesisPersonalVoiceAuthorizationStatusDenied@1$AVSpeechSynthesisPersonalVoiceAuthorizationStatusNotDetermined@0$AVSpeechSynthesisPersonalVoiceAuthorizationStatusUnsupported@2$AVSpeechSynthesisVoiceGenderFemale@2$AVSpeechSynthesisVoiceGenderMale@1$AVSpeechSynthesisVoiceGenderUnspecified@0$AVSpeechSynthesisVoiceQualityDefault@1$AVSpeechSynthesisVoiceQualityEnhanced@2$AVSpeechSynthesisVoiceQualityPremium@3$AVSpeechSynthesisVoiceTraitIsNoveltyVoice@1$AVSpeechSynthesisVoiceTraitIsPersonalVoice@2$AVSpeechSynthesisVoiceTraitNone@0$AVVariantPreferenceNone@0$AVVariantPreferenceScalabilityToLosslessAudio@1$AVVideoFieldModeBoth@0$AVVideoFieldModeBottomOnly@2$AVVideoFieldModeDeinterlace@3$AVVideoFieldModeTopOnly@1$kCMTagCollectionVideoOutputPreset_Monoscopic@0$kCMTagCollectionVideoOutputPreset_Stereoscopic@1$"""
misc.update(
    {
        "AVPlayerAudiovisualBackgroundPlaybackPolicy": NewType(
            "AVPlayerAudiovisualBackgroundPlaybackPolicy", int
        ),
        "AVSpeechSynthesisVoiceGender": NewType("AVSpeechSynthesisVoiceGender", int),
        "AVAudioSessionIOType": NewType("AVAudioSessionIOType", int),
        "AVCaptureMultichannelAudioMode": NewType(
            "AVCaptureMultichannelAudioMode", int
        ),
        "AVPlayerItemStatus": NewType("AVPlayerItemStatus", int),
        "AVAudioSessionRouteSharingPolicy": NewType(
            "AVAudioSessionRouteSharingPolicy", int
        ),
        "AVAssetImageGeneratorResult": NewType("AVAssetImageGeneratorResult", int),
        "AVPlayerInterstitialEventTimelineOccupancy": NewType(
            "AVPlayerInterstitialEventTimelineOccupancy", int
        ),
        "AVSpeechSynthesisMarkerMark": NewType("AVSpeechSynthesisMarkerMark", int),
        "AVAssetSegmentType": NewType("AVAssetSegmentType", int),
        "AVSampleBufferRequestDirection": NewType(
            "AVSampleBufferRequestDirection", int
        ),
        "AVCaptureVideoOrientation": NewType("AVCaptureVideoOrientation", int),
        "AVAudioEngineManualRenderingStatus": NewType(
            "AVAudioEngineManualRenderingStatus", int
        ),
        "AVAssetReferenceRestrictions": NewType("AVAssetReferenceRestrictions", int),
        "AVQueuedSampleBufferRenderingStatus": NewType(
            "AVQueuedSampleBufferRenderingStatus", int
        ),
        "AVAudioPlayerNodeBufferOptions": NewType(
            "AVAudioPlayerNodeBufferOptions", int
        ),
        "AVAudioSessionMicrophoneInjectionMode": NewType(
            "AVAudioSessionMicrophoneInjectionMode", int
        ),
        "AVCaptureVideoStabilizationMode": NewType(
            "AVCaptureVideoStabilizationMode", int
        ),
        "AVDepthDataAccuracy": NewType("AVDepthDataAccuracy", int),
        "AVAudio3DMixingSourceMode": NewType("AVAudio3DMixingSourceMode", int),
        "AVCaptionRegionScroll": NewType("AVCaptionRegionScroll", int),
        "AVAudioQuality": NewType("AVAudioQuality", int),
        "AVError": NewType("AVError", int),
        "AVAudioEnvironmentOutputType": NewType("AVAudioEnvironmentOutputType", int),
        "AVSpeechBoundary": NewType("AVSpeechBoundary", int),
        "AVSampleBufferRequestMode": NewType("AVSampleBufferRequestMode", int),
        "AVCaptureFocusMode": NewType("AVCaptureFocusMode", int),
        "AVAssetReaderStatus": NewType("AVAssetReaderStatus", int),
        "AVAudioSessionPortOverride": NewType("AVAudioSessionPortOverride", int),
        "AVCaptionFontWeight": NewType("AVCaptionFontWeight", int),
        "AVCaptureSystemPressureFactors": NewType(
            "AVCaptureSystemPressureFactors", int
        ),
        "AVAudio3DMixingPointSourceInHeadMode": NewType(
            "AVAudio3DMixingPointSourceInHeadMode", int
        ),
        "AVAudioConverterOutputStatus": NewType("AVAudioConverterOutputStatus", int),
        "AVCaptionRegionWritingMode": NewType("AVCaptionRegionWritingMode", int),
        "AVCaptureSessionInterruptionReason": NewType(
            "AVCaptureSessionInterruptionReason", int
        ),
        "AVPlayerInterstitialEventAssetListResponseStatus": NewType(
            "AVPlayerInterstitialEventAssetListResponseStatus", int
        ),
        "AVCaptureTorchMode": NewType("AVCaptureTorchMode", int),
        "AVAssetWriterStatus": NewType("AVAssetWriterStatus", int),
        "AVDelegatingPlaybackCoordinatorRateChangeOptions": NewType(
            "AVDelegatingPlaybackCoordinatorRateChangeOptions", int
        ),
        "AVContentAuthorizationStatus": NewType("AVContentAuthorizationStatus", int),
        "AVPlayerLooperItemOrdering": NewType("AVPlayerLooperItemOrdering", int),
        "AVAudioConverterInputStatus": NewType("AVAudioConverterInputStatus", int),
        "AVCaptureDeviceTransportControlsPlaybackMode": NewType(
            "AVCaptureDeviceTransportControlsPlaybackMode", int
        ),
        "AVAudioSessionRouteChangeReason": NewType(
            "AVAudioSessionRouteChangeReason", int
        ),
        "AVPlayerLooperStatus": NewType("AVPlayerLooperStatus", int),
        "AVAudioSessionInterruptionReason": NewType(
            "AVAudioSessionInterruptionReason", int
        ),
        "AVSpeechSynthesisVoiceQuality": NewType("AVSpeechSynthesisVoiceQuality", int),
        "AVMusicSequenceLoadOptions": NewType("AVMusicSequenceLoadOptions", int),
        "AVCaptionRubyAlignment": NewType("AVCaptionRubyAlignment", int),
        "AVCaptionTextCombine": NewType("AVCaptionTextCombine", int),
        "AVMovieWritingOptions": NewType("AVMovieWritingOptions", int),
        "AVCaptionRubyPosition": NewType("AVCaptionRubyPosition", int),
        "AVAudioEngineManualRenderingMode": NewType(
            "AVAudioEngineManualRenderingMode", int
        ),
        "AVPlayerHDRMode": NewType("AVPlayerHDRMode", int),
        "AVAudioUnitEQFilterType": NewType("AVAudioUnitEQFilterType", int),
        "AVAudioRoutingArbitrationCategory": NewType(
            "AVAudioRoutingArbitrationCategory", int
        ),
        "AVSpeechSynthesisVoiceTraits": NewType("AVSpeechSynthesisVoiceTraits", int),
        "AVCaptureSystemUserInterface": NewType("AVCaptureSystemUserInterface", int),
        "AVKeyValueStatus": NewType("AVKeyValueStatus", int),
        "AVAudioConverterPrimeMethod": NewType("AVAudioConverterPrimeMethod", int),
        "AVAudioEnvironmentDistanceAttenuationModel": NewType(
            "AVAudioEnvironmentDistanceAttenuationModel", int
        ),
        "AVAudioUnitReverbPreset": NewType("AVAudioUnitReverbPreset", int),
        "AVCaptionUnitsType": NewType("AVCaptionUnitsType", int),
        "AVCaptionDecoration": NewType("AVCaptionDecoration", int),
        "AVCaptionTextAlignment": NewType("AVCaptionTextAlignment", int),
        "AVAudioSessionPromptStyle": NewType("AVAudioSessionPromptStyle", int),
        "AVPlayerItemSegmentType": NewType("AVPlayerItemSegmentType", int),
        "AVAudio3DMixingRenderingAlgorithm": NewType(
            "AVAudio3DMixingRenderingAlgorithm", int
        ),
        "AVCaptionAnimation": NewType("AVCaptionAnimation", int),
        "AVSpeechSynthesisPersonalVoiceAuthorizationStatus": NewType(
            "AVSpeechSynthesisPersonalVoiceAuthorizationStatus", int
        ),
        "AVCaptureFlashMode": NewType("AVCaptureFlashMode", int),
        "AVCapturePrimaryConstituentDeviceSwitchingBehavior": NewType(
            "AVCapturePrimaryConstituentDeviceSwitchingBehavior", int
        ),
        "AVAudioSessionInterruptionOptions": NewType(
            "AVAudioSessionInterruptionOptions", int
        ),
        "AVCaptureExposureMode": NewType("AVCaptureExposureMode", int),
        "AVAuthorizationStatus": NewType("AVAuthorizationStatus", int),
        "AVAudioSessionCategoryOptions": NewType("AVAudioSessionCategoryOptions", int),
        "AVAudioSpatializationFormats": NewType("AVAudioSpatializationFormats", int),
        "AVCapturePhotoOutputCaptureReadiness": NewType(
            "AVCapturePhotoOutputCaptureReadiness", int
        ),
        "AVAudioSessionRecordPermission": NewType(
            "AVAudioSessionRecordPermission", int
        ),
        "AVDelegatingPlaybackCoordinatorSeekOptions": NewType(
            "AVDelegatingPlaybackCoordinatorSeekOptions", int
        ),
        "AVCapturePhotoQualityPrioritization": NewType(
            "AVCapturePhotoQualityPrioritization", int
        ),
        "AVCaptionRegionDisplayAlignment": NewType(
            "AVCaptionRegionDisplayAlignment", int
        ),
        "AVCaptureCenterStageControlMode": NewType(
            "AVCaptureCenterStageControlMode", int
        ),
        "AVMIDIControlChangeMessageType": NewType(
            "AVMIDIControlChangeMessageType", int
        ),
        "AVAudioPlayerNodeCompletionCallbackType": NewType(
            "AVAudioPlayerNodeCompletionCallbackType", int
        ),
        "AVAudioApplicationRecordPermission": NewType(
            "AVAudioApplicationRecordPermission", int
        ),
        "AVCaptureLensStabilizationStatus": NewType(
            "AVCaptureLensStabilizationStatus", int
        ),
        "AVAudioSessionSetActiveOptions": NewType(
            "AVAudioSessionSetActiveOptions", int
        ),
        "AVPlayerTimeControlStatus": NewType("AVPlayerTimeControlStatus", int),
        "AVMIDIMetaEventType": NewType("AVMIDIMetaEventType", int),
        "AVVariantPreferences": NewType("AVVariantPreferences", int),
        "AVAssetTrackGroupOutputHandling": NewType(
            "AVAssetTrackGroupOutputHandling", int
        ),
        "AVVideoFieldMode": NewType("AVVideoFieldMode", int),
        "AVPlayerInterstitialEventRestrictions": NewType(
            "AVPlayerInterstitialEventRestrictions", int
        ),
        "AVCaptureWhiteBalanceMode": NewType("AVCaptureWhiteBalanceMode", int),
        "AVAudioApplicationMicrophoneInjectionPermission": NewType(
            "AVAudioApplicationMicrophoneInjectionPermission", int
        ),
        "AVCaptureAutoFocusSystem": NewType("AVCaptureAutoFocusSystem", int),
        "AVCapturePrimaryConstituentDeviceRestrictedSwitchingBehaviorConditions": NewType(
            "AVCapturePrimaryConstituentDeviceRestrictedSwitchingBehaviorConditions",
            int,
        ),
        "AVAudioSessionActivationOptions": NewType(
            "AVAudioSessionActivationOptions", int
        ),
        "AVCaptureMicrophoneMode": NewType("AVCaptureMicrophoneMode", int),
        "AVAudioVoiceProcessingSpeechActivityEvent": NewType(
            "AVAudioVoiceProcessingSpeechActivityEvent", int
        ),
        "AVMusicTrackLoopCount": NewType("AVMusicTrackLoopCount", int),
        "AVCaptureOutputDataDroppedReason": NewType(
            "AVCaptureOutputDataDroppedReason", int
        ),
        "AVExternalContentProtectionStatus": NewType(
            "AVExternalContentProtectionStatus", int
        ),
        "AVCaptureAutoFocusRangeRestriction": NewType(
            "AVCaptureAutoFocusRangeRestriction", int
        ),
        "AVCaptionFontStyle": NewType("AVCaptionFontStyle", int),
        "AVAudioSessionSilenceSecondaryAudioHintType": NewType(
            "AVAudioSessionSilenceSecondaryAudioHintType", int
        ),
        "AVCaptureDevicePosition": NewType("AVCaptureDevicePosition", int),
        "CMTagCollectionVideoOutputPreset": NewType(
            "CMTagCollectionVideoOutputPreset", int
        ),
        "AVAudioCommonFormat": NewType("AVAudioCommonFormat", int),
        "AVCaptureColorSpace": NewType("AVCaptureColorSpace", int),
        "AVContentKeyRequestStatus": NewType("AVContentKeyRequestStatus", int),
        "AVPlayerActionAtItemEnd": NewType("AVPlayerActionAtItemEnd", int),
        "AVAudioSessionInterruptionType": NewType(
            "AVAudioSessionInterruptionType", int
        ),
        "AVPlayerStatus": NewType("AVPlayerStatus", int),
        "AVAudioStereoOrientation": NewType("AVAudioStereoOrientation", int),
        "AVAssetExportSessionStatus": NewType("AVAssetExportSessionStatus", int),
        "AVAudioVoiceProcessingOtherAudioDuckingLevel": NewType(
            "AVAudioVoiceProcessingOtherAudioDuckingLevel", int
        ),
        "AVCaptionConversionValidatorStatus": NewType(
            "AVCaptionConversionValidatorStatus", int
        ),
        "AVAudioEngineManualRenderingError": NewType(
            "AVAudioEngineManualRenderingError", int
        ),
        "AVDepthDataQuality": NewType("AVDepthDataQuality", int),
        "AVAudioSessionRenderingMode": NewType("AVAudioSessionRenderingMode", int),
    }
)
misc.update(
    {
        "AVCaptureReactionType": NewType("AVCaptureReactionType", str),
        "AVCaptureDeviceType": NewType("AVCaptureDeviceType", str),
        "AVPlayerWaitingReason": NewType("AVPlayerWaitingReason", str),
        "AVPlayerItemLegibleOutputTextStylingResolution": NewType(
            "AVPlayerItemLegibleOutputTextStylingResolution", str
        ),
        "AVAssetImageGeneratorApertureMode": NewType(
            "AVAssetImageGeneratorApertureMode", str
        ),
        "AVTrackAssociationType": NewType("AVTrackAssociationType", str),
        "AVContentKeySystem": NewType("AVContentKeySystem", str),
        "AVPlayerInterstitialEventCue": NewType("AVPlayerInterstitialEventCue", str),
        "AVOutputSettingsPreset": NewType("AVOutputSettingsPreset", str),
        "AVCaptureSystemPressureLevel": NewType("AVCaptureSystemPressureLevel", str),
        "AVContentKeyRequestRetryReason": NewType(
            "AVContentKeyRequestRetryReason", str
        ),
        "AVFileTypeProfile": NewType("AVFileTypeProfile", str),
        "AVCaptureSessionPreset": NewType("AVCaptureSessionPreset", str),
        "AVCaptionSettingsKey": NewType("AVCaptionSettingsKey", str),
        "AVVideoApertureMode": NewType("AVVideoApertureMode", str),
        "AVAssetDownloadedAssetEvictionPriority": NewType(
            "AVAssetDownloadedAssetEvictionPriority", str
        ),
        "AVSpatialCaptureDiscomfortReason": NewType(
            "AVSpatialCaptureDiscomfortReason", str
        ),
        "AVSemanticSegmentationMatteType": NewType(
            "AVSemanticSegmentationMatteType", str
        ),
        "AVContentKeySessionServerPlaybackContextOption": NewType(
            "AVContentKeySessionServerPlaybackContextOption", str
        ),
        "AVAudioSequencerInfoDictionaryKey": NewType(
            "AVAudioSequencerInfoDictionaryKey", str
        ),
        "AVPlayerRateDidChangeReason": NewType("AVPlayerRateDidChangeReason", str),
        "AVCaptionConversionAdjustmentType": NewType(
            "AVCaptionConversionAdjustmentType", str
        ),
        "AVAssetWriterInputMediaDataLocation": NewType(
            "AVAssetWriterInputMediaDataLocation", str
        ),
        "AVPlayerIntegratedTimelineSnapshotsOutOfSyncReason": NewType(
            "AVPlayerIntegratedTimelineSnapshotsOutOfSyncReason", str
        ),
        "AVLayerVideoGravity": NewType("AVLayerVideoGravity", str),
        "AVVideoCompositionPerFrameHDRDisplayMetadataPolicy": NewType(
            "AVVideoCompositionPerFrameHDRDisplayMetadataPolicy", str
        ),
        "AVCoordinatedPlaybackSuspensionReason": NewType(
            "AVCoordinatedPlaybackSuspensionReason", str
        ),
        "AVVideoCodecType": NewType("AVVideoCodecType", str),
        "AVCaptionConversionWarningType": NewType(
            "AVCaptionConversionWarningType", str
        ),
        "AVAudioTimePitchAlgorithm": NewType("AVAudioTimePitchAlgorithm", str),
        "AVVideoRange": NewType("AVVideoRange", str),
        "AVMetadataFormat": NewType("AVMetadataFormat", str),
        "AVMetadataObjectType": NewType("AVMetadataObjectType", str),
    }
)
misc.update({})
functions = {
    "AVMakeBeatRange": (b"{_AVBeatRange=dd}dd", "", {"inline": True}),
    "AVAudioMake3DPoint": (b"{AVAudio3DPoint=fff}fff",),
    "AVMakeRectWithAspectRatioInsideRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGSize=dd}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "AVCaptionPointMake": (
        b"{AVCaptionPoint={AVCaptionDimension=dq}{AVCaptionDimension=dq}}{AVCaptionDimension=dq}{AVCaptionDimension=dq}",
    ),
    "CMTagCollectionCreateWithVideoOutputPreset": (
        b"i^{__CFAllocator=}I^^{OpaqueCMTagCollection=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"already_cfretained": True, "type_modifier": "o"}},
        },
    ),
    "AVCaptureReactionSystemImageNameForType": (b"@@",),
    "AVAudioMake3DVector": (b"{AVAudio3DPoint=fff}fff",),
    "AVAudioMake3DVectorOrientation": (
        b"{AVAudio3DVectorOrientation={AVAudio3DPoint=fff}{AVAudio3DPoint=fff}}{AVAudio3DPoint=fff}{AVAudio3DPoint=fff}",
    ),
    "AVCaptionDimensionMake": (b"{AVCaptionDimension=dq}dq",),
    "AVSampleBufferAttachContentKey": (
        b"Z^{opaqueCMSampleBuffer=}@^@",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "AVAudioMake3DAngularOrientation": (b"{AVAudio3DAngularOrientation=fff}fff",),
    "AVCaptionSizeMake": (
        b"{AVCaptionSize={AVCaptionDimension=dq}{AVCaptionDimension=dq}}{AVCaptionDimension=dq}{AVCaptionDimension=dq}",
    ),
}
aliases = {
    "AVAudioSessionRouteSharingPolicyLongForm": "AVAudioSessionRouteSharingPolicyLongFormAudio",
    "AVAssetReferenceRestrictionDefaultPolicy": "AVAssetReferenceRestrictionForbidLocalReferenceToRemote",
    "AVLinearPCMIsNonInterleavedKey": "AVLinearPCMIsNonInterleaved",
    "AVAudio3DVector": "AVAudio3DPoint",
    "AVAssetTrackGroupOutputHandlingDefaultPolicy": "AVAssetTrackGroupOutputHandlingNone",
    "AVMusicTimeStampEndOfTrack": "DBL_MAX",
    "AVPlayerInterstitialEventRestrictionDefaultPolicy": "AVPlayerInterstitialEventRestrictionNone",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"AVAsset", b"canContainFragments", {"retval": {"type": "Z"}})
    r(b"AVAsset", b"containsFragments", {"retval": {"type": "Z"}})
    r(b"AVAsset", b"copyCGImageAtTime:actualTime:error:", {"retval": {"type": "Z"}})
    r(b"AVAsset", b"duration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVAsset",
        b"findCompatibleTrackForCompositionTrack:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAsset",
        b"findUnusedTrackIDWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"i"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVAsset", b"hasProtectedContent", {"retval": {"type": b"Z"}})
    r(b"AVAsset", b"isCompatibleWithAirPlayVideo", {"retval": {"type": "Z"}})
    r(b"AVAsset", b"isCompatibleWithSavedPhotosAlbum", {"retval": {"type": b"Z"}})
    r(b"AVAsset", b"isComposable", {"retval": {"type": b"Z"}})
    r(b"AVAsset", b"isExportable", {"retval": {"type": b"Z"}})
    r(b"AVAsset", b"isPlayable", {"retval": {"type": b"Z"}})
    r(b"AVAsset", b"isReadable", {"retval": {"type": b"Z"}})
    r(
        b"AVAsset",
        b"loadChapterMetadataGroupsBestMatchingPreferredLanguages:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAsset",
        b"loadChapterMetadataGroupsWithTitleLocale:containingItemsWithCommonKeys:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAsset",
        b"loadMediaSelectionGroupForMediaCharacteristic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAsset",
        b"loadMetadataForFormat:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAsset",
        b"loadTrackWithTrackID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAsset",
        b"loadTracksWithMediaCharacteristic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAsset",
        b"loadTracksWithMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVAsset", b"minimumTimeOffsetFromLive", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVAsset", b"overallDurationHint", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVAsset", b"providesPreciseDurationAndTiming", {"retval": {"type": b"Z"}})
    r(b"AVAssetCache", b"isPlayableOffline", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetDownloadConfiguration",
        b"downloadsInterstitialAssets",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetDownloadConfiguration",
        b"optimizesAuxiliaryContentConfigurations",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetDownloadConfiguration",
        b"setDownloadsInterstitialAssets:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetDownloadConfiguration",
        b"setOptimizesAuxiliaryContentConfigurations:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetDownloadURLSession",
        b"dataTaskWithRequest:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetDownloadURLSession",
        b"dataTaskWithURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetDownloadURLSession",
        b"downloadTaskWithRequest:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetDownloadURLSession",
        b"downloadTaskWithResumeData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetDownloadURLSession",
        b"downloadTaskWithURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetDownloadURLSession",
        b"uploadTaskWithRequest:fromData:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetDownloadURLSession",
        b"uploadTaskWithRequest:fromFile:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVAssetExportSession", b"allowsParallelizedExport", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetExportSession",
        b"canPerformMultiplePassesOverSourceMediaData",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetExportSession",
        b"determineCompatibilityOfExportPreset:withAsset:outputFileType:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"AVAssetExportSession",
        b"determineCompatibleFileTypesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"AVAssetExportSession",
        b"estimateMaximumDurationWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetExportSession",
        b"estimateOutputFileLengthWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetExportSession",
        b"exportAsynchronouslyWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"AVAssetExportSession", b"maxDuration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVAssetExportSession",
        b"setAllowsParallelizedExport:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetExportSession",
        b"setCanPerformMultiplePassesOverSourceMediaData:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetExportSession",
        b"setShouldOptimizeForNetworkUse:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetExportSession",
        b"setTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVAssetExportSession",
        b"shouldOptimizeForNetworkUse",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetExportSession",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVAssetImageGenerator",
        b"appliesPreferredTrackTransform",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetImageGenerator",
        b"copyCGImageAtTime:actualTime:error:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type": b"^{CMTime=qiIq}"},
                4: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"AVAssetImageGenerator",
        b"generateCGImageAsynchronouslyForTime:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{CGImage=}"},
                            2: {"type": b"{CMTime=qiIq}"},
                            3: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVAssetImageGenerator",
        b"generateCGImagesAsynchronouslyForTimes:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                            2: {"type": b"^{CGImage=}"},
                            3: {"type": b"{CMTime=qiIq}"},
                            4: {"type": b"q"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetImageGenerator",
        b"requestedTimeToleranceAfter",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVAssetImageGenerator",
        b"requestedTimeToleranceBefore",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVAssetImageGenerator",
        b"setAppliesPreferredTrackTransform:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetImageGenerator",
        b"setRequestedTimeToleranceAfter:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetImageGenerator",
        b"setRequestedTimeToleranceBefore:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetPlaybackAssistant",
        b"loadPlaybackConfigurationOptionsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAssetReader",
        b"assetReaderWithAsset:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"AVAssetReader", b"canAddOutput:", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetReader",
        b"initWithAsset:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAssetReader",
        b"setTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(b"AVAssetReader", b"startReading", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetReader",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(b"AVAssetReaderOutput", b"alwaysCopiesSampleData", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetReaderOutput",
        b"setAlwaysCopiesSampleData:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetReaderOutput",
        b"setSupportsRandomAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVAssetReaderOutput", b"supportsRandomAccess", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetResourceLoader",
        b"preloadsEligibleContentKeys",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetResourceLoader",
        b"sendsCommonMediaClientDataAsHTTPHeaders",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetResourceLoader",
        b"setPreloadsEligibleContentKeys:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetResourceLoader",
        b"setSendsCommonMediaClientDataAsHTTPHeaders:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetResourceLoadingContentInformationRequest",
        b"isByteRangeAccessSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetResourceLoadingContentInformationRequest",
        b"isEntireLengthAvailableOnDemand",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetResourceLoadingContentInformationRequest",
        b"setByteRangeAccessSupported:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetResourceLoadingContentInformationRequest",
        b"setEntireLengthAvailableOnDemand:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetResourceLoadingDataRequest",
        b"requestsAllDataToEndOfResource",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVAssetResourceLoadingRequest", b"isCancelled", {"retval": {"type": b"Z"}})
    r(b"AVAssetResourceLoadingRequest", b"isFinished", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetResourceLoadingRequest",
        b"persistentContentKeyFromKeyVendorResponse:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAssetResourceLoadingRequest",
        b"streamingContentKeyRequestDataForApp:contentIdentifier:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAssetResourceLoadingRequestor",
        b"providesExpiredSessionReports",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetSegmentReportSampleInformation",
        b"isSyncSample",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetSegmentReportSampleInformation",
        b"presentationTimeStamp",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVAssetSegmentTrackReport", b"duration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVAssetSegmentTrackReport",
        b"earliestPresentationTimeStamp",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVAssetTrack", b"canProvideSampleCursors", {"retval": {"type": b"Z"}})
    r(b"AVAssetTrack", b"hasAudioSampleDependencies", {"retval": {"type": b"Z"}})
    r(b"AVAssetTrack", b"hasMediaCharacteristic:", {"retval": {"type": b"Z"}})
    r(b"AVAssetTrack", b"isDecodable", {"retval": {"type": b"Z"}})
    r(b"AVAssetTrack", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"AVAssetTrack", b"isPlayable", {"retval": {"type": b"Z"}})
    r(b"AVAssetTrack", b"isSelfContained", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetTrack",
        b"loadAssociatedTracksOfType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetTrack",
        b"loadMetadataForFormat:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAssetTrack",
        b"loadSamplePresentationTimeForTrackTime:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVAssetTrack",
        b"loadSegmentForTrackTime:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVAssetTrack",
        b"makeSampleCursorWithPresentationTimeStamp:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVAssetTrack", b"minFrameDuration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVAssetTrack", b"requiresFrameReordering", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetTrack",
        b"samplePresentationTimeForTrackTime:",
        {
            "retval": {"type": b"{CMTime=qiIq}"},
            "arguments": {2: {"type": b"{CMTime=qiIq}"}},
        },
    )
    r(
        b"AVAssetTrack",
        b"segmentForTrackTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetTrack",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(b"AVAssetTrackSegment", b"isEmpty", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetTrackSegment",
        b"timeMapping",
        {
            "retval": {
                "type": b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}"
            }
        },
    )
    r(
        b"AVAssetVariantAudioRenditionSpecificAttributes",
        b"isBinaural",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetVariantAudioRenditionSpecificAttributes",
        b"isDownmix",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetVariantAudioRenditionSpecificAttributes",
        b"isImmersive",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetVariantQualifier",
        b"predicateForBinauralAudio:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetVariantQualifier",
        b"predicateForBinauralAudio:mediaSelectionOption:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetVariantQualifier",
        b"predicateForDownmixAudio:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetVariantQualifier",
        b"predicateForDownmixAudio:mediaSelectionOption:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetVariantQualifier",
        b"predicateForImmersiveAudio:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetVariantQualifier",
        b"predicateForImmersiveAudio:mediaSelectionOption:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetWriter",
        b"assetWriterWithURL:fileType:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"AVAssetWriter", b"canAddInput:", {"retval": {"type": b"Z"}})
    r(b"AVAssetWriter", b"canAddInputGroup:", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetWriter",
        b"canApplyOutputSettings:forMediaType:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetWriter",
        b"endSessionAtSourceTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVAssetWriter", b"finishWriting", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetWriter",
        b"finishWritingWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAssetWriter",
        b"initWithURL:fileType:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAssetWriter",
        b"initialSegmentStartTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVAssetWriter",
        b"movieFragmentInterval",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVAssetWriter", b"overallDurationHint", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVAssetWriter",
        b"preferredOutputSegmentInterval",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVAssetWriter", b"producesCombinableFragments", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetWriter",
        b"setInitialSegmentStartTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetWriter",
        b"setMovieFragmentInterval:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetWriter",
        b"setOverallDurationHint:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetWriter",
        b"setPreferredOutputSegmentInterval:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetWriter",
        b"setProducesCombinableFragments:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetWriter",
        b"setShouldOptimizeForNetworkUse:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVAssetWriter", b"shouldOptimizeForNetworkUse", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetWriter",
        b"startSessionAtSourceTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVAssetWriter", b"startWriting", {"retval": {"type": b"Z"}})
    r(b"AVAssetWriterInput", b"appendSampleBuffer:", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetWriterInput",
        b"canAddTrackAssociationWithTrackOfInput:type:",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVAssetWriterInput", b"canPerformMultiplePasses", {"retval": {"type": b"Z"}})
    r(b"AVAssetWriterInput", b"expectsMediaDataInRealTime", {"retval": {"type": b"Z"}})
    r(b"AVAssetWriterInput", b"isReadyForMoreMediaData", {"retval": {"type": b"Z"}})
    r(b"AVAssetWriterInput", b"marksOutputTrackAsEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVAssetWriterInput",
        b"performsMultiPassEncodingIfSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetWriterInput",
        b"preferredMediaChunkDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVAssetWriterInput",
        b"requestMediaDataWhenReadyOnQueue:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAssetWriterInput",
        b"respondToEachPassDescriptionOnQueue:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAssetWriterInput",
        b"setExpectsMediaDataInRealTime:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetWriterInput",
        b"setMarksOutputTrackAsEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetWriterInput",
        b"setPerformsMultiPassEncodingIfSupported:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAssetWriterInput",
        b"setPreferredMediaChunkDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetWriterInputCaptionAdaptor",
        b"appendCaption:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetWriterInputCaptionAdaptor",
        b"appendCaptionGroup:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetWriterInputMetadataAdaptor",
        b"appendTimedMetadataGroup:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAssetWriterInputPixelBufferAdaptor",
        b"appendPixelBuffer:withPresentationTime:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVAssetWriterInputTaggedPixelBufferGroupAdaptor",
        b"appendTaggedPixelBufferGroup:withPresentationTime:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAsynchronousCIImageFilteringRequest",
        b"compositionTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVAsynchronousVideoCompositionRequest",
        b"compositionTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVAudioApplication", b"isInputMuted", {"retval": {"type": "Z"}})
    r(
        b"AVAudioApplication",
        b"requestRecordPermissionWithCompletionHandler:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            },
        },
    )
    r(
        b"AVAudioApplication",
        b"setInputMuteStateChangeHandler:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                },
                3: {"type_modifier": b"o"},
            },
        },
    )
    r(b"AVAudioBuffer", b"data", {"retval": {"c_array_of_variable_length": True}})
    r(
        b"AVAudioBuffer",
        b"packetDescriptions",
        {"retval": {"c_array_of_variable_length": True}},
    )
    r(b"AVAudioChannelLayout", b"isEqual:", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioConverter",
        b"convertToBuffer:error:withInputFromBlock:",
        {
            "arguments": {
                3: {"type_modifier": b"o"},
                4: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"I"},
                            2: {"type": b"o^q"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVAudioConverter",
        b"convertToBuffer:fromBuffer:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioConverter", b"dither", {"retval": {"type": b"Z"}})
    r(b"AVAudioConverter", b"downmix", {"retval": {"type": b"Z"}})
    r(b"AVAudioConverter", b"setDither:", {"arguments": {2: {"type": b"Z"}}})
    r(b"AVAudioConverter", b"setDownmix:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVAudioEngine",
        b"connectMIDI:to:format:block:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"C"},
                            3: {"type": b"q"},
                            4: {"type": b"n^v", "c_array_length_in_arg": 3},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioEngine",
        b"connectMIDI:to:format:eventListBlock:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"z"},
                            3: {
                                "type": b"^{MIDIEventList=iI[1{MIDIEventPacket=II[64I]}]}"
                            },
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioEngine",
        b"connectMIDI:toNodes:format:block:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"C"},
                            3: {"type": b"q"},
                            4: {"type": b"n^v", "c_array_length_in_arg": 3},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioEngine",
        b"connectMIDI:toNodes:format:eventListBlock:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"z"},
                            3: {
                                "type": b"^{MIDIEventList=iI[1{MIDIEventPacket=II[64I]}]}"
                            },
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioEngine",
        b"enableManualRenderingMode:format:maximumFrameCount:error:",
        {"retval": {"type": b"Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioEngine", b"isAutoShutdownEnabled", {"retval": {"type": b"Z"}})
    r(b"AVAudioEngine", b"isInManualRenderingMode", {"retval": {"type": b"Z"}})
    r(b"AVAudioEngine", b"isRunning", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioEngine",
        b"manualRenderingBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"q"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"I"},
                        2: {"type": b"o^{AudioBufferList=L[1{AudioBuffer=LL^v}]}"},
                        3: {"type": b"o^i"},
                    },
                }
            }
        },
    )
    r(
        b"AVAudioEngine",
        b"renderOffline:toBuffer:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioEngine", b"setAutoShutdownEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVAudioEngine",
        b"startAndReturnError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioEnvironmentNode",
        b"isListenerHeadTrackingEnabled",
        {"retval": {"type": "Z"}},
    )
    r(
        b"AVAudioEnvironmentNode",
        b"setListenerHeadTrackingEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"AVAudioEnvironmentReverbParameters", b"enable", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioEnvironmentReverbParameters",
        b"setEnable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAudioFile",
        b"initForReading:commonFormat:interleaved:error:",
        {"arguments": {4: {"type": b"Z"}, 5: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioFile",
        b"initForReading:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioFile",
        b"initForWriting:settings:commonFormat:interleaved:error:",
        {"arguments": {5: {"type": b"Z"}, 6: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioFile",
        b"initForWriting:settings:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioFile",
        b"readIntoBuffer:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioFile",
        b"readIntoBuffer:frameCount:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioFile",
        b"writeFromBuffer:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioFormat",
        b"initWithCommonFormat:sampleRate:channels:interleaved:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"AVAudioFormat",
        b"initWithCommonFormat:sampleRate:interleaved:channelLayout:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"AVAudioFormat",
        b"initWithStreamDescription:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(
        b"AVAudioFormat",
        b"initWithStreamDescription:channelLayout:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(b"AVAudioFormat", b"isEqual:", {"retval": {"type": b"Z"}})
    r(b"AVAudioFormat", b"isInterleaved", {"retval": {"type": b"Z"}})
    r(b"AVAudioFormat", b"isStandard", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioFormat",
        b"streamDescription",
        {"retval": {"c_array_of_fixed_length": 1}},
    )
    r(b"AVAudioIONode", b"isVoiceProcessingEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioIONode",
        b"setVoiceProcessingEnabled:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"Z"}, 3: {"type_modifier": b"o"}},
        },
    )
    r(b"AVAudioInputNode", b"isVoiceProcessingAGCEnabled", {"retval": {"type": b"Z"}})
    r(b"AVAudioInputNode", b"isVoiceProcessingBypassed", {"retval": {"type": b"Z"}})
    r(b"AVAudioInputNode", b"isVoiceProcessingInputMuted", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioInputNode",
        b"setManualRenderingInputPCMFormat:inputBlock:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {
                            "type": b"^{AudioBufferList=L[1{AudioBuffer=LL^v}]}"
                        },
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"I"}},
                    }
                }
            },
        },
    )
    r(
        b"AVAudioInputNode",
        b"setMutedSpeechActivityEventListener:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            },
        },
    )
    r(
        b"AVAudioInputNode",
        b"setVoiceProcessingAGCEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAudioInputNode",
        b"setVoiceProcessingBypassed:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAudioInputNode",
        b"setVoiceProcessingInputMuted:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAudioMixInputParameters",
        b"getVolumeRampForTime:startVolume:endVolume:timeRange:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type_modifier": b"o"},
                4: {"type_modifier": b"o"},
                5: {
                    "type": b"^{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
                    "type_modifier": b"o",
                },
            },
        },
    )
    r(
        b"AVAudioNode",
        b"installTapOnBus:bufferSize:format:block:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioPCMBuffer",
        b"initWithPCMFormat:bufferListNoCopy:deallocator:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVAudioPlayer", b"enableRate", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioPlayer",
        b"initWithContentsOfURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioPlayer",
        b"initWithContentsOfURL:fileTypeHint:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioPlayer",
        b"initWithData:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioPlayer",
        b"initWithData:fileTypeHint:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioPlayer", b"isMeteringEnabled", {"retval": {"type": b"Z"}})
    r(b"AVAudioPlayer", b"isPlaying", {"retval": {"type": b"Z"}})
    r(b"AVAudioPlayer", b"play", {"retval": {"type": b"Z"}})
    r(b"AVAudioPlayer", b"playAtTime:", {"retval": {"type": b"Z"}})
    r(b"AVAudioPlayer", b"prepareToPlay", {"retval": {"type": b"Z"}})
    r(b"AVAudioPlayer", b"setEnableRate:", {"arguments": {2: {"type": b"Z"}}})
    r(b"AVAudioPlayer", b"setMeteringEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"AVAudioPlayerNode", b"isPlaying", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioPlayerNode",
        b"loadFromURL:options:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioPlayerNode",
        b"scheduleBuffer:atTime:options:completionCallbackType:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioPlayerNode",
        b"scheduleBuffer:atTime:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioPlayerNode",
        b"scheduleBuffer:completionCallbackType:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioPlayerNode",
        b"scheduleBuffer:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioPlayerNode",
        b"scheduleFile:atTime:completionCallbackType:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioPlayerNode",
        b"scheduleFile:atTime:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioPlayerNode",
        b"scheduleSegment:startingFrame:frameCount:atTime:completionCallbackType:completionHandler:",
        {
            "arguments": {
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioPlayerNode",
        b"scheduleSegment:startingFrame:frameCount:atTime:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"AVAudioRecorder", b"deleteRecording", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioRecorder",
        b"initWithURL:format:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioRecorder",
        b"initWithURL:settings:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioRecorder", b"isMeteringEnabled", {"retval": {"type": b"Z"}})
    r(b"AVAudioRecorder", b"isRecording", {"retval": {"type": b"Z"}})
    r(b"AVAudioRecorder", b"prepareToRecord", {"retval": {"type": b"Z"}})
    r(b"AVAudioRecorder", b"record", {"retval": {"type": b"Z"}})
    r(b"AVAudioRecorder", b"recordAtTime:", {"retval": {"type": b"Z"}})
    r(b"AVAudioRecorder", b"recordAtTime:forDuration:", {"retval": {"type": b"Z"}})
    r(b"AVAudioRecorder", b"recordForDuration:", {"retval": {"type": b"Z"}})
    r(b"AVAudioRecorder", b"setMeteringEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVAudioRoutingArbiter",
        b"beginArbitrationWithCategory:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioSequencer",
        b"beatsForHostTime:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioSequencer",
        b"dataWithSMPTEResolution:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioSequencer",
        b"hostTimeForBeats:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioSequencer", b"isPlaying", {"retval": {"type": "Z"}})
    r(
        b"AVAudioSequencer",
        b"loadFromData:options:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioSequencer",
        b"loadFromURL:options:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioSequencer", b"removeTrack:", {"retval": {"type": "Z"}})
    r(
        b"AVAudioSequencer",
        b"setUserCallback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"d"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioSequencer",
        b"startAndReturnError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioSequencer",
        b"writeToURL:SMPTEResolution:replaceExisting:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {4: {"type": "Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(
        b"AVAudioSession",
        b"activateWithOptions:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioSession",
        b"allowHapticsAndSystemSoundsDuringRecording",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVAudioSession", b"inputIsAvailable", {"retval": {"type": b"Z"}})
    r(b"AVAudioSession", b"isInputAvailable", {"retval": {"type": b"Z"}})
    r(b"AVAudioSession", b"isInputGainSettable", {"retval": {"type": b"Z"}})
    r(b"AVAudioSession", b"isOtherAudioPlaying", {"retval": {"type": b"Z"}})
    r(b"AVAudioSession", b"overrideOutputAudioPort:error:", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioSession",
        b"requestRecordPermission:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioSession",
        b"secondaryAudioShouldBeSilencedHint",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioSession",
        b"setActive:error:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAudioSession",
        b"setActive:withFlags:error:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAudioSession",
        b"setActive:withOptions:error:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVAudioSession",
        b"setAggregatedIOPreference:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioSession",
        b"setAllowHapticsAndSystemSoundsDuringRecording:error:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVAudioSession", b"setCategory:error:", {"retval": {"type": b"Z"}})
    r(b"AVAudioSession", b"setCategory:mode:options:error:", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioSession",
        b"setCategory:mode:routeSharingPolicy:options:error:",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVAudioSession", b"setCategory:withOptions:error:", {"retval": {"type": b"Z"}})
    r(b"AVAudioSession", b"setInputDataSource:error:", {"retval": {"type": b"Z"}})
    r(b"AVAudioSession", b"setInputGain:error:", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioSession",
        b"setMode:error:",
        {"retval": {"type": b"Z"}, "arguments": {1: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioSession", b"setOutputDataSource:error:", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioSession",
        b"setPreferredHardwareSampleRate:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioSession",
        b"setPreferredIOBufferDuration:error:",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVAudioSession", b"setPreferredInput:error:", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioSession",
        b"setPreferredInputNumberOfChannels:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioSession",
        b"setPreferredInputOrientation:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioSession",
        b"setPreferredOutputNumberOfChannels:error:",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVAudioSession", b"setPreferredSampleRate:error:", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioSessionDataSourceDescription",
        b"setPreferredPolarPattern:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioSessionPortDescription",
        b"hasHardwareVoiceCallProcessing",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioSessionPortDescription",
        b"setPreferredDataSource:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioSinkNode",
        b"initWithReceiverBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            2: {"type": b"I"},
                            3: {"type": b"n^^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioSourceNode",
        b"initWithFormat:renderBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"@?"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVAudioSourceNode",
        b"initWithRenderBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"o^Z"},
                            2: {
                                "type": b"n^^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            3: {"type": b"I"},
                            4: {"type": b"o^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioTime",
        b"initWithAudioTimeStamp:sampleRate:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(b"AVAudioTime", b"isHostTimeValid", {"retval": {"type": b"Z"}})
    r(b"AVAudioTime", b"isSampleTimeValid", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioTime",
        b"timeWithAudioTimeStamp:sampleRate:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(
        b"AVAudioUnit",
        b"instantiateWithComponentDescription:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVAudioUnit",
        b"loadAudioUnitPresetAtURL:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioUnitComponent", b"hasCustomView", {"retval": {"type": b"Z"}})
    r(b"AVAudioUnitComponent", b"hasMIDIInput", {"retval": {"type": b"Z"}})
    r(b"AVAudioUnitComponent", b"hasMIDIOutput", {"retval": {"type": b"Z"}})
    r(b"AVAudioUnitComponent", b"isSandboxSafe", {"retval": {"type": b"Z"}})
    r(b"AVAudioUnitComponent", b"passesAUVal", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioUnitComponent",
        b"supportsNumberInputChannels:outputChannels:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVAudioUnitComponentManager",
        b"componentsPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"o^Z"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"AVAudioUnitEQFilterParameters", b"bypass", {"retval": {"type": b"Z"}})
    r(
        b"AVAudioUnitEQFilterParameters",
        b"setBypass:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVAudioUnitEffect", b"bypass", {"retval": {"type": b"Z"}})
    r(b"AVAudioUnitEffect", b"setBypass:", {"arguments": {2: {"type": b"Z"}}})
    r(b"AVAudioUnitGenerator", b"bypass", {"retval": {"type": b"Z"}})
    r(b"AVAudioUnitGenerator", b"setBypass:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVAudioUnitSampler",
        b"loadAudioFilesAtURLs:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioUnitSampler",
        b"loadInstrumentAtURL:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVAudioUnitSampler",
        b"loadSoundBankInstrumentAtURL:program:bankMSB:bankLSB:error:",
        {"retval": {"type": b"Z"}, "arguments": {6: {"type_modifier": b"o"}}},
    )
    r(b"AVAudioUnitTimeEffect", b"bypass", {"retval": {"type": b"Z"}})
    r(b"AVAudioUnitTimeEffect", b"setBypass:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCameraCalibrationData",
        b"extrinsicMatrix",
        {
            "full_signature": b"{simd_float4x3=[4<3f>]}@:",
            "retval": {"type": b"{simd_float4x3=[4<3f>]}"},
        },
    )
    r(
        b"AVCameraCalibrationData",
        b"intrinsicMatrix",
        {
            "full_signature": b"{simd_float3x3=[3<3f>]}@:",
            "retval": {"type": b"{simd_float3x3=[3<3f>]}"},
        },
    )
    r(
        b"AVCaption",
        b"backgroundColorAtIndex:range:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaption",
        b"decorationAtIndex:range:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaption",
        b"fontStyleAtIndex:range:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaption",
        b"fontWeightAtIndex:range:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaption",
        b"initWithText:timeRange:",
        {"arguments": {3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(b"AVCaption", b"rubyAtIndex:range:", {"arguments": {3: {"type_modifier": b"o"}}})
    r(
        b"AVCaption",
        b"textColorAtIndex:range:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaption",
        b"textCombineAtIndex:range:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaption",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVCaptionConversionTimeRangeAdjustment",
        b"durationOffset",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptionConversionTimeRangeAdjustment",
        b"startTimeOffset",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptionConversionValidator",
        b"captionConversionValidatorWithCaptions:timeRange:conversionSettings:",
        {"arguments": {3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVCaptionConversionValidator",
        b"initWithCaptions:timeRange:conversionSettings:",
        {"arguments": {3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVCaptionConversionValidator",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVCaptionConversionValidator",
        b"validateCaptionConversionWithWarningHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptionFormatConformer",
        b"conformedCaptionForCaption:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaptionFormatConformer",
        b"conformsCaptionsToTimeRange",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptionFormatConformer",
        b"setConformsCaptionsToTimeRange:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptionGroup",
        b"initWithCaptions:timeRange:",
        {"arguments": {3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVCaptionGroup",
        b"initWithTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVCaptionGroup",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVCaptionGrouper",
        b"flushAddedCaptionsIntoGroupsUpToTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVCaptionRegion", b"isEqual:", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptionRenderer",
        b"captionSceneChangesInRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVCaptionRenderer",
        b"renderInContext:forTime:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVCaptionRendererScene", b"hasActiveCaptions", {"retval": {"type": b"Z"}})
    r(b"AVCaptionRendererScene", b"needsPeriodicRefresh", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptionRendererScene",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(b"AVCaptureAudioChannel", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureAudioChannel", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"AVCaptureAudioChannel_Tundra", b"isEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureAudioChannel_Tundra",
        b"setEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureConnection",
        b"automaticallyAdjustsVideoMirroring",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection",
        b"enablesVideoStabilizationWhenAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureConnection", b"isActive", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureConnection",
        b"isCameraIntrinsicMatrixDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection",
        b"isCameraIntrinsicMatrixDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureConnection", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureConnection", b"isVideoFieldModeSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureConnection",
        b"isVideoMaxFrameDurationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection",
        b"isVideoMinFrameDurationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureConnection", b"isVideoMirrored", {"retval": {"type": b"Z"}})
    r(b"AVCaptureConnection", b"isVideoMirroringSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureConnection",
        b"isVideoOrientationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection",
        b"isVideoRotationAngleSupported:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection",
        b"isVideoStabilizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection",
        b"isVideoStabilizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection",
        b"setAutomaticallyAdjustsVideoMirroring:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureConnection",
        b"setCameraIntrinsicMatrixDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCaptureConnection", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCaptureConnection",
        b"setEnablesVideoStabilizationWhenAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureConnection",
        b"setVideoMaxFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureConnection",
        b"setVideoMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVCaptureConnection", b"setVideoMirrored:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCaptureConnection",
        b"videoMaxFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureConnection",
        b"videoMinFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"automaticallyAdjustsVideoMirroring",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"enablesVideoStabilizationWhenAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureConnection_Tundra", b"isActive", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureConnection_Tundra",
        b"isCameraIntrinsicMatrixDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"isCameraIntrinsicMatrixDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureConnection_Tundra", b"isEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureConnection_Tundra",
        b"isVideoFieldModeSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"isVideoMaxFrameDurationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"isVideoMinFrameDurationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureConnection_Tundra", b"isVideoMirrored", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureConnection_Tundra",
        b"isVideoMirroringSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"isVideoOrientationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"isVideoRotationAngleSupported:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"isVideoStabilizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"isVideoStabilizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"setAutomaticallyAdjustsVideoMirroring:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"setCameraIntrinsicMatrixDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCaptureConnection_Tundra", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCaptureConnection_Tundra",
        b"setEnablesVideoStabilizationWhenAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"setVideoMaxFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"setVideoMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"setVideoMirrored:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"videoMaxFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureConnection_Tundra",
        b"videoMinFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVCaptureControl", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureControl", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCaptureDepthDataOutput",
        b"alwaysDiscardsLateDepthData",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDepthDataOutput", b"isFilteringEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDepthDataOutput",
        b"setAlwaysDiscardsLateDepthData:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDepthDataOutput",
        b"setFilteringEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDepthDataOutput_Tundra",
        b"alwaysDiscardsLateDepthData",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDepthDataOutput_Tundra",
        b"isFilteringEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDepthDataOutput_Tundra",
        b"setAlwaysDiscardsLateDepthData:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDepthDataOutput_Tundra",
        b"setFilteringEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDeskViewApplication",
        b"presentWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureDeskViewApplication",
        b"presentWithLaunchConfiguration:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureDeskViewApplicationLaunchConfiguration",
        b"requiresSetUpModeCompletion",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeskViewApplicationLaunchConfiguration",
        b"setRequiresSetUpModeCompletion:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"activeDepthDataMinFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice",
        b"activeMaxExposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice",
        b"activeVideoMaxFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice",
        b"activeVideoMinFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice",
        b"automaticallyAdjustsFaceDrivenAutoExposureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice",
        b"automaticallyAdjustsFaceDrivenAutoFocusEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice",
        b"automaticallyAdjustsVideoHDREnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice",
        b"automaticallyEnablesLowLightBoostWhenAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice", b"canPerformReactionEffects", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"chromaticityValuesForDeviceWhiteBalanceGains:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceChromaticityValues=ff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
        },
    )
    r(
        b"AVCaptureDevice",
        b"deviceWhiteBalanceGains",
        {"retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
    )
    r(
        b"AVCaptureDevice",
        b"deviceWhiteBalanceGainsForChromaticityValues:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceChromaticityValues=ff}"}},
        },
    )
    r(
        b"AVCaptureDevice",
        b"deviceWhiteBalanceGainsForTemperatureAndTintValues:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
            "arguments": {
                2: {"type": b"{AVCaptureWhiteBalanceTemperatureAndTintValues=ff}"}
            },
        },
    )
    r(b"AVCaptureDevice", b"exposureDuration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVCaptureDevice",
        b"grayWorldDeviceWhiteBalanceGains",
        {"retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
    )
    r(b"AVCaptureDevice", b"hasFlash", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"hasMediaType:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"hasTorch", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isAdjustingExposure", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isAdjustingFocus", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isAdjustingWhiteBalance", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"isAutoFocusRangeRestrictionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice", b"isAutoVideoFrameRateEnabled", {"retval": {"type": "Z"}})
    r(b"AVCaptureDevice", b"isBackgroundReplacementActive", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isBackgroundReplacementEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isCenterStageActive", {"retval": {"type": "Z"}})
    r(b"AVCaptureDevice", b"isCenterStageEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"isCenterStageRectOfInterestSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice", b"isConnected", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isContinuityCamera", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isExposureModeSupported:", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"isExposurePointOfInterestSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice",
        b"isFaceDrivenAutoExposureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice", b"isFaceDrivenAutoFocusEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isFlashActive", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isFlashAvailable", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isFlashModeSupported:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isFocusModeSupported:", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"isFocusPointOfInterestSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice",
        b"isGeometricDistortionCorrectionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice",
        b"isGeometricDistortionCorrectionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice", b"isGlobalToneMappingEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isInUseByAnotherApplication", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"isLockingFocusWithCustomLensPositionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice",
        b"isLockingWhiteBalanceWithCustomDeviceGainsSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice", b"isLowLightBoostEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isLowLightBoostSupported", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isPortraitEffectActive", {"retval": {"type": "Z"}})
    r(b"AVCaptureDevice", b"isPortraitEffectEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isRampingVideoZoom", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isSmoothAutoFocusEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isSmoothAutoFocusSupported", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isStudioLightActive", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isStudioLightEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"isSubjectAreaChangeMonitoringEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice", b"isSuspended", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isTorchActive", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isTorchAvailable", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isTorchModeSupported:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isVideoHDREnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isVirtualDevice", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"isWhiteBalanceModeSupported:", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"lockForConfiguration:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(b"AVCaptureDevice", b"reactionEffectGesturesEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice", b"reactionEffectsEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice",
        b"requestAccessForMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureDevice",
        b"setActiveDepthDataMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setActiveMaxExposureDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setActiveVideoMaxFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setActiveVideoMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setAutoVideoFrameRateEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setAutomaticallyAdjustsFaceDrivenAutoExposureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setAutomaticallyAdjustsFaceDrivenAutoFocusEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setAutomaticallyAdjustsVideoHDREnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setAutomaticallyEnablesLowLightBoostWhenAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCaptureDevice", b"setCenterStageEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCaptureDevice",
        b"setExposureModeCustomWithDuration:ISO:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVCaptureDevice",
        b"setExposureTargetBias:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureDevice",
        b"setFaceDrivenAutoExposureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setFaceDrivenAutoFocusEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setFocusModeLockedWithLensPosition:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureDevice",
        b"setGeometricDistortionCorrectionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setGlobalToneMappingEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setSmoothAutoFocusEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCaptureDevice", b"setStudioLightEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCaptureDevice",
        b"setSubjectAreaChangeMonitoringEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice",
        b"setTorchModeOnWithLevel:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"AVCaptureDevice", b"setVideoHDREnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCaptureDevice",
        b"setWhiteBalanceModeLockedWithDeviceWhiteBalanceGains:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVCaptureDevice",
        b"supportsAVCaptureSessionPreset:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice",
        b"temperatureAndTintValuesForDeviceWhiteBalanceGains:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceTemperatureAndTintValues=ff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
        },
    )
    r(b"AVCaptureDevice", b"transportControlsSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDeviceFormat",
        b"highResolutionStillImageDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureDeviceFormat",
        b"isAutoVideoFrameRateSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat",
        b"isBackgroundReplacementSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDeviceFormat", b"isCenterStageSupported", {"retval": {"type": "Z"}})
    r(
        b"AVCaptureDeviceFormat",
        b"isGlobalToneMappingSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat",
        b"isHighPhotoQualitySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat",
        b"isHighestPhotoQualitySupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDeviceFormat", b"isMultiCamSupported", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDeviceFormat", b"isPortraitEffectSupported", {"retval": {"type": "Z"}})
    r(
        b"AVCaptureDeviceFormat",
        b"isPortraitEffectsMatteStillImageDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat",
        b"isSpatialVideoCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDeviceFormat", b"isStudioLightSupported", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDeviceFormat", b"isVideoBinned", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDeviceFormat", b"isVideoHDRSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDeviceFormat",
        b"isVideoStabilizationModeSupported:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat",
        b"isVideoStabilizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat",
        b"maxExposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDeviceFormat",
        b"minExposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVCaptureDeviceFormat", b"reactionEffectsSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDeviceFormat",
        b"zoomFactorsOutsideOfVideoZoomRangesForDepthDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"highResolutionStillImageDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isAutoVideoFrameRateSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isBackgroundReplacementSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isCenterStageSupported",
        {"retval": {"type": "Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isGlobalToneMappingSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isHighPhotoQualitySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isHighestPhotoQualitySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isMultiCamSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isPortraitEffectSupported",
        {"retval": {"type": "Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isPortraitEffectsMatteStillImageDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isSpatialVideoCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isStudioLightSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDeviceFormat_Tundra", b"isVideoBinned", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isVideoHDRSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isVideoStabilizationModeSupported:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"isVideoStabilizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"maxExposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"minExposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"reactionEffectsSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceFormat_Tundra",
        b"zoomFactorsOutsideOfVideoZoomRangesForDepthDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceInput",
        b"deviceInputWithDevice:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaptureDeviceInput",
        b"initWithDevice:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaptureDeviceInput",
        b"isMultichannelAudioModeSupported:",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDeviceInput", b"isWindNoiseRemovalEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDeviceInput",
        b"isWindNoiseRemovalSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceInput",
        b"setUnifiedAutoExposureDefaultsEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDeviceInput",
        b"setVideoMinFrameDurationOverride:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDeviceInput",
        b"setWindNoiseRemovalEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDeviceInput",
        b"unifiedAutoExposureDefaultsEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceInput",
        b"videoMinFrameDurationOverride",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"deviceInputWithDevice:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"initWithDevice:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"isMultichannelAudioModeSupported:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"isWindNoiseRemovalEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"isWindNoiseRemovalSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"setUnifiedAutoExposureDefaultsEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"setVideoMinFrameDurationOverride:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"setWindNoiseRemovalEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"unifiedAutoExposureDefaultsEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDeviceInput_Tundra",
        b"videoMinFrameDurationOverride",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"activeDepthDataMinFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"activeMaxExposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"activeVideoMaxFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"activeVideoMinFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"automaticallyAdjustsFaceDrivenAutoExposureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"automaticallyAdjustsFaceDrivenAutoFocusEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"automaticallyAdjustsVideoHDREnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"automaticallyEnablesLowLightBoostWhenAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"canPerformReactionEffects",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"chromaticityValuesForDeviceWhiteBalanceGains:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceChromaticityValues=ff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"deviceWhiteBalanceGains",
        {"retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"deviceWhiteBalanceGainsForChromaticityValues:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceChromaticityValues=ff}"}},
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"deviceWhiteBalanceGainsForTemperatureAndTintValues:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
            "arguments": {
                2: {"type": b"{AVCaptureWhiteBalanceTemperatureAndTintValues=ff}"}
            },
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"exposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"grayWorldDeviceWhiteBalanceGains",
        {"retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
    )
    r(b"AVCaptureDevice_Tundra", b"hasFlash", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"hasMediaType:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"hasTorch", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isAdjustingExposure", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isAdjustingFocus", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isAdjustingWhiteBalance", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"isAutoFocusRangeRestrictionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isAutoVideoFrameRateEnabled",
        {"retval": {"type": "Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isBackgroundReplacementActive",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isBackgroundReplacementEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice_Tundra", b"isCenterStageActive", {"retval": {"type": "Z"}})
    r(b"AVCaptureDevice_Tundra", b"isCenterStageEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"isCenterStageRectOfInterestSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice_Tundra", b"isConnected", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isContinuityCamera", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"isExposureModeSupported:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isExposurePointOfInterestSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isFaceDrivenAutoExposureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isFaceDrivenAutoFocusEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice_Tundra", b"isFlashActive", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isFlashAvailable", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isFlashModeSupported:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isFocusModeSupported:", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"isFocusPointOfInterestSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isGeometricDistortionCorrectionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isGeometricDistortionCorrectionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isGlobalToneMappingEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isInUseByAnotherApplication",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isLockingFocusWithCustomLensPositionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isLockingWhiteBalanceWithCustomDeviceGainsSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice_Tundra", b"isLowLightBoostEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"isLowLightBoostSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice_Tundra", b"isPortraitEffectActive", {"retval": {"type": "Z"}})
    r(b"AVCaptureDevice_Tundra", b"isPortraitEffectEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isRampingVideoZoom", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"isSmoothAutoFocusEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"isSmoothAutoFocusSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice_Tundra", b"isStudioLightActive", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isStudioLightEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"isSubjectAreaChangeMonitoringEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice_Tundra", b"isSuspended", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isTorchActive", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isTorchAvailable", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isTorchModeSupported:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isVideoHDREnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureDevice_Tundra", b"isVirtualDevice", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"isWhiteBalanceModeSupported:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"lockForConfiguration:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"reactionEffectGesturesEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureDevice_Tundra", b"reactionEffectsEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureDevice_Tundra",
        b"requestAccessForMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setActiveDepthDataMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setActiveMaxExposureDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setActiveVideoMaxFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setActiveVideoMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setAutoVideoFrameRateEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setAutomaticallyAdjustsFaceDrivenAutoExposureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setAutomaticallyAdjustsFaceDrivenAutoFocusEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setAutomaticallyAdjustsVideoHDREnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setAutomaticallyEnablesLowLightBoostWhenAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setCenterStageEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setExposureModeCustomWithDuration:ISO:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setExposureTargetBias:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setFaceDrivenAutoExposureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setFaceDrivenAutoFocusEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setFocusModeLockedWithLensPosition:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setGeometricDistortionCorrectionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setGlobalToneMappingEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setSmoothAutoFocusEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setStudioLightEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setSubjectAreaChangeMonitoringEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setTorchModeOnWithLevel:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setVideoHDREnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"setWhiteBalanceModeLockedWithDeviceWhiteBalanceGains:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"supportsAVCaptureSessionPreset:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"temperatureAndTintValuesForDeviceWhiteBalanceGains:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceTemperatureAndTintValues=ff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
        },
    )
    r(
        b"AVCaptureDevice_Tundra",
        b"transportControlsSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureFileOutput", b"isRecording", {"retval": {"type": b"Z"}})
    r(b"AVCaptureFileOutput", b"isRecordingPaused", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureFileOutput",
        b"maxRecordedDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureFileOutput",
        b"recordedDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureFileOutput",
        b"setMaxRecordedDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVCaptureFileOutput_Tundra", b"isRecording", {"retval": {"type": b"Z"}})
    r(b"AVCaptureFileOutput_Tundra", b"isRecordingPaused", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureFileOutput_Tundra",
        b"maxRecordedDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureFileOutput_Tundra",
        b"recordedDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureFileOutput_Tundra",
        b"setMaxRecordedDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureIndexPicker",
        b"initWithLocalizedTitle:symbolName:numberOfIndexes:localizedTitleTransform:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureIndexPicker",
        b"setActionQueue:action:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(b"AVCaptureInputPort", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureInputPort", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"AVCaptureInputPort_Tundra", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCaptureInputPort_Tundra", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVCaptureManualExposureBracketedStillImageSettings",
        b"exposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureManualExposureBracketedStillImageSettings",
        b"manualExposureSettingsWithExposureDuration:ISO:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureManualExposureBracketedStillImageSettings_Tundra",
        b"exposureDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureManualExposureBracketedStillImageSettings_Tundra",
        b"manualExposureSettingsWithExposureDuration:ISO:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureMetadataInput",
        b"appendTimedMetadataGroup:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMetadataInput_Tundra",
        b"appendTimedMetadataGroup:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"isPrimaryConstituentDeviceSwitchingBehaviorForRecordingEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"isSpatialVideoCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"isSpatialVideoCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"movieFragmentInterval",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"recordsVideoOrientationAndMirroringChangesAsMetadataTrackForConnection:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"setMovieFragmentInterval:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"setPrimaryConstituentDeviceSwitchingBehaviorForRecordingEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"setRecordsVideoOrientationAndMirroringChanges:asMetadataTrackForConnection:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureMovieFileOutput",
        b"setSpatialVideoCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"isPrimaryConstituentDeviceSwitchingBehaviorForRecordingEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"isSpatialVideoCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"isSpatialVideoCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"movieFragmentInterval",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"recordsVideoOrientationAndMirroringChangesAsMetadataTrackForConnection:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"setMovieFragmentInterval:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"setPrimaryConstituentDeviceSwitchingBehaviorForRecordingEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"setRecordsVideoOrientationAndMirroringChanges:asMetadataTrackForConnection:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureMovieFileOutput_Tundra",
        b"setSpatialVideoCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCaptureMultiCamSession", b"isMultiCamSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureMultiCamSession_Tundra",
        b"isMultiCamSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhoto", b"isConstantColorFallbackPhoto", {"retval": {"type": b"Z"}})
    r(b"AVCapturePhoto", b"isRawPhoto", {"retval": {"type": b"Z"}})
    r(b"AVCapturePhoto", b"timestamp", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVCapturePhotoBracketSettings",
        b"isLensStabilizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoBracketSettings",
        b"setLensStabilizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoBracketSettings_Tundra",
        b"isLensStabilizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoBracketSettings_Tundra",
        b"setLensStabilizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCapturePhotoOutput", b"isAppleProRAWEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCapturePhotoOutput", b"isAppleProRAWPixelFormat:", {"retval": {"type": b"Z"}})
    r(b"AVCapturePhotoOutput", b"isAppleProRAWSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoOutput",
        b"isAutoDeferredPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isAutoDeferredPhotoDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isAutoRedEyeReductionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhotoOutput", b"isBayerRAWPixelFormat:", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoOutput",
        b"isCameraCalibrationDataDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhotoOutput", b"isConstantColorEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCapturePhotoOutput", b"isConstantColorSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoOutput",
        b"isContentAwareDistortionCorrectionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isContentAwareDistortionCorrectionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isDepthDataDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isDepthDataDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isDualCameraDualPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isDualCameraDualPhotoDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isDualCameraFusionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isFastCapturePrioritizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isFastCapturePrioritizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhotoOutput", b"isFlashScene", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoOutput",
        b"isHighResolutionCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isLensStabilizationDuringBracketedCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isLivePhotoAutoTrimmingEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhotoOutput", b"isLivePhotoCaptureEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoOutput",
        b"isLivePhotoCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isLivePhotoCaptureSuspended",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isPortraitEffectsMatteDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isPortraitEffectsMatteDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isResponsiveCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isResponsiveCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isShutterSoundSuppressionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isStillImageStabilizationScene",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isStillImageStabilizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isVirtualDeviceConstituentPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isVirtualDeviceConstituentPhotoDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"isVirtualDeviceFusionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhotoOutput", b"isZeroShutterLagEnabled", {"retval": {"type": b"Z"}})
    r(b"AVCapturePhotoOutput", b"isZeroShutterLagSupported", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoOutput",
        b"maxPhotoDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"preservesLivePhotoCaptureSuspendedOnSessionStop",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setAppleProRAWEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setAutoDeferredPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setConstantColorEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setContentAwareDistortionCorrectionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setDepthDataDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setDualCameraDualPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setFastCapturePrioritizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setFastCapturePrioritizationSupported:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setHighResolutionCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setLivePhotoAutoTrimmingEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setLivePhotoCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setLivePhotoCaptureSuspended:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setMaxPhotoDimensions:",
        {"arguments": {2: {"type": b"{_CMVideoDimensions=ii}"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setPortraitEffectsMatteDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setPreparedPhotoSettingsArray:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCapturePhotoOutput",
        b"setPreservesLivePhotoCaptureSuspendedOnSessionStop:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setResponsiveCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setVirtualDeviceConstituentPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput",
        b"setZeroShutterLagEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isAppleProRAWEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isAppleProRAWPixelFormat:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isAppleProRAWSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isAutoDeferredPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isAutoDeferredPhotoDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isAutoRedEyeReductionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isBayerRAWPixelFormat:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isCameraCalibrationDataDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isConstantColorEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isConstantColorSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isContentAwareDistortionCorrectionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isContentAwareDistortionCorrectionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isDepthDataDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isDepthDataDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isDualCameraDualPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isDualCameraDualPhotoDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isDualCameraFusionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isFastCapturePrioritizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isFastCapturePrioritizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhotoOutput_Tundra", b"isFlashScene", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isHighResolutionCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isLensStabilizationDuringBracketedCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isLivePhotoAutoTrimmingEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isLivePhotoCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isLivePhotoCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isLivePhotoCaptureSuspended",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isPortraitEffectsMatteDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isPortraitEffectsMatteDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isResponsiveCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isResponsiveCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isShutterSoundSuppressionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isStillImageStabilizationScene",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isStillImageStabilizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isVirtualDeviceConstituentPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isVirtualDeviceConstituentPhotoDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isVirtualDeviceFusionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isZeroShutterLagEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"isZeroShutterLagSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"maxPhotoDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"preservesLivePhotoCaptureSuspendedOnSessionStop",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setAppleProRAWEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setAutoDeferredPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setConstantColorEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setContentAwareDistortionCorrectionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setDepthDataDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setDualCameraDualPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setFastCapturePrioritizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setFastCapturePrioritizationSupported:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setHighResolutionCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setLivePhotoAutoTrimmingEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setLivePhotoCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setLivePhotoCaptureSuspended:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setMaxPhotoDimensions:",
        {"arguments": {2: {"type": b"{_CMVideoDimensions=ii}"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setPortraitEffectsMatteDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setPreparedPhotoSettingsArray:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setPreservesLivePhotoCaptureSuspendedOnSessionStop:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setResponsiveCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setVirtualDeviceConstituentPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoOutput_Tundra",
        b"setZeroShutterLagEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCapturePhotoSettings", b"embedsDepthDataInPhoto", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoSettings",
        b"embedsPortraitEffectsMatteInPhoto",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"embedsSemanticSegmentationMattesInPhoto",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isAutoContentAwareDistortionCorrectionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isAutoDualCameraFusionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isAutoRedEyeReductionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isAutoStillImageStabilizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isAutoVirtualDeviceFusionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isCameraCalibrationDataDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhotoSettings", b"isConstantColorEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoSettings",
        b"isConstantColorFallbackPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isDepthDataDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhotoSettings", b"isDepthDataFiltered", {"retval": {"type": b"Z"}})
    r(
        b"AVCapturePhotoSettings",
        b"isDualCameraDualPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isHighResolutionPhotoEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isPortraitEffectsMatteDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"isShutterSoundSuppressionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"maxPhotoDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setAutoContentAwareDistortionCorrectionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setAutoDualCameraFusionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setAutoRedEyeReductionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setAutoStillImageStabilizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setAutoVirtualDeviceFusionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setCameraCalibrationDataDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setConstantColorEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setConstantColorFallbackPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setDepthDataDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setDepthDataFiltered:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setDualCameraDualPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setEmbedsDepthDataInPhoto:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setEmbedsPortraitEffectsMatteInPhoto:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setEmbedsSemanticSegmentationMattesInPhoto:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setHighResolutionPhotoEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setMaxPhotoDimensions:",
        {"arguments": {2: {"type": b"{_CMVideoDimensions=ii}"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setPortraitEffectsMatteDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings",
        b"setShutterSoundSuppressionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"embedsDepthDataInPhoto",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"embedsPortraitEffectsMatteInPhoto",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"embedsSemanticSegmentationMattesInPhoto",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isAutoContentAwareDistortionCorrectionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isAutoDualCameraFusionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isAutoRedEyeReductionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isAutoStillImageStabilizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isAutoVirtualDeviceFusionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isCameraCalibrationDataDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isConstantColorEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isConstantColorFallbackPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isDepthDataDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isDepthDataFiltered",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isDualCameraDualPhotoDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isHighResolutionPhotoEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isPortraitEffectsMatteDeliveryEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"isShutterSoundSuppressionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"maxPhotoDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setAutoContentAwareDistortionCorrectionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setAutoDualCameraFusionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setAutoRedEyeReductionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setAutoStillImageStabilizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setAutoVirtualDeviceFusionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setCameraCalibrationDataDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setConstantColorEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setConstantColorFallbackPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setDepthDataDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setDepthDataFiltered:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setDualCameraDualPhotoDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setEmbedsDepthDataInPhoto:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setEmbedsPortraitEffectsMatteInPhoto:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setEmbedsSemanticSegmentationMattesInPhoto:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setHighResolutionPhotoEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setMaxPhotoDimensions:",
        {"arguments": {2: {"type": b"{_CMVideoDimensions=ii}"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setPortraitEffectsMatteDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhotoSettings_Tundra",
        b"setShutterSoundSuppressionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCapturePhoto_Tundra",
        b"isConstantColorFallbackPhoto",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCapturePhoto_Tundra", b"isRawPhoto", {"retval": {"type": b"Z"}})
    r(b"AVCapturePhoto_Tundra", b"timestamp", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"dimensionsForSemanticSegmentationMatteOfType:",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"embeddedThumbnailDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"isContentAwareDistortionCorrectionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"isDualCameraFusionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"isFastCapturePrioritizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureResolvedPhotoSettings", b"isFlashEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"isRedEyeReductionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"isStillImageStabilizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"isVirtualDeviceFusionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"livePhotoMovieDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"photoDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"photoProcessingTimeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"portraitEffectsMatteDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"previewDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"rawEmbeddedThumbnailDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings",
        b"rawPhotoDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"dimensionsForSemanticSegmentationMatteOfType:",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"embeddedThumbnailDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"isContentAwareDistortionCorrectionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"isDualCameraFusionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"isFastCapturePrioritizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"isFlashEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"isRedEyeReductionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"isStillImageStabilizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"isVirtualDeviceFusionEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"livePhotoMovieDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"photoDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"photoProcessingTimeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"portraitEffectsMatteDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"previewDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"rawEmbeddedThumbnailDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(
        b"AVCaptureResolvedPhotoSettings_Tundra",
        b"rawPhotoDimensions",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(b"AVCaptureScreenInput", b"capturesCursor", {"retval": {"type": b"Z"}})
    r(b"AVCaptureScreenInput", b"capturesMouseClicks", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureScreenInput",
        b"minFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVCaptureScreenInput", b"removesDuplicateFrames", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureScreenInput",
        b"setCapturesCursor:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureScreenInput",
        b"setCapturesMouseClicks:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureScreenInput",
        b"setMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureScreenInput",
        b"setRemovesDuplicateFrames:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCaptureScreenInput_Tundra", b"capturesCursor", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureScreenInput_Tundra",
        b"capturesMouseClicks",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureScreenInput_Tundra",
        b"minFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureScreenInput_Tundra",
        b"removesDuplicateFrames",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureScreenInput_Tundra",
        b"setCapturesCursor:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureScreenInput_Tundra",
        b"setCapturesMouseClicks:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureScreenInput_Tundra",
        b"setMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureScreenInput_Tundra",
        b"setRemovesDuplicateFrames:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession",
        b"automaticallyConfiguresApplicationAudioSession",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSession",
        b"automaticallyConfiguresCaptureDeviceForWideColor",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureSession", b"canAddConnection:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession", b"canAddControl:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession", b"canAddInput:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession", b"canAddOutput:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession", b"canSetSessionPreset:", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureSession",
        b"configuresApplicationAudioSessionToMixWithOthers",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureSession", b"isInterrupted", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureSession",
        b"isMultitaskingCameraAccessEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSession",
        b"isMultitaskingCameraAccessSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureSession", b"isRunning", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureSession",
        b"setAutomaticallyConfiguresApplicationAudioSession:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession",
        b"setAutomaticallyConfiguresCaptureDeviceForWideColor:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession",
        b"setConfiguresApplicationAudioSessionToMixWithOthers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession",
        b"setMultitaskingCameraAccessEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession",
        b"setUsesApplicationAudioSession:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCaptureSession", b"supportsControls", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession", b"usesApplicationAudioSession", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureSession_Tundra",
        b"automaticallyConfiguresApplicationAudioSession",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSession_Tundra",
        b"automaticallyConfiguresCaptureDeviceForWideColor",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureSession_Tundra", b"canAddConnection:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession_Tundra", b"canAddControl:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession_Tundra", b"canAddInput:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession_Tundra", b"canAddOutput:", {"retval": {"type": b"Z"}})
    r(b"AVCaptureSession_Tundra", b"canSetSessionPreset:", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureSession_Tundra",
        b"configuresApplicationAudioSessionToMixWithOthers",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureSession_Tundra", b"isInterrupted", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureSession_Tundra",
        b"isMultitaskingCameraAccessEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSession_Tundra",
        b"isMultitaskingCameraAccessSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureSession_Tundra", b"isRunning", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureSession_Tundra",
        b"setAutomaticallyConfiguresApplicationAudioSession:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession_Tundra",
        b"setAutomaticallyConfiguresCaptureDeviceForWideColor:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession_Tundra",
        b"setConfiguresApplicationAudioSessionToMixWithOthers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession_Tundra",
        b"setMultitaskingCameraAccessEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSession_Tundra",
        b"setUsesApplicationAudioSession:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVCaptureSession_Tundra", b"supportsControls", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureSession_Tundra",
        b"usesApplicationAudioSession",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSlider",
        b"setActionQueue:action:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"f"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureStillImageOutput",
        b"automaticallyEnablesStillImageStabilizationWhenAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"captureStillImageAsynchronouslyFromConnection:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMSampleBuffer=}"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"AVCaptureStillImageOutput",
        b"captureStillImageBracketAsynchronouslyFromConnection:withSettingsArray:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMSampleBuffer=}"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureStillImageOutput",
        b"isCapturingStillImage",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"isHighResolutionStillImageOutputEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"isLensStabilizationDuringBracketedCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"isLensStabilizationDuringBracketedCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"isStillImageStabilizationActive",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"isStillImageStabilizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"prepareToCaptureStillImageBracketFromConnection:withSettingsArray:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureStillImageOutput",
        b"setAutomaticallyEnablesStillImageStabilizationWhenAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"setHighResolutionStillImageOutputEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureStillImageOutput",
        b"setLensStabilizationDuringBracketedCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"automaticallyEnablesStillImageStabilizationWhenAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"captureStillImageAsynchronouslyFromConnection:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMSampleBuffer=}"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"captureStillImageBracketAsynchronouslyFromConnection:withSettingsArray:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMSampleBuffer=}"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"isCapturingStillImage",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"isHighResolutionStillImageOutputEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"isLensStabilizationDuringBracketedCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"isLensStabilizationDuringBracketedCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"isStillImageStabilizationActive",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"isStillImageStabilizationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"prepareToCaptureStillImageBracketFromConnection:withSettingsArray:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"setAutomaticallyEnablesStillImageStabilizationWhenAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"setHighResolutionStillImageOutputEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureStillImageOutput_Tundra",
        b"setLensStabilizationDuringBracketedCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureSynchronizedData",
        b"timestamp",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureSynchronizedData_Tundra",
        b"timestamp",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureSynchronizedDepthData",
        b"depthDataWasDropped",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSynchronizedDepthData_Tundra",
        b"depthDataWasDropped",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSynchronizedSampleBufferData",
        b"sampleBufferWasDropped",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSynchronizedSampleBufferData_Tundra",
        b"sampleBufferWasDropped",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureSystemExposureBiasSlider",
        b"initWithDevice:action:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"f"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureSystemZoomSlider",
        b"initWithDevice:action:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"d"}},
                    }
                }
            }
        },
    )
    r(
        b"AVCaptureVideoDataOutput",
        b"alwaysDiscardsLateVideoFrames",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureVideoDataOutput",
        b"automaticallyConfiguresOutputBufferDimensions",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureVideoDataOutput",
        b"deliversPreviewSizedOutputBuffers",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureVideoDataOutput",
        b"minFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureVideoDataOutput",
        b"setAlwaysDiscardsLateVideoFrames:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoDataOutput",
        b"setAutomaticallyConfiguresOutputBufferDimensions:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoDataOutput",
        b"setDeliversPreviewSizedOutputBuffers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoDataOutput",
        b"setMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureVideoDataOutput_Tundra",
        b"alwaysDiscardsLateVideoFrames",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureVideoDataOutput_Tundra",
        b"automaticallyConfiguresOutputBufferDimensions",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureVideoDataOutput_Tundra",
        b"deliversPreviewSizedOutputBuffers",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureVideoDataOutput_Tundra",
        b"minFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVCaptureVideoDataOutput_Tundra",
        b"setAlwaysDiscardsLateVideoFrames:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoDataOutput_Tundra",
        b"setAutomaticallyConfiguresOutputBufferDimensions:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoDataOutput_Tundra",
        b"setDeliversPreviewSizedOutputBuffers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoDataOutput_Tundra",
        b"setMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCaptureVideoPreviewLayer",
        b"automaticallyAdjustsMirroring",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureVideoPreviewLayer", b"isMirrored", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureVideoPreviewLayer",
        b"isMirroringSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureVideoPreviewLayer",
        b"isOrientationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureVideoPreviewLayer", b"isPreviewing", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureVideoPreviewLayer",
        b"setAutomaticallyAdjustsMirroring:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoPreviewLayer",
        b"setMirrored:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoPreviewLayer_Tundra",
        b"automaticallyAdjustsMirroring",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureVideoPreviewLayer_Tundra", b"isMirrored", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureVideoPreviewLayer_Tundra",
        b"isMirroringSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVCaptureVideoPreviewLayer_Tundra",
        b"isOrientationSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVCaptureVideoPreviewLayer_Tundra", b"isPreviewing", {"retval": {"type": b"Z"}})
    r(
        b"AVCaptureVideoPreviewLayer_Tundra",
        b"setAutomaticallyAdjustsMirroring:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVCaptureVideoPreviewLayer_Tundra",
        b"setMirrored:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVComposition",
        b"loadTrackWithTrackID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVComposition",
        b"loadTracksWithMediaCharacteristic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVComposition",
        b"loadTracksWithMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVCompositionTrack", b"hasMediaCharacteristic:", {"retval": {"type": b"Z"}})
    r(
        b"AVCompositionTrack",
        b"samplePresentationTimeForTrackTime:",
        {
            "retval": {"type": b"{CMTime=qiIq}"},
            "arguments": {2: {"type": b"{CMTime=qiIq}"}},
        },
    )
    r(
        b"AVCompositionTrack",
        b"segmentForTrackTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVCompositionTrackSegment",
        b"compositionTrackSegmentWithTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVCompositionTrackSegment",
        b"compositionTrackSegmentWithURL:trackID:sourceTimeRange:targetTimeRange:",
        {
            "arguments": {
                4: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                5: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
            }
        },
    )
    r(
        b"AVCompositionTrackSegment",
        b"initWithTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVCompositionTrackSegment",
        b"initWithURL:trackID:sourceTimeRange:targetTimeRange:",
        {
            "arguments": {
                4: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                5: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
            }
        },
    )
    r(b"AVCompositionTrackSegment", b"isEmpty", {"retval": {"type": b"Z"}})
    r(
        b"AVContentKeyRequest",
        b"canProvidePersistableContentKey",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVContentKeyRequest",
        b"makeStreamingContentKeyRequestDataForApp:contentIdentifier:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVContentKeyRequest",
        b"persistableContentKeyFromKeyVendorResponse:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"AVContentKeyRequest", b"renewsExpiringResponseData", {"retval": {"type": b"Z"}})
    r(
        b"AVContentKeyRequest",
        b"respondByRequestingPersistableContentKeyRequestAndReturnError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"AVContentKeySession",
        b"invalidateAllPersistableContentKeysForApp:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVContentKeySession",
        b"invalidatePersistableContentKey:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVContentKeySession",
        b"makeSecureTokenForExpirationDateOfPersistableContentKey:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVContinuityDevice", b"isConnected", {"retval": {"type": b"Z"}})
    r(b"AVCoordinatedPlaybackParticipant", b"isReadyToPlay", {"retval": {"type": b"Z"}})
    r(
        b"AVCoordinatedPlaybackSuspension",
        b"endProposingNewTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVDelegatingPlaybackCoordinator",
        b"coordinateSeekToTime:options:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVDelegatingPlaybackCoordinatorPauseCommand",
        b"shouldBufferInAnticipationOfPlayback",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVDelegatingPlaybackCoordinatorPlayCommand",
        b"hostClockTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVDelegatingPlaybackCoordinatorPlayCommand",
        b"itemTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVDelegatingPlaybackCoordinatorSeekCommand",
        b"itemTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVDelegatingPlaybackCoordinatorSeekCommand",
        b"shouldBufferInAnticipationOfPlayback",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVDepthData",
        b"depthDataByReplacingDepthDataMapWithPixelBuffer:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVDepthData",
        b"depthDataFromDictionaryRepresentation:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVDepthData",
        b"dictionaryRepresentationForAuxiliaryDataType:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(b"AVDepthData", b"isDepthDataFiltered", {"retval": {"type": b"Z"}})
    r(b"AVExposureBiasRange", b"containsExposureBias:", {"retval": {"type": b"Z"}})
    r(b"AVExternalStorageDevice", b"isConnected", {"retval": {"type": b"Z"}})
    r(
        b"AVExternalStorageDevice",
        b"isNotRecommendedForCaptureUse",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVExternalStorageDevice",
        b"nextAvailableURLsWithPathExtensions:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVExternalStorageDevice",
        b"requestAccessWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"AVExternalStorageDeviceDiscoverySession",
        b"isSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVFragmentedAsset",
        b"loadTrackWithTrackID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVFragmentedAsset",
        b"loadTracksWithMediaCharacteristic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVFragmentedAsset",
        b"loadTracksWithMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVFragmentedMovie",
        b"loadTrackWithTrackID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVFragmentedMovie",
        b"loadTracksWithMediaCharacteristic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVFragmentedMovie",
        b"loadTracksWithMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVFrameRateRange", b"maxFrameDuration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVFrameRateRange", b"minFrameDuration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVFrameRateRange_Tundra",
        b"maxFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVFrameRateRange_Tundra",
        b"minFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVMIDIPlayer",
        b"initWithContentsOfURL:soundBankURL:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMIDIPlayer",
        b"initWithData:soundBankURL:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"AVMIDIPlayer", b"isPlaying", {"retval": {"type": b"Z"}})
    r(
        b"AVMIDIPlayer",
        b"play:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"AVMediaSelection",
        b"mediaSelectionCriteriaCanBeAppliedAutomaticallyToMediaSelectionGroup:",
        {"retval": {"type": "Z"}},
    )
    r(b"AVMediaSelectionGroup", b"allowsEmptySelection", {"retval": {"type": b"Z"}})
    r(b"AVMediaSelectionOption", b"hasMediaCharacteristic:", {"retval": {"type": b"Z"}})
    r(b"AVMediaSelectionOption", b"isPlayable", {"retval": {"type": b"Z"}})
    r(b"AVMetadataFaceObject", b"hasRollAngle", {"retval": {"type": b"Z"}})
    r(b"AVMetadataFaceObject", b"hasYawAngle", {"retval": {"type": b"Z"}})
    r(b"AVMetadataItem", b"duration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVMetadataItem",
        b"loadValuesAsynchronouslyForKeys:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVMetadataItem",
        b"metadataItemWithPropertiesOfMetadataItem:valueLoadingHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"AVMetadataItem",
        b"statusOfValueForKey:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"AVMetadataItem", b"time", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVMetadataObject", b"duration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVMetadataObject", b"time", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVMetricContentKeyRequestEvent",
        b"isClientInitiated",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVMetricErrorEvent", b"didRecover", {"retval": {"type": b"Z"}})
    r(b"AVMetricEventStream", b"addPublisher:", {"retval": {"type": b"Z"}})
    r(b"AVMetricEventStream", b"setSubscriber:queue:", {"retval": {"type": b"Z"}})
    r(
        b"AVMetricHLSMediaSegmentRequestEvent",
        b"isMapSegment",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVMetricHLSPlaylistRequestEvent",
        b"isMultivariantPlaylist",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVMetricMediaResourceRequestEvent",
        b"wasReadFromCache",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVMetricPlayerItemSeekDidCompleteEvent",
        b"didSeekInBuffer",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVMetricPlayerItemVariantSwitchEvent",
        b"didSucceed",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVMovie", b"canContainMovieFragments", {"retval": {"type": b"Z"}})
    r(b"AVMovie", b"containsMovieFragments", {"retval": {"type": "Z"}})
    r(b"AVMovie", b"isCompatibleWithFileType:", {"retval": {"type": "Z"}})
    r(
        b"AVMovie",
        b"loadTrackWithTrackID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMovie",
        b"loadTracksWithMediaCharacteristic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMovie",
        b"loadTracksWithMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMovie",
        b"movieHeaderWithFileType:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMovie",
        b"writeMovieHeaderToURL:fileType:options:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMovieTrack",
        b"mediaDecodeTimeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVMovieTrack",
        b"mediaPresentationTimeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVMusicTrack",
        b"enumerateEventsInRange:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"N^d"},
                            3: {"type": b"N^Z"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVMusicTrack", b"isLoopingEnabled", {"retval": {"type": "Z"}})
    r(b"AVMusicTrack", b"isMuted", {"retval": {"type": "Z"}})
    r(b"AVMusicTrack", b"isSoloed", {"retval": {"type": "Z"}})
    r(b"AVMusicTrack", b"setLoopingEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"AVMusicTrack", b"setMuted:", {"arguments": {2: {"type": "Z"}}})
    r(b"AVMusicTrack", b"setSoloed:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"AVMusicTrack",
        b"setUsesAutomatedParameters:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"AVMusicTrack", b"usesAutomatedParameters", {"retval": {"type": "Z"}})
    r(
        b"AVMutableAudioMixInputParameters",
        b"setVolume:atTime:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVMutableAudioMixInputParameters",
        b"setVolumeRampFromStartVolume:toEndVolume:timeRange:",
        {"arguments": {4: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableCaption",
        b"setTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableCaption",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVMutableComposition",
        b"insertEmptyTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableComposition",
        b"insertTimeRange:ofAsset:atTime:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"AVMutableComposition",
        b"insertTimeRange:ofAsset:atTime:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"AVMutableComposition",
        b"insertTimeRange:ofTracks:atTime:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMutableComposition",
        b"loadTrackWithTrackID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMutableComposition",
        b"loadTracksWithMediaCharacteristic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMutableComposition",
        b"loadTracksWithMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMutableComposition",
        b"removeTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableComposition",
        b"scaleTimeRange:toDuration:",
        {
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                3: {"type": b"{CMTime=qiIq}"},
            }
        },
    )
    r(
        b"AVMutableCompositionTrack",
        b"insertEmptyTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableCompositionTrack",
        b"insertTimeRange:ofTrack:atTime:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"AVMutableCompositionTrack",
        b"insertTimeRanges:ofTracks:atTime:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {4: {"type": b"{CMTime=qiIq}"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(b"AVMutableCompositionTrack", b"isEnabled", {"retval": {"type": b"Z"}})
    r(
        b"AVMutableCompositionTrack",
        b"removeTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableCompositionTrack",
        b"scaleTimeRange:toDuration:",
        {
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                3: {"type": b"{CMTime=qiIq}"},
            }
        },
    )
    r(b"AVMutableCompositionTrack", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVMutableCompositionTrack",
        b"validateTrackSegments:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"AVMutableMetadataItem", b"duration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVMutableMetadataItem",
        b"setDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVMutableMetadataItem",
        b"setTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVMutableMetadataItem", b"time", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVMutableMovie",
        b"initWithData:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMutableMovie",
        b"initWithSettingsFromMovie:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMutableMovie",
        b"initWithURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMutableMovie",
        b"insertEmptyTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableMovie",
        b"insertTimeRange:ofAsset:atTime:copySampleData:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {"type": "Z"},
                6: {"type_modifier": b"o"},
            },
        },
    )
    r(b"AVMutableMovie", b"interleavingPeriod", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVMutableMovie", b"isModified", {"retval": {"type": "Z"}})
    r(
        b"AVMutableMovie",
        b"loadTrackWithTrackID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMutableMovie",
        b"loadTracksWithMediaCharacteristic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMutableMovie",
        b"loadTracksWithMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMutableMovie",
        b"movieWithData:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMutableMovie",
        b"movieWithSettingsFromMovie:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMutableMovie",
        b"movieWithURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMutableMovie",
        b"removeTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableMovie",
        b"scaleTimeRange:toDuration:",
        {
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                3: {"type": b"{CMTime=qiIq}"},
            }
        },
    )
    r(
        b"AVMutableMovie",
        b"setInterleavingPeriod:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVMutableMovie", b"setModified:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"AVMutableMovieTrack",
        b"appendSampleBuffer:decodeTime:presentationTime:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                3: {"type": b"^{CMTime=qiIq}", "type_modifier": b"o"},
                4: {"type": b"^{CMTime=qiIq}", "type_modifier": b"o"},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(b"AVMutableMovieTrack", b"hasMediaCharacteristic:", {"retval": {"type": b"Z"}})
    r(b"AVMutableMovieTrack", b"hasProtectedContent", {"retval": {"type": "Z"}})
    r(
        b"AVMutableMovieTrack",
        b"insertEmptyTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableMovieTrack",
        b"insertMediaTimeRange:intoTimeRange:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
            },
        },
    )
    r(
        b"AVMutableMovieTrack",
        b"insertTimeRange:ofTrack:atTime:copySampleData:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {"type": "Z"},
                6: {"type_modifier": b"o"},
            },
        },
    )
    r(b"AVMutableMovieTrack", b"isEnabled", {"retval": {"type": "Z"}})
    r(b"AVMutableMovieTrack", b"isModified", {"retval": {"type": "Z"}})
    r(
        b"AVMutableMovieTrack",
        b"movieWithURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVMutableMovieTrack",
        b"preferredMediaChunkDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVMutableMovieTrack",
        b"removeTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableMovieTrack",
        b"samplePresentationTimeForTrackTime:",
        {
            "retval": {"type": b"{CMTime=qiIq}"},
            "arguments": {2: {"type": b"{CMTime=qiIq}"}},
        },
    )
    r(
        b"AVMutableMovieTrack",
        b"scaleTimeRange:toDuration:",
        {
            "arguments": {
                2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                3: {"type": b"{CMTime=qiIq}"},
            }
        },
    )
    r(
        b"AVMutableMovieTrack",
        b"segmentForTrackTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVMutableMovieTrack", b"setEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"AVMutableMovieTrack", b"setModified:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"AVMutableMovieTrack",
        b"setPreferredMediaChunkDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVMutableTimedMetadataGroup",
        b"setTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableTimedMetadataGroup",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVMutableVideoComposition",
        b"frameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVMutableVideoComposition",
        b"setFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVMutableVideoComposition",
        b"videoCompositionWithAsset:applyingCIFiltersWithHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVMutableVideoComposition",
        b"videoCompositionWithAsset:applyingCIFiltersWithHandler:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVMutableVideoComposition",
        b"videoCompositionWithPropertiesOfAsset:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMutableVideoComposition",
        b"videoCompositionWithPropertiesOfAsset:prototypeInstruction:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVMutableVideoCompositionInstruction",
        b"enablePostProcessing",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVMutableVideoCompositionInstruction",
        b"setEnablePostProcessing:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVMutableVideoCompositionInstruction",
        b"setTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableVideoCompositionInstruction",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVMutableVideoCompositionLayerInstruction",
        b"setCropRectangle:atTime:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVMutableVideoCompositionLayerInstruction",
        b"setCropRectangleRampFromStartCropRectangle:toEndCropRectangle:timeRange:",
        {"arguments": {4: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableVideoCompositionLayerInstruction",
        b"setOpacity:atTime:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVMutableVideoCompositionLayerInstruction",
        b"setOpacityRampFromStartOpacity:toEndOpacity:timeRange:",
        {"arguments": {4: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVMutableVideoCompositionLayerInstruction",
        b"setTransform:atTime:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVMutableVideoCompositionLayerInstruction",
        b"setTransformRampFromStartTransform:toEndTransform:timeRange:",
        {"arguments": {4: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVOutputSettingsAssistant",
        b"setSourceVideoAverageFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVOutputSettingsAssistant",
        b"setSourceVideoMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVOutputSettingsAssistant",
        b"sourceVideoAverageFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVOutputSettingsAssistant",
        b"sourceVideoMinFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVPlaybackCoordinator",
        b"expectedItemTimeAtHostTime:",
        {
            "retval": {"type": b"{CMTime=qiIq}"},
            "arguments": {2: {"type": b"{CMTime=qiIq}"}},
        },
    )
    r(
        b"AVPlaybackCoordinator",
        b"pauseSnapsToMediaTimeOfOriginator",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVPlaybackCoordinator",
        b"setPauseSnapsToMediaTimeOfOriginator:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayer",
        b"addBoundaryTimeObserverForTimes:queue:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVPlayer",
        b"addPeriodicTimeObserverForInterval:queue:usingBlock:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                },
            }
        },
    )
    r(b"AVPlayer", b"allowsExternalPlayback", {"retval": {"type": "Z"}})
    r(
        b"AVPlayer",
        b"appliesMediaSelectionCriteriaAutomatically",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVPlayer", b"automaticallyWaitsToMinimizeStalling", {"retval": {"type": b"Z"}})
    r(b"AVPlayer", b"currentTime", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVPlayer", b"eligibleForHDRPlayback", {"retval": {"type": b"Z"}})
    r(b"AVPlayer", b"isClosedCaptionDisplayEnabled", {"retval": {"type": b"Z"}})
    r(b"AVPlayer", b"isExternalPlaybackActive", {"retval": {"type": "Z"}})
    r(b"AVPlayer", b"isMuted", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayer",
        b"outputObscuredDueToInsufficientExternalProtection",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVPlayer",
        b"prerollAtRate:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"AVPlayer",
        b"preventsAutomaticBackgroundingDuringVideoPlayback",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVPlayer",
        b"preventsDisplaySleepDuringVideoPlayback",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVPlayer",
        b"seekToDate:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"AVPlayer", b"seekToTime:", {"arguments": {2: {"type": b"{CMTime=qiIq}"}}})
    r(
        b"AVPlayer",
        b"seekToTime:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                },
            }
        },
    )
    r(
        b"AVPlayer",
        b"seekToTime:toleranceBefore:toleranceAfter:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type": b"{CMTime=qiIq}"},
                4: {"type": b"{CMTime=qiIq}"},
            }
        },
    )
    r(
        b"AVPlayer",
        b"seekToTime:toleranceBefore:toleranceAfter:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type": b"{CMTime=qiIq}"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                },
            }
        },
    )
    r(b"AVPlayer", b"setAllowsExternalPlayback:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"AVPlayer",
        b"setAppliesMediaSelectionCriteriaAutomatically:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayer",
        b"setAutomaticallyWaitsToMinimizeStalling:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayer",
        b"setClosedCaptionDisplayEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVPlayer", b"setMuted:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVPlayer",
        b"setPreventsAutomaticBackgroundingDuringVideoPlayback:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayer",
        b"setPreventsDisplaySleepDuringVideoPlayback:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayer",
        b"setRate:time:atHostTime:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}, 4: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayer",
        b"setUsesExternalPlaybackWhileExternalScreenIsActive:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayer",
        b"usesExternalPlaybackWhileExternalScreenIsActive",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"alignsResumptionWithPrimarySegmentBoundary",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"alignsStartWithPrimarySegmentBoundary",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVPlayerInterstitialEvent", b"contentMayVary", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerInterstitialEvent",
        b"interstitialEventWithPrimaryItem:identifier:date:templateItems:restrictions:resumptionOffset:playoutLimit:userDefinedAttributes:",
        {"arguments": {7: {"type": b"{CMTime=qiIq}"}, 8: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"interstitialEventWithPrimaryItem:identifier:time:templateItems:restrictions:resumptionOffset:playoutLimit:userDefinedAttributes:",
        {
            "arguments": {
                4: {"type": b"{CMTime=qiIq}"},
                7: {"type": b"{CMTime=qiIq}"},
                8: {"type": b"{CMTime=qiIq}"},
            }
        },
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"interstitialEventWithPrimaryItem:time:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"playoutLimit",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"resumptionOffset",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"setAlignsResumptionWithPrimarySegmentBoundary:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"setAlignsStartWithPrimarySegmentBoundary:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"setContentMayVary:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"setPlayoutLimit:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"setResumptionOffset:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"setSupplementsPrimaryContent:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"setTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"setWillPlayOnce:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerInterstitialEvent",
        b"supplementsPrimaryContent",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVPlayerInterstitialEvent", b"time", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVPlayerInterstitialEvent", b"willPlayOnce", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerInterstitialEventController",
        b"cancelCurrentEventWithResumptionOffset:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVPlayerItem", b"appliesPerFrameHDRDisplayMetadata", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerItem",
        b"automaticallyHandlesInterstitialEvents",
        {"retval": {"type": "Z"}},
    )
    r(
        b"AVPlayerItem",
        b"automaticallyPreservesTimeOffsetFromLive",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVPlayerItem", b"canPlayFastForward", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"canPlayFastReverse", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"canPlayReverse", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"canPlaySlowForward", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"canPlaySlowReverse", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"canStepBackward", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"canStepForward", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerItem",
        b"canUseNetworkResourcesForLiveStreamingWhilePaused",
        {"retval": {"type": "Z"}},
    )
    r(
        b"AVPlayerItem",
        b"configuredTimeOffsetFromLive",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVPlayerItem", b"currentTime", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(b"AVPlayerItem", b"duration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVPlayerItem",
        b"forwardPlaybackEndTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVPlayerItem",
        b"isApplicationAuthorizedForPlayback",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVPlayerItem", b"isAudioSpatializationAllowed", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerItem",
        b"isAuthorizationRequiredForPlayback",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVPlayerItem", b"isContentAuthorizedForPlayback", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"isPlaybackBufferEmpty", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"isPlaybackBufferFull", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItem", b"isPlaybackLikelyToKeepUp", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerItem",
        b"recommendedTimeOffsetFromLive",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVPlayerItem",
        b"requestContentAuthorizationAsynchronouslyWithTimeoutInterval:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVPlayerItem",
        b"reversePlaybackEndTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVPlayerItem", b"seekToDate:", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerItem",
        b"seekToDate:completionHandler:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                }
            },
        },
    )
    r(b"AVPlayerItem", b"seekToTime:", {"arguments": {2: {"type": b"{CMTime=qiIq}"}}})
    r(
        b"AVPlayerItem",
        b"seekToTime:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                },
            }
        },
    )
    r(
        b"AVPlayerItem",
        b"seekToTime:toleranceBefore:toleranceAfter:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type": b"{CMTime=qiIq}"},
                4: {"type": b"{CMTime=qiIq}"},
            }
        },
    )
    r(
        b"AVPlayerItem",
        b"seekToTime:toleranceBefore:toleranceAfter:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"AVPlayerItem",
        b"seekingWaitsForVideoCompositionRendering",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVPlayerItem",
        b"setAppliesPerFrameHDRDisplayMetadata:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerItem",
        b"setAudioSpatializationAllowed:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerItem",
        b"setAutomaticallyHandlesInterstitialEvents:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"AVPlayerItem",
        b"setAutomaticallyPreservesTimeOffsetFromLive:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerItem",
        b"setCanUseNetworkResourcesForLiveStreamingWhilePaused:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"AVPlayerItem",
        b"setConfiguredTimeOffsetFromLive:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerItem",
        b"setForwardPlaybackEndTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerItem",
        b"setReversePlaybackEndTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerItem",
        b"setSeekingWaitsForVideoCompositionRendering:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVPlayerItem",
        b"setStartsOnFirstEligibleVariant:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVPlayerItem", b"startsOnFirstEligibleVariant", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerItemIntegratedTimeline",
        b"addBoundaryTimeObserverForSegment:offsetsIntoSegment:queue:usingBlock:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"AVPlayerItemIntegratedTimeline",
        b"addPeriodicTimeObserverForInterval:queue:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVPlayerItemIntegratedTimeline",
        b"seekToDate:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"AVPlayerItemIntegratedTimeline",
        b"seekToTime:toleranceBefore:toleranceAfter:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"AVPlayerItemOutput",
        b"itemTimeForCVTimeStamp:",
        {
            "retval": {"type": b"{CMTime=qiIq}"},
            "arguments": {
                2: {"type": b"{_CVTimeStamp=IiqQdq{CVSMPTETime=ssIIIssss}QQ}"}
            },
        },
    )
    r(
        b"AVPlayerItemOutput",
        b"itemTimeForHostTime:",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVPlayerItemOutput",
        b"itemTimeForMachAbsoluteTime:",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVPlayerItemOutput",
        b"setSuppressesPlayerRendering:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVPlayerItemOutput", b"suppressesPlayerRendering", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItemTrack", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"AVPlayerItemTrack", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVPlayerItemVideoOutput",
        b"copyPixelBufferForItemTime:itemTimeForDisplay:",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type": b"^{CMTime=qiIq}", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"AVPlayerItemVideoOutput",
        b"hasNewPixelBufferForItemTime:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVPlayerLayer",
        b"copyDisplayedPixelBuffer",
        {"retval": {"already_cfretained": True}},
    )
    r(b"AVPlayerLayer", b"isReadyForDisplay", {"retval": {"type": b"Z"}})
    r(
        b"AVPlayerLooper",
        b"initWithPlayer:templateItem:timeRange:",
        {"arguments": {4: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVPlayerLooper",
        b"playerLooperWithPlayer:templateItem:timeRange:",
        {"arguments": {4: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVPlayerVideoOutput",
        b"copyTaggedBufferGroupForHostTime:presentationTimeStamp:activeConfiguration:",
        {"arguments": {3: {"type_modifier": b"o"}, 4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVPortraitEffectsMatte",
        b"dictionaryRepresentationForAuxiliaryDataType:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"AVPortraitEffectsMatte",
        b"portraitEffectsMatteByReplacingPortraitEffectsMatteWithPixelBuffer:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVPortraitEffectsMatte",
        b"portraitEffectsMatteFromDictionaryRepresentation:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"AVQueuePlayer", b"canInsertItem:afterItem:", {"retval": {"type": b"Z"}})
    r(b"AVRouteDetector", b"detectsCustomRoutes", {"retval": {"type": b"Z"}})
    r(b"AVRouteDetector", b"isRouteDetectionEnabled", {"retval": {"type": "Z"}})
    r(b"AVRouteDetector", b"multipleRoutesDetected", {"retval": {"type": "Z"}})
    r(
        b"AVRouteDetector",
        b"setDetectsCustomRoutes:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVRouteDetector",
        b"setRouteDetectionEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"AVSampleBufferAudioRenderer",
        b"flushFromSourceTime:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                },
            }
        },
    )
    r(b"AVSampleBufferAudioRenderer", b"isMuted", {"retval": {"type": b"Z"}})
    r(b"AVSampleBufferAudioRenderer", b"setMuted:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"AVSampleBufferDisplayLayer",
        b"hasSufficientMediaDataForReliablePlaybackStart",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVSampleBufferDisplayLayer", b"isReadyForDisplay", {"retval": {"type": b"Z"}})
    r(
        b"AVSampleBufferDisplayLayer",
        b"isReadyForMoreMediaData",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVSampleBufferDisplayLayer",
        b"outputObscuredDueToInsufficientExternalProtection",
        {"retval": {"type": "Z"}},
    )
    r(
        b"AVSampleBufferDisplayLayer",
        b"preventsAutomaticBackgroundingDuringVideoPlayback",
        {"retval": {"type": b"Z"}},
    )
    r(b"AVSampleBufferDisplayLayer", b"preventsCapture", {"retval": {"type": "Z"}})
    r(
        b"AVSampleBufferDisplayLayer",
        b"preventsDisplaySleepDuringVideoPlayback",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVSampleBufferDisplayLayer",
        b"requestMediaDataWhenReadyOnQueue:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVSampleBufferDisplayLayer",
        b"requiresFlushToResumeDecoding",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVSampleBufferDisplayLayer",
        b"setOutputObscuredDueToInsufficientExternalProtection:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"AVSampleBufferDisplayLayer",
        b"setPreventsAutomaticBackgroundingDuringVideoPlayback:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVSampleBufferDisplayLayer",
        b"setPreventsCapture:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"AVSampleBufferDisplayLayer",
        b"setPreventsDisplaySleepDuringVideoPlayback:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVSampleBufferGenerator",
        b"createSampleBufferForRequest:addingToBatch:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVSampleBufferGenerator",
        b"createSampleBufferForRequest:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVSampleBufferGenerator",
        b"notifyOfDataReadyForSampleBuffer:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVSampleBufferGeneratorBatch",
        b"makeDataReadyWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVSampleBufferRenderSynchronizer",
        b"addBoundaryTimeObserverForTimes:queue:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"AVSampleBufferRenderSynchronizer",
        b"addPeriodicTimeObserverForInterval:queue:usingBlock:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVSampleBufferRenderSynchronizer",
        b"currentTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVSampleBufferRenderSynchronizer",
        b"delaysRateChangeUntilHasSufficientMediaData",
        {"retval": {"type": "Z"}},
    )
    r(
        b"AVSampleBufferRenderSynchronizer",
        b"removeRenderer:atTime:completionHandler:",
        {
            "arguments": {
                3: {"type": b"{CMTime=qiIq}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                },
            }
        },
    )
    r(
        b"AVSampleBufferRenderSynchronizer",
        b"setDelaysRateChangeUntilHasSufficientMediaData:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"AVSampleBufferRenderSynchronizer",
        b"setRate:time:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVSampleBufferRenderSynchronizer",
        b"setRate:time:atHostTime:",
        {"arguments": {3: {"type": b"{CMTime=qiIq}"}, 4: {"type": b"{CMTime=qiIq}"}}},
    )
    r(b"AVSampleBufferRequest", b"overrideTime", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVSampleBufferRequest",
        b"setOverrideTime:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"AVSampleBufferVideoRenderer",
        b"copyDisplayedPixelBuffer",
        {"retval": {"already_cfretained": True}},
    )
    r(
        b"AVSampleBufferVideoRenderer",
        b"flushWithRemovalOfDisplayedImage:completionHandler:",
        {
            "arguments": {
                2: {"type": b"Z"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
            }
        },
    )
    r(
        b"AVSampleBufferVideoRenderer",
        b"loadVideoPerformanceMetricsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVSampleBufferVideoRenderer",
        b"requiresFlushToResumeDecoding",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVSampleCursor",
        b"currentChunkInfo",
        {"retval": {"type": b"{AVSampleCursorChunkInfo=qZZZ}"}},
    )
    r(
        b"AVSampleCursor",
        b"currentChunkStorageRange",
        {"retval": {"type": b"{AVSampleCursorStorageRange=qq}"}},
    )
    r(
        b"AVSampleCursor",
        b"currentSampleAudioDependencyInfo",
        {"retval": {"type": b"{AVSampleCursorAudioDependencyInfo=Zq}"}},
    )
    r(
        b"AVSampleCursor",
        b"currentSampleDependencyInfo",
        {"retval": {"type": b"{AVSampleCursorDependencyInfo=ZZZZZZ}"}},
    )
    r(
        b"AVSampleCursor",
        b"currentSampleDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVSampleCursor",
        b"currentSampleStorageRange",
        {"retval": {"type": b"{AVSampleCursorStorageRange=qq}"}},
    )
    r(
        b"AVSampleCursor",
        b"currentSampleSyncInfo",
        {"retval": {"type": b"{AVSampleCursorSyncInfo=ZZZ}"}},
    )
    r(b"AVSampleCursor", b"decodeTimeStamp", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVSampleCursor",
        b"presentationTimeStamp",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVSampleCursor",
        b"samplesWithEarlierDecodeTimeStampsMayHaveLaterPresentationTimeStampsThanCursor:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVSampleCursor",
        b"samplesWithLaterDecodeTimeStampsMayHaveEarlierPresentationTimeStampsThanCursor:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVSampleCursor",
        b"stepByDecodeTime:wasPinned:",
        {
            "retval": {"type": b"{CMTime=qiIq}"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type": b"^Z", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"AVSampleCursor",
        b"stepByPresentationTime:wasPinned:",
        {
            "retval": {"type": b"{CMTime=qiIq}"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type": b"^Z", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"AVSemanticSegmentationMatte",
        b"semanticSegmentationMatteByReplacingSemanticSegmentationMatteWithPixelBuffer:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"AVSemanticSegmentationMatte",
        b"semanticSegmentationMatteFromImageSourceAuxiliaryDataType:dictionaryRepresentation:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"AVSpeechSynthesisProviderAudioUnit",
        b"setSpeechSynthesisOutputMetadataBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVSpeechSynthesisProviderAudioUnit",
        b"speechSynthesisOutputMetadataBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(b"AVSpeechSynthesizer", b"continueSpeaking", {"retval": {"type": "Z"}})
    r(b"AVSpeechSynthesizer", b"isPaused", {"retval": {"type": "Z"}})
    r(b"AVSpeechSynthesizer", b"isSpeaking", {"retval": {"type": "Z"}})
    r(
        b"AVSpeechSynthesizer",
        b"makeSecureTokenForExpirationDateOfPersistableContentKey:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVSpeechSynthesizer", b"mixToTelephonyUplink", {"retval": {"type": b"Z"}})
    r(b"AVSpeechSynthesizer", b"pauseSpeakingAtBoundary:", {"retval": {"type": "Z"}})
    r(
        b"AVSpeechSynthesizer",
        b"requestPersonalVoiceAuthorizationWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"AVSpeechSynthesizer",
        b"setMixToTelephonyUplink:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVSpeechSynthesizer", b"setPaused:", {"arguments": {2: {"type": "Z"}}})
    r(b"AVSpeechSynthesizer", b"setSpeaking:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"AVSpeechSynthesizer",
        b"setUsesApplicationAudioSession:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"AVSpeechSynthesizer", b"stopSpeakingAtBoundary:", {"retval": {"type": "Z"}})
    r(
        b"AVSpeechSynthesizer",
        b"usesApplicationAudioSession",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVSpeechSynthesizer",
        b"writeUtterance:toBufferCallback:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVSpeechSynthesizer",
        b"writeUtterance:toBufferCallback:toMarkerCallback:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"AVSpeechUtterance",
        b"prefersAssistiveTechnologySettings",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVSpeechUtterance",
        b"setPrefersAssistiveTechnologySettings:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"AVTimedMetadataGroup",
        b"initWithItems:timeRange:",
        {"arguments": {3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"AVTimedMetadataGroup",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVURLAsset",
        b"findCompatibleTrackForCompositionTrack:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"AVURLAsset", b"isPlayableExtendedMIMEType:", {"retval": {"type": b"Z"}})
    r(
        b"AVURLAsset",
        b"mayRequireContentKeysForMediaDataProcessing",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVVideoComposition",
        b"determineValidityForAsset:timeRange:validationDelegate:completionHandler:",
        {
            "arguments": {
                3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(b"AVVideoComposition", b"frameDuration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"AVVideoComposition",
        b"isValidForAsset:timeRange:validationDelegate:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
        },
    )
    r(
        b"AVVideoComposition",
        b"isValidForTracks:assetDuration:timeRange:validationDelegate:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVVideoComposition",
        b"videoCompositionWithAsset:applyingCIFiltersWithHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"AVVideoComposition",
        b"videoCompositionWithAsset:applyingCIFiltersWithHandler:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"AVVideoComposition",
        b"videoCompositionWithPropertiesOfAsset:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"AVVideoCompositionInstruction",
        b"enablePostProcessing",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVVideoCompositionInstruction",
        b"timeRange",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"AVVideoCompositionLayerInstruction",
        b"getCropRectangleRampForTime:startCropRectangle:endCropRectangle:timeRange:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type_modifier": b"o"},
                4: {"type_modifier": b"o"},
                5: {
                    "type": b"^{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
                    "type_modifier": b"o",
                },
            },
        },
    )
    r(
        b"AVVideoCompositionLayerInstruction",
        b"getOpacityRampForTime:startOpacity:endOpacity:timeRange:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type_modifier": b"o"},
                4: {"type_modifier": b"o"},
                5: {
                    "type": b"^{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
                    "type_modifier": b"o",
                },
            },
        },
    )
    r(
        b"AVVideoCompositionLayerInstruction",
        b"getTransformRampForTime:startTransform:endTransform:timeRange:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                3: {"type_modifier": b"o"},
                4: {"type_modifier": b"o"},
                5: {
                    "type": b"^{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}",
                    "type_modifier": b"o",
                },
            },
        },
    )
    r(
        b"AVVideoCompositionRenderContext",
        b"edgeWidths",
        {"retval": {"type": b"{AVEdgeWidths=dddd}"}},
    )
    r(
        b"AVVideoCompositionRenderContext",
        b"highQualityRendering",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"AVVideoCompositionRenderContext",
        b"pixelAspectRatio",
        {"retval": {"type": b"{AVPixelAspectRatio=qq}"}},
    )
    r(
        b"AVVideoCompositionRenderHint",
        b"endCompositionTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(
        b"AVVideoCompositionRenderHint",
        b"startCompositionTime",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"AVZoomRange", b"containsZoomFactor:", {"retval": {"type": b"Z"}})
    r(b"NSCoder", b"decodeCMTimeForKey:", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"NSCoder",
        b"decodeCMTimeMappingForKey:",
        {
            "retval": {
                "type": b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}"
            }
        },
    )
    r(
        b"NSCoder",
        b"decodeCMTimeRangeForKey:",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(
        b"NSCoder",
        b"encodeCMTime:forKey:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"NSCoder",
        b"encodeCMTimeMapping:forKey:",
        {
            "arguments": {
                2: {
                    "type": b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}"
                }
            }
        },
    )
    r(
        b"NSCoder",
        b"encodeCMTimeRange:forKey:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"NSObject",
        b"URLSession:aggregateAssetDownloadTask:didCompleteForMediaSelection:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:aggregateAssetDownloadTask:didLoadTimeRange:totalTimeRangesLoaded:timeRangeExpectedToLoad:forMediaSelection:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": "{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                5: {"type": b"@"},
                6: {"type": "{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                7: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:aggregateAssetDownloadTask:willDownloadToURL:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:assetDownloadTask:didFinishDownloadingToURL:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:assetDownloadTask:didLoadTimeRange:totalTimeRangesLoaded:timeRangeExpectedToLoad:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": "{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
                5: {"type": b"@"},
                6: {"type": "{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:assetDownloadTask:didResolveMediaSelection:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:assetDownloadTask:willDownloadToURL:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:assetDownloadTask:willDownloadVariants:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"anticipateRenderingUsingHint:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"assetWriter:didOutputSegmentData:segmentType:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"assetWriter:didOutputSegmentData:segmentType:segmentReport:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"audioPlayerDecodeErrorDidOccur:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"audioPlayerDidFinishPlaying:successfully:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"audioRecorderDidFinishRecording:successfully:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"audioRecorderEncodeErrorDidOccur:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"beginInterruption", {"required": False, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"canConformColorOfSourceFrames",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"cancelAllPendingVideoCompositionRequests",
        {"required": False, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"captionAdaptor:didVendCaption:skippingUnsupportedSourceSyntaxElements:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didCapturePhotoForResolvedSettings:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didDropSampleBuffer:fromConnection:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"^{opaqueCMSampleBuffer=}"},
                4: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didFinishCaptureForResolvedSettings:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didFinishCapturingDeferredPhotoProxy:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didFinishProcessingLivePhotoToMovieFileAtURL:duration:photoDisplayTime:resolvedSettings:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {"type": b"{CMTime=qiIq}"},
                6: {"type": b"@"},
                7: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didFinishProcessingPhoto:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didFinishProcessingPhotoSampleBuffer:previewPhotoSampleBuffer:resolvedSettings:bracketSettings:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"^{opaqueCMSampleBuffer=}"},
                4: {"type": b"^{opaqueCMSampleBuffer=}"},
                5: {"type": b"@"},
                6: {"type": b"@"},
                7: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didFinishProcessingRawPhotoSampleBuffer:previewPhotoSampleBuffer:resolvedSettings:bracketSettings:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"^{opaqueCMSampleBuffer=}"},
                4: {"type": b"^{opaqueCMSampleBuffer=}"},
                5: {"type": b"@"},
                6: {"type": b"@"},
                7: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didFinishRecordingLivePhotoMovieForEventualFileAtURL:resolvedSettings:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didFinishRecordingToOutputFileAtURL:fromConnections:error:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didOutputMetadataObjects:fromConnection:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didOutputSampleBuffer:fromConnection:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"^{opaqueCMSampleBuffer=}"},
                4: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didPauseRecordingToOutputFileAtURL:fromConnections:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didResumeRecordingToOutputFileAtURL:fromConnections:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didStartRecordingToOutputFileAtURL:fromConnections:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:didStartRecordingToOutputFileAtURL:startPTS:fromConnections:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"captureOutput:willBeginCaptureForResolvedSettings:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:willCapturePhotoForResolvedSettings:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"captureOutput:willFinishRecordingToOutputFileAtURL:fromConnections:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"captureOutputShouldProvideSampleAccurateRecordingStart:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"containsTweening", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"contentKeySession:contentKeyRequest:didFailWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:contentKeyRequestDidSucceed:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:didProvideContentKey:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:didProvideContentKeyRequest:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:didProvideContentKeyRequests:forInitializationData:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:didProvidePersistableContentKeyRequest:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:didProvideRenewingContentKeyRequest:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:didUpdatePersistableContentKey:forContentKeyIdentifier:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:externalProtectionStatusDidChangeForContentKey:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySession:shouldRetryContentKeyRequest:reason:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"contentKeySessionContentProtectionSessionIdentifierDidChange:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"contentKeySessionDidGenerateExpiredSessionReport:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"dataOutputSynchronizer:didOutputSynchronizedDataCollection:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"depthDataOutput:didDropDepthData:timestamp:connection:reason:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {"type": b"@"},
                6: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"depthDataOutput:didOutputDepthData:timestamp:connection:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"{CMTime=qiIq}"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"destinationForMixer:bus:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"enablePostProcessing",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(b"NSObject", b"endInterruption", {"required": False, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"endInterruptionWithFlags:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"enqueueSampleBuffer:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"^{opaqueCMSampleBuffer=}"}},
        },
    )
    r(b"NSObject", b"flush", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"hasSufficientMediaDataForReliablePlaybackStart",
        {"required": True, "retval": {"type": "Z"}},
    )
    r(
        b"NSObject",
        b"inputIsAvailableChanged:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"isAssociatedWithFragmentMinder",
        {"required": True, "retval": {"type": "Z"}},
    )
    r(
        b"NSObject",
        b"isReadyForMoreMediaData",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"legibleOutput:didOutputAttributedStrings:nativeSampleBuffers:forItemTime:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"{CMTime=qiIq}"},
            },
        },
    )
    r(
        b"NSObject",
        b"loadValuesAsynchronouslyForKeys:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"mayRequireContentKeysForMediaDataProcessing",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"metadataCollector:didCollectDateRangeMetadataGroups:indexesOfNewGroups:indexesOfModifiedGroups:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"metadataOutput:didOutputTimedMetadataGroups:fromPlayerItemTrack:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"obstruction", {"required": True, "retval": {"type": b"f"}})
    r(b"NSObject", b"occlusion", {"required": True, "retval": {"type": b"f"}})
    r(
        b"NSObject",
        b"outputMediaDataWillChange:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"outputSequenceWasFlushed:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"pan", {"required": True, "retval": {"type": b"f"}})
    r(b"NSObject", b"passthroughTrackID", {"required": True, "retval": {"type": b"i"}})
    r(
        b"NSObject",
        b"playbackCoordinator:didIssueBufferingCommand:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"playbackCoordinator:didIssuePauseCommand:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"playbackCoordinator:didIssuePlayCommand:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"playbackCoordinator:didIssueSeekCommand:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"playbackCoordinator:identifierForPlayerItem:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"playbackCoordinator:interstitialTimeRangesForPlayerItem:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"pointSourceInHeadMode",
        {"required": True, "retval": {"type": b"q"}},
    )
    r(
        b"NSObject",
        b"position",
        {"required": True, "retval": {"type": b"{AVAudio3DPoint=fff}"}},
    )
    r(
        b"NSObject",
        b"prerollForRenderingUsingHint:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"publisher:didReceiveEvent:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"rate", {"required": True, "retval": {"type": b"f"}})
    r(
        b"NSObject",
        b"readinessCoordinator:captureReadinessDidChange:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"renderContextChanged:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"renderedLegibleOutput:didOutputRenderedCaptionImages:forItemTime:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"{CMTime=qiIq}"},
            },
        },
    )
    r(b"NSObject", b"renderingAlgorithm", {"required": True, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"replacementAppleProRAWCompressionSettingsForPhoto:defaultSettings:maximumBitDepth:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"replacementDepthDataForPhoto:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"replacementEmbeddedThumbnailPixelBufferWithPhotoFormat:forPhoto:",
        {
            "required": False,
            "retval": {"type": b"^{__CVBuffer=}"},
            "arguments": {2: {"type": b"^@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"replacementMetadataForPhoto:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"replacementPortraitEffectsMatteForPhoto:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"replacementSemanticSegmentationMatteOfType:forPhoto:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"requestMediaDataWhenReadyOnQueue:usingBlock:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"requiredPixelBufferAttributesForRenderContext",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"requiredSourceSampleDataTrackIDs",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"requiredSourceTrackIDs",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"resourceLoader:didCancelAuthenticationChallenge:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"resourceLoader:didCancelLoadingRequest:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"resourceLoader:shouldWaitForLoadingOfRequestedResource:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"resourceLoader:shouldWaitForRenewalOfRequestedResource:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"resourceLoader:shouldWaitForResponseToAuthenticationChallenge:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"reverbBlend", {"required": True, "retval": {"type": b"f"}})
    r(
        b"NSObject",
        b"sessionControlsDidBecomeActive:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"sessionControlsDidBecomeInactive:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"sessionControlsWillEnterFullscreenAppearance:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"sessionControlsWillExitFullscreenAppearance:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setObstruction:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(
        b"NSObject",
        b"setOcclusion:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(
        b"NSObject",
        b"setPan:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(
        b"NSObject",
        b"setPointSourceInHeadMode:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"setPosition:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{AVAudio3DPoint=fff}"}},
        },
    )
    r(
        b"NSObject",
        b"setRate:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(
        b"NSObject",
        b"setRenderingAlgorithm:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "q"}}},
    )
    r(
        b"NSObject",
        b"setReverbBlend:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(
        b"NSObject",
        b"setSourceMode:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"setVolume:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(b"NSObject", b"sourceMode", {"required": True, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"sourcePixelBufferAttributes",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"speechSynthesizer:didCancelSpeechUtterance:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"speechSynthesizer:didContinueSpeechUtterance:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"speechSynthesizer:didFinishSpeechUtterance:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"speechSynthesizer:didPauseSpeechUtterance:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"speechSynthesizer:didStartSpeechUtterance:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"speechSynthesizer:willSpeakRangeOfSpeechString:utterance:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "{_NSRange=QQ}"},
                4: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"startVideoCompositionRequest:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"statusOfValueForKey:error:",
        {
            "required": True,
            "retval": {"type": b"q"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"stopRequestingMediaData",
        {"required": True, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"supportsHDRSourceFrames",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsWideColorSourceFrames",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"timeRange",
        {
            "required": True,
            "retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
        },
    )
    r(
        b"NSObject",
        b"timebase",
        {"required": True, "retval": {"type": b"^{OpaqueCMTimebase=}"}},
    )
    r(
        b"NSObject",
        b"videoComposition:shouldContinueValidatingAfterFindingEmptyTimeRange:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"},
            },
        },
    )
    r(
        b"NSObject",
        b"videoComposition:shouldContinueValidatingAfterFindingInvalidTimeRangeInInstruction:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"videoComposition:shouldContinueValidatingAfterFindingInvalidTrackIDInInstruction:layerInstruction:asset:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"videoComposition:shouldContinueValidatingAfterFindingInvalidValueForKey:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"volume", {"required": True, "retval": {"type": b"f"}})
    r(
        b"NSValue",
        b"CMTimeMappingValue",
        {
            "retval": {
                "type": b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}"
            }
        },
    )
    r(
        b"NSValue",
        b"CMTimeRangeValue",
        {"retval": {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}},
    )
    r(b"NSValue", b"CMTimeValue", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"NSValue",
        b"CMVideoDimensionsValue",
        {"retval": {"type": b"{_CMVideoDimensions=ii}"}},
    )
    r(b"NSValue", b"valueWithCMTime:", {"arguments": {2: {"type": b"{CMTime=qiIq}"}}})
    r(
        b"NSValue",
        b"valueWithCMTimeMapping:",
        {
            "arguments": {
                2: {
                    "type": b"{CMTimeMapping={CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}}"
                }
            }
        },
    )
    r(
        b"NSValue",
        b"valueWithCMTimeRange:",
        {"arguments": {2: {"type": b"{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}"}}},
    )
    r(
        b"NSValue",
        b"valueWithCMVideoDimensions:",
        {"arguments": {2: {"type": b"{_CMVideoDimensions=ii}"}}},
    )
    r(
        b"null",
        b"activeDepthDataMinFrameDuration",
        {"retval": {"type": b"{CMTime=qiIq}"}},
    )
    r(b"null", b"activeMaxExposureDuration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"null",
        b"automaticallyAdjustsFaceDrivenAutoExposureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"null",
        b"automaticallyAdjustsFaceDrivenAutoFocusEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"null", b"automaticallyAdjustsVideoHDREnabled", {"retval": {"type": b"Z"}})
    r(
        b"null",
        b"automaticallyEnablesLowLightBoostWhenAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"null",
        b"captureStillImageBracketAsynchronouslyFromConnection:withSettingsArray:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{opaqueCMSampleBuffer=}"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"chromaticityValuesForDeviceWhiteBalanceGains:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceChromaticityValues=ff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
        },
    )
    r(
        b"null",
        b"deviceWhiteBalanceGains",
        {"retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
    )
    r(
        b"null",
        b"deviceWhiteBalanceGainsForChromaticityValues:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceChromaticityValues=ff}"}},
        },
    )
    r(
        b"null",
        b"deviceWhiteBalanceGainsForTemperatureAndTintValues:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
            "arguments": {
                2: {"type": b"{AVCaptureWhiteBalanceTemperatureAndTintValues=ff}"}
            },
        },
    )
    r(b"null", b"exposureDuration", {"retval": {"type": b"{CMTime=qiIq}"}})
    r(
        b"null",
        b"grayWorldDeviceWhiteBalanceGains",
        {"retval": {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
    )
    r(b"null", b"hasFlash", {"retval": {"type": b"Z"}})
    r(b"null", b"hasTorch", {"retval": {"type": b"Z"}})
    r(b"null", b"isAdjustingExposure", {"retval": {"type": b"Z"}})
    r(b"null", b"isAdjustingFocus", {"retval": {"type": b"Z"}})
    r(b"null", b"isAdjustingWhiteBalance", {"retval": {"type": b"Z"}})
    r(b"null", b"isAutoFocusRangeRestrictionSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isCenterStageActive", {"retval": {"type": b"Z"}})
    r(b"null", b"isCenterStageEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isCenterStageSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isDepthDataDeliveryEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isDepthDataDeliverySupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isExposureModeSupported:", {"retval": {"type": b"Z"}})
    r(b"null", b"isExposurePointOfInterestSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isFaceDrivenAutoExposureEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isFaceDrivenAutoFocusEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isFlashActive", {"retval": {"type": b"Z"}})
    r(b"null", b"isFlashAvailable", {"retval": {"type": b"Z"}})
    r(b"null", b"isFlashModeSupported:", {"retval": {"type": b"Z"}})
    r(b"null", b"isFocusModeSupported:", {"retval": {"type": b"Z"}})
    r(b"null", b"isFocusPointOfInterestSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isGeometricDistortionCorrectionEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isGeometricDistortionCorrectionSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isGlobalToneMappingEnabled", {"retval": {"type": b"Z"}})
    r(
        b"null",
        b"isLensStabilizationDuringBracketedCaptureEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"null",
        b"isLensStabilizationDuringBracketedCaptureSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"null",
        b"isLockingFocusWithCustomLensPositionSupported",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"null",
        b"isLockingWhiteBalanceWithCustomDeviceGainsSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"null", b"isLowLightBoostEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isLowLightBoostSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isMultiCamSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isPortraitEffectActive", {"retval": {"type": b"Z"}})
    r(b"null", b"isPortraitEffectEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isPortraitEffectSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isPortraitEffectsMatteDeliveryEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isPortraitEffectsMatteDeliverySupported", {"retval": {"type": b"Z"}})
    r(
        b"null",
        b"isPortraitEffectsMatteStillImageDeliverySupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"null", b"isRampingVideoZoom", {"retval": {"type": b"Z"}})
    r(b"null", b"isSmoothAutoFocusEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isSmoothAutoFocusSupported", {"retval": {"type": b"Z"}})
    r(b"null", b"isSubjectAreaChangeMonitoringEnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isTorchActive", {"retval": {"type": b"Z"}})
    r(b"null", b"isTorchAvailable", {"retval": {"type": b"Z"}})
    r(b"null", b"isTorchModeSupported:", {"retval": {"type": b"Z"}})
    r(b"null", b"isVideoHDREnabled", {"retval": {"type": b"Z"}})
    r(b"null", b"isVirtualDevice", {"retval": {"type": b"Z"}})
    r(b"null", b"isWhiteBalanceModeSupported:", {"retval": {"type": b"Z"}})
    r(
        b"null",
        b"mayRequireContentKeysForMediaDataProcessing",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"null",
        b"prepareToCaptureStillImageBracketFromConnection:withSettingsArray:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"requestAccessForMediaType:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"setActiveDepthDataMinFrameDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"null",
        b"setActiveMaxExposureDuration:",
        {"arguments": {2: {"type": b"{CMTime=qiIq}"}}},
    )
    r(
        b"null",
        b"setAutomaticallyAdjustsFaceDrivenAutoExposureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"null",
        b"setAutomaticallyAdjustsFaceDrivenAutoFocusEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"null",
        b"setAutomaticallyAdjustsVideoHDREnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"null",
        b"setAutomaticallyEnablesLowLightBoostWhenAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"null", b"setCenterStageEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"null", b"setDepthDataDeliveryEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"null",
        b"setExposureModeCustomWithDuration:ISO:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{CMTime=qiIq}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"null",
        b"setExposureTargetBias:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                }
            }
        },
    )
    r(b"null", b"setFaceDrivenAutoExposureEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"null", b"setFaceDrivenAutoFocusEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"null",
        b"setFocusModeLockedWithLensPosition:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"setGeometricDistortionCorrectionEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"null", b"setGlobalToneMappingEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"null",
        b"setLensStabilizationDuringBracketedCaptureEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"null",
        b"setPortraitEffectsMatteDeliveryEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"null", b"setSmoothAutoFocusEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"null",
        b"setSubjectAreaChangeMonitoringEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"null", b"setTorchModeOnWithLevel:error:", {"retval": {"type": b"Z"}})
    r(b"null", b"setVideoHDREnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"null",
        b"setWhiteBalanceModeLockedWithDeviceWhiteBalanceGains:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{CMTime=qiIq}"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"null",
        b"temperatureAndTintValuesForDeviceWhiteBalanceGains:",
        {
            "retval": {"type": b"{AVCaptureWhiteBalanceTemperatureAndTintValues=ff}"},
            "arguments": {2: {"type": b"{AVCaptureWhiteBalanceGains=fff}"}},
        },
    )
    r(b"null", b"transportControlsSupported", {"retval": {"type": b"Z"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "AVAssetExportSession", b"initWithAsset:presetName:"
)
objc.registerNewKeywordsFromSelector("AVAssetImageGenerator", b"initWithAsset:")
objc.registerNewKeywordsFromSelector("AVAssetReader", b"initWithAsset:error:")
objc.registerNewKeywordsFromSelector(
    "AVAssetReaderAudioMixOutput", b"initWithAudioTracks:audioSettings:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetReaderOutputCaptionAdaptor", b"initWithAssetReaderTrackOutput:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetReaderOutputMetadataAdaptor", b"initWithAssetReaderTrackOutput:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetReaderSampleReferenceOutput", b"initWithTrack:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetReaderTrackOutput", b"initWithTrack:outputSettings:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetReaderVideoCompositionOutput", b"initWithVideoTracks:videoSettings:"
)
objc.registerNewKeywordsFromSelector("AVAssetWriter", b"initWithContentType:")
objc.registerNewKeywordsFromSelector("AVAssetWriter", b"initWithURL:fileType:error:")
objc.registerNewKeywordsFromSelector(
    "AVAssetWriterInput", b"initWithMediaType:outputSettings:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetWriterInput", b"initWithMediaType:outputSettings:sourceFormatHint:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetWriterInputCaptionAdaptor", b"initWithAssetWriterInput:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetWriterInputGroup", b"initWithInputs:defaultInput:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetWriterInputMetadataAdaptor", b"initWithAssetWriterInput:"
)
objc.registerNewKeywordsFromSelector(
    "AVAssetWriterInputPixelBufferAdaptor",
    b"initWithAssetWriterInput:sourcePixelBufferAttributes:",
)
objc.registerNewKeywordsFromSelector(
    "AVAssetWriterInputTaggedPixelBufferGroupAdaptor",
    b"initWithAssetWriterInput:sourcePixelBufferAttributes:",
)
objc.registerNewKeywordsFromSelector("AVAudioChannelLayout", b"initWithLayout:")
objc.registerNewKeywordsFromSelector("AVAudioChannelLayout", b"initWithLayoutTag:")
objc.registerNewKeywordsFromSelector(
    "AVAudioCompressedBuffer", b"initWithFormat:packetCapacity:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioCompressedBuffer", b"initWithFormat:packetCapacity:maximumPacketSize:"
)
objc.registerNewKeywordsFromSelector("AVAudioConnectionPoint", b"initWithNode:bus:")
objc.registerNewKeywordsFromSelector("AVAudioConverter", b"initFromFormat:toFormat:")
objc.registerNewKeywordsFromSelector(
    "AVAudioFile", b"initForReading:commonFormat:interleaved:error:"
)
objc.registerNewKeywordsFromSelector("AVAudioFile", b"initForReading:error:")
objc.registerNewKeywordsFromSelector(
    "AVAudioFile", b"initForWriting:settings:commonFormat:interleaved:error:"
)
objc.registerNewKeywordsFromSelector("AVAudioFile", b"initForWriting:settings:error:")
objc.registerNewKeywordsFromSelector(
    "AVAudioFormat", b"initStandardFormatWithSampleRate:channelLayout:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioFormat", b"initStandardFormatWithSampleRate:channels:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioFormat", b"initWithCMAudioFormatDescription:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioFormat", b"initWithCommonFormat:sampleRate:channels:interleaved:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioFormat", b"initWithCommonFormat:sampleRate:interleaved:channelLayout:"
)
objc.registerNewKeywordsFromSelector("AVAudioFormat", b"initWithSettings:")
objc.registerNewKeywordsFromSelector("AVAudioFormat", b"initWithStreamDescription:")
objc.registerNewKeywordsFromSelector(
    "AVAudioFormat", b"initWithStreamDescription:channelLayout:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioPCMBuffer", b"initWithPCMFormat:frameCapacity:"
)
objc.registerNewKeywordsFromSelector("AVAudioPlayer", b"initWithContentsOfURL:error:")
objc.registerNewKeywordsFromSelector(
    "AVAudioPlayer", b"initWithContentsOfURL:fileTypeHint:error:"
)
objc.registerNewKeywordsFromSelector("AVAudioPlayer", b"initWithData:error:")
objc.registerNewKeywordsFromSelector(
    "AVAudioPlayer", b"initWithData:fileTypeHint:error:"
)
objc.registerNewKeywordsFromSelector("AVAudioRecorder", b"initWithURL:format:error:")
objc.registerNewKeywordsFromSelector("AVAudioRecorder", b"initWithURL:settings:error:")
objc.registerNewKeywordsFromSelector("AVAudioSequencer", b"initWithAudioEngine:")
objc.registerNewKeywordsFromSelector("AVAudioSinkNode", b"initWithReceiverBlock:")
objc.registerNewKeywordsFromSelector(
    "AVAudioSourceNode", b"initWithFormat:renderBlock:"
)
objc.registerNewKeywordsFromSelector("AVAudioSourceNode", b"initWithRenderBlock:")
objc.registerNewKeywordsFromSelector(
    "AVAudioTime", b"initWithAudioTimeStamp:sampleRate:"
)
objc.registerNewKeywordsFromSelector("AVAudioTime", b"initWithHostTime:")
objc.registerNewKeywordsFromSelector(
    "AVAudioTime", b"initWithHostTime:sampleTime:atRate:"
)
objc.registerNewKeywordsFromSelector("AVAudioTime", b"initWithSampleTime:atRate:")
objc.registerNewKeywordsFromSelector("AVAudioUnitEQ", b"initWithNumberOfBands:")
objc.registerNewKeywordsFromSelector(
    "AVAudioUnitEffect", b"initWithAudioComponentDescription:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioUnitGenerator", b"initWithAudioComponentDescription:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioUnitMIDIInstrument", b"initWithAudioComponentDescription:"
)
objc.registerNewKeywordsFromSelector(
    "AVAudioUnitTimeEffect", b"initWithAudioComponentDescription:"
)
objc.registerNewKeywordsFromSelector("AVCaption", b"initWithText:timeRange:")
objc.registerNewKeywordsFromSelector(
    "AVCaptionConversionValidator", b"initWithCaptions:timeRange:conversionSettings:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptionFormatConformer", b"initWithConversionSettings:"
)
objc.registerNewKeywordsFromSelector("AVCaptionGroup", b"initWithCaptions:timeRange:")
objc.registerNewKeywordsFromSelector("AVCaptionGroup", b"initWithTimeRange:")
objc.registerNewKeywordsFromSelector("AVCaptionRuby", b"initWithText:")
objc.registerNewKeywordsFromSelector(
    "AVCaptionRuby", b"initWithText:position:alignment:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureConnection", b"initWithInputPort:videoPreviewLayer:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureConnection", b"initWithInputPorts:output:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureDataOutputSynchronizer", b"initWithDataOutputs:"
)
objc.registerNewKeywordsFromSelector("AVCaptureDeviceInput", b"initWithDevice:error:")
objc.registerNewKeywordsFromSelector(
    "AVCaptureDeviceRotationCoordinator", b"initWithDevice:previewLayer:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureIndexPicker", b"initWithLocalizedTitle:symbolName:localizedIndexTitles:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureIndexPicker", b"initWithLocalizedTitle:symbolName:numberOfIndexes:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureIndexPicker",
    b"initWithLocalizedTitle:symbolName:numberOfIndexes:localizedTitleTransform:",
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureMetadataInput", b"initWithFormatDescription:clock:"
)
objc.registerNewKeywordsFromSelector(
    "AVCapturePhotoOutputReadinessCoordinator", b"initWithPhotoOutput:"
)
objc.registerNewKeywordsFromSelector("AVCaptureScreenInput", b"initWithDisplayID:")
objc.registerNewKeywordsFromSelector(
    "AVCaptureSlider", b"initWithLocalizedTitle:symbolName:minValue:maxValue:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureSlider", b"initWithLocalizedTitle:symbolName:minValue:maxValue:step:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureSlider", b"initWithLocalizedTitle:symbolName:values:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureSystemExposureBiasSlider", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "AVCaptureSystemExposureBiasSlider", b"initWithDevice:action:"
)
objc.registerNewKeywordsFromSelector("AVCaptureSystemZoomSlider", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "AVCaptureSystemZoomSlider", b"initWithDevice:action:"
)
objc.registerNewKeywordsFromSelector("AVCaptureVideoPreviewLayer", b"initWithSession:")
objc.registerNewKeywordsFromSelector(
    "AVCaptureVideoPreviewLayer", b"initWithSessionWithNoConnection:"
)
objc.registerNewKeywordsFromSelector("AVCompositionTrackSegment", b"initWithTimeRange:")
objc.registerNewKeywordsFromSelector(
    "AVCompositionTrackSegment", b"initWithURL:trackID:sourceTimeRange:targetTimeRange:"
)
objc.registerNewKeywordsFromSelector(
    "AVContentKeySpecifier", b"initForKeySystem:identifier:options:"
)
objc.registerNewKeywordsFromSelector(
    "AVDateRangeMetadataGroup", b"initWithItems:startDate:endDate:"
)
objc.registerNewKeywordsFromSelector(
    "AVDelegatingPlaybackCoordinator", b"initWithPlaybackControlDelegate:"
)
objc.registerNewKeywordsFromSelector(
    "AVFragmentedAssetMinder", b"initWithAsset:mindingInterval:"
)
objc.registerNewKeywordsFromSelector(
    "AVFragmentedMovieMinder", b"initWithMovie:mindingInterval:"
)
objc.registerNewKeywordsFromSelector(
    "AVMIDIPlayer", b"initWithContentsOfURL:soundBankURL:error:"
)
objc.registerNewKeywordsFromSelector(
    "AVMIDIPlayer", b"initWithData:soundBankURL:error:"
)
objc.registerNewKeywordsFromSelector("AVMediaDataStorage", b"initWithURL:options:")
objc.registerNewKeywordsFromSelector("AVMovie", b"initWithData:options:")
objc.registerNewKeywordsFromSelector("AVMovie", b"initWithURL:options:")
objc.registerNewKeywordsFromSelector("AVMutableCaptionRegion", b"initWithIdentifier:")
objc.registerNewKeywordsFromSelector("AVMutableMovie", b"initWithData:options:error:")
objc.registerNewKeywordsFromSelector(
    "AVMutableMovie", b"initWithSettingsFromMovie:options:error:"
)
objc.registerNewKeywordsFromSelector("AVMutableMovie", b"initWithURL:options:error:")
objc.registerNewKeywordsFromSelector("AVPlayer", b"initWithPlayerItem:")
objc.registerNewKeywordsFromSelector("AVPlayer", b"initWithURL:")
objc.registerNewKeywordsFromSelector(
    "AVPlayerInterstitialEventController", b"initWithPrimaryPlayer:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerInterstitialEventMonitor", b"initWithPrimaryPlayer:"
)
objc.registerNewKeywordsFromSelector("AVPlayerItem", b"initWithAsset:")
objc.registerNewKeywordsFromSelector(
    "AVPlayerItem", b"initWithAsset:automaticallyLoadedAssetKeys:"
)
objc.registerNewKeywordsFromSelector("AVPlayerItem", b"initWithURL:")
objc.registerNewKeywordsFromSelector(
    "AVPlayerItemLegibleOutput", b"initWithMediaSubtypesForNativeRepresentation:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerItemMetadataCollector", b"initWithIdentifiers:classifyingLabels:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerItemMetadataOutput", b"initWithIdentifiers:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerItemRenderedLegibleOutput", b"initWithVideoDisplaySize:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerItemVideoOutput", b"initWithOutputSettings:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerItemVideoOutput", b"initWithPixelBufferAttributes:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerLooper", b"initWithPlayer:templateItem:timeRange:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerLooper", b"initWithPlayer:templateItem:timeRange:existingItemsOrdering:"
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerMediaSelectionCriteria",
    b"initWithPreferredLanguages:preferredMediaCharacteristics:",
)
objc.registerNewKeywordsFromSelector(
    "AVPlayerMediaSelectionCriteria",
    b"initWithPrincipalMediaCharacteristics:preferredLanguages:preferredMediaCharacteristics:",
)
objc.registerNewKeywordsFromSelector("AVPlayerVideoOutput", b"initWithSpecification:")
objc.registerNewKeywordsFromSelector("AVQueuePlayer", b"initWithItems:")
objc.registerNewKeywordsFromSelector(
    "AVSampleBufferGenerator", b"initWithAsset:timebase:"
)
objc.registerNewKeywordsFromSelector("AVSampleBufferRequest", b"initWithStartCursor:")
objc.registerNewKeywordsFromSelector("AVSpeechUtterance", b"initWithAttributedString:")
objc.registerNewKeywordsFromSelector("AVSpeechUtterance", b"initWithString:")
objc.registerNewKeywordsFromSelector(
    "AVTextStyleRule", b"initWithTextMarkupAttributes:"
)
objc.registerNewKeywordsFromSelector(
    "AVTextStyleRule", b"initWithTextMarkupAttributes:textSelector:"
)
objc.registerNewKeywordsFromSelector(
    "AVTimedMetadataGroup", b"initWithItems:timeRange:"
)
objc.registerNewKeywordsFromSelector("AVTimedMetadataGroup", b"initWithSampleBuffer:")
objc.registerNewKeywordsFromSelector("AVURLAsset", b"initWithURL:options:")
objc.registerNewKeywordsFromSelector(
    "AVVideoOutputSpecification", b"initWithTagCollections:"
)
expressions = {}

# END OF FILE
