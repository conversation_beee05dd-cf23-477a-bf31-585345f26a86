# This file is generated by objective.metadata
#
# Last update: Tue <PERSON> 11 10:10:30 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$EKErrorDomain$EKEventStoreChangedNotification$"""
enums = """$EKAlarmProximityEnter@1$EKAlarmProximityLeave@2$EKAlarmProximityNone@0$EKAlarmTypeAudio@1$EKAlarmTypeDisplay@0$EKAlarmTypeEmail@3$EKAlarmTypeProcedure@2$EKAuthorizationStatusAuthorized@3$EKAuthorizationStatusDenied@2$EKAuthorizationStatusFullAccess@3$EKAuthorizationStatusNotDetermined@0$EKAuthorizationStatusRestricted@1$EKAuthorizationStatusWriteOnly@4$EKCalendarEventAvailabilityBusy@1$EKCalendarEventAvailabilityFree@2$EKCalendarEventAvailabilityNone@0$EKCalendarEventAvailabilityTentative@4$EKCalendarEventAvailabilityUnavailable@8$EKCalendarTypeBirthday@4$EKCalendarTypeCalDAV@1$EKCalendarTypeExchange@2$EKCalendarTypeLocal@0$EKCalendarTypeSubscription@3$EKEntityMaskEvent@1$EKEntityMaskReminder@2$EKEntityTypeEvent@0$EKEntityTypeReminder@1$EKErrorAlarmGreaterThanRecurrence@8$EKErrorAlarmProximityNotSupported@21$EKErrorCalendarDoesNotAllowEvents@22$EKErrorCalendarDoesNotAllowReminders@23$EKErrorCalendarHasNoSource@14$EKErrorCalendarIsImmutable@16$EKErrorCalendarReadOnly@6$EKErrorCalendarSourceCannotBeModified@15$EKErrorDatesInverted@4$EKErrorDurationGreaterThanRecurrence@7$EKErrorEventNotMutable@0$EKErrorEventStoreNotAuthorized@29$EKErrorInternalFailure@5$EKErrorInvalidEntityType@27$EKErrorInvalidInviteReplyCalendar@31$EKErrorInvalidSpan@13$EKErrorInvitesCannotBeMoved@12$EKErrorLast@31$EKErrorNoCalendar@1$EKErrorNoEndDate@3$EKErrorNoStartDate@2$EKErrorNotificationCollectionMismatch@34$EKErrorNotificationSavedWithoutCollection@35$EKErrorNotificationsCollectionFlagNotSet@32$EKErrorOSNotSupported@30$EKErrorObjectBelongsToDifferentStore@11$EKErrorPriorityIsInvalid@26$EKErrorProcedureAlarmsNotMutable@28$EKErrorRecurringReminderRequiresDueDate@18$EKErrorReminderAlarmContainsEmailOrUrl@36$EKErrorReminderLocationsNotSupported@20$EKErrorSourceDoesNotAllowCalendarAddDelete@17$EKErrorSourceDoesNotAllowEvents@25$EKErrorSourceDoesNotAllowReminders@24$EKErrorSourceMismatch@33$EKErrorStartDateCollidesWithOtherOccurrence@10$EKErrorStartDateTooFarInFuture@9$EKErrorStructuredLocationsNotSupported@19$EKEventAvailabilityBusy@0$EKEventAvailabilityFree@1$EKEventAvailabilityNotSupported@-1$EKEventAvailabilityTentative@2$EKEventAvailabilityUnavailable@3$EKEventStatusCanceled@3$EKEventStatusConfirmed@1$EKEventStatusNone@0$EKEventStatusTentative@2$EKFriday@6$EKMonday@2$EKParticipantRoleChair@3$EKParticipantRoleNonParticipant@4$EKParticipantRoleOptional@2$EKParticipantRoleRequired@1$EKParticipantRoleUnknown@0$EKParticipantScheduleStatusCannotDeliver@7$EKParticipantScheduleStatusDelivered@3$EKParticipantScheduleStatusDeliveryFailed@6$EKParticipantScheduleStatusNoPrivileges@5$EKParticipantScheduleStatusNone@0$EKParticipantScheduleStatusPending@1$EKParticipantScheduleStatusRecipientNotAllowed@8$EKParticipantScheduleStatusRecipientNotRecognized@4$EKParticipantScheduleStatusSent@2$EKParticipantStatusAccepted@2$EKParticipantStatusCompleted@6$EKParticipantStatusDeclined@3$EKParticipantStatusDelegated@5$EKParticipantStatusInProcess@7$EKParticipantStatusPending@1$EKParticipantStatusTentative@4$EKParticipantStatusUnknown@0$EKParticipantTypeGroup@4$EKParticipantTypePerson@1$EKParticipantTypeResource@3$EKParticipantTypeRoom@2$EKParticipantTypeUnknown@0$EKRecurrenceFrequencyDaily@0$EKRecurrenceFrequencyMonthly@2$EKRecurrenceFrequencyWeekly@1$EKRecurrenceFrequencyYearly@3$EKReminderPriorityHigh@1$EKReminderPriorityLow@9$EKReminderPriorityMedium@5$EKReminderPriorityNone@0$EKSaturday@7$EKSourceTypeBirthdays@5$EKSourceTypeCalDAV@2$EKSourceTypeExchange@1$EKSourceTypeLocal@0$EKSourceTypeMobileMe@3$EKSourceTypeSubscribed@4$EKSpanFutureEvents@1$EKSpanThisEvent@0$EKSunday@1$EKThursday@5$EKTuesday@3$EKWednesday@4$EKWeekdayFriday@6$EKWeekdayMonday@2$EKWeekdaySaturday@7$EKWeekdaySunday@1$EKWeekdayThursday@5$EKWeekdayTuesday@3$EKWeekdayWednesday@4$"""
misc.update(
    {
        "EKCalendarEventAvailabilityMask": NewType(
            "EKCalendarEventAvailabilityMask", int
        ),
        "EKReminderPriority": NewType("EKReminderPriority", int),
        "EKParticipantRole": NewType("EKParticipantRole", int),
        "EKErrorCode": NewType("EKErrorCode", int),
        "EKEventStatus": NewType("EKEventStatus", int),
        "EKCalendarType": NewType("EKCalendarType", int),
        "EKEntityType": NewType("EKEntityType", int),
        "EKParticipantStatus": NewType("EKParticipantStatus", int),
        "EKSourceType": NewType("EKSourceType", int),
        "EKSpan": NewType("EKSpan", int),
        "EKEventAvailability": NewType("EKEventAvailability", int),
        "EKAuthorizationStatus": NewType("EKAuthorizationStatus", int),
        "EKParticipantType": NewType("EKParticipantType", int),
        "EKWeekday": NewType("EKWeekday", int),
        "EKRecurrenceFrequency": NewType("EKRecurrenceFrequency", int),
        "EKAlarmType": NewType("EKAlarmType", int),
        "EKAlarmProximity": NewType("EKAlarmProximity", int),
        "EKEntityMask": NewType("EKEntityMask", int),
        "EKParticipantScheduleStatus": NewType("EKParticipantScheduleStatus", int),
    }
)
misc.update({})
misc.update({})
functions = {
    "DATE_COMPONENTS_DO_NOT_USE": (b"v",),
    "DATETIME_COMPONENTS_DO_NOT_USE": (b"v",),
    "EK_LOSE_FRACTIONAL_SECONDS_DO_NOT_USE": (b"v",),
}
aliases = {
    "EKWednesday": "EKWeekdayWednesday",
    "EKThursday": "EKWeekdayThursday",
    "EKSunday": "EKWeekdaySunday",
    "EKFriday": "EKWeekdayFriday",
    "EKSaturday": "EKWeekdaySaturday",
    "EKTuesday": "EKWeekdayTuesday",
    "EKAuthorizationStatusAuthorized": "EKAuthorizationStatusFullAccess",
    "EKMonday": "EKWeekdayMonday",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"EKCalendar", b"allowsContentModifications", {"retval": {"type": b"Z"}})
    r(b"EKCalendar", b"isImmutable", {"retval": {"type": b"Z"}})
    r(b"EKCalendar", b"isSubscribed", {"retval": {"type": b"Z"}})
    r(b"EKCalendarItem", b"hasAlarms", {"retval": {"type": b"Z"}})
    r(b"EKCalendarItem", b"hasAttendees", {"retval": {"type": b"Z"}})
    r(b"EKCalendarItem", b"hasNotes", {"retval": {"type": b"Z"}})
    r(b"EKCalendarItem", b"hasRecurrenceRules", {"retval": {"type": b"Z"}})
    r(b"EKCalendarItem", b"isAllDay", {"retval": {"type": "Z"}})
    r(b"EKEvent", b"isAllDay", {"retval": {"type": b"Z"}})
    r(b"EKEvent", b"isDetached", {"retval": {"type": b"Z"}})
    r(b"EKEvent", b"refresh", {"retval": {"type": b"Z"}})
    r(b"EKEvent", b"setAllDay:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"EKEventStore",
        b"commit:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"EKEventStore",
        b"enumerateEventsMatchingPredicate:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"o^B"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"EKEventStore",
        b"fetchRemindersMatchingPredicate:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"EKEventStore",
        b"removeCalendar:commit:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(
        b"EKEventStore",
        b"removeEvent:span:commit:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {4: {"type": b"Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(
        b"EKEventStore",
        b"removeEvent:span:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"EKEventStore",
        b"removeReminder:commit:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(
        b"EKEventStore",
        b"requestAccessToEntityType:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"EKEventStore",
        b"requestFullAccessToEventsWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"EKEventStore",
        b"requestFullAccessToRemindersWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"EKEventStore",
        b"requestWriteOnlyAccessToEventsWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"EKEventStore",
        b"saveCalendar:commit:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(
        b"EKEventStore",
        b"saveEvent:span:commit:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {4: {"type": b"Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(
        b"EKEventStore",
        b"saveEvent:span:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"EKEventStore",
        b"saveReminder:commit:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(b"EKObject", b"hasChanges", {"retval": {"type": b"Z"}})
    r(b"EKObject", b"isNew", {"retval": {"type": b"Z"}})
    r(b"EKObject", b"refresh", {"retval": {"type": b"Z"}})
    r(b"EKParticipant", b"isCurrentUser", {"retval": {"type": b"Z"}})
    r(b"EKReminder", b"isCompleted", {"retval": {"type": b"Z"}})
    r(b"EKReminder", b"setCompleted:", {"arguments": {2: {"type": b"Z"}}})
    r(b"EKSource", b"isDelegate", {"retval": {"type": b"Z"}})
    r(
        b"EKVirtualConferenceProvider",
        b"fetchAvailableRoomTypesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"EKVirtualConferenceProvider",
        b"fetchVirtualConferenceForIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("EKEventStore", b"initWithAccessToEntityTypes:")
objc.registerNewKeywordsFromSelector("EKEventStore", b"initWithSources:")
objc.registerNewKeywordsFromSelector(
    "EKRecurrenceDayOfWeek", b"initWithDayOfTheWeek:weekNumber:"
)
objc.registerNewKeywordsFromSelector(
    "EKRecurrenceRule",
    b"initRecurrenceWithFrequency:interval:daysOfTheWeek:daysOfTheMonth:monthsOfTheYear:weeksOfTheYear:daysOfTheYear:setPositions:end:",
)
objc.registerNewKeywordsFromSelector(
    "EKRecurrenceRule", b"initRecurrenceWithFrequency:interval:end:"
)
objc.registerNewKeywordsFromSelector(
    "EKVirtualConferenceDescriptor", b"initWithTitle:URLDescriptors:conferenceDetails:"
)
objc.registerNewKeywordsFromSelector(
    "EKVirtualConferenceRoomTypeDescriptor", b"initWithTitle:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "EKVirtualConferenceURLDescriptor", b"initWithTitle:URL:"
)
expressions = {}

# END OF FILE
