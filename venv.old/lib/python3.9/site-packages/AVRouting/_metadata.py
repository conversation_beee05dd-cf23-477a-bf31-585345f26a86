# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:04:32 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$AVCustomRoutingControllerAuthorizedRoutesDidChangeNotification$"""
enums = """$AVCustomRoutingEventReasonActivate@0$AVCustomRoutingEventReasonDeactivate@1$AVCustomRoutingEventReasonReactivate@2$"""
misc.update({"AVCustomRoutingEventReason": NewType("AVCustomRoutingEventReason", int)})
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"AVCustomRoutingController", b"isRouteActive:", {"retval": {"type": b"Z"}})
    r(
        b"AVCustomRoutingController",
        b"setActive:forRoute:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"customRoutingController:didSelectItem:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"customRoutingController:eventDidTimeOut:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"customRoutingController:handleEvent:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "AVCustomRoutingPartialIP", b"initWithAddress:mask:"
)
expressions = {}

# END OF FILE
