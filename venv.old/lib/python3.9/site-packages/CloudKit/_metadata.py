# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:06:34 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$CKAccountChangedNotification$CKCurrentUserDefaultName$CKErrorDomain$CKErrorRetryAfterKey$CKErrorUserDidResetEncryptedDataKey$CKOwnerDefaultName$CKPartialErrorsByItemIDKey$CKQueryOperationMaximumResults@Q$CKRecordChangedErrorAncestorRecordKey$CKRecordChangedErrorClientRecordKey$CKRecordChangedErrorServerRecordKey$CKRecordCreationDateKey$CKRecordCreatorUserRecordIDKey$CKRecordLastModifiedUserRecordIDKey$CKRecordModificationDateKey$CKRecordNameZoneWideShare$CKRecordParentKey$CKRecordRecordIDKey$CKRecordShareKey$CKRecordTypeShare$CKRecordTypeUserRecord$CKRecordZoneDefaultName$CKShareThumbnailImageDataKey$CKShareTitleKey$CKShareTypeKey$"""
enums = """$CKAccountStatusAvailable@1$CKAccountStatusCouldNotDetermine@0$CKAccountStatusNoAccount@3$CKAccountStatusRestricted@2$CKAccountStatusTemporarilyUnavailable@4$CKApplicationPermissionStatusCouldNotComplete@1$CKApplicationPermissionStatusDenied@2$CKApplicationPermissionStatusGranted@3$CKApplicationPermissionStatusInitialState@0$CKApplicationPermissionUserDiscoverability@1$CKDatabaseScopePrivate@2$CKDatabaseScopePublic@1$CKDatabaseScopeShared@3$CKErrorAccountTemporarilyUnavailable@36$CKErrorAlreadyShared@30$CKErrorAssetFileModified@17$CKErrorAssetFileNotFound@16$CKErrorAssetNotAvailable@35$CKErrorBadContainer@5$CKErrorBadDatabase@24$CKErrorBatchRequestFailed@22$CKErrorChangeTokenExpired@21$CKErrorConstraintViolation@19$CKErrorIncompatibleVersion@18$CKErrorInternalError@1$CKErrorInvalidArguments@12$CKErrorLimitExceeded@27$CKErrorManagedAccountRestricted@32$CKErrorMissingEntitlement@8$CKErrorNetworkFailure@4$CKErrorNetworkUnavailable@3$CKErrorNotAuthenticated@9$CKErrorOperationCancelled@20$CKErrorPartialFailure@2$CKErrorParticipantMayNeedVerification@33$CKErrorPermissionFailure@10$CKErrorQuotaExceeded@25$CKErrorReferenceViolation@31$CKErrorRequestRateLimited@7$CKErrorResultsTruncated@13$CKErrorServerRecordChanged@14$CKErrorServerRejectedRequest@15$CKErrorServerResponseLost@34$CKErrorServiceUnavailable@6$CKErrorTooManyParticipants@29$CKErrorUnknownItem@11$CKErrorUserDeletedZone@28$CKErrorZoneBusy@23$CKErrorZoneNotFound@26$CKNotificationTypeDatabase@4$CKNotificationTypeQuery@1$CKNotificationTypeReadNotification@3$CKNotificationTypeRecordZone@2$CKOperationGroupTransferSizeGigabytes@5$CKOperationGroupTransferSizeHundredsOfGigabytes@7$CKOperationGroupTransferSizeHundredsOfMegabytes@4$CKOperationGroupTransferSizeKilobytes@1$CKOperationGroupTransferSizeMegabytes@2$CKOperationGroupTransferSizeTensOfGigabytes@6$CKOperationGroupTransferSizeTensOfMegabytes@3$CKOperationGroupTransferSizeUnknown@0$CKQueryNotificationReasonRecordCreated@1$CKQueryNotificationReasonRecordDeleted@3$CKQueryNotificationReasonRecordUpdated@2$CKQuerySubscriptionOptionsFiresOnRecordCreation@1$CKQuerySubscriptionOptionsFiresOnRecordDeletion@4$CKQuerySubscriptionOptionsFiresOnRecordUpdate@2$CKQuerySubscriptionOptionsFiresOnce@8$CKRecordSaveAllKeys@2$CKRecordSaveChangedKeys@1$CKRecordSaveIfServerRecordUnchanged@0$CKRecordZoneCapabilityAtomic@2$CKRecordZoneCapabilityFetchChanges@1$CKRecordZoneCapabilitySharing@4$CKRecordZoneCapabilityZoneWideSharing@8$CKReferenceActionDeleteSelf@1$CKReferenceActionNone@0$CKShareParticipantAcceptanceStatusAccepted@2$CKShareParticipantAcceptanceStatusPending@1$CKShareParticipantAcceptanceStatusRemoved@3$CKShareParticipantAcceptanceStatusUnknown@0$CKShareParticipantPermissionNone@1$CKShareParticipantPermissionReadOnly@2$CKShareParticipantPermissionReadWrite@3$CKShareParticipantPermissionUnknown@0$CKShareParticipantRoleOwner@1$CKShareParticipantRolePrivateUser@3$CKShareParticipantRolePublicUser@4$CKShareParticipantRoleUnknown@0$CKShareParticipantTypeOwner@1$CKShareParticipantTypePrivateUser@3$CKShareParticipantTypePublicUser@4$CKShareParticipantTypeUnknown@0$CKSharingParticipantAccessOptionAny@3$CKSharingParticipantAccessOptionAnyoneWithLink@1$CKSharingParticipantAccessOptionSpecifiedRecipientsOnly@2$CKSharingParticipantPermissionOptionAny@3$CKSharingParticipantPermissionOptionReadOnly@1$CKSharingParticipantPermissionOptionReadWrite@2$CKSubscriptionOptionsFiresOnRecordCreation@1$CKSubscriptionOptionsFiresOnRecordDeletion@4$CKSubscriptionOptionsFiresOnRecordUpdate@2$CKSubscriptionOptionsFiresOnce@8$CKSubscriptionTypeDatabase@3$CKSubscriptionTypeQuery@1$CKSubscriptionTypeRecordZone@2$CKSyncEngineAccountChangeTypeSignIn@0$CKSyncEngineAccountChangeTypeSignOut@1$CKSyncEngineAccountChangeTypeSwitchAccounts@2$CKSyncEngineEventTypeAccountChange@1$CKSyncEngineEventTypeDidFetchChanges@9$CKSyncEngineEventTypeDidFetchRecordZoneChanges@8$CKSyncEngineEventTypeDidSendChanges@11$CKSyncEngineEventTypeFetchedDatabaseChanges@2$CKSyncEngineEventTypeFetchedRecordZoneChanges@3$CKSyncEngineEventTypeSentDatabaseChanges@4$CKSyncEngineEventTypeSentRecordZoneChanges@5$CKSyncEngineEventTypeStateUpdate@0$CKSyncEngineEventTypeWillFetchChanges@6$CKSyncEngineEventTypeWillFetchRecordZoneChanges@7$CKSyncEngineEventTypeWillSendChanges@10$CKSyncEnginePendingDatabaseChangeTypeDeleteZone@1$CKSyncEnginePendingDatabaseChangeTypeSaveZone@0$CKSyncEnginePendingRecordZoneChangeTypeDeleteRecord@1$CKSyncEnginePendingRecordZoneChangeTypeSaveRecord@0$CKSyncEngineSyncReasonManual@1$CKSyncEngineSyncReasonScheduled@0$CKSyncEngineZoneDeletionReasonDeleted@0$CKSyncEngineZoneDeletionReasonEncryptedDataReset@2$CKSyncEngineZoneDeletionReasonPurged@1$"""
misc.update(
    {
        "CKSubscriptionType": NewType("CKSubscriptionType", int),
        "CKSyncEnginePendingDatabaseChangeType": NewType(
            "CKSyncEnginePendingDatabaseChangeType", int
        ),
        "CKShareParticipantRole": NewType("CKShareParticipantRole", int),
        "CKSharingParticipantPermissionOption": NewType(
            "CKSharingParticipantPermissionOption", int
        ),
        "CKSyncEngineEventType": NewType("CKSyncEngineEventType", int),
        "CKSharingParticipantAccessOption": NewType(
            "CKSharingParticipantAccessOption", int
        ),
        "CKRecordSavePolicy": NewType("CKRecordSavePolicy", int),
        "CKDatabaseScope": NewType("CKDatabaseScope", int),
        "CKReferenceAction": NewType("CKReferenceAction", int),
        "CKQueryNotificationReason": NewType("CKQueryNotificationReason", int),
        "CKSyncEngineSyncReason": NewType("CKSyncEngineSyncReason", int),
        "CKShareParticipantPermission": NewType("CKShareParticipantPermission", int),
        "CKOperationGroupTransferSize": NewType("CKOperationGroupTransferSize", int),
        "CKAccountStatus": NewType("CKAccountStatus", int),
        "CKApplicationPermissions": NewType("CKApplicationPermissions", int),
        "CKApplicationPermissionStatus": NewType("CKApplicationPermissionStatus", int),
        "CKShareParticipantAcceptanceStatus": NewType(
            "CKShareParticipantAcceptanceStatus", int
        ),
        "CKRecordZoneCapabilities": NewType("CKRecordZoneCapabilities", int),
        "CKQuerySubscriptionOptions": NewType("CKQuerySubscriptionOptions", int),
        "CKNotificationType": NewType("CKNotificationType", int),
        "CKSyncEngineZoneDeletionReason": NewType(
            "CKSyncEngineZoneDeletionReason", int
        ),
        "CKShareParticipantType": NewType("CKShareParticipantType", int),
        "CKErrorCode": NewType("CKErrorCode", int),
        "CKSyncEngineAccountChangeType": NewType("CKSyncEngineAccountChangeType", int),
        "CKSyncEnginePendingRecordZoneChangeType": NewType(
            "CKSyncEnginePendingRecordZoneChangeType", int
        ),
    }
)
misc.update({})
misc.update({})
aliases = {"CK_UNIT_TESTS_EXTERN": "CK_EXTERN"}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"CKAcceptSharesOperation",
        b"acceptSharesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKAcceptSharesOperation",
        b"perShareCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKAcceptSharesOperation",
        b"setAcceptSharesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKAcceptSharesOperation",
        b"setPerShareCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"acceptShareMetadata:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"accountStatusWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"discoverAllContactUserInfosWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"discoverAllIdentitiesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"discoverUserIdentityWithEmailAddress:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"discoverUserIdentityWithPhoneNumber:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"discoverUserIdentityWithUserRecordID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"discoverUserInfoWithEmailAddress:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"discoverUserInfoWithUserRecordID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"fetchAllLongLivedOperationIDsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"fetchLongLivedOperationWithID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"fetchShareMetadataWithURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"fetchShareParticipantWithEmailAddress:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"fetchShareParticipantWithPhoneNumber:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"fetchShareParticipantWithUserRecordID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"fetchUserRecordIDWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"requestApplicationPermission:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKContainer",
        b"statusForApplicationPermission:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"deleteRecordWithID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"deleteRecordZoneWithID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"deleteSubscriptionWithID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"fetchAllRecordZonesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"fetchAllSubscriptionsWithCompletionHandler:",
        {
            1: {
                0: {
                    "callable": {
                        "retval": {"typestr": "v"},
                        "arguments": {
                            0: {"typestr": "^v"},
                            1: {"typestr": "@"},
                            2: {"typestr": "@"},
                        },
                    }
                }
            },
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            },
        },
    )
    r(
        b"CKDatabase",
        b"fetchRecordWithID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"fetchRecordZoneWithID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"fetchSubscriptionWithID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"performQuery:inZoneWithID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"CKDatabase",
        b"saveRecord:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"saveRecordZone:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDatabase",
        b"saveSubscription:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDiscoverAllContactsOperation",
        b"discoverAllContactsCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"@"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKDiscoverAllContactsOperation",
        b"setDiscoverAllContactsCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKDiscoverAllUserIdentitiesOperation",
        b"discoverAllUserIdentitiesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKDiscoverAllUserIdentitiesOperation",
        b"setDiscoverAllUserIdentitiesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKDiscoverAllUserIdentitiesOperation",
        b"setUserIdentityDiscoveredBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKDiscoverAllUserIdentitiesOperation",
        b"userIdentityDiscoveredBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKDiscoverUserIdentitiesOperation",
        b"discoverUserIdentitiesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKDiscoverUserIdentitiesOperation",
        b"setDiscoverUserIdentitiesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKDiscoverUserIdentitiesOperation",
        b"setUserIdentityDiscoveredBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKDiscoverUserIdentitiesOperation",
        b"userIdentityDiscoveredBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKDiscoverUserInfosOperation",
        b"discoverUserInfosCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKDiscoverUserInfosOperation",
        b"setDiscoverUserInfosCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"changeTokenUpdatedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(b"CKFetchDatabaseChangesOperation", b"fetchAllChanges", {"retval": {"type": "Z"}})
    r(
        b"CKFetchDatabaseChangesOperation",
        b"fetchDatabaseChangesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"Z"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"recordZoneWithIDChangedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"recordZoneWithIDWasDeletedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"recordZoneWithIDWasDeletedDueToUserEncryptedDataResetBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"recordZoneWithIDWasPurgedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"setChangeTokenUpdatedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"setFetchAllChanges:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"setFetchDatabaseChangesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"setRecordZoneWithIDChangedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"setRecordZoneWithIDWasDeletedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"setRecordZoneWithIDWasDeletedDueToUserEncryptedDataResetBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchDatabaseChangesOperation",
        b"setRecordZoneWithIDWasPurgedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchNotificationChangesOperation",
        b"fetchNotificationChangesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(b"CKFetchNotificationChangesOperation", b"moreComing", {"retval": {"type": b"Z"}})
    r(
        b"CKFetchNotificationChangesOperation",
        b"notificationChangedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchNotificationChangesOperation",
        b"setFetchNotificationChangesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchNotificationChangesOperation",
        b"setNotificationChangedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"CKFetchRecordChangesOperation", b"fetchAllChanges", {"retval": {"type": "Z"}})
    r(
        b"CKFetchRecordChangesOperation",
        b"fetchRecordChangesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(b"CKFetchRecordChangesOperation", b"moreComing", {"retval": {"type": b"Z"}})
    r(
        b"CKFetchRecordChangesOperation",
        b"perRecordProgressBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"d"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"recordChangedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"recordWithIDWasDeletedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"serverChangeTokenFetchedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"setFetchAllChanges:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"setFetchRecordChangesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"setPerRecordProgressBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"d"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"setRecordChangedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"setRecordWithIDWasDeletedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordChangesOperation",
        b"setServerChangeTokenFetchedBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"fetchAllChanges",
        {"retval": {"type": "Z"}},
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"fetchRecordZoneChangesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"recordChangedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"recordWasChangedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"recordWithIDWasDeletedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"recordZoneChangeTokensUpdatedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"recordZoneFetchCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                        4: {"type": b"Z"},
                        5: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"setFetchAllChanges:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"setFetchRecordZoneChangesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"setRecordChangedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"setRecordWasChangedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"setRecordWithIDWasDeletedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"setRecordZoneChangeTokensUpdatedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordZoneChangesOperation",
        b"setRecordZoneFetchCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"Z"},
                            5: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordZonesOperation",
        b"fetchRecordZonesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordZonesOperation",
        b"perRecordZoneCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordZonesOperation",
        b"setFetchRecordZonesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordZonesOperation",
        b"setPerRecordZoneCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordsOperation",
        b"fetchRecordsCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordsOperation",
        b"perRecordCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordsOperation",
        b"perRecordProgressBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"d"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchRecordsOperation",
        b"setFetchRecordsCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordsOperation",
        b"setPerRecordCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchRecordsOperation",
        b"setPerRecordProgressBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"d"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchShareMetadataOperation",
        b"fetchShareMetadataCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchShareMetadataOperation",
        b"perShareMetadataBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchShareMetadataOperation",
        b"setFetchShareMetadataCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchShareMetadataOperation",
        b"setPerShareMetadataBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchShareMetadataOperation",
        b"setShouldFetchRootRecord:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"CKFetchShareMetadataOperation",
        b"shouldFetchRootRecord",
        {"retval": {"type": "Z"}},
    )
    r(
        b"CKFetchShareParticipantsOperation",
        b"fetchShareParticipantsCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchShareParticipantsOperation",
        b"perShareParticipantCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchShareParticipantsOperation",
        b"setFetchShareParticipantsCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchShareParticipantsOperation",
        b"setPerShareParticipantCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchShareParticipantsOperation",
        b"setShareParticipantFetchedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKFetchShareParticipantsOperation",
        b"shareParticipantFetchedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKFetchSubscriptionsOperation",
        b"fetchSubscriptionCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchSubscriptionsOperation",
        b"perSubscriptionCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchSubscriptionsOperation",
        b"setFetchSubscriptionCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchSubscriptionsOperation",
        b"setPerSubscriptionCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKFetchWebAuthTokenOperation",
        b"fetchWebAuthTokenCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKFetchWebAuthTokenOperation",
        b"setFetchWebAuthTokenCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKMarkNotificationsReadOperation",
        b"markNotificationsReadCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKMarkNotificationsReadOperation",
        b"setMarkNotificationsReadCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifyBadgeOperation",
        b"modifyBadgeCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKModifyBadgeOperation",
        b"setModifyBadgeCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKModifyRecordZonesOperation",
        b"modifyRecordZonesCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifyRecordZonesOperation",
        b"perRecordZoneDeleteBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifyRecordZonesOperation",
        b"perRecordZoneSaveBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifyRecordZonesOperation",
        b"setModifyRecordZonesCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifyRecordZonesOperation",
        b"setPerRecordZoneDeleteBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifyRecordZonesOperation",
        b"setPerRecordZoneSaveBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"CKModifyRecordsOperation", b"atomic", {"retval": {"type": b"Z"}})
    r(
        b"CKModifyRecordsOperation",
        b"modifyRecordsCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"perRecordCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"perRecordDeleteBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"perRecordProgressBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"d"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"perRecordSaveBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"perSubscriptionDeleteBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(b"CKModifyRecordsOperation", b"setAtomic:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"CKModifyRecordsOperation",
        b"setModifyRecordsCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"setPerRecordCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"setPerRecordDeleteBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"setPerRecordProgressBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"d"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"setPerRecordSaveBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifyRecordsOperation",
        b"setPerSubscriptionDeleteBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifySubscriptionsOperation",
        b"modifySubscriptionsCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifySubscriptionsOperation",
        b"perSubscriptionDeleteBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifySubscriptionsOperation",
        b"perSubscriptionSaveBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKModifySubscriptionsOperation",
        b"setModifySubscriptionsCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifySubscriptionsOperation",
        b"setPerSubscriptionDeleteBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKModifySubscriptionsOperation",
        b"setPerSubscriptionSaveBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"CKNotification", b"isPruned", {"retval": {"type": b"Z"}})
    r(b"CKNotificationInfo", b"setShouldBadge:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"CKNotificationInfo",
        b"setShouldSendContentAvailable:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"CKNotificationInfo",
        b"setShouldSendMutableContent:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CKNotificationInfo", b"shouldBadge", {"retval": {"type": b"Z"}})
    r(b"CKNotificationInfo", b"shouldSendContentAvailable", {"retval": {"type": b"Z"}})
    r(b"CKNotificationInfo", b"shouldSendMutableContent", {"retval": {"type": "Z"}})
    r(b"CKOperation", b"allowsCellularAccess", {"retval": {"type": b"Z"}})
    r(b"CKOperation", b"isLongLived", {"retval": {"type": "Z"}})
    r(
        b"CKOperation",
        b"longLivedOperationWasPersistedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"CKOperation", b"setAllowsCellularAccess:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CKOperation", b"setLongLived:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"CKOperation",
        b"setLongLivedOperationWasPersistedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"CKOperation", b"setUsesBackgroundSession:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CKOperation", b"usesBackgroundSession", {"retval": {"type": b"Z"}})
    r(b"CKOperationConfiguration", b"allowsCellularAccess", {"retval": {"type": "Z"}})
    r(b"CKOperationConfiguration", b"isLongLived", {"retval": {"type": "Z"}})
    r(
        b"CKOperationConfiguration",
        b"setAllowsCellularAccess:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CKOperationConfiguration", b"setLongLived:", {"arguments": {2: {"type": "Z"}}})
    r(b"CKQueryNotification", b"isPublicDatabase", {"retval": {"type": b"Z"}})
    r(
        b"CKQueryOperation",
        b"queryCompletionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKQueryOperation",
        b"recordFetchedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"CKQueryOperation",
        b"recordMatchedBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKQueryOperation",
        b"setQueryCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKQueryOperation",
        b"setRecordFetchedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKQueryOperation",
        b"setRecordMatchedBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKSyncEngine",
        b"cancelOperationsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"CKSyncEngine",
        b"fetchChangesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKSyncEngine",
        b"fetchChangesWithOptions:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKSyncEngine",
        b"sendChangesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKSyncEngine",
        b"sendChangesWithOptions:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"CKSyncEngineConfiguration", b"automaticallySync", {"retval": {"type": b"Z"}})
    r(
        b"CKSyncEngineConfiguration",
        b"setAutomaticallySync:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"CKSyncEngineFetchChangesScope", b"containsZoneID:", {"retval": {"type": b"Z"}})
    r(b"CKSyncEngineRecordZoneChangeBatch", b"atomicByZone", {"retval": {"type": b"Z"}})
    r(
        b"CKSyncEngineRecordZoneChangeBatch",
        b"initWithPendingChanges:recordProvider:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CKSyncEngineRecordZoneChangeBatch",
        b"initWithRecordsToSave:recordIDsToDelete:atomicByZone:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"CKSyncEngineRecordZoneChangeBatch",
        b"setAtomicByZone:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"CKSyncEngineSendChangesScope",
        b"containsPendingRecordZoneChange:",
        {"retval": {"type": b"Z"}},
    )
    r(b"CKSyncEngineSendChangesScope", b"containsRecordID:", {"retval": {"type": b"Z"}})
    r(b"CKSyncEngineState", b"hasPendingUntrackedChanges", {"retval": {"type": b"Z"}})
    r(
        b"CKSyncEngineState",
        b"setHasPendingUntrackedChanges:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"CKSystemSharingUIObserver",
        b"setSystemSharingUIDidSaveShareBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKSystemSharingUIObserver",
        b"setSystemSharingUIDidStopSharingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKSystemSharingUIObserver",
        b"setSystemUIDidSaveShareBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKSystemSharingUIObserver",
        b"setSystemUIDidStopSharingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CKSystemSharingUIObserver",
        b"systemSharingUIDidSaveShareBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKSystemSharingUIObserver",
        b"systemSharingUIDidStopSharingBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKSystemSharingUIObserver",
        b"systemUIDidSaveShareBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"CKSystemSharingUIObserver",
        b"systemUIDidStopSharingBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(b"CKUserIdentity", b"hasiCloudAccount", {"retval": {"type": "Z"}})
    r(
        b"NSItemProvider",
        b"registerCKShareWithContainer:allowedSharingOptions:preparationHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {
                                        0: {"type": "^v"},
                                        1: {"type": "@"},
                                        2: {"type": "@"},
                                    },
                                },
                                "type": b"@?",
                            },
                        },
                    }
                }
            }
        },
    )
    r(b"NSObject", b"allKeys", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"changedKeys", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"objectForKey:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"objectForKeyedSubscript:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setObject:forKey:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"setObject:forKeyedSubscript:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"syncEngine:handleEvent:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"syncEngine:nextFetchChangesOptionsForContext:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"syncEngine:nextRecordZoneChangeBatchForContext:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "CKAcceptSharesOperation", b"initWithShareMetadatas:"
)
objc.registerNewKeywordsFromSelector(
    "CKAllowedSharingOptions",
    b"initWithAllowedParticipantPermissionOptions:allowedParticipantAccessOptions:",
)
objc.registerNewKeywordsFromSelector("CKAsset", b"initWithFileURL:")
objc.registerNewKeywordsFromSelector("CKDatabaseSubscription", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "CKDatabaseSubscription", b"initWithSubscriptionID:"
)
objc.registerNewKeywordsFromSelector(
    "CKDiscoverUserIdentitiesOperation", b"initWithUserIdentityLookupInfos:"
)
objc.registerNewKeywordsFromSelector(
    "CKDiscoverUserInfosOperation", b"initWithEmailAddresses:userRecordIDs:"
)
objc.registerNewKeywordsFromSelector(
    "CKFetchDatabaseChangesOperation", b"initWithPreviousServerChangeToken:"
)
objc.registerNewKeywordsFromSelector(
    "CKFetchNotificationChangesOperation", b"initWithPreviousServerChangeToken:"
)
objc.registerNewKeywordsFromSelector(
    "CKFetchRecordChangesOperation", b"initWithRecordZoneID:previousServerChangeToken:"
)
objc.registerNewKeywordsFromSelector(
    "CKFetchRecordZoneChangesOperation",
    b"initWithRecordZoneIDs:configurationsByRecordZoneID:",
)
objc.registerNewKeywordsFromSelector(
    "CKFetchRecordZoneChangesOperation", b"initWithRecordZoneIDs:optionsByRecordZoneID:"
)
objc.registerNewKeywordsFromSelector(
    "CKFetchRecordZonesOperation", b"initWithRecordZoneIDs:"
)
objc.registerNewKeywordsFromSelector("CKFetchRecordsOperation", b"initWithRecordIDs:")
objc.registerNewKeywordsFromSelector(
    "CKFetchShareMetadataOperation", b"initWithShareURLs:"
)
objc.registerNewKeywordsFromSelector(
    "CKFetchShareParticipantsOperation", b"initWithUserIdentityLookupInfos:"
)
objc.registerNewKeywordsFromSelector(
    "CKFetchSubscriptionsOperation", b"initWithSubscriptionIDs:"
)
objc.registerNewKeywordsFromSelector(
    "CKFetchWebAuthTokenOperation", b"initWithAPIToken:"
)
objc.registerNewKeywordsFromSelector("CKLocationSortDescriptor", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "CKLocationSortDescriptor", b"initWithKey:relativeLocation:"
)
objc.registerNewKeywordsFromSelector(
    "CKMarkNotificationsReadOperation", b"initWithNotificationIDsToMarkRead:"
)
objc.registerNewKeywordsFromSelector("CKModifyBadgeOperation", b"initWithBadgeValue:")
objc.registerNewKeywordsFromSelector(
    "CKModifyRecordZonesOperation", b"initWithRecordZonesToSave:recordZoneIDsToDelete:"
)
objc.registerNewKeywordsFromSelector(
    "CKModifyRecordsOperation", b"initWithRecordsToSave:recordIDsToDelete:"
)
objc.registerNewKeywordsFromSelector(
    "CKModifySubscriptionsOperation",
    b"initWithSubscriptionsToSave:subscriptionIDsToDelete:",
)
objc.registerNewKeywordsFromSelector("CKOperationGroup", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("CKQuery", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("CKQuery", b"initWithRecordType:predicate:")
objc.registerNewKeywordsFromSelector("CKQueryOperation", b"initWithCursor:")
objc.registerNewKeywordsFromSelector("CKQueryOperation", b"initWithQuery:")
objc.registerNewKeywordsFromSelector("CKQuerySubscription", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "CKQuerySubscription", b"initWithRecordType:predicate:options:"
)
objc.registerNewKeywordsFromSelector(
    "CKQuerySubscription", b"initWithRecordType:predicate:subscriptionID:options:"
)
objc.registerNewKeywordsFromSelector("CKRecord", b"initWithRecordType:")
objc.registerNewKeywordsFromSelector("CKRecord", b"initWithRecordType:recordID:")
objc.registerNewKeywordsFromSelector("CKRecord", b"initWithRecordType:zoneID:")
objc.registerNewKeywordsFromSelector("CKRecordID", b"initWithRecordName:")
objc.registerNewKeywordsFromSelector("CKRecordID", b"initWithRecordName:zoneID:")
objc.registerNewKeywordsFromSelector("CKRecordZone", b"initWithZoneID:")
objc.registerNewKeywordsFromSelector("CKRecordZone", b"initWithZoneName:")
objc.registerNewKeywordsFromSelector("CKRecordZoneID", b"initWithZoneName:ownerName:")
objc.registerNewKeywordsFromSelector("CKRecordZoneSubscription", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("CKRecordZoneSubscription", b"initWithZoneID:")
objc.registerNewKeywordsFromSelector(
    "CKRecordZoneSubscription", b"initWithZoneID:subscriptionID:"
)
objc.registerNewKeywordsFromSelector("CKReference", b"initWithRecord:action:")
objc.registerNewKeywordsFromSelector("CKReference", b"initWithRecordID:action:")
objc.registerNewKeywordsFromSelector("CKShare", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("CKShare", b"initWithRecordType:")
objc.registerNewKeywordsFromSelector("CKShare", b"initWithRecordType:recordID:")
objc.registerNewKeywordsFromSelector("CKShare", b"initWithRecordType:zoneID:")
objc.registerNewKeywordsFromSelector("CKShare", b"initWithRecordZoneID:")
objc.registerNewKeywordsFromSelector("CKShare", b"initWithRootRecord:")
objc.registerNewKeywordsFromSelector("CKShare", b"initWithRootRecord:shareID:")
objc.registerNewKeywordsFromSelector("CKSubscription", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "CKSubscription", b"initWithRecordType:predicate:options:"
)
objc.registerNewKeywordsFromSelector(
    "CKSubscription", b"initWithRecordType:predicate:subscriptionID:options:"
)
objc.registerNewKeywordsFromSelector("CKSubscription", b"initWithZoneID:options:")
objc.registerNewKeywordsFromSelector(
    "CKSubscription", b"initWithZoneID:subscriptionID:options:"
)
objc.registerNewKeywordsFromSelector("CKSyncEngine", b"initWithConfiguration:")
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineConfiguration", b"initWithDatabase:stateSerialization:delegate:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineFetchChangesOptions", b"initWithScope:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineFetchChangesScope", b"initWithExcludedZoneIDs:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineFetchChangesScope", b"initWithZoneIDs:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEnginePendingRecordZoneChange", b"initWithRecordID:type:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEnginePendingZoneDelete", b"initWithZoneID:"
)
objc.registerNewKeywordsFromSelector("CKSyncEnginePendingZoneSave", b"initWithZone:")
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineRecordZoneChangeBatch", b"initWithPendingChanges:recordProvider:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineRecordZoneChangeBatch",
    b"initWithRecordsToSave:recordIDsToDelete:atomicByZone:",
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineSendChangesOptions", b"initWithScope:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineSendChangesScope", b"initWithExcludedZoneIDs:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineSendChangesScope", b"initWithRecordIDs:"
)
objc.registerNewKeywordsFromSelector(
    "CKSyncEngineSendChangesScope", b"initWithZoneIDs:"
)
objc.registerNewKeywordsFromSelector("CKSystemSharingUIObserver", b"initWithContainer:")
objc.registerNewKeywordsFromSelector(
    "CKUserIdentityLookupInfo", b"initWithEmailAddress:"
)
objc.registerNewKeywordsFromSelector(
    "CKUserIdentityLookupInfo", b"initWithPhoneNumber:"
)
objc.registerNewKeywordsFromSelector(
    "CKUserIdentityLookupInfo", b"initWithUserRecordID:"
)
expressions = {}

# END OF FILE
