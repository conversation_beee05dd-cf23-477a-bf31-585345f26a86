# This file is generated by objective.metadata
#
# Last update: Thu Jun 20 21:38:33 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$VNAnimalBodyPoseObservationJointNameLeftBackElbow$VNAnimalBodyPoseObservationJointNameLeftBackKnee$VNAnimalBodyPoseObservationJointNameLeftBackPaw$VNAnimalBodyPoseObservationJointNameLeftEarBottom$VNAnimalBodyPoseObservationJointNameLeftEarMiddle$VNAnimalBodyPoseObservationJointNameLeftEarTop$VNAnimalBodyPoseObservationJointNameLeftEye$VNAnimalBodyPoseObservationJointNameLeftFrontElbow$VNAnimalBodyPoseObservationJointNameLeftFrontKnee$VNAnimalBodyPoseObservationJointNameLeftFrontPaw$VNAnimalBodyPoseObservationJointNameNeck$VNAnimalBodyPoseObservationJointNameNose$VNAnimalBodyPoseObservationJointNameRightBackElbow$VNAnimalBodyPoseObservationJointNameRightBackKnee$VNAnimalBodyPoseObservationJointNameRightBackPaw$VNAnimalBodyPoseObservationJointNameRightEarBottom$VNAnimalBodyPoseObservationJointNameRightEarMiddle$VNAnimalBodyPoseObservationJointNameRightEarTop$VNAnimalBodyPoseObservationJointNameRightEye$VNAnimalBodyPoseObservationJointNameRightFrontElbow$VNAnimalBodyPoseObservationJointNameRightFrontKnee$VNAnimalBodyPoseObservationJointNameRightFrontPaw$VNAnimalBodyPoseObservationJointNameTailBottom$VNAnimalBodyPoseObservationJointNameTailMiddle$VNAnimalBodyPoseObservationJointNameTailTop$VNAnimalBodyPoseObservationJointsGroupNameAll$VNAnimalBodyPoseObservationJointsGroupNameForelegs$VNAnimalBodyPoseObservationJointsGroupNameHead$VNAnimalBodyPoseObservationJointsGroupNameHindlegs$VNAnimalBodyPoseObservationJointsGroupNameTail$VNAnimalBodyPoseObservationJointsGroupNameTrunk$VNAnimalDetectorCat$VNAnimalDetectorDog$VNAnimalIdentifierCat$VNAnimalIdentifierDog$VNBarcodeSymbologyAztec$VNBarcodeSymbologyCodabar$VNBarcodeSymbologyCode128$VNBarcodeSymbologyCode39$VNBarcodeSymbologyCode39Checksum$VNBarcodeSymbologyCode39FullASCII$VNBarcodeSymbologyCode39FullASCIIChecksum$VNBarcodeSymbologyCode93$VNBarcodeSymbologyCode93i$VNBarcodeSymbologyDataMatrix$VNBarcodeSymbologyEAN13$VNBarcodeSymbologyEAN8$VNBarcodeSymbologyGS1DataBar$VNBarcodeSymbologyGS1DataBarExpanded$VNBarcodeSymbologyGS1DataBarLimited$VNBarcodeSymbologyI2of5$VNBarcodeSymbologyI2of5Checksum$VNBarcodeSymbologyITF14$VNBarcodeSymbologyMSIPlessey$VNBarcodeSymbologyMicroPDF417$VNBarcodeSymbologyMicroQR$VNBarcodeSymbologyPDF417$VNBarcodeSymbologyQR$VNBarcodeSymbologyUPCE$VNBodyLandmarkKeyLeftAnkle$VNBodyLandmarkKeyLeftEar$VNBodyLandmarkKeyLeftElbow$VNBodyLandmarkKeyLeftEye$VNBodyLandmarkKeyLeftHip$VNBodyLandmarkKeyLeftKnee$VNBodyLandmarkKeyLeftShoulder$VNBodyLandmarkKeyLeftWrist$VNBodyLandmarkKeyNeck$VNBodyLandmarkKeyNose$VNBodyLandmarkKeyRightAnkle$VNBodyLandmarkKeyRightEar$VNBodyLandmarkKeyRightElbow$VNBodyLandmarkKeyRightEye$VNBodyLandmarkKeyRightHip$VNBodyLandmarkKeyRightKnee$VNBodyLandmarkKeyRightShoulder$VNBodyLandmarkKeyRightWrist$VNBodyLandmarkKeyRoot$VNBodyLandmarkRegionKeyFace$VNBodyLandmarkRegionKeyLeftArm$VNBodyLandmarkRegionKeyLeftLeg$VNBodyLandmarkRegionKeyRightArm$VNBodyLandmarkRegionKeyRightLeg$VNBodyLandmarkRegionKeyTorso$VNComputeStageMain$VNComputeStagePostProcessing$VNErrorDomain$VNHandLandmarkKeyIndexDIP$VNHandLandmarkKeyIndexMCP$VNHandLandmarkKeyIndexPIP$VNHandLandmarkKeyIndexTIP$VNHandLandmarkKeyLittleDIP$VNHandLandmarkKeyLittleMCP$VNHandLandmarkKeyLittlePIP$VNHandLandmarkKeyLittleTIP$VNHandLandmarkKeyMiddleDIP$VNHandLandmarkKeyMiddleMCP$VNHandLandmarkKeyMiddlePIP$VNHandLandmarkKeyMiddleTIP$VNHandLandmarkKeyRingDIP$VNHandLandmarkKeyRingMCP$VNHandLandmarkKeyRingPIP$VNHandLandmarkKeyRingTIP$VNHandLandmarkKeyThumbCMC$VNHandLandmarkKeyThumbIP$VNHandLandmarkKeyThumbMP$VNHandLandmarkKeyThumbTIP$VNHandLandmarkKeyWrist$VNHandLandmarkRegionKeyIndexFinger$VNHandLandmarkRegionKeyLittleFinger$VNHandLandmarkRegionKeyMiddleFinger$VNHandLandmarkRegionKeyRingFinger$VNHandLandmarkRegionKeyThumb$VNHumanBodyPose3DObservationJointNameCenterHead$VNHumanBodyPose3DObservationJointNameCenterShoulder$VNHumanBodyPose3DObservationJointNameLeftAnkle$VNHumanBodyPose3DObservationJointNameLeftElbow$VNHumanBodyPose3DObservationJointNameLeftHip$VNHumanBodyPose3DObservationJointNameLeftKnee$VNHumanBodyPose3DObservationJointNameLeftShoulder$VNHumanBodyPose3DObservationJointNameLeftWrist$VNHumanBodyPose3DObservationJointNameRightAnkle$VNHumanBodyPose3DObservationJointNameRightElbow$VNHumanBodyPose3DObservationJointNameRightHip$VNHumanBodyPose3DObservationJointNameRightKnee$VNHumanBodyPose3DObservationJointNameRightShoulder$VNHumanBodyPose3DObservationJointNameRightWrist$VNHumanBodyPose3DObservationJointNameRoot$VNHumanBodyPose3DObservationJointNameSpine$VNHumanBodyPose3DObservationJointNameTopHead$VNHumanBodyPose3DObservationJointsGroupNameAll$VNHumanBodyPose3DObservationJointsGroupNameHead$VNHumanBodyPose3DObservationJointsGroupNameLeftArm$VNHumanBodyPose3DObservationJointsGroupNameLeftLeg$VNHumanBodyPose3DObservationJointsGroupNameRightArm$VNHumanBodyPose3DObservationJointsGroupNameRightLeg$VNHumanBodyPose3DObservationJointsGroupNameTorso$VNHumanBodyPoseObservationJointNameLeftAnkle$VNHumanBodyPoseObservationJointNameLeftEar$VNHumanBodyPoseObservationJointNameLeftElbow$VNHumanBodyPoseObservationJointNameLeftEye$VNHumanBodyPoseObservationJointNameLeftHip$VNHumanBodyPoseObservationJointNameLeftKnee$VNHumanBodyPoseObservationJointNameLeftShoulder$VNHumanBodyPoseObservationJointNameLeftWrist$VNHumanBodyPoseObservationJointNameNeck$VNHumanBodyPoseObservationJointNameNose$VNHumanBodyPoseObservationJointNameRightAnkle$VNHumanBodyPoseObservationJointNameRightEar$VNHumanBodyPoseObservationJointNameRightElbow$VNHumanBodyPoseObservationJointNameRightEye$VNHumanBodyPoseObservationJointNameRightHip$VNHumanBodyPoseObservationJointNameRightKnee$VNHumanBodyPoseObservationJointNameRightShoulder$VNHumanBodyPoseObservationJointNameRightWrist$VNHumanBodyPoseObservationJointNameRoot$VNHumanBodyPoseObservationJointsGroupNameAll$VNHumanBodyPoseObservationJointsGroupNameFace$VNHumanBodyPoseObservationJointsGroupNameLeftArm$VNHumanBodyPoseObservationJointsGroupNameLeftLeg$VNHumanBodyPoseObservationJointsGroupNameRightArm$VNHumanBodyPoseObservationJointsGroupNameRightLeg$VNHumanBodyPoseObservationJointsGroupNameTorso$VNHumanHandPoseObservationJointNameIndexDIP$VNHumanHandPoseObservationJointNameIndexMCP$VNHumanHandPoseObservationJointNameIndexPIP$VNHumanHandPoseObservationJointNameIndexTip$VNHumanHandPoseObservationJointNameLittleDIP$VNHumanHandPoseObservationJointNameLittleMCP$VNHumanHandPoseObservationJointNameLittlePIP$VNHumanHandPoseObservationJointNameLittleTip$VNHumanHandPoseObservationJointNameMiddleDIP$VNHumanHandPoseObservationJointNameMiddleMCP$VNHumanHandPoseObservationJointNameMiddlePIP$VNHumanHandPoseObservationJointNameMiddleTip$VNHumanHandPoseObservationJointNameRingDIP$VNHumanHandPoseObservationJointNameRingMCP$VNHumanHandPoseObservationJointNameRingPIP$VNHumanHandPoseObservationJointNameRingTip$VNHumanHandPoseObservationJointNameThumbCMC$VNHumanHandPoseObservationJointNameThumbIP$VNHumanHandPoseObservationJointNameThumbMP$VNHumanHandPoseObservationJointNameThumbTip$VNHumanHandPoseObservationJointNameWrist$VNHumanHandPoseObservationJointsGroupNameAll$VNHumanHandPoseObservationJointsGroupNameIndexFinger$VNHumanHandPoseObservationJointsGroupNameLittleFinger$VNHumanHandPoseObservationJointsGroupNameMiddleFinger$VNHumanHandPoseObservationJointsGroupNameRingFinger$VNHumanHandPoseObservationJointsGroupNameThumb$VNImageOptionCIContext$VNImageOptionCameraIntrinsics$VNImageOptionProperties$VNNormalizedIdentityRect@{CGRect={CGPoint=dd}{CGSize=dd}}$VNRecognizedPoint3DGroupKeyAll$VNRecognizedPointGroupKeyAll$VNVideoProcessingOptionFrameCadence$VNVideoProcessingOptionTimeInterval$VNVisionVersionNumber@d$"""
enums = """$VNBarcodeCompositeTypeGS1TypeA@2$VNBarcodeCompositeTypeGS1TypeB@3$VNBarcodeCompositeTypeGS1TypeC@4$VNBarcodeCompositeTypeLinked@1$VNBarcodeCompositeTypeNone@0$VNCalculateImageAestheticsScoresRequestRevision1@1$VNChiralityLeft@-1$VNChiralityRight@1$VNChiralityUnknown@0$VNClassifyImageRequestRevision1@1$VNClassifyImageRequestRevision2@2$VNCoreMLRequestRevision1@1$VNDetectAnimalBodyPoseRequestRevision1@1$VNDetectAnimalRectanglesRequestRevision1@1$VNDetectBarcodesRequestRevision1@1$VNDetectBarcodesRequestRevision2@2$VNDetectBarcodesRequestRevision3@3$VNDetectBarcodesRequestRevision4@4$VNDetectContourRequestRevision1@1$VNDetectDocumentSegmentationRequestRevision1@1$VNDetectFaceCaptureQualityRequestRevision1@1$VNDetectFaceCaptureQualityRequestRevision2@2$VNDetectFaceCaptureQualityRequestRevision3@3$VNDetectFaceLandmarksRequestRevision1@1$VNDetectFaceLandmarksRequestRevision2@2$VNDetectFaceLandmarksRequestRevision3@3$VNDetectFaceQualityRequestRevision1@1$VNDetectFaceRectanglesRequestRevision1@1$VNDetectFaceRectanglesRequestRevision2@2$VNDetectFaceRectanglesRequestRevision3@3$VNDetectHorizonRequestRevision1@1$VNDetectHumanBodyPose3DRequestRevision1@1$VNDetectHumanBodyPoseRequestRevision1@1$VNDetectHumanHandPoseRequestRevision1@1$VNDetectHumanRectanglesRequestRevision1@1$VNDetectHumanRectanglesRequestRevision2@2$VNDetectRectanglesRequestRevision1@1$VNDetectTextRectanglesRequestRevision1@1$VNDetectTrajectoriesRequestRevision1@1$VNElementTypeDouble@2$VNElementTypeFloat@1$VNElementTypeUnknown@0$VNErrorDataUnavailable@17$VNErrorIOError@6$VNErrorInternalError@9$VNErrorInvalidArgument@14$VNErrorInvalidFormat@2$VNErrorInvalidImage@13$VNErrorInvalidModel@15$VNErrorInvalidOperation@12$VNErrorInvalidOption@5$VNErrorMissingOption@7$VNErrorNotImplemented@8$VNErrorOK@0$VNErrorOperationFailed@3$VNErrorOutOfBoundsError@4$VNErrorOutOfMemory@10$VNErrorRequestCancelled@1$VNErrorTimeStampNotFound@18$VNErrorTimeout@20$VNErrorTuriCoreErrorCode@-1$VNErrorUnknownError@11$VNErrorUnsupportedComputeDevice@22$VNErrorUnsupportedComputeStage@21$VNErrorUnsupportedRequest@19$VNErrorUnsupportedRevision@16$VNGenerateAttentionBasedSaliencyImageRequestRevision1@1$VNGenerateAttentionBasedSaliencyImageRequestRevision2@2$VNGenerateForegroundInstanceMaskRequestRevision1@1$VNGenerateImageFeaturePrintRequestRevision1@1$VNGenerateImageFeaturePrintRequestRevision2@2$VNGenerateObjectnessBasedSaliencyImageRequestRevision1@1$VNGenerateObjectnessBasedSaliencyImageRequestRevision2@2$VNGenerateOpticalFlowRequestComputationAccuracyHigh@2$VNGenerateOpticalFlowRequestComputationAccuracyLow@0$VNGenerateOpticalFlowRequestComputationAccuracyMedium@1$VNGenerateOpticalFlowRequestComputationAccuracyVeryHigh@3$VNGenerateOpticalFlowRequestRevision1@1$VNGenerateOpticalFlowRequestRevision2@2$VNGeneratePersonInstanceMaskRequestRevision1@1$VNGeneratePersonSegmentationRequestQualityLevelAccurate@0$VNGeneratePersonSegmentationRequestQualityLevelBalanced@1$VNGeneratePersonSegmentationRequestQualityLevelFast@2$VNGeneratePersonSegmentationRequestRevision1@1$VNHomographicImageRegistrationRequestRevision1@1$VNHumanBodyPose3DObservationHeightEstimationMeasured@1$VNHumanBodyPose3DObservationHeightEstimationReference@0$VNImageCropAndScaleOptionCenterCrop@0$VNImageCropAndScaleOptionScaleFill@2$VNImageCropAndScaleOptionScaleFillRotate90CCW@258$VNImageCropAndScaleOptionScaleFit@1$VNImageCropAndScaleOptionScaleFitRotate90CCW@257$VNPointsClassificationClosedPath@2$VNPointsClassificationDisconnected@0$VNPointsClassificationOpenPath@1$VNRecognizeAnimalsRequestRevision1@1$VNRecognizeAnimalsRequestRevision2@2$VNRecognizeTextRequestRevision1@1$VNRecognizeTextRequestRevision2@2$VNRecognizeTextRequestRevision3@3$VNRequestFaceLandmarksConstellation65Points@1$VNRequestFaceLandmarksConstellation76Points@2$VNRequestFaceLandmarksConstellationNotDefined@0$VNRequestRevisionUnspecified@0$VNRequestTextRecognitionLevelAccurate@0$VNRequestTextRecognitionLevelFast@1$VNRequestTrackingLevelAccurate@0$VNRequestTrackingLevelFast@1$VNTrackHomographicImageRegistrationRequestRevision1@1$VNTrackObjectRequestRevision1@1$VNTrackObjectRequestRevision2@2$VNTrackOpticalFlowRequestComputationAccuracyHigh@2$VNTrackOpticalFlowRequestComputationAccuracyLow@0$VNTrackOpticalFlowRequestComputationAccuracyMedium@1$VNTrackOpticalFlowRequestComputationAccuracyVeryHigh@3$VNTrackOpticalFlowRequestRevision1@1$VNTrackRectangleRequestRevision1@1$VNTrackTranslationalImageRegistrationRequestRevision1@1$VNTranslationalImageRegistrationRequestRevision1@1$"""
misc.update(
    {
        "VNImageCropAndScaleOption": NewType("VNImageCropAndScaleOption", int),
        "VNHumanBodyPose3DObservationHeightEstimation": NewType(
            "VNHumanBodyPose3DObservationHeightEstimation", int
        ),
        "VNPointsClassification": NewType("VNPointsClassification", int),
        "VNRequestTextRecognitionLevel": NewType("VNRequestTextRecognitionLevel", int),
        "VNRequestFaceLandmarksConstellation": NewType(
            "VNRequestFaceLandmarksConstellation", int
        ),
        "VNErrorCode": NewType("VNErrorCode", int),
        "VNTrackOpticalFlowRequestComputationAccuracy": NewType(
            "VNTrackOpticalFlowRequestComputationAccuracy", int
        ),
        "VNElementType": NewType("VNElementType", int),
        "VNChirality": NewType("VNChirality", int),
        "VNGeneratePersonSegmentationRequestQualityLevel": NewType(
            "VNGeneratePersonSegmentationRequestQualityLevel", int
        ),
        "VNBarcodeCompositeType": NewType("VNBarcodeCompositeType", int),
        "VNRequestTrackingLevel": NewType("VNRequestTrackingLevel", int),
        "VNGenerateOpticalFlowRequestComputationAccuracy": NewType(
            "VNGenerateOpticalFlowRequestComputationAccuracy", int
        ),
    }
)
misc.update(
    {
        "VNBarcodeSymbology": NewType("VNBarcodeSymbology", str),
        "VNAnimalIdentifier": NewType("VNAnimalIdentifier", str),
        "VNHumanBodyPoseObservationJointName": NewType(
            "VNHumanBodyPoseObservationJointName", str
        ),
        "VNRecognizedPointGroupKey": NewType("VNRecognizedPointGroupKey", str),
        "VNHumanBodyPoseObservationJointsGroupName": NewType(
            "VNHumanBodyPoseObservationJointsGroupName", str
        ),
        "VNImageOption": NewType("VNImageOption", str),
        "VNHumanBodyPose3DObservationJointName": NewType(
            "VNHumanBodyPose3DObservationJointName", str
        ),
        "VNVideoProcessingOption": NewType("VNVideoProcessingOption", str),
        "VNHumanHandPoseObservationJointsGroupName": NewType(
            "VNHumanHandPoseObservationJointsGroupName", str
        ),
        "VNAnimalBodyPoseObservationJointsGroupName": NewType(
            "VNAnimalBodyPoseObservationJointsGroupName", str
        ),
        "VNHumanHandPoseObservationJointName": NewType(
            "VNHumanHandPoseObservationJointName", str
        ),
        "VNHumanBodyPose3DObservationJointsGroupName": NewType(
            "VNHumanBodyPose3DObservationJointsGroupName", str
        ),
        "VNComputeStage": NewType("VNComputeStage", str),
        "VNRecognizedPointKey": NewType("VNRecognizedPointKey", str),
        "VNAnimalBodyPoseObservationJointName": NewType(
            "VNAnimalBodyPoseObservationJointName", str
        ),
    }
)
misc.update({})
functions = {
    "VNImageRectForNormalizedRectUsingRegionOfInterest": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}QQ{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "VNImageRectForNormalizedRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}QQ",
    ),
    "VNNormalizedPointForImagePoint": (b"{CGPoint=dd}{CGPoint=dd}QQ",),
    "VNImagePointForNormalizedPointUsingRegionOfInterest": (
        b"{CGPoint=dd}{CGPoint=dd}QQ{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "VNNormalizedRectIsIdentityRect": (b"B{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "VNNormalizedRectForImageRectUsingRegionOfInterest": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}QQ{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "VNNormalizedRectForImageRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}QQ",
    ),
    "VNImagePointForNormalizedPoint": (b"{CGPoint=dd}{CGPoint=dd}QQ",),
    "VNNormalizedPointForImagePointUsingRegionOfInterest": (
        b"{CGPoint=dd}{CGPoint=dd}QQ{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "VNElementTypeSize": (b"QQ",),
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"NSObject", b"indeterminate", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"inputFaceObservations",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"progressHandler",
        {
            "required": True,
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"d"},
                        3: {"type": b"@"},
                    },
                },
                "type": "@?",
            },
        },
    )
    r(b"NSObject", b"requestRevision", {"required": True, "retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"setInputFaceObservations:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setProgressHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"d"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            },
        },
    )
    r(
        b"VNAnimalBodyPoseObservation",
        b"recognizedPointForJointName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNAnimalBodyPoseObservation",
        b"recognizedPointsForJointsGroupName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"VNBarcodeObservation", b"isColorInverted", {"retval": {"type": b"Z"}})
    r(b"VNBarcodeObservation", b"isGS1DataCarrier", {"retval": {"type": b"Z"}})
    r(b"VNCircle", b"containsPoint:", {"retval": {"type": b"Z"}})
    r(
        b"VNCircle",
        b"containsPoint:inCircumferentialRingOfWidth:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VNClassificationObservation",
        b"hasMinimumPrecision:forRecall:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"VNClassificationObservation",
        b"hasMinimumRecall:forPrecision:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"VNClassificationObservation",
        b"hasPrecisionRecallCurve",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VNClassifyImageRequest",
        b"knownClassificationsForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNClassifyImageRequest",
        b"supportedIdentifiersAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNContour",
        b"childContourAtIndex:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNContour",
        b"normalizedPoints",
        {
            "full_signature": b"^<2f>@:",
            "retval": {"type": b"^<2f>", "c_array_of_variable_length": True},
        },
    )
    r(
        b"VNContour",
        b"polygonApproximationWithEpsilon:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNContoursObservation",
        b"contourAtIndex:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNContoursObservation",
        b"contourAtIndexPath:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNCoreMLModel",
        b"modelForMLModel:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNCoreMLRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNCoreMLRequest",
        b"initWithModel:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNDetectAnimalBodyPoseRequest",
        b"supportedJointNamesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectAnimalBodyPoseRequest",
        b"supportedJointsGroupNamesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectBarcodesRequest",
        b"coalesceCompositeSymbologies",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VNDetectBarcodesRequest",
        b"setCoalesceCompositeSymbologies:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VNDetectBarcodesRequest",
        b"supportedSymbologiesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(b"VNDetectContoursRequest", b"detectDarkOnLight", {"retval": {"type": b"Z"}})
    r(b"VNDetectContoursRequest", b"detectsDarkOnLight", {"retval": {"type": b"Z"}})
    r(
        b"VNDetectContoursRequest",
        b"setDetectDarkOnLight:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VNDetectContoursRequest",
        b"setDetectsDarkOnLight:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VNDetectFaceLandmarksRequest",
        b"revision:supportsConstellation:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VNDetectHumanBodyPose3DRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNDetectHumanBodyPose3DRequest",
        b"supportedJointNamesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPose3DRequest",
        b"supportedJointsGroupNamesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPoseRequest",
        b"supportedIdentifiedPointGroupKeysForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPoseRequest",
        b"supportedIdentifiedPointKeysForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPoseRequest",
        b"supportedJointNamesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPoseRequest",
        b"supportedJointNamesForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPoseRequest",
        b"supportedJointsGroupNamesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPoseRequest",
        b"supportedJointsGroupNamesForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPoseRequest",
        b"supportedRecognizedPointGroupKeysForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanBodyPoseRequest",
        b"supportedRecognizedPointKeysForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanHandPoseRequest",
        b"supportedIdentifiedPointGroupKeysForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanHandPoseRequest",
        b"supportedIdentifiedPointKeysForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanHandPoseRequest",
        b"supportedJointNamesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanHandPoseRequest",
        b"supportedJointNamesForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanHandPoseRequest",
        b"supportedJointsGroupNamesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanHandPoseRequest",
        b"supportedJointsGroupNamesForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanHandPoseRequest",
        b"supportedRecognizedPointGroupKeysForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanHandPoseRequest",
        b"supportedRecognizedPointKeysForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNDetectHumanRectanglesRequest",
        b"setUpperBodyOnly:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"VNDetectHumanRectanglesRequest", b"upperBodyOnly", {"retval": {"type": b"Z"}})
    r(
        b"VNDetectTextRectanglesRequest",
        b"reportCharacterBoxes",
        {"retval": {"type": "Z"}},
    )
    r(
        b"VNDetectTextRectanglesRequest",
        b"setReportCharacterBoxes:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"VNDetectTrajectoriesRequest",
        b"initWithFrameAnalysisSpacing:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{_CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"VNDetectTrajectoriesRequest",
        b"initWithFrameAnalysisSpacing:trajectoryLength:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{_CMTime=qiIq}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"VNDetectTrajectoriesRequest",
        b"setTargetFrameTime:",
        {"arguments": {2: {"type": b"{_CMTime=qiIq}"}}},
    )
    r(
        b"VNDetectTrajectoriesRequest",
        b"targetFrameTime",
        {"retval": {"type": b"{_CMTime=qiIq}"}},
    )
    r(
        b"VNFaceLandmarkRegion2D",
        b"normalizedPoints",
        {"retval": {"c_array_of_variable_length": True}},
    )
    r(
        b"VNFaceLandmarkRegion2D",
        b"pointsInImageOfSize:",
        {"retval": {"c_array_of_variable_length": True}},
    )
    r(
        b"VNFeaturePrintObservation",
        b"computeDistance:toFeaturePrintObservation:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"VNGenerateOpticalFlowRequest", b"keepNetworkOutput", {"retval": {"type": b"Z"}})
    r(
        b"VNGenerateOpticalFlowRequest",
        b"setKeepNetworkOutput:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VNGeneratePersonSegmentationRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNGeneratePersonSegmentationRequest",
        b"initWithFrameAnalysisSpacing:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{_CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"VNGeneratePersonSegmentationRequest",
        b"supportedOutputPixelFormatsAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNGeometryUtils",
        b"boundingCircleForContour:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNGeometryUtils",
        b"boundingCircleForPoints:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNGeometryUtils",
        b"boundingCircleForSIMDPoints:pointCount:error:",
        {
            "full_signature": b"@@:^<2f>q^@",
            "arguments": {
                2: {
                    "type": b"^<2f>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                },
                4: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"VNGeometryUtils",
        b"calculateArea:forContour:orientedArea:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type_modifier": b"n"},
                4: {"type": b"Z"},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"VNGeometryUtils",
        b"calculatePerimeter:forContour:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type_modifier": b"n"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(
        b"VNGeometryUtils",
        b"normalizedPoints",
        {"retval": {"c_array_of_variable_length": True}},
    )
    r(
        b"VNHumanBodyPose3DObservation",
        b"cameraOriginMatrix",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"VNHumanBodyPose3DObservation",
        b"getCameraRelativePosition:forJointName:error:",
        {
            "full_signature": b"Z@:^{simd_float4x4=[4<4f>]}@^@",
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"^{simd_float4x4=[4<4f>]}", "type_modifier": b"o"},
                4: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"VNHumanBodyPose3DObservation",
        b"pointInImageForJointName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNHumanBodyPose3DObservation",
        b"recognizedPointForJointName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNHumanBodyPose3DObservation",
        b"recognizedPointsForJointsGroupName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNHumanBodyPoseObservation",
        b"recognizedPointForJointName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNHumanBodyPoseObservation",
        b"recognizedPointsForJointsGroupName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNHumanBodyRecognizedPoint3D",
        b"localPosition",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"VNHumanHandPoseObservation",
        b"recognizedPointForJointName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNHumanHandPoseObservation",
        b"recognizedPointsForJointsGroupName:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"VNHumanObservation", b"upperBodyOnly", {"retval": {"type": b"Z"}})
    r(b"VNImageAestheticsScoresObservation", b"isUtility", {"retval": {"type": b"Z"}})
    r(
        b"VNImageHomographicAlignmentObservation",
        b"setWarpTransform:",
        {
            "full_signature": b"v@:{simd_float3x3=[3<3f>]}",
            "arguments": {2: {"type": b"{simd_float3x3=[3<3f>]}"}},
        },
    )
    r(
        b"VNImageHomographicAlignmentObservation",
        b"warpTransform",
        {
            "full_signature": b"{simd_float3x3=[3<3f>]}@:",
            "retval": {"type": b"{simd_float3x3=[3<3f>]}"},
        },
    )
    r(
        b"VNImageRequestHandler",
        b"performRequests:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNInstanceMaskObservation",
        b"generateMaskForInstances:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNInstanceMaskObservation",
        b"generateMaskedImageOfInstances:fromRequestHandler:croppedToInstancesExtent:error:",
        {"arguments": {4: {"type": b"Z"}, 5: {"type_modifier": b"o"}}},
    )
    r(
        b"VNInstanceMaskObservation",
        b"generateScaledMaskForImageForInstances:fromRequestHandler:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNObservation",
        b"timeRange",
        {"retval": {"type": b"{_CMTimeRange={_CMTime=qiIq}{_CMTime=qiIq}}"}},
    )
    r(
        b"VNPoint3D",
        b"initWithPosition:",
        {
            "full_signature": b"@@:{simd_float4x4=[4<4f>]}",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"VNPoint3D",
        b"position",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"VNRecognizeAnimalsRequest",
        b"knownAnimalIdentifiersForRevision:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizeAnimalsRequest",
        b"supportedIdentifiersAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizeTextRequest",
        b"automaticallyDetectsLanguage",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VNRecognizeTextRequest",
        b"setAutomaticallyDetectsLanguage:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VNRecognizeTextRequest",
        b"setUsesLanguageCorrection:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"VNRecognizeTextRequest",
        b"supportedRecognitionLanguagesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizeTextRequest",
        b"supportedRecognitionLanguagesForTextRecognitionLevel:revision:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"VNRecognizeTextRequest", b"usesLanguageCorrection", {"retval": {"type": "Z"}})
    r(
        b"VNRecognizedPoints3DObservation",
        b"recognizedPointForKey:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizedPoints3DObservation",
        b"recognizedPointsForGroupKey:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizedPointsObservation",
        b"identifiedPointsForGroupKey_error_",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizedPointsObservation",
        b"keypointsMultiArrayAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizedPointsObservation",
        b"recognizedPointForKey:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizedPointsObservation",
        b"recognizedPointsForGroupKey:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRecognizedText",
        b"boundingBoxForRange:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"VNRequest",
        b"completionHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"VNRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"VNRequest", b"preferBackgroundProcessing", {"retval": {"type": "Z"}})
    r(
        b"VNRequest",
        b"setPreferBackgroundProcessing:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"VNRequest", b"setUsesCPUOnly:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"VNRequest",
        b"supportedComputeStageDevicesAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(b"VNRequest", b"usesCPUOnly", {"retval": {"type": "Z"}})
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onCGImage:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onCGImage:orientation:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onCIImage:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onCIImage:orientation:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onCMSampleBuffer:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onCMSampleBuffer:orientation:error:",
        {"retval": {"type": b"Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onCVPixelBuffer:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onCVPixelBuffer:orientation:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onImageData:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onImageData:orientation:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onImageURL:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNSequenceRequestHandler",
        b"performRequests:onImageURL:orientation:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"VNStatefulRequest",
        b"frameAnalysisSpacing",
        {"retval": {"type": b"{_CMTime=qiIq}"}},
    )
    r(
        b"VNStatefulRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNStatefulRequest",
        b"initWithFrameAnalysisSpacing:completionHandler:",
        {
            "arguments": {
                2: {"type": b"{_CMTime=qiIq}"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"VNStatefulRequest",
        b"requestFrameAnalysisSpacing",
        {"retval": {"type": b"{_CMTime=qiIq}"}},
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCGImage:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCGImage:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCGImage:orientation:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCIImage:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCIImage:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCIImage:orientation:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCMSampleBuffer:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCMSampleBuffer:orientation:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCVPixelBuffer:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCVPixelBuffer:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedCVPixelBuffer:orientation:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedImageData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedImageData:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedImageData:orientation:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedImageURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedImageURL:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTargetedImageRequest",
        b"initWithTargetedImageURL:orientation:options:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTrackHomographicImageRegistrationRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTrackObjectRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTrackObjectRequest",
        b"initWithDetectedObjectObservation:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTrackOpticalFlowRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"VNTrackOpticalFlowRequest", b"keepNetworkOutput", {"retval": {"type": b"Z"}})
    r(
        b"VNTrackOpticalFlowRequest",
        b"setKeepNetworkOutput:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VNTrackRectangleRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTrackRectangleRequest",
        b"initWithRectangleObservation:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTrackTranslationalImageRegistrationRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VNTrackingRequest",
        b"initWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"VNTrackingRequest", b"isLastFrame", {"retval": {"type": "Z"}})
    r(b"VNTrackingRequest", b"setLastFrame:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"VNTrackingRequest",
        b"supportedNumberOfTrackersAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"VNTrajectoryObservation",
        b"equationCoefficients",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"VNVideoProcessor",
        b"addRequest:processingOptions:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNVideoProcessor",
        b"addRequest:withProcessingOptions:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"VNVideoProcessor",
        b"analyzeTimeRange:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{_CMTimeRange={_CMTime=qiIq}{_CMTime=qiIq}}"},
                3: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"VNVideoProcessor",
        b"analyzeWithTimeRange:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"{_CMTimeRange={_CMTime=qiIq}{_CMTime=qiIq}}"},
                3: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"VNVideoProcessor",
        b"removeRequest:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"null", b"hasMinimumPrecision:forRecall:", {"retval": {"type": b"Z"}})
    r(b"null", b"hasMinimumRecall:forPrecision:", {"retval": {"type": b"Z"}})
    r(b"null", b"hasPrecisionRecallCurve", {"retval": {"type": b"Z"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("VNCircle", b"initWithCenter:diameter:")
objc.registerNewKeywordsFromSelector("VNCircle", b"initWithCenter:radius:")
objc.registerNewKeywordsFromSelector("VNCoreMLRequest", b"initWithCompletionHandler:")
objc.registerNewKeywordsFromSelector("VNCoreMLRequest", b"initWithModel:")
objc.registerNewKeywordsFromSelector(
    "VNCoreMLRequest", b"initWithModel:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNDetectHumanBodyPose3DRequest", b"initWithCompletionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNDetectTrajectoriesRequest", b"initWithFrameAnalysisSpacing:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNDetectTrajectoriesRequest",
    b"initWithFrameAnalysisSpacing:trajectoryLength:completionHandler:",
)
objc.registerNewKeywordsFromSelector("VNDetectedPoint", b"initWithLocation:")
objc.registerNewKeywordsFromSelector("VNDetectedPoint", b"initWithX:y:")
objc.registerNewKeywordsFromSelector(
    "VNGeneratePersonSegmentationRequest", b"initWithCompletionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNGeneratePersonSegmentationRequest",
    b"initWithFrameAnalysisSpacing:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCGImage:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCGImage:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCIImage:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCIImage:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCMSampleBuffer:depthData:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCMSampleBuffer:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCMSampleBuffer:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCVPixelBuffer:depthData:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCVPixelBuffer:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithCVPixelBuffer:orientation:options:"
)
objc.registerNewKeywordsFromSelector("VNImageRequestHandler", b"initWithData:options:")
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithData:orientation:options:"
)
objc.registerNewKeywordsFromSelector("VNImageRequestHandler", b"initWithURL:options:")
objc.registerNewKeywordsFromSelector(
    "VNImageRequestHandler", b"initWithURL:orientation:options:"
)
objc.registerNewKeywordsFromSelector("VNPoint", b"initWithLocation:")
objc.registerNewKeywordsFromSelector("VNPoint", b"initWithX:y:")
objc.registerNewKeywordsFromSelector("VNPoint3D", b"initWithPosition:")
objc.registerNewKeywordsFromSelector("VNRequest", b"initWithCompletionHandler:")
objc.registerNewKeywordsFromSelector("VNStatefulRequest", b"initWithCompletionHandler:")
objc.registerNewKeywordsFromSelector(
    "VNStatefulRequest", b"initWithFrameAnalysisSpacing:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithCompletionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCGImage:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCGImage:options:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCGImage:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest",
    b"initWithTargetedCGImage:orientation:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCIImage:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCIImage:options:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCIImage:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest",
    b"initWithTargetedCIImage:orientation:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCMSampleBuffer:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest",
    b"initWithTargetedCMSampleBuffer:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCMSampleBuffer:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest",
    b"initWithTargetedCMSampleBuffer:orientation:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCVPixelBuffer:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest",
    b"initWithTargetedCVPixelBuffer:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedCVPixelBuffer:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest",
    b"initWithTargetedCVPixelBuffer:orientation:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedImageData:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedImageData:options:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedImageData:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest",
    b"initWithTargetedImageData:orientation:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedImageURL:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedImageURL:options:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest", b"initWithTargetedImageURL:orientation:options:"
)
objc.registerNewKeywordsFromSelector(
    "VNTargetedImageRequest",
    b"initWithTargetedImageURL:orientation:options:completionHandler:",
)
objc.registerNewKeywordsFromSelector(
    "VNTrackHomographicImageRegistrationRequest", b"initWithCompletionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTrackObjectRequest", b"initWithCompletionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTrackObjectRequest", b"initWithDetectedObjectObservation:"
)
objc.registerNewKeywordsFromSelector(
    "VNTrackObjectRequest", b"initWithDetectedObjectObservation:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTrackOpticalFlowRequest", b"initWithCompletionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTrackRectangleRequest", b"initWithCompletionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTrackRectangleRequest", b"initWithRectangleObservation:"
)
objc.registerNewKeywordsFromSelector(
    "VNTrackRectangleRequest", b"initWithRectangleObservation:completionHandler:"
)
objc.registerNewKeywordsFromSelector(
    "VNTrackTranslationalImageRegistrationRequest", b"initWithCompletionHandler:"
)
objc.registerNewKeywordsFromSelector("VNTrackingRequest", b"initWithCompletionHandler:")
objc.registerNewKeywordsFromSelector("VNVector", b"initWithR:theta:")
objc.registerNewKeywordsFromSelector("VNVector", b"initWithVectorHead:tail:")
objc.registerNewKeywordsFromSelector("VNVector", b"initWithXComponent:yComponent:")
objc.registerNewKeywordsFromSelector("VNVideoProcessor", b"initWithURL:")
objc.registerNewKeywordsFromSelector(
    "VNVideoProcessorFrameRateCadence", b"initWithFrameRate:"
)
objc.registerNewKeywordsFromSelector(
    "VNVideoProcessorTimeIntervalCadence", b"initWithTimeInterval:"
)
expressions = {}

# END OF FILE
