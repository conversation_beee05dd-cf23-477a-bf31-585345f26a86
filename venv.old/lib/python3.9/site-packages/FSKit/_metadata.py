# This file is generated by objective.metadata
#
# Last update: Thu Mar 20 20:30:47 2025
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$FSDirectoryCookieInitial@Q$FSDirectoryVerifierInitial@Q$FSKitErrorDomain$FSKitVersionNumber@d$FSOperationIDUnspecified@Q$"""
enums = """$FSAccessAddFile@4$FSAccessAddSubdirectory@32$FSAccessAppendData@32$FSAccessDelete@16$FSAccessDeleteChild@64$FSAccessExecute@8$FSAccessListDirectory@2$FSAccessReadAttributes@128$FSAccessReadData@2$FSAccessReadSecurity@2048$FSAccessReadXattr@512$FSAccessSearch@8$FSAccessTakeOwnership@8192$FSAccessWriteAttributes@256$FSAccessWriteData@4$FSAccessWriteSecurity@4096$FSAccessWriteXattr@1024$FSBlockmapFlagsRead@256$FSBlockmapFlagsWrite@512$FSCompleteIOFlagsAsync@1024$FSCompleteIOFlagsRead@256$FSCompleteIOFlagsWrite@512$FSContainerStateActive@3$FSContainerStateBlocked@1$FSContainerStateNotReady@0$FSContainerStateReady@2$FSDeactivateOptionsForce@1$FSErrorInvalidDirectoryCookie@4506$FSErrorModuleLoadFailed@4500$FSErrorResourceDamaged@4502$FSErrorResourceUnrecognized@4501$FSErrorResourceUnusable@4503$FSErrorStatusOperationInProgress@4504$FSErrorStatusOperationPaused@4505$FSExtentTypeData@0$FSExtentTypeZeroFill@1$FSItemAttributeAccessTime@1024$FSItemAttributeAddedTime@32768$FSItemAttributeAllocSize@128$FSItemAttributeBackupTime@16384$FSItemAttributeBirthTime@8192$FSItemAttributeChangeTime@4096$FSItemAttributeFileID@256$FSItemAttributeFlags@32$FSItemAttributeGID@16$FSItemAttributeInhibitKernelOffloadedIO@131072$FSItemAttributeLinkCount@4$FSItemAttributeMode@2$FSItemAttributeModifyTime@2048$FSItemAttributeParentID@512$FSItemAttributeSize@64$FSItemAttributeSupportsLimitedXAttrs@65536$FSItemAttributeType@1$FSItemAttributeUID@8$FSItemDeactivationAlways@18446744073709551615$FSItemDeactivationForPreallocatedItems@2$FSItemDeactivationForRemovedItems@1$FSItemDeactivationNever@0$FSItemIDInvalid@0$FSItemIDParentOfRoot@1$FSItemIDRootDirectory@2$FSItemTypeBlockDevice@6$FSItemTypeCharDevice@5$FSItemTypeDirectory@2$FSItemTypeFIFO@4$FSItemTypeFile@1$FSItemTypeSocket@7$FSItemTypeSymlink@3$FSItemTypeUnknown@0$FSMatchResultNotRecognized@0$FSMatchResultRecognized@1$FSMatchResultUsable@3$FSMatchResultUsableButLimited@2$FSPreallocateFlagsAll@4$FSPreallocateFlagsContiguous@2$FSPreallocateFlagsFromEOF@16$FSPreallocateFlagsPersist@8$FSSetXattrPolicyAlwaysSet@0$FSSetXattrPolicyDelete@3$FSSetXattrPolicyMustCreate@1$FSSetXattrPolicyMustReplace@2$FSSyncFlagsDWait@4$FSSyncFlagsNoWait@2$FSSyncFlagsWait@1$FSVolumeCaseFormatInsensitive@1$FSVolumeCaseFormatInsensitiveCasePreserving@2$FSVolumeCaseFormatSensitive@0$FSVolumeOpenModesRead@1$FSVolumeOpenModesWrite@2$"""
misc.update(
    {
        "FSItemDeactivationOptions": NewType("FSItemDeactivationOptions", int),
        "FSContainerState": NewType("FSContainerState", int),
        "FSMatchResult": NewType("FSMatchResult", int),
        "FSItemID": NewType("FSItemID", int),
        "FSCompleteIOFlags": NewType("FSCompleteIOFlags", int),
        "FSSyncFlags": NewType("FSSyncFlags", int),
        "FSVolumeOpenModes": NewType("FSVolumeOpenModes", int),
        "FSSetXattrPolicy": NewType("FSSetXattrPolicy", int),
        "FSVolumeCaseFormat": NewType("FSVolumeCaseFormat", int),
        "FSDeactivateOptions": NewType("FSDeactivateOptions", int),
        "FSErrorCode": NewType("FSErrorCode", int),
        "FSBlockmapFlags": NewType("FSBlockmapFlags", int),
        "FSExtentType": NewType("FSExtentType", int),
        "FSItemAttribute": NewType("FSItemAttribute", int),
        "FSPreallocateFlags": NewType("FSPreallocateFlags", int),
        "FSAccessMask": NewType("FSAccessMask", int),
        "FSItemType": NewType("FSItemType", int),
    }
)
misc.update({"FSTaskParameterConstant": NewType("FSTaskParameterConstant", str)})
misc.update({})
functions = {
    "fs_errorForMachError": (b"@i",),
    "fs_errorForCocoaError": (b"@i",),
    "fs_errorForPOSIXError": (b"@i",),
}
aliases = {
    "FSCompleteIOFlagsWrite": "FSBlockmapFlagsWrite",
    "FSCompleteIOFlagsRead": "FSBlockmapFlagsRead",
    "FSAccessAddSubdirectory": "FSAccessAppendData",
    "FSAccessAddFile": "FSAccessWriteData",
    "FSAccessListDirectory": "FSAccessReadData",
    "FSAccessSearch": "FSAccessExecute",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"FSBlockDeviceResource",
        b"asynchronousMetadataFlushWithError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"delayedMetadataWriteFrom:startingAt:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"delayedMetadataWriteFrom:startingAt:length:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(b"FSBlockDeviceResource", b"isWritable", {"retval": {"type": b"Z"}})
    r(
        b"FSBlockDeviceResource",
        b"metadataClear:withDelayedWrites:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(
        b"FSBlockDeviceResource",
        b"metadataFlushWithError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"metadataPurge:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"metadataReadInto:startingAt:length:",
        {"arguments": {2: {"type_modifier": b"o", "c_array_length_in_arg": 4}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"metadataReadInto:startingAt:length:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type_modifier": b"o", "c_array_length_in_arg": 4},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"FSBlockDeviceResource",
        b"metadataReadInto:startingAt:length:readAheadExtents:readAheadCount:",
        {
            "arguments": {
                2: {"type_modifier": b"o", "c_array_length_in_arg": 4},
                5: {"type_modifier": b"n", "c_array_length_in_arg": 6},
            }
        },
    )
    r(
        b"FSBlockDeviceResource",
        b"metadataWriteFrom:startingAt:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"metadataWriteFrom:startingAt:length:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"FSBlockDeviceResource",
        b"proxyResourceForBSDName:isWritable:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"readInto:startingAt:length:completionHandler:",
        {
            "arguments": {
                2: {"type_modifier": b"N", "c_array_length_in_arg": 4},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"FSBlockDeviceResource",
        b"readInto:startingAt:length:error:",
        {
            "arguments": {
                2: {"type_modifier": b"o", "c_array_length_in_arg": 4},
                5: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"FSBlockDeviceResource",
        b"readInto:startingAt:length:replyHandler:",
        {"arguments": {2: {"type_modifier": b"N", "c_array_length_in_arg": 4}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"synchronousReadInto:startingAt:length:replyHandler:",
        {"arguments": {2: {"type_modifier": b"N", "c_array_length_in_arg": 4}}},
    )
    r(
        b"FSBlockDeviceResource",
        b"writeFrom:startingAt:length:completionHandler:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"FSBlockDeviceResource",
        b"writeFrom:startingAt:length:error:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                5: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"FSBlockDeviceResource",
        b"writeFrom:startingAt:length:replyHandler:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"FSClient",
        b"fetchInstalledExtensionsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"FSDirectoryEntryPacker",
        b"packEntryWithName:itemType:itemID:nextCookie:attributes:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSExtentPacker",
        b"packExtentWithResource:type:logicalOffset:physicalOffset:length:",
        {"retval": {"type": b"Z"}},
    )
    r(b"FSFileDataBuffer", b"bytes", {"retval": {"c_array_of_variable_length": True}})
    r(
        b"FSFileDataBuffer",
        b"withBytes:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "type": b"^v",
                                "type_modifier": "n",
                                "c_array_of_variable_length": True,
                            },
                        },
                    }
                }
            }
        },
    )
    r(
        b"FSFileName",
        b"initWithBytes:length:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"FSFileName",
        b"initWithCString:",
        {"arguments": {2: {"c_array_delimited_by_null": True, "type_modifier": b"n"}}},
    )
    r(
        b"FSFileName",
        b"nameWithBytes:length:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"FSFileName",
        b"nameWithCString:",
        {"arguments": {2: {"c_array_delimited_by_null": True, "type_modifier": b"n"}}},
    )
    r(b"FSItemAttributes", b"accessTime:", {"arguments": {2: {"type_modifier": b"o"}}})
    r(b"FSItemAttributes", b"addedTime:", {"arguments": {2: {"type_modifier": b"o"}}})
    r(b"FSItemAttributes", b"backupTime:", {"arguments": {2: {"type_modifier": b"o"}}})
    r(b"FSItemAttributes", b"birthTime:", {"arguments": {2: {"type_modifier": b"o"}}})
    r(b"FSItemAttributes", b"changeTime:", {"arguments": {2: {"type_modifier": b"o"}}})
    r(b"FSItemAttributes", b"inhibitKernelOffloadedIO", {"retval": {"type": b"Z"}})
    r(b"FSItemAttributes", b"isValid:", {"retval": {"type": b"Z"}})
    r(b"FSItemAttributes", b"modifyTime:", {"arguments": {2: {"type_modifier": b"o"}}})
    r(
        b"FSItemAttributes",
        b"setAccessTime:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(
        b"FSItemAttributes",
        b"setAddedTime:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(
        b"FSItemAttributes",
        b"setBackupTime:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(
        b"FSItemAttributes",
        b"setBirthTime:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(
        b"FSItemAttributes",
        b"setChangeTime:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(
        b"FSItemAttributes",
        b"setInhibitKernelOffloadedIO:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSItemAttributes",
        b"setModifyTime:",
        {"arguments": {2: {"type_modifier": b"n"}}},
    )
    r(
        b"FSItemAttributes",
        b"setSupportsLimitedXAttrs:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"FSItemAttributes", b"supportsLimitedXAttrs", {"retval": {"type": b"Z"}})
    r(b"FSItemGetAttributesRequest", b"isAttributeWanted:", {"retval": {"type": b"Z"}})
    r(
        b"FSItemSetAttributesRequest",
        b"wasAttributeConsumed:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSMessageConnection",
        b"localizedMessage:table:bundle:",
        {"arguments": {2: {"printf_format": True}}, "variadic": True},
    )
    r(
        b"FSMessageConnection",
        b"logLocalizedMessage:table:bundle:",
        {"arguments": {2: {"printf_format": True}}, "variadic": True},
    )
    r(
        b"FSMessageConnection",
        b"logLocalizedMessage:table:bundle:arguments:",
        {"suggestion": "Use one of the other log methods"},
    )
    r(b"FSModuleIdentity", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"FSModuleIdentity", b"isSystem", {"retval": {"type": "Z"}})
    r(
        b"FSMutableFileDataBuffer",
        b"mutableBytes",
        {"retval": {"c_array_of_variable_length": True}},
    )
    r(
        b"FSMutableFileDataBuffer",
        b"withMutableBytes:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "type": b"^v",
                                "type_modifier": "N",
                                "c_array_of_variable_length": True,
                            },
                        },
                    }
                }
            }
        },
    )
    r(b"FSResource", b"isRevoked", {"retval": {"type": b"Z"}})
    r(
        b"FSVolumeSupportedCapabilities",
        b"doesNotSupportImmutableFiles",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"doesNotSupportRootTimes",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"doesNotSupportSettingFilePermissions",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"doesNotSupportVolumeSizes",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setDoesNotSupportImmutableFiles:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setDoesNotSupportRootTimes:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setDoesNotSupportSettingFilePermissions:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setDoesNotSupportVolumeSizes:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupports2TBFiles:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupports64BitObjectIDs:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsActiveJournal:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsDocumentID:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsFastStatFS:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsHardLinks:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsHiddenFiles:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsJournal:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsOpenDenyModes:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsPersistentObjectIDs:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsSharedSpace:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsSparseFiles:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsSymbolicLinks:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsVolumeGroups:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"setSupportsZeroRuns:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"FSVolumeSupportedCapabilities", b"supports2TBFiles", {"retval": {"type": b"Z"}})
    r(
        b"FSVolumeSupportedCapabilities",
        b"supports64BitObjectIDs",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsActiveJournal",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsDocumentID",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsFastStatFS",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsHardLinks",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsHiddenFiles",
        {"retval": {"type": b"Z"}},
    )
    r(b"FSVolumeSupportedCapabilities", b"supportsJournal", {"retval": {"type": b"Z"}})
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsOpenDenyModes",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsPersistentObjectIDs",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsSharedSpace",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsSparseFiles",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsSymbolicLinks",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"FSVolumeSupportedCapabilities",
        b"supportsVolumeGroups",
        {"retval": {"type": b"Z"}},
    )
    r(b"FSVolumeSupportedCapabilities", b"supportsZeroRuns", {"retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"activateWithOptions:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"blockmapFile:offset:length:flags:operationID:packer:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
                6: {"type": b"Q"},
                7: {"type": b"@"},
                8: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"blockmapFile:range:startIO:flags:operationID:usingPacker:replyHandler:",
        {
            "arguments": {
                7: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"i"},
                            3: {"type": b"Q"},
                            4: {"type": b"Q"},
                            5: {"type": b"I"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSObject",
        b"checkAccessToItem:requestedAccess:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Z"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"closeItem:keepingModes:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"completeIOForFile:offset:length:status:flags:operationID:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
                8: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"containerStatus", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"createFileNamed:inDirectory:attributes:packer:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"createItemNamed:type:inDirectory:attributes:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"createItemNamed:type:inDirectory:attributes:usingPacker:replyHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"i"},
                            3: {"type": b"Q"},
                            4: {"type": b"Q"},
                            5: {"type": b"I"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSObject",
        b"createLinkToItem:named:inDirectory:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"createSymbolicLinkNamed:inDirectory:attributes:linkContents:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"deactivateItem:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"deactivateWithOptions:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"q"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"didFinishLoading", {"required": False, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"enumerateDirectory:startingAtCookie:verifier:provideAttributes:attributes:usingBlock:replyHandler:",
        {
            "arguments": {
                7: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"C"},
                            3: {"type": b"Q"},
                            4: {"type": b"Q"},
                            5: {"type": b"@"},
                            6: {"type": b"B"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSObject",
        b"enumerateDirectory:startingAtCookie:verifier:providingAttributes:usingPacker:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"@"},
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"getAttributes:ofItem:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"getXattrNamed:ofItem:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"isAccessCheckInhibited",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"isOpenCloseInhibited",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"isPreallocateInhibited",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"isVolumeRenameInhibited",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"itemDeactivationPolicy",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"listXattrsOfItem:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"loadResource:options:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"lookupItemNamed:inDirectory:packer:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"lookupItemNamed:inDirectory:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"lookupName:inDirectory:usingPacker:replyHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"i"},
                            3: {"type": b"Q"},
                            4: {"type": b"Q"},
                            5: {"type": b"I"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSObject", b"maximumFileSize", {"required": False, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"maximumFileSizeInBits",
        {"required": False, "retval": {"type": b"q"}},
    )
    r(b"NSObject", b"maximumLinkCount", {"required": True, "retval": {"type": b"q"}})
    r(b"NSObject", b"maximumNameLength", {"required": True, "retval": {"type": b"q"}})
    r(b"NSObject", b"maximumXattrSize", {"required": False, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"maximumXattrSizeInBits",
        {"required": False, "retval": {"type": b"q"}},
    )
    r(
        b"NSObject",
        b"mountWithOptions:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"openItem:withModes:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"preallocate:offset:length:flags:usingPacker:replyHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"i"},
                            3: {"type": b"Q"},
                            4: {"type": b"Q"},
                            5: {"type": b"I"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSObject",
        b"preallocateSpaceForFile:atOffset:length:flags:packer:replyHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
                6: {"type": b"@"},
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"preallocateSpaceForItem:atOffset:length:flags:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"probeResource:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"readFromFile:offset:length:intoBuffer:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"readSymbolicLink:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"reclaimItem:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"removeItem:named:fromDirectory:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"renameItem:inDirectory:named:toNewName:inDirectory:overItem:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
                7: {"type": b"@"},
                8: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"restrictsOwnershipChanges",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"setAccessCheckInhibited:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setAttributes:onItem:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setContainerStatus:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setOpenCloseInhibited:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setPreallocateInhibited:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setVolumeName:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setVolumeRenameInhibited:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setXattrNamed:toData:onItem:policy:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setXattrOperationsInhibited:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"startCheckWithTask:options:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"startFormatWithTask:options:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"supportedVolumeCapabilities",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"supportedXattrNamesForItem:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"synchronizeWithFlags:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"q"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"truncatesLongNames", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"unloadResource:options:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"unmountWithReplyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(b"NSObject", b"volumeStatistics", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"wipeResource:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"writeContents:toFile:atOffset:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"xattrOperationsInhibited",
        {"required": False, "retval": {"type": b"Z"}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("FSEntityIdentifier", b"initWithUUID:")
objc.registerNewKeywordsFromSelector("FSEntityIdentifier", b"initWithUUID:data:")
objc.registerNewKeywordsFromSelector("FSEntityIdentifier", b"initWithUUID:qualifier:")
objc.registerNewKeywordsFromSelector("FSFileName", b"initWithBytes:length:")
objc.registerNewKeywordsFromSelector("FSFileName", b"initWithCString:")
objc.registerNewKeywordsFromSelector("FSFileName", b"initWithData:")
objc.registerNewKeywordsFromSelector("FSFileName", b"initWithString:")
objc.registerNewKeywordsFromSelector(
    "FSMetadataRange", b"initWithOffset:segmentLength:segmentCount:"
)
objc.registerNewKeywordsFromSelector("FSStatFSResult", b"initWithFileSystemTypeName:")
objc.registerNewKeywordsFromSelector("FSVolume", b"initWithVolumeID:volumeName:")
expressions = {}

# END OF FILE
