# This file is generated by objective.metadata
#
# Last update: Fri Jun 28 12:57:12 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "MIDIIOErrorNotification": objc.createStructType(
            "CoreMIDI.MIDIIOErrorNotification",
            b"{MIDIIOErrorNotification=iIIi}",
            ["messageID", "messageSize", "driverDevice", "errorCode"],
        ),
        "MIDIMessage_96": objc.createStructType(
            "CoreMIDI.MIDIMessage_96",
            b"{MIDIMessage_96=III}",
            ["word0", "word1", "word2"],
        ),
        "MIDIMessage_128": objc.createStructType(
            "CoreMIDI.MIDIMessage_128",
            b"{MIDIMessage_128=IIII}",
            ["word0", "word1", "word2", "word3"],
        ),
        "MIDIThruConnectionEndpoint": objc.createStructType(
            "CoreMIDI.MIDIThruConnectionEndpoint",
            b"{MIDIThruConnectionEndpoint=Ii}",
            ["endpointRef", "uniqueID"],
        ),
        "MIDIThruConnectionParams": objc.createStructType(
            "CoreMIDI.MIDIThruConnectionParams",
            b"{MIDIThruConnectionParams=II[8{MIDIThruConnectionEndpoint=Ii}]I[8{MIDIThruConnectionEndpoint=Ii}][16C]CCCC{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}CCCC[3C]CSS[4S]}",
            [
                "version",
                "numSources",
                "sources",
                "numDestinations",
                "destinations",
                "channelMap",
                "lowVelocity",
                "highVelocity",
                "lowNote",
                "highNote",
                "noteNumber",
                "velocity",
                "keyPressure",
                "channelPressure",
                "programChange",
                "pitchBend",
                "filterOutSysEx",
                "filterOutMTC",
                "filterOutBeatClock",
                "filterOutTuneRequest",
                "reserved2",
                "filterOutAllControls",
                "numControlTransforms",
                "numMaps",
                "reserved3",
            ],
        ),
        "MIDIObjectPropertyChangeNotification": objc.createStructType(
            "CoreMIDI.MIDIObjectPropertyChangeNotification",
            b"{MIDIObjectPropertyChangeNotification=iIIi^{__CFString=}}",
            ["messageID", "messageSize", "object", "objectType", "propertyName"],
        ),
        "MIDIMessage_64": objc.createStructType(
            "CoreMIDI.MIDIMessage_64", b"{MIDIMessage_64=II}", ["word0", "word1"]
        ),
        "MIDIValueMap": objc.createStructType(
            "CoreMIDI.MIDIValueMap", b"{MIDIValueMap=[128C]}", ["value"]
        ),
        "MIDIPacket": objc.createStructType(
            "CoreMIDI.MIDIPacket",
            b"{MIDIPacket=QS[256C]}",
            ["timeStamp", "length", "data"],
        ),
        "MIDIControlTransform": objc.createStructType(
            "CoreMIDI.MIDIControlTransform",
            b"{MIDIControlTransform=CCSSs}",
            [
                "controlType",
                "remappedControlType",
                "controlNumber",
                "transform",
                "param",
            ],
        ),
        "MIDICIDeviceIdentification": objc.createStructType(
            "CoreMIDI.MIDICIDeviceIdentification",
            b"{MIDICIDeviceIdentification=[3C][2C][2C][4C][5C]}",
            ["manufacturer", "family", "modelNumber", "revisionLevel", "reserved"],
        ),
        "MIDICIProfileIDStandard": objc.createStructType(
            "CoreMIDI.MIDICIProfileIDStandard",
            b"{MIDICIProfileIDStandard=CCCCC}",
            [
                "profileIDByte1",
                "profileBank",
                "profileNumber",
                "profileVersion",
                "profileLevel",
            ],
        ),
        "MIDIEventList": objc.createStructType(
            "CoreMIDI.MIDIEventList",
            b"{MIDIEventList=iI[1{MIDIEventPacket=QI[64I]}]}",
            ["protocol", "numPackets", "packet"],
        ),
        "MIDIObjectAddRemoveNotification": objc.createStructType(
            "CoreMIDI.MIDIObjectAddRemoveNotification",
            b"{MIDIObjectAddRemoveNotification=iIIiIi}",
            ["messageID", "messageSize", "parent", "parentType", "child", "childType"],
        ),
        "MIDINotification": objc.createStructType(
            "CoreMIDI.MIDINotification",
            b"{MIDINotification=iI}",
            ["messageID", "messageSize"],
        ),
        "MIDITransform": objc.createStructType(
            "CoreMIDI.MIDITransform", b"{MIDITransform=Ss}", ["transform", "param"]
        ),
        "MIDIEventPacket": objc.createStructType(
            "CoreMIDI.MIDIEventPacket",
            b"{MIDIEventPacket=QI[64I]}",
            ["timeStamp", "wordCount", "words"],
        ),
        "MIDI2DeviceRevisionLevel": objc.createStructType(
            "CoreMIDI.MIDI2DeviceRevisionLevel",
            b"{MIDI2DeviceRevisionLevel=[4C]}",
            ["revisionLevel"],
        ),
        "MIDI2DeviceManufacturer": objc.createStructType(
            "CoreMIDI.MIDI2DeviceManufacturer",
            b"{MIDI2DeviceManufacturer=[3C]}",
            ["sysExIDByte"],
        ),
        "MIDICIProfileIDManufacturerSpecific": objc.createStructType(
            "CoreMIDI.MIDICIProfileIDManufacturerSpecific",
            b"{MIDICIProfileIDManufacturerSpecific=CCCCC}",
            ["sysExID1", "sysExID2", "sysExID3", "info1", "info2"],
        ),
        "MIDIPacketList": objc.createStructType(
            "CoreMIDI.MIDIPacketList",
            b"{MIDIPacketList=I[1{MIDIPacket=QS[256C]}]}",
            ["numPackets", "packet"],
        ),
    }
)
constants = """$MIDICIDeviceObjectKey$MIDICIDeviceWasAddedNotification$MIDICIDeviceWasRemovedNotification$MIDICIProfileObjectKey$MIDICIProfileWasRemovedNotification$MIDICIProfileWasUpdatedNotification$MIDINetworkBonjourServiceType$MIDINetworkNotificationContactsDidChange$MIDINetworkNotificationSessionDidChange$MIDIUMPEndpointObjectKey$MIDIUMPEndpointWasAddedNotification$MIDIUMPEndpointWasRemovedNotification$MIDIUMPEndpointWasUpdatedNotification$MIDIUMPFunctionBlockObjectKey$MIDIUMPFunctionBlockWasUpdatedNotification$kMIDIDriverPropertyUsesSerial$kMIDIPropertyAdvanceScheduleTimeMuSec$kMIDIPropertyAssociatedEndpoint$kMIDIPropertyCanRoute$kMIDIPropertyConnectionUniqueID$kMIDIPropertyDeviceID$kMIDIPropertyDisplayName$kMIDIPropertyDriverDeviceEditorApp$kMIDIPropertyDriverOwner$kMIDIPropertyDriverVersion$kMIDIPropertyFactoryPatchNameFile$kMIDIPropertyImage$kMIDIPropertyIsBroadcast$kMIDIPropertyIsDrumMachine$kMIDIPropertyIsEffectUnit$kMIDIPropertyIsEmbeddedEntity$kMIDIPropertyIsMixer$kMIDIPropertyIsSampler$kMIDIPropertyManufacturer$kMIDIPropertyMaxReceiveChannels$kMIDIPropertyMaxSysExSpeed$kMIDIPropertyMaxTransmitChannels$kMIDIPropertyModel$kMIDIPropertyName$kMIDIPropertyNameConfiguration$kMIDIPropertyNameConfigurationDictionary$kMIDIPropertyOffline$kMIDIPropertyPanDisruptsStereo$kMIDIPropertyPrivate$kMIDIPropertyProtocolID$kMIDIPropertyReceiveChannels$kMIDIPropertyReceivesBankSelectLSB$kMIDIPropertyReceivesBankSelectMSB$kMIDIPropertyReceivesClock$kMIDIPropertyReceivesMTC$kMIDIPropertyReceivesNotes$kMIDIPropertyReceivesProgramChanges$kMIDIPropertySingleRealtimeEntity$kMIDIPropertySupportsGeneralMIDI$kMIDIPropertySupportsMMC$kMIDIPropertySupportsShowControl$kMIDIPropertyTransmitChannels$kMIDIPropertyTransmitsBankSelectLSB$kMIDIPropertyTransmitsBankSelectMSB$kMIDIPropertyTransmitsClock$kMIDIPropertyTransmitsMTC$kMIDIPropertyTransmitsNotes$kMIDIPropertyTransmitsProgramChanges$kMIDIPropertyUMPActiveGroupBitmap$kMIDIPropertyUMPCanTransmitGroupless$kMIDIPropertyUniqueID$kMIDIPropertyUserPatchNameFile$"""
enums = """$MIDINetworkConnectionPolicy_Anyone@2$MIDINetworkConnectionPolicy_HostsInContactList@1$MIDINetworkConnectionPolicy_NoOne@0$kMIDICICategoryOptionsProcessInquirySupported@16$kMIDICICategoryOptionsProfileConfigurationSupported@4$kMIDICICategoryOptionsPropertyExchangeSupported@8$kMIDICICategoryOptionsProtocolNegotiation@2$kMIDICIDeviceTypeLegacyMIDI1@1$kMIDICIDeviceTypeUSBMIDI@3$kMIDICIDeviceTypeUnknown@0$kMIDICIDeviceTypeVirtual@2$kMIDICIManagementMessageTypeDiscovery@112$kMIDICIManagementMessageTypeInquiryEndpointInformation@114$kMIDICIManagementMessageTypeInvalidateMUID@126$kMIDICIManagementMessageTypeMIDICIACK@125$kMIDICIManagementMessageTypeMIDICINAK@127$kMIDICIManagementMessageTypeReplyToDiscovery@113$kMIDICIManagementMessageTypeReplyToEndpointInformation@115$kMIDICIProcessInquiryMessageTypeEndOfMIDIMessageReport@68$kMIDICIProcessInquiryMessageTypeInquiryMIDIMessageReport@66$kMIDICIProcessInquiryMessageTypeInquiryProcessInquiryCapabilities@64$kMIDICIProcessInquiryMessageTypeReplyToMIDIMessageReport@67$kMIDICIProcessInquiryMessageTypeReplyToProcessInquiryCapabilities@65$kMIDICIProfileMessageTypeDetailsInquiry@40$kMIDICIProfileMessageTypeProfileAdded@38$kMIDICIProfileMessageTypeProfileDisabledReport@37$kMIDICIProfileMessageTypeProfileEnabledReport@36$kMIDICIProfileMessageTypeProfileInquiry@32$kMIDICIProfileMessageTypeProfileRemoved@39$kMIDICIProfileMessageTypeProfileSpecificData@47$kMIDICIProfileMessageTypeReplyToDetailsInquiry@41$kMIDICIProfileMessageTypeReplyToProfileInquiry@33$kMIDICIProfileMessageTypeSetProfileOff@35$kMIDICIProfileMessageTypeSetProfileOn@34$kMIDICIProfileTypeFunctionBlock@3$kMIDICIProfileTypeGroup@2$kMIDICIProfileTypeMultichannel@4$kMIDICIProfileTypeSingleChannel@1$kMIDICIPropertyExchangeBadRequestID@255$kMIDICIPropertyExchangeMessageTypeInquiryGetPropertyData@52$kMIDICIPropertyExchangeMessageTypeInquiryHasPropertyData_Reserved@50$kMIDICIPropertyExchangeMessageTypeInquiryPropertyExchangeCapabilities@48$kMIDICIPropertyExchangeMessageTypeInquiryReplyToHasPropertyData_Reserved@51$kMIDICIPropertyExchangeMessageTypeInquirySetPropertyData@54$kMIDICIPropertyExchangeMessageTypeNotify@63$kMIDICIPropertyExchangeMessageTypeReplyToGetProperty@53$kMIDICIPropertyExchangeMessageTypeReplyToPropertyExchangeCapabilities@49$kMIDICIPropertyExchangeMessageTypeReplyToSetPropertyData@55$kMIDICIPropertyExchangeMessageTypeReplyToSubscription@57$kMIDICIPropertyExchangeMessageTypeSubscription@56$kMIDICVStatusAssignableControl@3$kMIDICVStatusAssignablePNC@1$kMIDICVStatusChannelPressure@13$kMIDICVStatusControlChange@11$kMIDICVStatusNoteOff@8$kMIDICVStatusNoteOn@9$kMIDICVStatusPerNoteMgmt@15$kMIDICVStatusPerNotePitchBend@6$kMIDICVStatusPitchBend@14$kMIDICVStatusPolyPressure@10$kMIDICVStatusProgramChange@12$kMIDICVStatusRegisteredControl@2$kMIDICVStatusRegisteredPNC@0$kMIDICVStatusRelAssignableControl@5$kMIDICVStatusRelRegisteredControl@4$kMIDIControlType_14Bit@1$kMIDIControlType_14BitNRPN@5$kMIDIControlType_14BitRPN@3$kMIDIControlType_7Bit@0$kMIDIControlType_7BitNRPN@4$kMIDIControlType_7BitRPN@2$kMIDIDeviceIDFunctionBlock@127$kMIDIDeviceIDUMPGroup@126$kMIDIIDNotUnique@-10843$kMIDIInvalidClient@-10830$kMIDIInvalidPort@-10831$kMIDIInvalidUniqueID@0$kMIDIMessageSendErr@-10838$kMIDIMessageTypeChannelVoice1@2$kMIDIMessageTypeChannelVoice2@4$kMIDIMessageTypeData128@5$kMIDIMessageTypeFlexData@13$kMIDIMessageTypeInvalid@255$kMIDIMessageTypeStream@15$kMIDIMessageTypeSysEx@3$kMIDIMessageTypeSystem@1$kMIDIMessageTypeUnknownF@15$kMIDIMessageTypeUtility@0$kMIDIMsgIOError@7$kMIDIMsgInternalStart@4096$kMIDIMsgObjectAdded@2$kMIDIMsgObjectRemoved@3$kMIDIMsgPropertyChanged@4$kMIDIMsgSerialPortOwnerChanged@6$kMIDIMsgSetupChanged@1$kMIDIMsgThruConnectionsChanged@5$kMIDINoConnection@-10833$kMIDINoCurrentSetup@-10837$kMIDINotPermitted@-10844$kMIDINoteAttributeManufacturerSpecific@1$kMIDINoteAttributeNone@0$kMIDINoteAttributePitch@3$kMIDINoteAttributeProfileSpecific@2$kMIDIObjectNotFound@-10842$kMIDIObjectType_Destination@3$kMIDIObjectType_Device@0$kMIDIObjectType_Entity@1$kMIDIObjectType_ExternalDestination@19$kMIDIObjectType_ExternalDevice@16$kMIDIObjectType_ExternalEntity@17$kMIDIObjectType_ExternalMask@16$kMIDIObjectType_ExternalSource@18$kMIDIObjectType_Other@-1$kMIDIObjectType_Source@2$kMIDIPerNoteManagementDetach@2$kMIDIPerNoteManagementReset@1$kMIDIProgramChangeBankValid@1$kMIDIProtocol_1_0@1$kMIDIProtocol_2_0@2$kMIDIServerStartErr@-10839$kMIDISetupFormatErr@-10840$kMIDIStatusActiveSending@254$kMIDIStatusActiveSensing@254$kMIDIStatusContinue@251$kMIDIStatusEndOfExclusive@247$kMIDIStatusMTC@241$kMIDIStatusSongPosPointer@242$kMIDIStatusSongSelect@243$kMIDIStatusStart@250$kMIDIStatusStartOfExclusive@240$kMIDIStatusStop@252$kMIDIStatusSystemReset@255$kMIDIStatusTimingClock@248$kMIDIStatusTuneRequest@246$kMIDISysExStatusComplete@0$kMIDISysExStatusContinue@2$kMIDISysExStatusEnd@3$kMIDISysExStatusMixedDataSetHeader@8$kMIDISysExStatusMixedDataSetPayload@9$kMIDISysExStatusStart@1$kMIDIThruConnection_MaxEndpoints@8$kMIDITransform_Add@8$kMIDITransform_FilterOut@1$kMIDITransform_MapControl@2$kMIDITransform_MapValue@12$kMIDITransform_MaxValue@11$kMIDITransform_MinValue@10$kMIDITransform_None@0$kMIDITransform_Scale@9$kMIDIUInteger14Max@16383$kMIDIUInteger28Max@*********$kMIDIUInteger2Max@3$kMIDIUInteger4Max@15$kMIDIUInteger7Max@127$kMIDIUMPCIObjectBackingTypeDriverDevice@2$kMIDIUMPCIObjectBackingTypeUSBMIDI@3$kMIDIUMPCIObjectBackingTypeUnknown@0$kMIDIUMPCIObjectBackingTypeVirtual@1$kMIDIUMPFunctionBlockDirectionBidirectional@3$kMIDIUMPFunctionBlockDirectionInput@1$kMIDIUMPFunctionBlockDirectionOutput@2$kMIDIUMPFunctionBlockDirectionUnknown@0$kMIDIUMPFunctionBlockMIDI1InfoNotMIDI1@0$kMIDIUMPFunctionBlockMIDI1InfoRestrictedBandwidth@2$kMIDIUMPFunctionBlockMIDI1InfoUnrestrictedBandwidth@1$kMIDIUMPFunctionBlockUIHintReceiver@1$kMIDIUMPFunctionBlockUIHintSender@2$kMIDIUMPFunctionBlockUIHintSenderReceiver@3$kMIDIUMPFunctionBlockUIHintUnknown@0$kMIDIUMPProtocolOptionsMIDI1@1$kMIDIUMPProtocolOptionsMIDI2@2$kMIDIUnknownEndpoint@-10834$kMIDIUnknownError@-10845$kMIDIUnknownProperty@-10835$kMIDIUtilityStatusDeltaClockstampTicksPerQuarterNote@3$kMIDIUtilityStatusJitterReductionClock@1$kMIDIUtilityStatusJitterReductionTimestamp@2$kMIDIUtilityStatusNOOP@0$kMIDIUtilityStatusTicksSinceLastEvent@4$kMIDIWrongEndpointType@-10832$kMIDIWrongPropertyType@-10836$kMIDIWrongThread@-10841$kUMPStreamMessageFormatComplete@0$kUMPStreamMessageFormatContinuing@2$kUMPStreamMessageFormatEnd@3$kUMPStreamMessageFormatStart@1$kUMPStreamMessageStatusDeviceIdentityNotification@2$kUMPStreamMessageStatusEndOfClip@33$kUMPStreamMessageStatusEndpointDiscovery@0$kUMPStreamMessageStatusEndpointInfoNotification@1$kUMPStreamMessageStatusEndpointNameNotification@3$kUMPStreamMessageStatusFunctionBlockDiscovery@16$kUMPStreamMessageStatusFunctionBlockInfoNotification@17$kUMPStreamMessageStatusFunctionBlockNameNotification@18$kUMPStreamMessageStatusProductInstanceIDNotification@4$kUMPStreamMessageStatusStartOfClip@32$kUMPStreamMessageStatusStreamConfigurationNotification@6$kUMPStreamMessageStatusStreamConfigurationRequest@5$"""
misc.update(
    {
        "MIDIUMPFunctionBlockUIHint": NewType("MIDIUMPFunctionBlockUIHint", int),
        "MIDICIPropertyExchangeMessageType": NewType(
            "MIDICIPropertyExchangeMessageType", int
        ),
        "MIDINotificationMessageID": NewType("MIDINotificationMessageID", int),
        "MIDICIProfileMessageType": NewType("MIDICIProfileMessageType", int),
        "MIDIUtilityStatus": NewType("MIDIUtilityStatus", int),
        "MIDICIDeviceType": NewType("MIDICIDeviceType", int),
        "MIDICIManagementMessageType": NewType("MIDICIManagementMessageType", int),
        "MIDICIProcessInquiryMessageType": NewType(
            "MIDICIProcessInquiryMessageType", int
        ),
        "MIDINoteAttribute": NewType("MIDINoteAttribute", int),
        "MIDIProgramChangeOptions": NewType("MIDIProgramChangeOptions", int),
        "MIDIUMPFunctionBlockMIDI1Info": NewType("MIDIUMPFunctionBlockMIDI1Info", int),
        "MIDIUMPFunctionBlockDirection": NewType("MIDIUMPFunctionBlockDirection", int),
        "MIDIMessageType": NewType("MIDIMessageType", int),
        "UMPStreamMessageFormat": NewType("UMPStreamMessageFormat", int),
        "MIDITransformControlType": NewType("MIDITransformControlType", int),
        "MIDIProtocolID": NewType("MIDIProtocolID", int),
        "MIDICICategoryOptions": NewType("MIDICICategoryOptions", int),
        "MIDIPerNoteManagementOptions": NewType("MIDIPerNoteManagementOptions", int),
        "MIDICVStatus": NewType("MIDICVStatus", int),
        "MIDISysExStatus": NewType("MIDISysExStatus", int),
        "MIDIUMPCIObjectBackingType": NewType("MIDIUMPCIObjectBackingType", int),
        "MIDICIProfileType": NewType("MIDICIProfileType", int),
        "MIDIObjectType": NewType("MIDIObjectType", int),
        "MIDIUMPProtocolOptions": NewType("MIDIUMPProtocolOptions", int),
        "MIDISystemStatus": NewType("MIDISystemStatus", int),
        "UMPStreamMessageStatus": NewType("UMPStreamMessageStatus", int),
        "MIDITransformType": NewType("MIDITransformType", int),
        "MIDINetworkConnectionPolicy": NewType("MIDINetworkConnectionPolicy", int),
    }
)
misc.update({})
misc.update({})
functions = {
    "MIDIObjectSetDictionaryProperty": (b"iI^{__CFString=}^{__CFDictionary=}",),
    "MIDI1UPSysEx": (b"{MIDIMessage_64=II}CCCCCCCCC",),
    "MIDIPortDisconnectSource": (b"iII",),
    "MIDI1UPChannelVoiceMessage": (b"ICCCCC",),
    "MIDI1UPNoteOff": (b"ICCCC",),
    "MIDI2StreamConfigurationNotificationMessage": (b"{MIDIMessage_128=IIII}CBB",),
    "MIDIMessageTypeForUPWord": (b"II",),
    "MIDIObjectSetDataProperty": (b"iI^{__CFString=}^{__CFData=}",),
    "MIDISetupDispose": (b"iI",),
    "MIDI2StreamConfigurationRequestMessage": (b"{MIDIMessage_128=IIII}CBB",),
    "MIDI1UPControlChange": (b"ICCCC",),
    "MIDISetupFromData": (
        b"i^{__CFData=}^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "MIDIGetSerialPortDrivers": (
        b"i^^{__CFArray=}",
        "",
        {"arguments": {0: {"already_cfretained": True, "type_modifier": "o"}}},
    ),
    "MIDIObjectGetIntegerProperty": (
        b"iI^{__CFString=}^i",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "MIDI2FunctionBlockNameNotificationMessage": (
        b"{MIDIMessage_128=IIII}CC^vQ",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 3, "type_modifier": "n"}}},
    ),
    "MIDIDeviceGetNumberOfEntities": (b"QI",),
    "MIDITicksSinceLastEventMessage": (b"II",),
    "MIDIThruConnectionParamsInitialize": (
        b"v^{MIDIThruConnectionParams=II[8{MIDIThruConnectionEndpoint=Ii}]I[8{MIDIThruConnectionEndpoint=Ii}][16C]CCCC{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}CCCC[3C]CSS[4S]}",
        "",
        {"arguments": {0: {"type_modifier": "n"}}},
    ),
    "MIDIEntityGetNumberOfDestinations": (b"QI",),
    "MIDIGetNumberOfExternalDevices": (b"Q",),
    "MIDIDeviceNewEntity": (
        b"iI^{__CFString=}iZQQ^I",
        "",
        {"arguments": {6: {"type_modifier": "o"}}},
    ),
    "MIDISetupAddDevice": (b"iI",),
    "MIDISetupAddExternalDevice": (b"iI",),
    "MIDIJitterReductionTimestampMessage": (b"IS",),
    "MIDI2FunctionBlockDiscoveryMessage": (b"{MIDIMessage_128=IIII}CBB",),
    "MIDIGetNumberOfDevices": (b"Q",),
    "MIDIObjectRemoveProperty": (b"iI^{__CFString=}",),
    "MIDIInputPortCreate": (
        b"iI^{__CFString=}^?^v^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{MIDIPacketList=I[1{MIDIPacket=QS[256C]}]}"},
                            1: {"type": b"^v"},
                            2: {"type": b"^v"},
                        },
                    }
                },
                4: {"type_modifier": "o"},
            },
        },
    ),
    "MIDI2PitchBend": (b"{MIDIMessage_64=II}CCI",),
    "MIDI1UPChannelPressure": (b"ICCC",),
    "MIDI2FunctionBlockInfoNotificationMessage": (b"{MIDIMessage_128=IIII}BCiiiCCCC",),
    "MIDIPortConnectSource": (b"iII^v",),
    "MIDI2PerNotePitchBend": (b"{MIDIMessage_64=II}CCCI",),
    "MIDINoOpMessage": (b"I",),
    "MIDIThruConnectionSetParams": (b"iI^{__CFData=}",),
    "MIDIObjectSetStringProperty": (b"iI^{__CFString=}^{__CFString=}",),
    "MIDI2ChannelPressure": (b"{MIDIMessage_64=II}CCI",),
    "MIDIEndpointSetRefCons": (b"iI^v^v",),
    "MIDI2StartOfClipMessage": (b"{MIDIMessage_128=IIII}",),
    "MIDIObjectGetDictionaryProperty": (
        b"iI^{__CFString=}^^{__CFDictionary=}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "MIDI1UPSystemCommon": (b"ICCCC",),
    "MIDIFlushOutput": (b"iI",),
    "MIDIClientDispose": (b"iI",),
    "MIDI2EndpointInfoNotificationMessage": (b"{MIDIMessage_128=IIII}CCBCBBBB",),
    "MIDI1UPNoteOn": (b"ICCCC",),
    "MIDIDeviceDispose": (b"iI",),
    "MIDIDeviceListGetDevice": (b"IIQ",),
    "MIDIBluetoothDriverDisconnect": (b"i^{__CFString=}",),
    "MIDIEntityAddOrRemoveEndpoints": (b"iIQQ",),
    "MIDI2EndpointNameNotificationMessage": (
        b"{MIDIMessage_128=IIII}C^vQ",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "MIDI2EndpointDiscoveryMessage": (b"{MIDIMessage_128=IIII}CCBBBBB",),
    "MIDIGetSource": (b"IQ",),
    "MIDIEndpointGetEntity": (b"iI^I", "", {"arguments": {1: {"type_modifier": "o"}}}),
    "MIDI2RegisteredPNC": (b"{MIDIMessage_64=II}CCCCI",),
    "MIDIEventPacketSysexBytesForGroup": (
        b"i^{MIDIEventPacket=QI[64I]}C^^{__CFData=}",
        "",
        {
            "arguments": {
                0: {"type_modifier": "n"},
                2: {"already_cfretained": True, "type_modifier": "o"},
            }
        },
    ),
    "MIDISetupToData": (
        b"iI^^{__CFData=}",
        "",
        {"arguments": {1: {"already_cfretained": True, "type_modifier": "o"}}},
    ),
    "MIDIThruConnectionParamsSize": (
        b"Q^{MIDIThruConnectionParams=II[8{MIDIThruConnectionEndpoint=Ii}]I[8{MIDIThruConnectionEndpoint=Ii}][16C]CCCC{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}{MIDITransform=Ss}CCCC[3C]CSS[4S]}",
    ),
    "MIDIGetDestination": (b"IQ",),
    "MIDIDestinationCreate": (
        b"iI^{__CFString=}^?^v^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{MIDIPacketList=I[1{MIDIPacket=QS[256C]}]}"},
                            1: {"type": b"^v"},
                            2: {"type": b"^v"},
                        },
                    }
                },
                4: {"type_modifier": "o"},
            },
        },
    ),
    "MIDI2NoteOn": (b"{MIDIMessage_64=II}CCCCSS",),
    "MIDIJitterReductionClockMessage": (b"IS",),
    "MIDI2RelAssignableControl": (b"{MIDIMessage_64=II}CCCCI",),
    "MIDIObjectGetProperties": (
        b"iI^@Z",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "MIDISetupGetCurrent": (b"i^I", "", {"arguments": {0: {"type_modifier": "o"}}}),
    "MIDI2StreamMessage": (b"{MIDIMessage_128=IIII}CISIII",),
    "MIDIObjectGetDataProperty": (
        b"iI^{__CFString=}^^{__CFData=}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "MIDISetupCreate": (
        b"i^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {0: {"type_modifier": "o"}},
        },
    ),
    "MIDIDeltaClockstampTicksPerQuarterNoteMessage": (b"IS",),
    "MIDIEndpointDispose": (b"iI",),
    "MIDIExternalDeviceCreate": (
        b"i^{__CFString=}^{__CFString=}^{__CFString=}^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}},
        },
    ),
    "MIDI2StreamMessageFromData": (
        b"{MIDIMessage_128=IIII}CI^vQ",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 3, "type_modifier": "n"}}},
    ),
    "MIDISetSerialPortOwner": (b"i^{__CFString=}^{__CFString=}",),
    "MIDIClientCreate": (
        b"i^{__CFString=}^?^v^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{MIDINotification=iI}"},
                            1: {"type": b"^v"},
                        },
                    }
                },
                3: {"type_modifier": "o"},
            },
        },
    ),
    "MIDIPacketNext": (b"^{MIDIPacket=QS[256C]}^{MIDIPacket=QS[256C]}",),
    "MIDIDeviceAddEntity": (
        b"iI^{__CFString=}ZQQ^I",
        "",
        {"arguments": {5: {"type_modifier": "o"}}},
    ),
    "MIDIThruConnectionFind": (
        b"i^{__CFString=}^^{__CFData=}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "MIDIThruConnectionDispose": (b"iI",),
    "MIDISetupInstall": (b"iI",),
    "MIDIDestinationCreateWithProtocol": (
        b"iI^{__CFString=}i^I@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}},
        },
    ),
    "MIDIObjectFindByUniqueID": (
        b"ii^I^i",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "MIDIObjectSetIntegerProperty": (b"iI^{__CFString=}i",),
    "MIDIEventPacketNext": (b"^{MIDIEventPacket=QI[64I]}^{MIDIEventPacket=QI[64I]}",),
    "MIDI2ChannelVoiceMessage": (b"{MIDIMessage_64=II}CCCSI",),
    "MIDI2EndpointDeviceIdentityNotificationMessage": (
        b"{MIDIMessage_128=IIII}CCCSSI",
    ),
    "MIDIPortDispose": (b"iI",),
    "MIDIDeviceListAddDevice": (b"iII",),
    "MIDIGetNumberOfDestinations": (b"Q",),
    "MIDIEndpointGetRefCons": (
        b"iI^^v^^v",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "MIDIRestart": (b"i",),
    "MIDI1UPProgramChange": (b"ICCC",),
    "MIDIClientCreateWithBlock": (
        b"i^{__CFString=}^I@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "MIDIThruConnectionGetParams": (
        b"iI^^{__CFData=}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "MIDIObjectGetStringProperty": (
        b"iI^{__CFString=}^^{__CFString=}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "MIDIGetExternalDevice": (b"IQ",),
    "MIDI2RelRegisteredControl": (b"{MIDIMessage_64=II}CCCCI",),
    "MIDI2ProgramChange": (b"{MIDIMessage_64=II}CCBCCC",),
    "MIDIThruConnectionCreate": (
        b"i^{__CFString=}^{__CFData=}^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"type_modifier": "o"}},
        },
    ),
    "MIDIDeviceListDispose": (b"iI",),
    "MIDI2PerNoteManagment": (b"{MIDIMessage_64=II}CCCBB",),
    "MIDIGetSerialPortOwner": (
        b"i^{__CFString=}^^{__CFString=}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "MIDIGetDevice": (b"IQ",),
    "MIDI2ControlChange": (b"{MIDIMessage_64=II}CCCI",),
    "MIDISetupRemoveExternalDevice": (b"iI",),
    "MIDISetupRemoveDevice": (b"iI",),
    "MIDIDeviceListGetNumberOfDevices": (b"QI",),
    "MIDIInputPortCreateWithProtocol": (
        b"iI^{__CFString=}i^I@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}},
        },
    ),
    "MIDIDeviceGetEntity": (b"IIQ",),
    "MIDI2NoteOff": (b"{MIDIMessage_64=II}CCCCSS",),
    "MIDI2PolyPressure": (b"{MIDIMessage_64=II}CCCI",),
    "MIDI1UPPitchBend": (b"ICCCC",),
    "MIDI2AssignablePNC": (b"{MIDIMessage_64=II}CCCCI",),
    "MIDIOutputPortCreate": (
        b"iI^{__CFString=}^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"type_modifier": "o"}},
        },
    ),
    "MIDISourceCreateWithProtocol": (
        b"iI^{__CFString=}i^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}},
        },
    ),
    "MIDIEntityGetDestination": (b"IIQ",),
    "MIDIInputPortCreateWithBlock": (
        b"iI^{__CFString=}^I@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"type_modifier": "o"}},
        },
    ),
    "MIDI1UPPolyPressure": (b"ICCCC",),
    "MIDI2AssignableControl": (b"{MIDIMessage_64=II}CCCCI",),
    "MIDI2EndpointProductInstanceIDNotificationMessage": (
        b"{MIDIMessage_128=IIII}C^vQ",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "MIDI2FlexDataMessage": (b"{MIDIMessage_128=IIII}CCCCCCIII",),
    "MIDI2RegisteredControl": (b"{MIDIMessage_64=II}CCCCI",),
    "MIDIGetDriverIORunLoop": (b"^{__CFRunLoop=}",),
    "MIDIEntityGetDevice": (b"iI^I", "", {"arguments": {1: {"type_modifier": "o"}}}),
    "MIDIDeviceRemoveEntity": (b"iII",),
    "MIDIGetNumberOfSources": (b"Q",),
    "MIDI2EndOfClipMessage": (b"{MIDIMessage_128=IIII}",),
    "MIDISourceCreate": (
        b"iI^{__CFString=}^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"type_modifier": "o"}},
        },
    ),
    "MIDIEntityGetNumberOfSources": (b"QI",),
    "MIDIBluetoothDriverActivateAllConnections": (b"i",),
    "MIDIEntityGetSource": (b"IIQ",),
}
aliases = {"kMIDIStatusActiveSensing": "kMIDIStatusActiveSending"}
misc.update(
    {
        "MIDIDriverRef": objc.createOpaquePointerType(
            "MIDIDriverRef", b"^^{MIDriverInterface=}"
        )
    }
)
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"MIDICIDevice", b"supportsProcessInquiry", {"retval": {"type": b"Z"}})
    r(b"MIDICIDevice", b"supportsProfileConfiguration", {"retval": {"type": b"Z"}})
    r(b"MIDICIDevice", b"supportsPropertyExchange", {"retval": {"type": b"Z"}})
    r(b"MIDICIDevice", b"supportsProtocolNegotiation", {"retval": {"type": b"Z"}})
    r(b"MIDICIDiscoveredNode", b"supportsProfiles", {"retval": {"type": b"Z"}})
    r(b"MIDICIDiscoveredNode", b"supportsProperties", {"retval": {"type": b"Z"}})
    r(
        b"MIDICIDiscoveryManager",
        b"discoverWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"MIDICIResponder",
        b"initWithDeviceInfo:profileDelegate:profileStates:supportProperties:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MIDICIResponder",
        b"notifyProfile:onChannel:isEnabled:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MIDICIResponder",
        b"sendProfile:onChannel:profileData:",
        {"retval": {"type": b"Z"}},
    )
    r(b"MIDICIResponder", b"start", {"retval": {"type": b"Z"}})
    r(
        b"MIDICISession",
        b"disableProfile:onChannel:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"MIDICISession",
        b"enableProfile:onChannel:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"MIDICISession",
        b"initWithDiscoveredNode:dataReadyHandler:disconnectHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"MIDICISession",
        b"profileChangedCallback",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"q"},
                        3: {"type": b"@"},
                        4: {"type": b"Z"},
                    },
                }
            }
        },
    )
    r(
        b"MIDICISession",
        b"profileSpecificDataHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"q"},
                        3: {"type": b"@"},
                        4: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"MIDICISession",
        b"sendProfile:onChannel:profileData:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MIDICISession",
        b"setProfileChangedCallback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"q"},
                            3: {"type": b"@"},
                            4: {"type": b"Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"MIDICISession",
        b"setProfileSpecificDataHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"q"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"MIDICISession", b"supportsProfileCapability", {"retval": {"type": b"Z"}})
    r(b"MIDICISession", b"supportsPropertyCapability", {"retval": {"type": b"Z"}})
    r(b"MIDINetworkHost", b"hasSameAddressAs:", {"retval": {"type": b"Z"}})
    r(b"MIDINetworkSession", b"addConnection:", {"retval": {"type": "Z"}})
    r(b"MIDINetworkSession", b"addContact:", {"retval": {"type": "Z"}})
    r(b"MIDINetworkSession", b"isEnabled", {"retval": {"type": "Z"}})
    r(b"MIDINetworkSession", b"removeConnection:", {"retval": {"type": "Z"}})
    r(b"MIDINetworkSession", b"removeContact:", {"retval": {"type": "Z"}})
    r(b"MIDINetworkSession", b"setEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"MIDIUMPCIProfile", b"isEnabled", {"retval": {"type": b"Z"}})
    r(
        b"MIDIUMPCIProfile",
        b"setProfileState:enabledChannelCount:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"Z"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(b"MIDIUMPEndpoint", b"hasJRTSReceiveCapability", {"retval": {"type": b"Z"}})
    r(b"MIDIUMPEndpoint", b"hasJRTSTransmitCapability", {"retval": {"type": b"Z"}})
    r(b"MIDIUMPEndpoint", b"hasStaticFunctionBlocks", {"retval": {"type": b"Z"}})
    r(b"MIDIUMPFunctionBlock", b"isEnabled", {"retval": {"type": b"Z"}})
    r(
        b"MIDIUMPMutableEndpoint",
        b"initWithName:deviceInfo:productInstanceID:MIDIProtocol:destinationCallback:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "type": b"^{MIDIEventList=iI[1{MIDIEventPacket=QI[64I]}]}"
                            },
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    )
    r(b"MIDIUMPMutableEndpoint", b"isEnabled", {"retval": {"type": b"Z"}})
    r(
        b"MIDIUMPMutableEndpoint",
        b"registerFunctionBlocks:markAsStatic:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": b"Z"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(
        b"MIDIUMPMutableEndpoint",
        b"setEnabled:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"Z"}, 3: {"type_modifier": b"o"}},
        },
    )
    r(
        b"MIDIUMPMutableEndpoint",
        b"setName:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"MIDIUMPMutableFunctionBlock",
        b"initWithName:direction:firstGroup:totalGroupsSpanned:maxSysEx8Streams:MIDI1Info:UIHint:isEnabled:",
        {"arguments": {9: {"type": b"Z"}}},
    )
    r(
        b"MIDIUMPMutableFunctionBlock",
        b"reconfigureWithFirstGroup:direction:MIDI1Info:UIHint:error:",
        {"retval": {"type": b"Z"}, "arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"MIDIUMPMutableFunctionBlock",
        b"setEnabled:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"Z"}, 3: {"type_modifier": b"o"}},
        },
    )
    r(
        b"MIDIUMPMutableFunctionBlock",
        b"setName:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSObject",
        b"connectInitiator:withDeviceInfo:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"handleDataForProfile:onChannel:data:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"C"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"initiatorDisconnected:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"willSetProfile:onChannel:enabled:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"C"}, 4: {"type": b"Z"}},
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "MIDI2DeviceInfo", b"initWithManufacturerID:family:modelNumber:revisionLevel:"
)
objc.registerNewKeywordsFromSelector(
    "MIDICIDeviceInfo", b"initWithDestination:manufacturer:family:model:revision:"
)
objc.registerNewKeywordsFromSelector("MIDICIProfile", b"initWithData:")
objc.registerNewKeywordsFromSelector("MIDICIProfile", b"initWithData:name:")
objc.registerNewKeywordsFromSelector(
    "MIDICIProfileState", b"initWithChannel:enabledProfiles:disabledProfiles:"
)
objc.registerNewKeywordsFromSelector(
    "MIDICIProfileState", b"initWithEnabledProfiles:disabledProfiles:"
)
objc.registerNewKeywordsFromSelector(
    "MIDICIResponder",
    b"initWithDeviceInfo:profileDelegate:profileStates:supportProperties:",
)
objc.registerNewKeywordsFromSelector(
    "MIDICISession", b"initWithDiscoveredNode:dataReadyHandler:disconnectHandler:"
)
objc.registerNewKeywordsFromSelector(
    "MIDIUMPMutableEndpoint",
    b"initWithName:deviceInfo:productInstanceID:MIDIProtocol:destinationCallback:",
)
objc.registerNewKeywordsFromSelector(
    "MIDIUMPMutableFunctionBlock",
    b"initWithName:direction:firstGroup:totalGroupsSpanned:maxSysEx8Streams:MIDI1Info:UIHint:isEnabled:",
)
expressions = {}

# END OF FILE
