# This file is generated by objective.metadata
#
# Last update: Sun Jun  1 11:23:36 2025
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$DOMEventException$DOMException$DOMRangeException$DOMXPathException$NSReadAccessURLDocumentOption$WKErrorDomain$WKWebExtensionContextDeniedPermissionMatchPatternsWereRemovedNotification$WKWebExtensionContextDeniedPermissionsWereRemovedNotification$WKWebExtensionContextErrorDomain$WKWebExtensionContextErrorsDidUpdateNotification$WKWebExtensionContextGrantedPermissionMatchPatternsWereRemovedNotification$WKWebExtensionContextGrantedPermissionsWereRemovedNotification$WKWebExtensionContextNotificationUserInfoKeyMatchPatterns$WKWebExtensionContextNotificationUserInfoKeyPermissions$WKWebExtensionContextPermissionMatchPatternsWereDeniedNotification$WKWebExtensionContextPermissionMatchPatternsWereGrantedNotification$WKWebExtensionContextPermissionsWereDeniedNotification$WKWebExtensionContextPermissionsWereGrantedNotification$WKWebExtensionDataRecordErrorDomain$WKWebExtensionDataTypeLocal$WKWebExtensionDataTypeSession$WKWebExtensionDataTypeSynchronized$WKWebExtensionErrorDomain$WKWebExtensionMatchPatternErrorDomain$WKWebExtensionMessagePortErrorDomain$WKWebExtensionPermissionActiveTab$WKWebExtensionPermissionAlarms$WKWebExtensionPermissionClipboardWrite$WKWebExtensionPermissionContextMenus$WKWebExtensionPermissionCookies$WKWebExtensionPermissionDeclarativeNetRequest$WKWebExtensionPermissionDeclarativeNetRequestFeedback$WKWebExtensionPermissionDeclarativeNetRequestWithHostAccess$WKWebExtensionPermissionMenus$WKWebExtensionPermissionNativeMessaging$WKWebExtensionPermissionScripting$WKWebExtensionPermissionStorage$WKWebExtensionPermissionTabs$WKWebExtensionPermissionUnlimitedStorage$WKWebExtensionPermissionWebNavigation$WKWebExtensionPermissionWebRequest$WKWebsiteDataTypeCookies$WKWebsiteDataTypeDiskCache$WKWebsiteDataTypeFetchCache$WKWebsiteDataTypeFileSystem$WKWebsiteDataTypeHashSalt$WKWebsiteDataTypeIndexedDBDatabases$WKWebsiteDataTypeLocalStorage$WKWebsiteDataTypeMediaKeys$WKWebsiteDataTypeMemoryCache$WKWebsiteDataTypeOfflineWebApplicationCache$WKWebsiteDataTypeSearchFieldRecentSearches$WKWebsiteDataTypeServiceWorkerRegistrations$WKWebsiteDataTypeSessionStorage$WKWebsiteDataTypeWebSQLDatabases$WebActionButtonKey$WebActionElementKey$WebActionModifierFlagsKey$WebActionNavigationTypeKey$WebActionOriginalURLKey$WebArchivePboardType$WebElementDOMNodeKey$WebElementFrameKey$WebElementImageAltStringKey$WebElementImageKey$WebElementImageRectKey$WebElementImageURLKey$WebElementIsSelectedKey$WebElementLinkLabelKey$WebElementLinkTargetFrameKey$WebElementLinkTitleKey$WebElementLinkURLKey$WebHistoryAllItemsRemovedNotification$WebHistoryItemChangedNotification$WebHistoryItemsAddedNotification$WebHistoryItemsKey$WebHistoryItemsRemovedNotification$WebHistoryLoadedNotification$WebHistorySavedNotification$WebKitErrorDomain$WebKitErrorMIMETypeKey$WebKitErrorPlugInNameKey$WebKitErrorPlugInPageURLStringKey$WebPlugInAttributesKey$WebPlugInBaseURLKey$WebPlugInContainerKey$WebPlugInContainingElementKey$WebPlugInShouldLoadMainResourceKey$WebPreferencesChangedNotification$WebViewDidBeginEditingNotification$WebViewDidChangeNotification$WebViewDidChangeSelectionNotification$WebViewDidChangeTypingStyleNotification$WebViewDidEndEditingNotification$WebViewProgressEstimateChangedNotification$WebViewProgressFinishedNotification$WebViewProgressStartedNotification$"""
enums = """$DOM_ADDITION@2$DOM_ALLOW_KEYBOARD_INPUT@1$DOM_ANY_TYPE@0$DOM_ANY_UNORDERED_NODE_TYPE@8$DOM_ATTRIBUTE_NODE@2$DOM_AT_TARGET@2$DOM_BAD_BOUNDARYPOINTS_ERR@1$DOM_BOOLEAN_TYPE@3$DOM_BOTH@2$DOM_BUBBLING_PHASE@3$DOM_CAPTURING_PHASE@1$DOM_CDATA_SECTION_NODE@4$DOM_CHARSET_RULE@2$DOM_COMMENT_NODE@8$DOM_CSS_ATTR@22$DOM_CSS_CM@6$DOM_CSS_COUNTER@23$DOM_CSS_CUSTOM@3$DOM_CSS_DEG@11$DOM_CSS_DIMENSION@18$DOM_CSS_EMS@3$DOM_CSS_EXS@4$DOM_CSS_GRAD@13$DOM_CSS_HZ@16$DOM_CSS_IDENT@21$DOM_CSS_IN@8$DOM_CSS_INHERIT@0$DOM_CSS_KHZ@17$DOM_CSS_MM@7$DOM_CSS_MS@14$DOM_CSS_NUMBER@1$DOM_CSS_PC@10$DOM_CSS_PERCENTAGE@2$DOM_CSS_PRIMITIVE_VALUE@1$DOM_CSS_PT@9$DOM_CSS_PX@5$DOM_CSS_RAD@12$DOM_CSS_RECT@24$DOM_CSS_RGBCOLOR@25$DOM_CSS_S@15$DOM_CSS_STRING@19$DOM_CSS_UNKNOWN@0$DOM_CSS_URI@20$DOM_CSS_VALUE_LIST@2$DOM_CSS_VH@27$DOM_CSS_VMAX@29$DOM_CSS_VMIN@28$DOM_CSS_VW@26$DOM_DOCUMENT_FRAGMENT_NODE@11$DOM_DOCUMENT_NODE@9$DOM_DOCUMENT_POSITION_CONTAINED_BY@16$DOM_DOCUMENT_POSITION_CONTAINS@8$DOM_DOCUMENT_POSITION_DISCONNECTED@1$DOM_DOCUMENT_POSITION_FOLLOWING@4$DOM_DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC@32$DOM_DOCUMENT_POSITION_PRECEDING@2$DOM_DOCUMENT_TYPE_NODE@10$DOM_DOMSTRING_SIZE_ERR@2$DOM_DOM_DELTA_LINE@1$DOM_DOM_DELTA_PAGE@2$DOM_DOM_DELTA_PIXEL@0$DOM_ELEMENT_NODE@1$DOM_END_TO_END@2$DOM_END_TO_START@3$DOM_ENTITY_NODE@6$DOM_ENTITY_REFERENCE_NODE@5$DOM_FILTER_ACCEPT@1$DOM_FILTER_REJECT@2$DOM_FILTER_SKIP@3$DOM_FIRST_ORDERED_NODE_TYPE@9$DOM_FONT_FACE_RULE@5$DOM_HIERARCHY_REQUEST_ERR@3$DOM_HORIZONTAL@0$DOM_IMPORT_RULE@3$DOM_INDEX_SIZE_ERR@1$DOM_INUSE_ATTRIBUTE_ERR@10$DOM_INVALID_ACCESS_ERR@15$DOM_INVALID_CHARACTER_ERR@5$DOM_INVALID_EXPRESSION_ERR@51$DOM_INVALID_MODIFICATION_ERR@13$DOM_INVALID_NODE_TYPE_ERR@2$DOM_INVALID_STATE_ERR@11$DOM_KEYFRAMES_RULE@7$DOM_KEYFRAME_RULE@8$DOM_KEY_LOCATION_LEFT@1$DOM_KEY_LOCATION_NUMPAD@3$DOM_KEY_LOCATION_RIGHT@2$DOM_KEY_LOCATION_STANDARD@0$DOM_MEDIA_RULE@4$DOM_MODIFICATION@1$DOM_NAMESPACE_ERR@14$DOM_NAMESPACE_RULE@10$DOM_NODE_AFTER@1$DOM_NODE_BEFORE@0$DOM_NODE_BEFORE_AND_AFTER@2$DOM_NODE_INSIDE@3$DOM_NONE@0$DOM_NOTATION_NODE@12$DOM_NOT_FOUND_ERR@8$DOM_NOT_SUPPORTED_ERR@9$DOM_NO_DATA_ALLOWED_ERR@6$DOM_NO_MODIFICATION_ALLOWED_ERR@7$DOM_NUMBER_TYPE@1$DOM_ORDERED_NODE_ITERATOR_TYPE@5$DOM_ORDERED_NODE_SNAPSHOT_TYPE@7$DOM_PAGE_RULE@6$DOM_PROCESSING_INSTRUCTION_NODE@7$DOM_REMOVAL@3$DOM_SHOW_ALL@4294967295$DOM_SHOW_ATTRIBUTE@2$DOM_SHOW_CDATA_SECTION@8$DOM_SHOW_COMMENT@128$DOM_SHOW_DOCUMENT@256$DOM_SHOW_DOCUMENT_FRAGMENT@1024$DOM_SHOW_DOCUMENT_TYPE@512$DOM_SHOW_ELEMENT@1$DOM_SHOW_ENTITY@32$DOM_SHOW_ENTITY_REFERENCE@16$DOM_SHOW_NOTATION@2048$DOM_SHOW_PROCESSING_INSTRUCTION@64$DOM_SHOW_TEXT@4$DOM_START_TO_END@1$DOM_START_TO_START@0$DOM_STRING_TYPE@2$DOM_STYLE_RULE@1$DOM_SUPPORTS_RULE@12$DOM_SYNTAX_ERR@12$DOM_TEXT_NODE@3$DOM_TYPE_ERR@52$DOM_UNKNOWN_RULE@0$DOM_UNORDERED_NODE_ITERATOR_TYPE@4$DOM_UNORDERED_NODE_SNAPSHOT_TYPE@6$DOM_UNSPECIFIED_EVENT_TYPE_ERR@0$DOM_VARIABLES_RULE@7$DOM_VERTICAL@1$DOM_WEBKIT_KEYFRAMES_RULE@7$DOM_WEBKIT_KEYFRAME_RULE@8$DOM_WEBKIT_REGION_RULE@16$DOM_WRONG_DOCUMENT_ERR@4$WKAudiovisualMediaTypeAll@18446744073709551615$WKAudiovisualMediaTypeAudio@1$WKAudiovisualMediaTypeNone@0$WKAudiovisualMediaTypeVideo@2$WKContentModeDesktop@2$WKContentModeMobile@1$WKContentModeRecommended@0$WKCookiePolicyAllow@0$WKCookiePolicyDisallow@1$WKDialogResultAskAgain@2$WKDialogResultHandled@3$WKDialogResultShowDefault@1$WKDownloadPlaceholderPolicyDisable@0$WKDownloadPlaceholderPolicyEnable@1$WKDownloadRedirectPolicyAllow@1$WKDownloadRedirectPolicyCancel@0$WKErrorAttributedStringContentFailedToLoad@10$WKErrorAttributedStringContentLoadTimedOut@11$WKErrorContentRuleListStoreCompileFailed@6$WKErrorContentRuleListStoreLookUpFailed@7$WKErrorContentRuleListStoreRemoveFailed@8$WKErrorContentRuleListStoreVersionMismatch@9$WKErrorCredentialNotFound@17$WKErrorDuplicateCredential@15$WKErrorJavaScriptAppBoundDomain@14$WKErrorJavaScriptExceptionOccurred@4$WKErrorJavaScriptInvalidFrameTarget@12$WKErrorJavaScriptResultTypeIsUnsupported@5$WKErrorMalformedCredential@16$WKErrorNavigationAppBoundDomain@13$WKErrorUnknown@1$WKErrorWebContentProcessTerminated@2$WKErrorWebViewInvalidated@3$WKFullscreenStateEnteringFullscreen@1$WKFullscreenStateExitingFullscreen@3$WKFullscreenStateInFullscreen@2$WKFullscreenStateNotInFullscreen@0$WKInactiveSchedulingPolicyNone@2$WKInactiveSchedulingPolicySuspend@0$WKInactiveSchedulingPolicyThrottle@1$WKMediaCaptureStateActive@1$WKMediaCaptureStateMuted@2$WKMediaCaptureStateNone@0$WKMediaCaptureTypeCamera@0$WKMediaCaptureTypeCameraAndMicrophone@2$WKMediaCaptureTypeMicrophone@1$WKMediaPlaybackStateNone@0$WKMediaPlaybackStatePaused@2$WKMediaPlaybackStatePlaying@1$WKMediaPlaybackStateSuspended@3$WKNavigationActionPolicyAllow@1$WKNavigationActionPolicyCancel@0$WKNavigationActionPolicyDownload@2$WKNavigationResponsePolicyAllow@1$WKNavigationResponsePolicyCancel@0$WKNavigationResponsePolicyDownload@2$WKNavigationTypeBackForward@2$WKNavigationTypeFormResubmitted@4$WKNavigationTypeFormSubmitted@1$WKNavigationTypeLinkActivated@0$WKNavigationTypeOther@-1$WKNavigationTypeReload@3$WKPermissionDecisionDeny@2$WKPermissionDecisionGrant@1$WKPermissionDecisionPrompt@0$WKUserInterfaceDirectionPolicyContent@0$WKUserInterfaceDirectionPolicySystem@1$WKUserScriptInjectionTimeAtDocumentEnd@1$WKUserScriptInjectionTimeAtDocumentStart@0$WKWebExtensionContextErrorAlreadyLoaded@2$WKWebExtensionContextErrorBackgroundContentFailedToLoad@6$WKWebExtensionContextErrorBaseURLAlreadyInUse@4$WKWebExtensionContextErrorNoBackgroundContent@5$WKWebExtensionContextErrorNotLoaded@3$WKWebExtensionContextErrorUnknown@1$WKWebExtensionContextPermissionStatusDeniedExplicitly@-3$WKWebExtensionContextPermissionStatusDeniedImplicitly@-2$WKWebExtensionContextPermissionStatusGrantedExplicitly@3$WKWebExtensionContextPermissionStatusGrantedImplicitly@2$WKWebExtensionContextPermissionStatusRequestedExplicitly@1$WKWebExtensionContextPermissionStatusRequestedImplicitly@-1$WKWebExtensionContextPermissionStatusUnknown@0$WKWebExtensionDataRecordErrorLocalStorageFailed@2$WKWebExtensionDataRecordErrorSessionStorageFailed@3$WKWebExtensionDataRecordErrorSynchronizedStorageFailed@4$WKWebExtensionDataRecordErrorUnknown@1$WKWebExtensionErrorInvalidArchive@9$WKWebExtensionErrorInvalidBackgroundPersistence@8$WKWebExtensionErrorInvalidDeclarativeNetRequestEntry@7$WKWebExtensionErrorInvalidManifest@4$WKWebExtensionErrorInvalidManifestEntry@6$WKWebExtensionErrorInvalidResourceCodeSignature@3$WKWebExtensionErrorResourceNotFound@2$WKWebExtensionErrorUnknown@1$WKWebExtensionErrorUnsupportedManifestVersion@5$WKWebExtensionMatchPatternErrorInvalidHost@3$WKWebExtensionMatchPatternErrorInvalidPath@4$WKWebExtensionMatchPatternErrorInvalidScheme@2$WKWebExtensionMatchPatternErrorUnknown@1$WKWebExtensionMatchPatternOptionsIgnorePaths@2$WKWebExtensionMatchPatternOptionsIgnoreSchemes@1$WKWebExtensionMatchPatternOptionsMatchBidirectionally@4$WKWebExtensionMatchPatternOptionsNone@0$WKWebExtensionMessagePortErrorMessageInvalid@3$WKWebExtensionMessagePortErrorNotConnected@2$WKWebExtensionMessagePortErrorUnknown@1$WKWebExtensionTabChangedPropertiesLoading@2$WKWebExtensionTabChangedPropertiesMuted@4$WKWebExtensionTabChangedPropertiesNone@0$WKWebExtensionTabChangedPropertiesPinned@8$WKWebExtensionTabChangedPropertiesPlayingAudio@16$WKWebExtensionTabChangedPropertiesReaderMode@32$WKWebExtensionTabChangedPropertiesSize@64$WKWebExtensionTabChangedPropertiesTitle@128$WKWebExtensionTabChangedPropertiesURL@256$WKWebExtensionTabChangedPropertiesZoomFactor@512$WKWebExtensionWindowStateFullscreen@3$WKWebExtensionWindowStateMaximized@2$WKWebExtensionWindowStateMinimized@1$WKWebExtensionWindowStateNormal@0$WKWebExtensionWindowTypeNormal@0$WKWebExtensionWindowTypePopup@1$WKWebpagePreferencesUpgradeToHTTPSPolicyAutomaticFallbackToHTTP@1$WKWebpagePreferencesUpgradeToHTTPSPolicyErrorOnFailure@3$WKWebpagePreferencesUpgradeToHTTPSPolicyKeepAsRequested@0$WKWebpagePreferencesUpgradeToHTTPSPolicyUserMediatedFallbackToHTTP@2$WK_API_ENABLED@1$WebCacheModelDocumentBrowser@1$WebCacheModelDocumentViewer@0$WebCacheModelPrimaryWebBrowser@2$WebDragDestinationActionAny@4294967295$WebDragDestinationActionDHTML@1$WebDragDestinationActionEdit@2$WebDragDestinationActionLoad@4$WebDragDestinationActionNone@0$WebDragSourceActionAny@4294967295$WebDragSourceActionDHTML@1$WebDragSourceActionImage@2$WebDragSourceActionLink@4$WebDragSourceActionNone@0$WebDragSourceActionSelection@8$WebJNIReturnTypeBoolean@3$WebJNIReturnTypeByte@4$WebJNIReturnTypeChar@5$WebJNIReturnTypeDouble@10$WebJNIReturnTypeFloat@9$WebJNIReturnTypeInt@7$WebJNIReturnTypeInvalid@0$WebJNIReturnTypeLong@8$WebJNIReturnTypeObject@2$WebJNIReturnTypeShort@6$WebJNIReturnTypeVoid@1$WebKitErrorBlockedPlugInVersion@203$WebKitErrorCannotFindPlugIn@200$WebKitErrorCannotLoadPlugIn@201$WebKitErrorCannotShowMIMEType@100$WebKitErrorCannotShowURL@101$WebKitErrorFrameLoadInterruptedByPolicyChange@102$WebKitErrorJavaUnavailable@202$WebMenuItemPDFActualSize@24$WebMenuItemPDFAutoSize@27$WebMenuItemPDFContinuous@30$WebMenuItemPDFFacingPages@29$WebMenuItemPDFNextPage@31$WebMenuItemPDFPreviousPage@32$WebMenuItemPDFSinglePage@28$WebMenuItemPDFZoomIn@25$WebMenuItemPDFZoomOut@26$WebMenuItemTagCopy@8$WebMenuItemTagCopyImageToClipboard@6$WebMenuItemTagCopyLinkToClipboard@3$WebMenuItemTagCut@13$WebMenuItemTagDownloadImageToDisk@5$WebMenuItemTagDownloadLinkToDisk@2$WebMenuItemTagGoBack@9$WebMenuItemTagGoForward@10$WebMenuItemTagIgnoreSpelling@17$WebMenuItemTagLearnSpelling@18$WebMenuItemTagLookUpInDictionary@22$WebMenuItemTagNoGuessesFound@16$WebMenuItemTagOpenFrameInNewWindow@7$WebMenuItemTagOpenImageInNewWindow@4$WebMenuItemTagOpenLinkInNewWindow@1$WebMenuItemTagOpenWithDefaultApplication@23$WebMenuItemTagOther@19$WebMenuItemTagPaste@14$WebMenuItemTagReload@12$WebMenuItemTagSearchInSpotlight@20$WebMenuItemTagSearchWeb@21$WebMenuItemTagSpellingGuess@15$WebMenuItemTagStop@11$WebNavigationTypeBackForward@2$WebNavigationTypeFormResubmitted@4$WebNavigationTypeFormSubmitted@1$WebNavigationTypeLinkClicked@0$WebNavigationTypeOther@5$WebNavigationTypeReload@3$WebViewInsertActionDropped@2$WebViewInsertActionPasted@1$WebViewInsertActionTyped@0$"""
misc.update(
    {
        "WKMediaPlaybackState": NewType("WKMediaPlaybackState", int),
        "WebDragDestinationAction": NewType("WebDragDestinationAction", int),
        "WKNavigationType": NewType("WKNavigationType", int),
        "WKWebExtensionMatchPatternOptions": NewType(
            "WKWebExtensionMatchPatternOptions", int
        ),
        "WKNavigationResponsePolicy": NewType("WKNavigationResponsePolicy", int),
        "WKMediaCaptureType": NewType("WKMediaCaptureType", int),
        "DOMExceptionCode": NewType("DOMExceptionCode", int),
        "WKWebpagePreferencesUpgradeToHTTPSPolicy": NewType(
            "WKWebpagePreferencesUpgradeToHTTPSPolicy", int
        ),
        "WKWebExtensionWindowType": NewType("WKWebExtensionWindowType", int),
        "DOMXPathExceptionCode": NewType("DOMXPathExceptionCode", int),
        "WKWebExtensionDataRecordError": NewType("WKWebExtensionDataRecordError", int),
        "WKWebExtensionError": NewType("WKWebExtensionError", int),
        "WKWebExtensionTabChangedProperties": NewType(
            "WKWebExtensionTabChangedProperties", int
        ),
        "WKUserInterfaceDirectionPolicy": NewType(
            "WKUserInterfaceDirectionPolicy", int
        ),
        "WKDownloadPlaceholderPolicy": NewType("WKDownloadPlaceholderPolicy", int),
        "WKErrorCode": NewType("WKErrorCode", int),
        "DOMEventExceptionCode": NewType("DOMEventExceptionCode", int),
        "WKNavigationActionPolicy": NewType("WKNavigationActionPolicy", int),
        "WKPermissionDecision": NewType("WKPermissionDecision", int),
        "WKWebExtensionWindowState": NewType("WKWebExtensionWindowState", int),
        "WKWebExtensionContextError": NewType("WKWebExtensionContextError", int),
        "WKWebExtensionContextPermissionStatus": NewType(
            "WKWebExtensionContextPermissionStatus", int
        ),
        "WKContentMode": NewType("WKContentMode", int),
        "WKInactiveSchedulingPolicy": NewType("WKInactiveSchedulingPolicy", int),
        "WKDialogResult": NewType("WKDialogResult", int),
        "WKAudiovisualMediaTypes": NewType("WKAudiovisualMediaTypes", int),
        "WebCacheModel": NewType("WebCacheModel", int),
        "WKUserScriptInjectionTime": NewType("WKUserScriptInjectionTime", int),
        "WKCookiePolicy": NewType("WKCookiePolicy", int),
        "WebViewInsertAction": NewType("WebViewInsertAction", int),
        "DOMRangeExceptionCode": NewType("DOMRangeExceptionCode", int),
        "WKFullscreenState": NewType("WKFullscreenState", int),
        "WKDownloadRedirectPolicy": NewType("WKDownloadRedirectPolicy", int),
        "WKWebExtensionMessagePortError": NewType(
            "WKWebExtensionMessagePortError", int
        ),
        "WKMediaCaptureState": NewType("WKMediaCaptureState", int),
        "WebNavigationType": NewType("WebNavigationType", int),
        "WebDragSourceAction": NewType("WebDragSourceAction", int),
        "WKWebExtensionMatchPatternError": NewType(
            "WKWebExtensionMatchPatternError", int
        ),
    }
)
misc.update(
    {
        "WKWebExtensionContextNotificationUserInfoKey": NewType(
            "WKWebExtensionContextNotificationUserInfoKey", str
        ),
        "WKWebExtensionPermission": NewType("WKWebExtensionPermission", str),
        "WKWebExtensionDataType": NewType("WKWebExtensionDataType", str),
    }
)
misc.update({})
aliases = {
    "WK_NULLABLE_RESULT": "_Nullable_result",
    "WebNSInteger": "NSInteger",
    "WKAudiovisualMediaTypeAll": "NSUIntegerMax",
    "WK_HEADER_AUDIT_END": "NS_HEADER_AUDIT_END",
    "WK_SWIFT_UI_ACTOR": "NS_SWIFT_UI_ACTOR",
    "WK_HEADER_AUDIT_BEGIN": "NS_HEADER_AUDIT_BEGIN",
    "WebNSUInteger": "NSUInteger",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"DOMAttr", b"specified", {"retval": {"type": "Z"}})
    r(b"DOMCSSStyleDeclaration", b"isPropertyImplicit:", {"retval": {"type": "Z"}})
    r(b"DOMDocument", b"createNodeIterator::::", {"arguments": {5: {"type": "Z"}}})
    r(
        b"DOMDocument",
        b"createNodeIterator:whatToShow:filter:expandEntityReferences:",
        {"arguments": {5: {"type": "Z"}}},
    )
    r(b"DOMDocument", b"createTreeWalker::::", {"arguments": {5: {"type": "Z"}}})
    r(
        b"DOMDocument",
        b"createTreeWalker:whatToShow:filter:expandEntityReferences:",
        {"arguments": {5: {"type": "Z"}}},
    )
    r(b"DOMDocument", b"execCommand:", {"retval": {"type": "Z"}})
    r(
        b"DOMDocument",
        b"execCommand:userInterface:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"DOMDocument",
        b"execCommand:userInterface:value:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"DOMDocument",
        b"getMatchedCSSRules:pseudoElement:authorOnly:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(b"DOMDocument", b"hasFocus", {"retval": {"type": b"Z"}})
    r(b"DOMDocument", b"importNode::", {"arguments": {3: {"type": "Z"}}})
    r(b"DOMDocument", b"importNode:deep:", {"arguments": {3: {"type": "Z"}}})
    r(b"DOMDocument", b"queryCommandEnabled:", {"retval": {"type": "Z"}})
    r(b"DOMDocument", b"queryCommandIndeterm:", {"retval": {"type": "Z"}})
    r(b"DOMDocument", b"queryCommandState:", {"retval": {"type": "Z"}})
    r(b"DOMDocument", b"queryCommandSupported:", {"retval": {"type": "Z"}})
    r(b"DOMDocument", b"setXmlStandalone:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMDocument", b"xmlStandalone", {"retval": {"type": "Z"}})
    r(b"DOMElement", b"contains:", {"retval": {"type": "Z"}})
    r(b"DOMElement", b"hasAttribute:", {"retval": {"type": "Z"}})
    r(b"DOMElement", b"hasAttributeNS::", {"retval": {"type": "Z"}})
    r(b"DOMElement", b"hasAttributeNS:localName:", {"retval": {"type": "Z"}})
    r(b"DOMElement", b"scrollIntoView:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMElement", b"scrollIntoViewIfNeeded:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMEvent", b"bubbles", {"retval": {"type": "Z"}})
    r(b"DOMEvent", b"cancelBubble", {"retval": {"type": "Z"}})
    r(b"DOMEvent", b"cancelable", {"retval": {"type": "Z"}})
    r(b"DOMEvent", b"initEvent:::", {"arguments": {3: {"type": "Z"}, 4: {"type": "Z"}}})
    r(
        b"DOMEvent",
        b"initEvent:canBubbleArg:cancelableArg:",
        {"arguments": {3: {"type": "Z"}, 4: {"type": "Z"}}},
    )
    r(b"DOMEvent", b"returnValue", {"retval": {"type": "Z"}})
    r(b"DOMEvent", b"setCancelBubble:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMEvent", b"setReturnValue:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLAreaElement", b"noHref", {"retval": {"type": "Z"}})
    r(b"DOMHTMLAreaElement", b"setNoHref:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLButtonElement", b"autofocus", {"retval": {"type": "Z"}})
    r(b"DOMHTMLButtonElement", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMHTMLButtonElement", b"setAutofocus:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLButtonElement", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLButtonElement", b"setWillValidate:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLButtonElement", b"willValidate", {"retval": {"type": "Z"}})
    r(b"DOMHTMLDListElement", b"compact", {"retval": {"type": "Z"}})
    r(b"DOMHTMLDListElement", b"setCompact:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLDirectoryElement", b"compact", {"retval": {"type": "Z"}})
    r(b"DOMHTMLDirectoryElement", b"setCompact:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLDocument", b"hasFocus", {"retval": {"type": b"Z"}})
    r(b"DOMHTMLElement", b"isContentEditable", {"retval": {"type": "Z"}})
    r(b"DOMHTMLFrameElement", b"noResize", {"retval": {"type": "Z"}})
    r(b"DOMHTMLFrameElement", b"setNoResize:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLHRElement", b"noShade", {"retval": {"type": "Z"}})
    r(b"DOMHTMLHRElement", b"setNoShade:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLImageElement", b"complete", {"retval": {"type": "Z"}})
    r(b"DOMHTMLImageElement", b"isMap", {"retval": {"type": "Z"}})
    r(b"DOMHTMLImageElement", b"setComplete:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLImageElement", b"setIsMap:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLInputElement", b"autofocus", {"retval": {"type": "Z"}})
    r(b"DOMHTMLInputElement", b"checked", {"retval": {"type": "Z"}})
    r(b"DOMHTMLInputElement", b"defaultChecked", {"retval": {"type": "Z"}})
    r(b"DOMHTMLInputElement", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMHTMLInputElement", b"indeterminate", {"retval": {"type": "Z"}})
    r(b"DOMHTMLInputElement", b"multiple", {"retval": {"type": "Z"}})
    r(b"DOMHTMLInputElement", b"readOnly", {"retval": {"type": "Z"}})
    r(b"DOMHTMLInputElement", b"setAutofocus:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLInputElement", b"setChecked:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLInputElement", b"setDefaultChecked:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLInputElement", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLInputElement", b"setIndeterminate:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLInputElement", b"setMultiple:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLInputElement", b"setReadOnly:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLInputElement", b"willValidate", {"retval": {"type": "Z"}})
    r(b"DOMHTMLLinkElement", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMHTMLLinkElement", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLMenuElement", b"compact", {"retval": {"type": "Z"}})
    r(b"DOMHTMLMenuElement", b"setCompact:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLOListElement", b"compact", {"retval": {"type": "Z"}})
    r(b"DOMHTMLOListElement", b"setCompact:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLObjectElement", b"declare", {"retval": {"type": "Z"}})
    r(b"DOMHTMLObjectElement", b"setDeclare:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLOptGroupElement", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMHTMLOptGroupElement", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLOptionElement", b"defaultSelected", {"retval": {"type": "Z"}})
    r(b"DOMHTMLOptionElement", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMHTMLOptionElement", b"selected", {"retval": {"type": "Z"}})
    r(
        b"DOMHTMLOptionElement",
        b"setDefaultSelected:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"DOMHTMLOptionElement", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLOptionElement", b"setSelected:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLPreElement", b"setWrap:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLPreElement", b"wrap", {"retval": {"type": "Z"}})
    r(b"DOMHTMLScriptElement", b"defer", {"retval": {"type": "Z"}})
    r(b"DOMHTMLScriptElement", b"setDefer:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLSelectElement", b"autofocus", {"retval": {"type": "Z"}})
    r(b"DOMHTMLSelectElement", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMHTMLSelectElement", b"multiple", {"retval": {"type": "Z"}})
    r(b"DOMHTMLSelectElement", b"setAutofocus:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLSelectElement", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLSelectElement", b"setMultiple:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLSelectElement", b"willValidate", {"retval": {"type": "Z"}})
    r(b"DOMHTMLStyleElement", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMHTMLStyleElement", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLTableCellElement", b"noWrap", {"retval": {"type": "Z"}})
    r(b"DOMHTMLTableCellElement", b"setNoWrap:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLTextAreaElement", b"autofocus", {"retval": {"type": b"Z"}})
    r(b"DOMHTMLTextAreaElement", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMHTMLTextAreaElement", b"readOnly", {"retval": {"type": "Z"}})
    r(b"DOMHTMLTextAreaElement", b"setAutofocus:", {"arguments": {2: {"type": b"Z"}}})
    r(b"DOMHTMLTextAreaElement", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLTextAreaElement", b"setReadOnly:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMHTMLTextAreaElement", b"willValidate", {"retval": {"type": "Z"}})
    r(b"DOMHTMLUListElement", b"compact", {"retval": {"type": "Z"}})
    r(b"DOMHTMLUListElement", b"setCompact:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMImplementation", b"hasFeature::", {"retval": {"type": "Z"}})
    r(b"DOMImplementation", b"hasFeature:version:", {"retval": {"type": "Z"}})
    r(b"DOMKeyboardEvent", b"altGraphKey", {"retval": {"type": "Z"}})
    r(b"DOMKeyboardEvent", b"altKey", {"retval": {"type": "Z"}})
    r(b"DOMKeyboardEvent", b"ctrlKey", {"retval": {"type": "Z"}})
    r(b"DOMKeyboardEvent", b"getModifierState:", {"retval": {"type": "Z"}})
    r(
        b"DOMKeyboardEvent",
        b"initKeyboardEvent:canBubble:cancelable:view:keyIdentifier:keyLocation:ctrlKey:altKey:shiftKey:metaKey:",
        {
            "arguments": {
                3: {"type": "Z"},
                4: {"type": "Z"},
                8: {"type": "Z"},
                9: {"type": "Z"},
                10: {"type": "Z"},
                11: {"type": "Z"},
            }
        },
    )
    r(
        b"DOMKeyboardEvent",
        b"initKeyboardEvent:canBubble:cancelable:view:keyIdentifier:keyLocation:ctrlKey:altKey:shiftKey:metaKey:altGraphKey:",
        {
            "arguments": {
                3: {"type": "Z"},
                4: {"type": "Z"},
                8: {"type": "Z"},
                9: {"type": "Z"},
                10: {"type": "Z"},
                11: {"type": "Z"},
                12: {"type": "Z"},
            }
        },
    )
    r(
        b"DOMKeyboardEvent",
        b"initKeyboardEvent:canBubble:cancelable:view:keyIdentifier:location:ctrlKey:altKey:shiftKey:metaKey:",
        {
            "arguments": {
                3: {"type": b"Z"},
                4: {"type": b"Z"},
                8: {"type": b"Z"},
                9: {"type": b"Z"},
                10: {"type": b"Z"},
                11: {"type": b"Z"},
            }
        },
    )
    r(
        b"DOMKeyboardEvent",
        b"initKeyboardEvent:canBubble:cancelable:view:keyIdentifier:location:ctrlKey:altKey:shiftKey:metaKey:altGraphKey:",
        {
            "arguments": {
                3: {"type": b"Z"},
                4: {"type": b"Z"},
                8: {"type": b"Z"},
                9: {"type": b"Z"},
                10: {"type": b"Z"},
                11: {"type": b"Z"},
                12: {"type": b"Z"},
            }
        },
    )
    r(b"DOMKeyboardEvent", b"metaKey", {"retval": {"type": "Z"}})
    r(b"DOMKeyboardEvent", b"shiftKey", {"retval": {"type": "Z"}})
    r(b"DOMMouseEvent", b"altKey", {"retval": {"type": "Z"}})
    r(b"DOMMouseEvent", b"ctrlKey", {"retval": {"type": "Z"}})
    r(
        b"DOMMouseEvent",
        b"initMouseEvent:::::::::::::::",
        {
            "arguments": {
                3: {"type": b"Z"},
                4: {"type": b"Z"},
                11: {"type": b"Z"},
                12: {"type": b"Z"},
                13: {"type": b"Z"},
                14: {"type": b"Z"},
            }
        },
    )
    r(
        b"DOMMouseEvent",
        b"initMouseEvent:canBubble:cancelable:view:detail:screenX:screenY:clientX:clientY:ctrlKey:altKey:shiftKey:metaKey:button:relatedTarget:",
        {
            "arguments": {
                3: {"type": "Z"},
                4: {"type": "Z"},
                11: {"type": "Z"},
                12: {"type": "Z"},
                13: {"type": "Z"},
                14: {"type": "Z"},
            }
        },
    )
    r(b"DOMMouseEvent", b"metaKey", {"retval": {"type": "Z"}})
    r(b"DOMMouseEvent", b"shiftKey", {"retval": {"type": "Z"}})
    r(
        b"DOMMutationEvent",
        b"initMutationEvent::::::::",
        {"arguments": {3: {"type": "Z"}, 4: {"type": "Z"}}},
    )
    r(
        b"DOMMutationEvent",
        b"initMutationEvent:canBubble:cancelable:relatedNode:prevValue:newValue:attrName:attrChange:",
        {"arguments": {3: {"type": "Z"}, 4: {"type": "Z"}}},
    )
    r(b"DOMNode", b"cloneNode:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMNode", b"contains:", {"retval": {"type": "Z"}})
    r(b"DOMNode", b"hasAttributes", {"retval": {"type": "Z"}})
    r(b"DOMNode", b"hasChildNodes", {"retval": {"type": "Z"}})
    r(b"DOMNode", b"isContentEditable", {"retval": {"type": "Z"}})
    r(b"DOMNode", b"isDefaultNamespace:", {"retval": {"type": b"Z"}})
    r(b"DOMNode", b"isEqualNode:", {"retval": {"type": "Z"}})
    r(b"DOMNode", b"isSameNode:", {"retval": {"type": "Z"}})
    r(b"DOMNode", b"isSupported::", {"retval": {"type": "Z"}})
    r(b"DOMNode", b"isSupported:version:", {"retval": {"type": "Z"}})
    r(b"DOMNode", b"setIsContentEditable:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMNodeIterator", b"expandEntityReferences", {"retval": {"type": "Z"}})
    r(b"DOMNodeIterator", b"pointerBeforeReferenceNode", {"retval": {"type": "Z"}})
    r(b"DOMOverflowEvent", b"horizontalOverflow", {"retval": {"type": "Z"}})
    r(
        b"DOMOverflowEvent",
        b"initOverflowEvent:horizontalOverflow:verticalOverflow:",
        {"arguments": {3: {"type": "Z"}, 4: {"type": "Z"}}},
    )
    r(b"DOMOverflowEvent", b"verticalOverflow", {"retval": {"type": "Z"}})
    r(b"DOMProgressEvent", b"lengthComputable", {"retval": {"type": b"Z"}})
    r(b"DOMRange", b"collapse:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMRange", b"collapsed", {"retval": {"type": "Z"}})
    r(b"DOMRange", b"intersectsNode:", {"retval": {"type": "Z"}})
    r(b"DOMRange", b"isPointInRange:offset:", {"retval": {"type": "Z"}})
    r(b"DOMStyleSheet", b"disabled", {"retval": {"type": "Z"}})
    r(b"DOMStyleSheet", b"setDisabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"DOMTreeWalker", b"expandEntityReferences", {"retval": {"type": "Z"}})
    r(
        b"DOMUIEvent",
        b"initUIEvent:::::",
        {"arguments": {3: {"type": "Z"}, 4: {"type": "Z"}}},
    )
    r(
        b"DOMUIEvent",
        b"initUIEvent:canBubble:cancelable:view:detail:",
        {"arguments": {3: {"type": "Z"}, 4: {"type": "Z"}}},
    )
    r(b"DOMWheelEvent", b"altKey", {"retval": {"type": "Z"}})
    r(b"DOMWheelEvent", b"ctrlKey", {"retval": {"type": "Z"}})
    r(
        b"DOMWheelEvent",
        b"initWheelEvent:wheelDeltaY:view:screenX:screenY:clientX:clientY:ctrlKey:altKey:shiftKey:metaKey:",
        {
            "arguments": {
                9: {"type": b"Z"},
                10: {"type": b"Z"},
                11: {"type": b"Z"},
                12: {"type": b"Z"},
            }
        },
    )
    r(b"DOMWheelEvent", b"isHorizontal", {"retval": {"type": "Z"}})
    r(b"DOMWheelEvent", b"metaKey", {"retval": {"type": "Z"}})
    r(b"DOMWheelEvent", b"shiftKey", {"retval": {"type": "Z"}})
    r(b"DOMXPathResult", b"booleanValue", {"retval": {"type": "Z"}})
    r(b"DOMXPathResult", b"invalidIteratorState", {"retval": {"type": "Z"}})
    r(
        b"NSAttributedString",
        b"loadFromHTMLWithData:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSAttributedString",
        b"loadFromHTMLWithFileURL:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSAttributedString",
        b"loadFromHTMLWithRequest:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSAttributedString",
        b"loadFromHTMLWithString:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSObject",
        b"acceptNode:",
        {"required": True, "retval": {"type": "s"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"activateForWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"activeTabForWebExtensionContext:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"addEventListener:::",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"addEventListener:listener:useCapture:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(b"NSObject", b"attributedString", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"canProvideDocumentSource",
        {"required": True, "retval": {"type": "Z"}},
    )
    r(b"NSObject", b"cancel", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"chooseFilename:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"chooseFilenames:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"closeForWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"cookiesDidChangeInCookieStore:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"dataSourceUpdated:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"deselectAll", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"detectWebpageLocaleForWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"didFailWithError:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"didFinish", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"didReceiveData:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"didReceiveResponse:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"dispatchEvent:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"documentSource", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"download", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"download:decideDestinationUsingResponse:suggestedFilename:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"download:decidePlaceholderPolicy:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"download:didFailWithError:resumeData:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:didReceiveAuthenticationChallenge:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"download:didReceiveFinalURL:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:didReceivePlaceholderURL:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"download:willPerformHTTPRedirection:newRequest:decisionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"downloadDidFinish:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"downloadWindowForAuthenticationSheet:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"duplicateUsingConfiguration:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"finalizeForWebScript", {"retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"finishedLoadingWithDataSource:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"focusForWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"frameForWebExtensionContext:",
        {
            "required": False,
            "retval": {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"goBackForWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"goForwardForWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"handleEvent:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"ignore", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"indexInWindowForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Q"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"invokeDefaultMethodWithArguments:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"invokeUndefinedMethodFromWebScript:withArguments:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isKeyExcludedFromWebScript:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"c_array_delimited_by_null": True, "type": "n^t"}},
        },
    )
    r(
        b"NSObject",
        b"isLoadingCompleteForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isMutedForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isPinnedForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isPlayingAudioForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isPrivateForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isReaderModeActiveForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isReaderModeAvailableForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isSelectedForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isSelectorExcludedFromWebScript:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": ":"}}},
    )
    r(b"NSObject", b"layout", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"loadURL:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"lookupNamespaceURI:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"objectForWebScript", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"parentTabForWebExtensionContext:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"pendingURLForWebExtensionContext:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"plugInViewWithArguments:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"receivedData:withDataSource:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"receivedError:withDataSource:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"reloadFromOrigin:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Z"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"removeEventListener:::",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"removeEventListener:listener:useCapture:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(b"NSObject", b"request", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"screenFrameForWebExtensionContext:",
        {
            "required": False,
            "retval": {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"searchFor:direction:caseSensitive:wrap:",
        {
            "required": True,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "Z"},
                4: {"type": "Z"},
                5: {"type": "Z"},
            },
        },
    )
    r(b"NSObject", b"selectAll", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"selectedAttributedString",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"selectedString", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"setDataSource:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setFrame:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setMuted:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Z"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setNeedsLayout:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSObject",
        b"setParentTab:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setPinned:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Z"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setReaderModeActive:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Z"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setSelected:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Z"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setWindowState:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"q"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"setZoomFactor:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"d"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"shouldBypassPermissionsForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"shouldGrantPermissionsOnUserGestureForWebExtensionContext:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"sizeForWebExtensionContext:",
        {
            "required": False,
            "retval": {"type": b"{CGSize=dd}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"string", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"supportsTextEncoding", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"tabsForWebExtensionContext:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"takeSnapshotUsingConfiguration:forWebExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"title", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"titleForWebExtensionContext:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"undoManagerForWebView:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"urlForWebExtensionContext:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"use", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"userContentController:didReceiveScriptMessage:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"userContentController:didReceiveScriptMessage:replyHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"viewDidMoveToHostWindow",
        {"required": True, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"viewWillMoveToHostWindow:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webExtensionController:connectUsingMessagePort:forExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:didUpdateAction:forExtensionContext:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:focusedWindowForExtensionContext:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:openNewTabUsingConfiguration:forExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:openNewWindowUsingConfiguration:forExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:openOptionsPageForExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:openWindowsForExtensionContext:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:presentPopupForAction:forExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:promptForPermissionMatchPatterns:inTab:forExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:promptForPermissionToAccessURLs:inTab:forExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:promptForPermissions:inTab:forExtensionContext:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webExtensionController:sendMessage:toApplicationWithIdentifier:forExtensionContext:replyHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(b"NSObject", b"webFrame", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"webPlugInCallJava:isStatic:returnType:method:arguments:callingURL:exceptionDescription:",
        {
            "retval": {"type": b"(jvalue=CcSsiqfd^{_jobject=})"},
            "arguments": {
                2: {"type": "^{_jobject=}"},
                3: {"type": "Z"},
                4: {"type": b"i"},
                5: {"type": "^{_jmethodID=}"},
                6: {"type": "^(jvalue=CcSsiqfd^{_jobject})"},
                7: {"type": b"@"},
                8: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"webPlugInContainerLoadRequest:inFrame:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(b"NSObject", b"webPlugInContainerSelectionColor", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"webPlugInContainerShowStatus:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"webPlugInDestroy", {"retval": {"type": b"v"}})
    r(b"NSObject", b"webPlugInGetApplet", {"retval": {"type": "^{_jobject=}"}})
    r(b"NSObject", b"webPlugInInitialize", {"retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"webPlugInMainResourceDidFailWithError:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"webPlugInMainResourceDidFinishLoading", {"retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"webPlugInMainResourceDidReceiveData:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webPlugInMainResourceDidReceiveResponse:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webPlugInSetIsSelected:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": "Z"}}},
    )
    r(b"NSObject", b"webPlugInStart", {"retval": {"type": b"v"}})
    r(b"NSObject", b"webPlugInStop", {"retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"webScriptNameForKey:",
        {
            "retval": {"type": b"@"},
            "arguments": {2: {"c_array_delimited_by_null": True, "type": "n^t"}},
        },
    )
    r(
        b"NSObject",
        b"webScriptNameForSelector:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": ":"}}},
    )
    r(
        b"NSObject",
        b"webView:authenticationChallenge:shouldAllowDeprecatedTLS:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:contextMenuItemsForElement:defaultMenuItems:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:createWebViewModalDialogWithRequest:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:createWebViewWithConfiguration:forNavigationAction:windowFeatures:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:createWebViewWithRequest:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:decidePolicyForMIMEType:request:frame:decisionListener:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:decidePolicyForNavigationAction:decisionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:decidePolicyForNavigationAction:preferences:decisionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:decidePolicyForNavigationAction:request:frame:decisionListener:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:decidePolicyForNavigationResponse:decisionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:decidePolicyForNewWindowAction:request:newFrameName:decisionListener:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:didCancelClientRedirectForFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didChangeLocationWithinPageForFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didClearWindowObject:forFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didCommitLoadForFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didCommitNavigation:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didCreateJavaScriptContext:forFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didFailLoadWithError:forFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didFailNavigation:withError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didFailProvisionalLoadWithError:forFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didFailProvisionalNavigation:withError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didFinishLoadForFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didFinishNavigation:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didReceiveAuthenticationChallenge:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:didReceiveIcon:forFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didReceiveServerRedirectForProvisionalLoadForFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didReceiveServerRedirectForProvisionalNavigation:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didReceiveTitle:forFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didStartProvisionalLoadForFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:didStartProvisionalNavigation:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:doCommandBySelector:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": ":"}},
        },
    )
    r(
        b"NSObject",
        b"webView:dragDestinationActionMaskForDraggingInfo:",
        {
            "required": False,
            "retval": {"type": b"Q"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:dragSourceActionMaskForPoint:",
        {
            "required": False,
            "retval": {"type": b"Q"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"{CGPoint=dd}"}},
        },
    )
    r(
        b"NSObject",
        b"webView:drawFooterInRect:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:drawHeaderInRect:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:identifierForInitialRequest:fromDataSource:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:makeFirstResponder:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:mouseDidMoveOverElement:modifierFlags:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"webView:navigationAction:didBecomeDownload:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:navigationResponse:didBecomeDownload:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:plugInFailedWithError:dataSource:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:printFrameView:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:requestDeviceOrientationAndMotionPermissionForOrigin:initiatedByFrame:decisionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:requestDeviceOrientationAndMotionPermissionForOrigin:initiatedByFrame:type:decisionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"webView:requestMediaCapturePermissionForOrigin:initiatedByFrame:type:decisionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"q"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:resource:didCancelAuthenticationChallenge:fromDataSource:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:resource:didFailLoadingWithError:fromDataSource:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:resource:didFinishLoadingFromDataSource:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:resource:didReceiveAuthenticationChallenge:fromDataSource:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:resource:didReceiveContentLength:fromDataSource:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:resource:didReceiveResponse:fromDataSource:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:resource:willSendRequest:redirectResponse:fromDataSource:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:runBeforeUnloadConfirmPanelWithMessage:initiatedByFrame:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptAlertPanelWithMessage:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptAlertPanelWithMessage:initiatedByFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptAlertPanelWithMessage:initiatedByFrame:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptConfirmPanelWithMessage:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptConfirmPanelWithMessage:initiatedByFrame:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptConfirmPanelWithMessage:initiatedByFrame:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptTextInputPanelWithPrompt:defaultText:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptTextInputPanelWithPrompt:defaultText:initiatedByFrame:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:runJavaScriptTextInputPanelWithPrompt:defaultText:initiatedByFrame:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:runOpenPanelForFileButtonWithResultListener:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:runOpenPanelForFileButtonWithResultListener:allowMultipleFiles:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"webView:runOpenPanelWithParameters:initiatedByFrame:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:setContentRect:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:setFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:setResizable:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"webView:setStatusBarVisible:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"webView:setStatusText:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:setToolbarsVisible:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"webView:shouldApplyStyle:toElementsInDOMRange:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:shouldBeginEditingInDOMRange:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:shouldChangeSelectedDOMRange:toDOMRange:affinity:stillSelecting:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
                6: {"type": "Z"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:shouldChangeTypingStyle:toStyle:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:shouldDeleteDOMRange:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:shouldEndEditingInDOMRange:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:shouldGoToBackForwardListItem:willUseInstantBack:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"Z"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"webView:shouldInsertNode:replacingDOMRange:givenAction:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:shouldInsertText:replacingDOMRange:givenAction:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:shouldPerformAction:fromSender:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": ":"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:startURLSchemeTask:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:stopURLSchemeTask:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:unableToImplementPolicyWithError:frame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:validateUserInterfaceItem:defaultValidation:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"webView:willCloseFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:willPerformClientRedirectToURL:delay:fireDate:forFrame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"d"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:willPerformDragDestinationAction:forDraggingInfo:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webView:willPerformDragSourceAction:fromPoint:withPasteboard:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"{CGPoint=dd}"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"webView:windowScriptObjectAvailable:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webViewAreToolbarsVisible:",
        {"required": False, "retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewClose:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewContentRect:",
        {
            "required": False,
            "retval": {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webViewDidBeginEditing:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewDidChange:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewDidChangeSelection:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewDidChangeTypingStyle:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewDidClose:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewDidEndEditing:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewFirstResponder:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewFocus:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewFooterHeight:",
        {"required": False, "retval": {"type": b"f"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewForWebExtensionContext:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewFrame:",
        {
            "required": False,
            "retval": {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"webViewHeaderHeight:",
        {"required": False, "retval": {"type": b"f"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewIsResizable:",
        {"required": False, "retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewIsStatusBarVisible:",
        {"required": False, "retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewRunModal:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewShow:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewStatusText:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewUnfocus:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"webViewWebContentProcessDidTerminate:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"windowForWebExtensionContext:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"windowStateForWebExtensionContext:",
        {"required": False, "retval": {"type": b"q"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"windowTypeForWebExtensionContext:",
        {"required": False, "retval": {"type": b"q"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"zoomFactorForWebExtensionContext:",
        {"required": False, "retval": {"type": b"d"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"WKContentRuleListStore",
        b"compileContentRuleListForIdentifier:encodedContentRuleList:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKContentRuleListStore",
        b"getAvailableContentRuleListIdentifiers:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKContentRuleListStore",
        b"lookUpContentRuleListForIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKContentRuleListStore",
        b"removeContentRuleListForIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKDownload",
        b"cancel:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"WKDownload", b"isUserInitiated", {"retval": {"type": b"Z"}})
    r(b"WKFindConfiguration", b"backwards", {"retval": {"type": b"Z"}})
    r(b"WKFindConfiguration", b"caseSensitive", {"retval": {"type": b"Z"}})
    r(b"WKFindConfiguration", b"setBackwards:", {"arguments": {2: {"type": b"Z"}}})
    r(b"WKFindConfiguration", b"setCaseSensitive:", {"arguments": {2: {"type": b"Z"}}})
    r(b"WKFindConfiguration", b"setWraps:", {"arguments": {2: {"type": b"Z"}}})
    r(b"WKFindConfiguration", b"wraps", {"retval": {"type": b"Z"}})
    r(b"WKFindResult", b"matchFound", {"retval": {"type": b"Z"}})
    r(b"WKFrameInfo", b"isMainFrame", {"retval": {"type": b"Z"}})
    r(
        b"WKHTTPCookieStore",
        b"deleteCookie:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKHTTPCookieStore",
        b"getAllCookies:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKHTTPCookieStore",
        b"getCookiePolicy:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"WKHTTPCookieStore",
        b"setCookie:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKHTTPCookieStore",
        b"setCookiePolicy:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKNavigationAction",
        b"setShouldPerformDownload:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"WKNavigationAction", b"shouldPerformDownload", {"retval": {"type": "Z"}})
    r(b"WKNavigationResponse", b"canShowMIMEType", {"retval": {"type": b"Z"}})
    r(b"WKNavigationResponse", b"isForMainFrame", {"retval": {"type": b"Z"}})
    r(b"WKOpenPanelParameters", b"allowsDirectories", {"retval": {"type": "Z"}})
    r(b"WKOpenPanelParameters", b"allowsMultipleSelection", {"retval": {"type": "Z"}})
    r(b"WKPDFConfiguration", b"allowTransparentBackground", {"retval": {"type": b"Z"}})
    r(
        b"WKPDFConfiguration",
        b"setAllowTransparentBackground:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"WKPreferences", b"isElementFullscreenEnabled", {"retval": {"type": b"Z"}})
    r(
        b"WKPreferences",
        b"isFraudulentWebsiteWarningEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKPreferences", b"isSafeBrowsingEnabled", {"retval": {"type": b"Z"}})
    r(b"WKPreferences", b"isSiteSpecificQuirksModeEnabled", {"retval": {"type": b"Z"}})
    r(b"WKPreferences", b"isTextInteractionEnabled", {"retval": {"type": b"Z"}})
    r(b"WKPreferences", b"javaEnabled", {"retval": {"type": b"Z"}})
    r(
        b"WKPreferences",
        b"javaScriptCanOpenWindowsAutomatically",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKPreferences", b"javaScriptEnabled", {"retval": {"type": b"Z"}})
    r(b"WKPreferences", b"plugInsEnabled", {"retval": {"type": b"Z"}})
    r(
        b"WKPreferences",
        b"setElementFullscreenEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKPreferences",
        b"setFraudulentWebsiteWarningEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"WKPreferences", b"setJavaEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"WKPreferences",
        b"setJavaScriptCanOpenWindowsAutomatically:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"WKPreferences", b"setJavaScriptEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"WKPreferences", b"setPlugInsEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"WKPreferences", b"setSafeBrowsingEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"WKPreferences",
        b"setShouldPrintBackgrounds:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKPreferences",
        b"setSiteSpecificQuirksModeEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"WKPreferences", b"setTabFocusesLinks:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"WKPreferences",
        b"setTextInteractionEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"WKPreferences", b"shouldPrintBackgrounds", {"retval": {"type": b"Z"}})
    r(b"WKPreferences", b"tabFocusesLinks", {"retval": {"type": "Z"}})
    r(b"WKPreferences", b"textInteractionEnabled", {"retval": {"type": "Z"}})
    r(b"WKSnapshotConfiguration", b"afterScreenUpdates", {"retval": {"type": "Z"}})
    r(
        b"WKSnapshotConfiguration",
        b"setAfterScreenUpdates:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"WKUserScript",
        b"initWithSource:injectionTime:forMainFrameOnly:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"WKUserScript",
        b"initWithSource:injectionTime:forMainFrameOnly:inContentWorld:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(b"WKUserScript", b"isForMainFrameOnly", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtension",
        b"extensionWithAppExtensionBundle:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebExtension",
        b"extensionWithResourceBaseURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"WKWebExtension", b"hasBackgroundContent", {"retval": {"type": b"Z"}})
    r(b"WKWebExtension", b"hasCommands", {"retval": {"type": b"Z"}})
    r(b"WKWebExtension", b"hasContentModificationRules", {"retval": {"type": b"Z"}})
    r(b"WKWebExtension", b"hasInjectedContent", {"retval": {"type": b"Z"}})
    r(b"WKWebExtension", b"hasOptionsPage", {"retval": {"type": b"Z"}})
    r(b"WKWebExtension", b"hasOverrideNewTabPage", {"retval": {"type": b"Z"}})
    r(b"WKWebExtension", b"hasPersistentBackgroundContent", {"retval": {"type": b"Z"}})
    r(b"WKWebExtension", b"supportsManifestVersion:", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionAction", b"hasUnreadBadgeText", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionAction", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionAction", b"presentsPopup", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionAction",
        b"setHasUnreadBadgeText:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKWebExtensionContext",
        b"didCloseTab:windowIsClosing:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"WKWebExtensionContext", b"hasAccessToAllHosts", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionContext", b"hasAccessToAllURLs", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionContext", b"hasAccessToPrivateData", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionContext", b"hasAccessToURL:", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionContext", b"hasAccessToURL:inTab:", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionContext",
        b"hasActiveUserGestureInTab:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"WKWebExtensionContext",
        b"hasContentModificationRules",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKWebExtensionContext", b"hasInjectedContent", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionContext",
        b"hasInjectedContentForURL:",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKWebExtensionContext", b"hasPermission:", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionContext", b"hasPermission:inTab:", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionContext",
        b"hasRequestedOptionalAccessToAllHosts",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKWebExtensionContext", b"isInspectable", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionContext", b"isLoaded", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionContext",
        b"loadBackgroundContentWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"WKWebExtensionContext", b"performCommandForEvent:", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionContext",
        b"setHasAccessToPrivateData:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKWebExtensionContext",
        b"setHasRequestedOptionalAccessToAllHosts:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"WKWebExtensionContext", b"setInspectable:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"WKWebExtensionController",
        b"didCloseTab:windowIsClosing:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"WKWebExtensionController",
        b"fetchDataRecordOfTypes:forExtensionContext:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebExtensionController",
        b"fetchDataRecordsOfTypes:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebExtensionController",
        b"loadExtensionContext:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"WKWebExtensionController",
        b"removeDataOfTypes:fromDataRecords:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebExtensionController",
        b"unloadExtensionContext:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"WKWebExtensionControllerConfiguration",
        b"isPersistent",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"WKWebExtensionMatchPattern",
        b"initWithScheme:host:path:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"WKWebExtensionMatchPattern",
        b"initWithString:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"WKWebExtensionMatchPattern", b"matchesAllHosts", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionMatchPattern", b"matchesAllURLs", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionMatchPattern", b"matchesPattern:", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionMatchPattern",
        b"matchesPattern:options:",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKWebExtensionMatchPattern", b"matchesURL:", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionMatchPattern", b"matchesURL:options:", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionMessagePort",
        b"disconnectHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(b"WKWebExtensionMessagePort", b"isDisconnected", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionMessagePort",
        b"messageHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"WKWebExtensionMessagePort",
        b"sendMessage:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebExtensionMessagePort",
        b"setDisconnectHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebExtensionMessagePort",
        b"setMessageHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebExtensionTabConfiguration",
        b"shouldAddToSelection",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKWebExtensionTabConfiguration", b"shouldBeActive", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionTabConfiguration", b"shouldBeMuted", {"retval": {"type": b"Z"}})
    r(b"WKWebExtensionTabConfiguration", b"shouldBePinned", {"retval": {"type": b"Z"}})
    r(
        b"WKWebExtensionTabConfiguration",
        b"shouldReaderModeBeActive",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"WKWebExtensionWindowConfiguration",
        b"shouldBeFocused",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"WKWebExtensionWindowConfiguration",
        b"shouldBePrivate",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKWebView", b"allowsBackForwardNavigationGestures", {"retval": {"type": b"Z"}})
    r(b"WKWebView", b"allowsLinkPreview", {"retval": {"type": "Z"}})
    r(b"WKWebView", b"allowsMagnification", {"retval": {"type": b"Z"}})
    r(
        b"WKWebView",
        b"callAsyncJavaScript:arguments:inContentWorld:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"callAsyncJavaScript:arguments:inFrame:inContentWorld:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"WKWebView", b"canGoBack", {"retval": {"type": b"Z"}})
    r(b"WKWebView", b"canGoForward", {"retval": {"type": b"Z"}})
    r(
        b"WKWebView",
        b"closeAllMediaPresentationsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"createPDFWithConfiguration:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"createWebArchiveDataWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"evaluateJavaScript:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"evaluateJavaScript:inContentWorld:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"evaluateJavaScript:inFrame:inContentWorld:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"findString:withConfiguration:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"WKWebView", b"handlesURLScheme:", {"retval": {"type": b"Z"}})
    r(b"WKWebView", b"hasOnlySecureContent", {"retval": {"type": b"Z"}})
    r(b"WKWebView", b"isInspectable", {"retval": {"type": b"Z"}})
    r(b"WKWebView", b"isLoading", {"retval": {"type": b"Z"}})
    r(b"WKWebView", b"isWritingToolsActive", {"retval": {"type": b"Z"}})
    r(
        b"WKWebView",
        b"pauseAllMediaPlayback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"pauseAllMediaPlaybackWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"requestMediaPlaybackState:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"requestMediaPlaybackStateWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"resumeAllMediaPlayback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"resumeDownloadFromResumeData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"setAllMediaPlaybackSuspended:completionHandler:",
        {
            "arguments": {
                2: {"type": b"Z"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
            }
        },
    )
    r(
        b"WKWebView",
        b"setAllowsBackForwardNavigationGestures:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"WKWebView", b"setAllowsLinkPreview:", {"arguments": {2: {"type": "Z"}}})
    r(b"WKWebView", b"setAllowsMagnification:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"WKWebView",
        b"setCameraCaptureState:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"WKWebView", b"setInspectable:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"WKWebView",
        b"setMicrophoneCaptureState:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"startDownloadUsingRequest:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"suspendAllMediaPlayback:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebView",
        b"takeSnapshotWithConfiguration:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"WKWebViewConfiguration",
        b"allowsAirPlayForMediaPlayback",
        {"retval": {"type": "Z"}},
    )
    r(b"WKWebViewConfiguration", b"allowsInlinePredictions", {"retval": {"type": "Z"}})
    r(
        b"WKWebViewConfiguration",
        b"allowsPictureInPictureMediaPlayback",
        {"retval": {"type": "Z"}},
    )
    r(
        b"WKWebViewConfiguration",
        b"limitsNavigationsToAppBoundDomains",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"WKWebViewConfiguration",
        b"setAllowsAirPlayForMediaPlayback:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"WKWebViewConfiguration",
        b"setAllowsInlinePredictions:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"WKWebViewConfiguration",
        b"setAllowsPictureInPictureMediaPlayback:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"WKWebViewConfiguration",
        b"setLimitsNavigationsToAppBoundDomains:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKWebViewConfiguration",
        b"setSupportsAdaptiveImageGlyph:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKWebViewConfiguration",
        b"setSuppressesIncrementalRendering:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKWebViewConfiguration",
        b"setUpgradeKnownHostsToHTTPS:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKWebViewConfiguration",
        b"supportsAdaptiveImageGlyph",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"WKWebViewConfiguration",
        b"suppressesIncrementalRendering",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"WKWebViewConfiguration",
        b"upgradeKnownHostsToHTTPS",
        {"retval": {"type": b"Z"}},
    )
    r(b"WKWebpagePreferences", b"allowsContentJavaScript", {"retval": {"type": b"Z"}})
    r(b"WKWebpagePreferences", b"isLockdownModeEnabled", {"retval": {"type": b"Z"}})
    r(
        b"WKWebpagePreferences",
        b"setAllowsContentJavaScript:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKWebpagePreferences",
        b"setLockdownModeEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"WKWebsiteDataStore",
        b"fetchAllDataStoreIdentifiers:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebsiteDataStore",
        b"fetchDataRecordsOfTypes:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"WKWebsiteDataStore", b"isPersistent", {"retval": {"type": "Z"}})
    r(
        b"WKWebsiteDataStore",
        b"removeDataOfTypes:forDataRecords:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebsiteDataStore",
        b"removeDataOfTypes:modifiedSince:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"WKWebsiteDataStore",
        b"removeDataStoreForIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"WebBackForwardList", b"containsItem:", {"retval": {"type": "Z"}})
    r(b"WebDataSource", b"isLoading", {"retval": {"type": "Z"}})
    r(b"WebFrame", b"globalContext", {"retval": {"type": "^{OpaqueJSContext=}"}})
    r(b"WebFrameView", b"allowsScrolling", {"retval": {"type": "Z"}})
    r(b"WebFrameView", b"canPrintHeadersAndFooters", {"retval": {"type": "Z"}})
    r(b"WebFrameView", b"documentViewShouldHandlePrint", {"retval": {"type": "Z"}})
    r(b"WebFrameView", b"setAllowsScrolling:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"WebHistory",
        b"loadFromURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"WebHistory",
        b"saveToURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"WebPreferences", b"allowsAirPlayForMediaPlayback", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"allowsAnimatedImageLooping", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"allowsAnimatedImages", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"arePlugInsEnabled", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"autosaves", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"isJavaEnabled", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"isJavaScriptEnabled", {"retval": {"type": "Z"}})
    r(
        b"WebPreferences",
        b"javaScriptCanOpenWindowsAutomatically",
        {"retval": {"type": "Z"}},
    )
    r(b"WebPreferences", b"loadsImagesAutomatically", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"privateBrowsingEnabled", {"retval": {"type": "Z"}})
    r(
        b"WebPreferences",
        b"setAllowsAirPlayForMediaPlayback:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"WebPreferences",
        b"setAllowsAnimatedImageLooping:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"WebPreferences", b"setAllowsAnimatedImages:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebPreferences", b"setAutosaves:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebPreferences", b"setJavaEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"WebPreferences",
        b"setJavaScriptCanOpenWindowsAutomatically:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"WebPreferences", b"setJavaScriptEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"WebPreferences",
        b"setLoadsImagesAutomatically:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"WebPreferences", b"setPlugInsEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"WebPreferences",
        b"setPrivateBrowsingEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"WebPreferences",
        b"setShouldPrintBackgrounds:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"WebPreferences",
        b"setSuppressesIncrementalRendering:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"WebPreferences", b"setTabsToLinks:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"WebPreferences",
        b"setUserStyleSheetEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"WebPreferences", b"setUsesPageCache:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebPreferences", b"shouldPrintBackgrounds", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"suppressesIncrementalRendering", {"retval": {"type": b"Z"}})
    r(b"WebPreferences", b"tabsToLinks", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"userStyleSheetEnabled", {"retval": {"type": "Z"}})
    r(b"WebPreferences", b"usesPageCache", {"retval": {"type": "Z"}})
    r(b"WebScriptObject", b"JSObject", {"retval": {"type": "^{OpaqueJSValue=}"}})
    r(b"WebScriptObject", b"throwException:", {"retval": {"type": "Z"}})
    r(b"WebView", b"canGoBack", {"retval": {"type": "Z"}})
    r(b"WebView", b"canGoForward", {"retval": {"type": "Z"}})
    r(b"WebView", b"canMakeTextLarger", {"retval": {"type": "Z"}})
    r(b"WebView", b"canMakeTextSmaller", {"retval": {"type": "Z"}})
    r(b"WebView", b"canMakeTextStandardSize", {"retval": {"type": "Z"}})
    r(b"WebView", b"canShowMIMEType:", {"retval": {"type": "Z"}})
    r(b"WebView", b"canShowMIMETypeAsHTML:", {"retval": {"type": "Z"}})
    r(b"WebView", b"drawsBackground", {"retval": {"type": "Z"}})
    r(b"WebView", b"goBack", {"retval": {"type": "Z"}})
    r(b"WebView", b"goForward", {"retval": {"type": "Z"}})
    r(b"WebView", b"goToBackForwardItem:", {"retval": {"type": "Z"}})
    r(b"WebView", b"isContinuousSpellCheckingEnabled", {"retval": {"type": "Z"}})
    r(b"WebView", b"isEditable", {"retval": {"type": "Z"}})
    r(b"WebView", b"isLoading", {"retval": {"type": "Z"}})
    r(b"WebView", b"maintainsInactiveSelection", {"retval": {"type": "Z"}})
    r(
        b"WebView",
        b"searchFor:direction:caseSensitive:wrap:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"type": "Z"}, 4: {"type": "Z"}, 5: {"type": "Z"}},
        },
    )
    r(
        b"WebView",
        b"setContinuousSpellCheckingEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"WebView", b"setDrawsBackground:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebView", b"setEditable:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebView", b"setMaintainsBackForwardList:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebView", b"setShouldCloseWithWindow:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebView", b"setShouldUpdateWhileOffscreen:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebView", b"setSmartInsertDeleteEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"WebView", b"shouldCloseWithWindow", {"retval": {"type": "Z"}})
    r(b"WebView", b"shouldUpdateWhileOffscreen", {"retval": {"type": "Z"}})
    r(b"WebView", b"smartInsertDeleteEnabled", {"retval": {"type": "Z"}})
    r(b"WebView", b"supportsTextEncoding", {"retval": {"type": "Z"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("DOMEvent", b"initEvent:::")
objc.registerNewKeywordsFromSelector(
    "DOMEvent", b"initEvent:canBubbleArg:cancelableArg:"
)
objc.registerNewKeywordsFromSelector(
    "DOMKeyboardEvent",
    b"initKeyboardEvent:canBubble:cancelable:view:keyIdentifier:keyLocation:ctrlKey:altKey:shiftKey:metaKey:",
)
objc.registerNewKeywordsFromSelector(
    "DOMKeyboardEvent",
    b"initKeyboardEvent:canBubble:cancelable:view:keyIdentifier:keyLocation:ctrlKey:altKey:shiftKey:metaKey:altGraphKey:",
)
objc.registerNewKeywordsFromSelector(
    "DOMKeyboardEvent",
    b"initKeyboardEvent:canBubble:cancelable:view:keyIdentifier:location:ctrlKey:altKey:shiftKey:metaKey:",
)
objc.registerNewKeywordsFromSelector(
    "DOMKeyboardEvent",
    b"initKeyboardEvent:canBubble:cancelable:view:keyIdentifier:location:ctrlKey:altKey:shiftKey:metaKey:altGraphKey:",
)
objc.registerNewKeywordsFromSelector("DOMMouseEvent", b"initMouseEvent:::::::::::::::")
objc.registerNewKeywordsFromSelector(
    "DOMMouseEvent",
    b"initMouseEvent:canBubble:cancelable:view:detail:screenX:screenY:clientX:clientY:ctrlKey:altKey:shiftKey:metaKey:button:relatedTarget:",
)
objc.registerNewKeywordsFromSelector("DOMMutationEvent", b"initMutationEvent::::::::")
objc.registerNewKeywordsFromSelector(
    "DOMMutationEvent",
    b"initMutationEvent:canBubble:cancelable:relatedNode:prevValue:newValue:attrName:attrChange:",
)
objc.registerNewKeywordsFromSelector(
    "DOMOverflowEvent", b"initOverflowEvent:horizontalOverflow:verticalOverflow:"
)
objc.registerNewKeywordsFromSelector("DOMUIEvent", b"initUIEvent:::::")
objc.registerNewKeywordsFromSelector(
    "DOMUIEvent", b"initUIEvent:canBubble:cancelable:view:detail:"
)
objc.registerNewKeywordsFromSelector(
    "DOMWheelEvent",
    b"initWheelEvent:wheelDeltaY:view:screenX:screenY:clientX:clientY:ctrlKey:altKey:shiftKey:metaKey:",
)
objc.registerNewKeywordsFromSelector(
    "WKUserScript", b"initWithSource:injectionTime:forMainFrameOnly:"
)
objc.registerNewKeywordsFromSelector(
    "WKUserScript", b"initWithSource:injectionTime:forMainFrameOnly:inContentWorld:"
)
objc.registerNewKeywordsFromSelector("WKWebExtensionContext", b"initForExtension:")
objc.registerNewKeywordsFromSelector(
    "WKWebExtensionController", b"initWithConfiguration:"
)
objc.registerNewKeywordsFromSelector(
    "WKWebExtensionMatchPattern", b"initWithScheme:host:path:error:"
)
objc.registerNewKeywordsFromSelector(
    "WKWebExtensionMatchPattern", b"initWithString:error:"
)
objc.registerNewKeywordsFromSelector("WKWebView", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("WKWebView", b"initWithFrame:configuration:")
objc.registerNewKeywordsFromSelector("WebArchive", b"initWithData:")
objc.registerNewKeywordsFromSelector(
    "WebArchive", b"initWithMainResource:subresources:subframeArchives:"
)
objc.registerNewKeywordsFromSelector("WebDataSource", b"initWithRequest:")
objc.registerNewKeywordsFromSelector("WebFrame", b"initWithName:webFrameView:webView:")
objc.registerNewKeywordsFromSelector(
    "WebHistoryItem", b"initWithURLString:title:lastVisitedTimeInterval:"
)
objc.registerNewKeywordsFromSelector("WebPreferences", b"initWithIdentifier:")
objc.registerNewKeywordsFromSelector(
    "WebResource", b"initWithData:URL:MIMEType:textEncodingName:frameName:"
)
objc.registerNewKeywordsFromSelector("WebView", b"initWithFrame:frameName:groupName:")
protocols = {
    "WebUIDelegate": objc.informal_protocol(
        "WebUIDelegate",
        [
            objc.selector(
                None,
                b"webView:runOpenPanelForFileButtonWithResultListener:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(None, b"webViewFirstResponder:", b"@@:@", isRequired=False),
            objc.selector(
                None,
                b"webView:runJavaScriptAlertPanelWithMessage:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:runJavaScriptConfirmPanelWithMessage:initiatedByFrame:",
                b"Z@:@@@",
                isRequired=False,
            ),
            objc.selector(None, b"webViewShow:", b"v@:@", isRequired=False),
            objc.selector(
                None,
                b"webView:runBeforeUnloadConfirmPanelWithMessage:initiatedByFrame:",
                b"Z@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:drawHeaderInRect:",
                b"v@:@{CGRect={CGPoint=dd}{CGSize=dd}}",
                isRequired=False,
            ),
            objc.selector(None, b"webViewRunModal:", b"v@:@", isRequired=False),
            objc.selector(
                None, b"webViewIsStatusBarVisible:", b"Z@:@", isRequired=False
            ),
            objc.selector(None, b"webViewFooterHeight:", b"f@:@", isRequired=False),
            objc.selector(
                None,
                b"webView:validateUserInterfaceItem:defaultValidation:",
                b"Z@:@@Z",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:runOpenPanelForFileButtonWithResultListener:allowMultipleFiles:",
                b"v@:@@Z",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:runJavaScriptTextInputPanelWithPrompt:defaultText:initiatedByFrame:",
                b"@@:@@@@",
                isRequired=False,
            ),
            objc.selector(None, b"webViewIsResizable:", b"Z@:@", isRequired=False),
            objc.selector(
                None, b"webView:setToolbarsVisible:", b"v@:@Z", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:setContentRect:",
                b"v@:@{CGRect={CGPoint=dd}{CGSize=dd}}",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:runJavaScriptAlertPanelWithMessage:initiatedByFrame:",
                b"v@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:drawFooterInRect:",
                b"v@:@{CGRect={CGPoint=dd}{CGSize=dd}}",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:runJavaScriptTextInputPanelWithPrompt:defaultText:",
                b"@@:@@@",
                isRequired=False,
            ),
            objc.selector(None, b"webView:setResizable:", b"v@:@Z", isRequired=False),
            objc.selector(
                None,
                b"webViewContentRect:",
                b"{CGRect={CGPoint=dd}{CGSize=dd}}@:@",
                isRequired=False,
            ),
            objc.selector(None, b"webViewClose:", b"v@:@", isRequired=False),
            objc.selector(
                None,
                b"webView:shouldPerformAction:fromSender:",
                b"Z@:@:@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:dragSourceActionMaskForPoint:",
                b"Q@:@{CGPoint=dd}",
                isRequired=False,
            ),
            objc.selector(
                None, b"webViewAreToolbarsVisible:", b"Z@:@", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:setFrame:",
                b"v@:@{CGRect={CGPoint=dd}{CGSize=dd}}",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:dragDestinationActionMaskForDraggingInfo:",
                b"Q@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:mouseDidMoveOverElement:modifierFlags:",
                b"v@:@@Q",
                isRequired=False,
            ),
            objc.selector(None, b"webViewHeaderHeight:", b"f@:@", isRequired=False),
            objc.selector(
                None,
                b"webView:runJavaScriptConfirmPanelWithMessage:",
                b"Z@:@@",
                isRequired=False,
            ),
            objc.selector(None, b"webViewStatusText:", b"@@:@", isRequired=False),
            objc.selector(
                None, b"webView:createWebViewWithRequest:", b"@@:@@", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:willPerformDragDestinationAction:forDraggingInfo:",
                b"v@:@Q@",
                isRequired=False,
            ),
            objc.selector(None, b"webViewUnfocus:", b"v@:@", isRequired=False),
            objc.selector(
                None, b"webView:makeFirstResponder:", b"v@:@@", isRequired=False
            ),
            objc.selector(None, b"webView:setStatusText:", b"v@:@@", isRequired=False),
            objc.selector(
                None,
                b"webView:willPerformDragSourceAction:fromPoint:withPasteboard:",
                b"v@:@Q{CGPoint=dd}@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:contextMenuItemsForElement:defaultMenuItems:",
                b"@@:@@@",
                isRequired=False,
            ),
            objc.selector(None, b"webViewFocus:", b"v@:@", isRequired=False),
            objc.selector(None, b"webView:printFrameView:", b"v@:@@", isRequired=False),
            objc.selector(
                None,
                b"webViewFrame:",
                b"{CGRect={CGPoint=dd}{CGSize=dd}}@:@",
                isRequired=False,
            ),
            objc.selector(
                None, b"webView:setStatusBarVisible:", b"v@:@Z", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:createWebViewModalDialogWithRequest:",
                b"@@:@@",
                isRequired=False,
            ),
        ],
    ),
    "WebViewEditingDelegate": objc.informal_protocol(
        "WebViewEditingDelegate",
        [
            objc.selector(None, b"webViewDidBeginEditing:", b"v@:@", isRequired=False),
            objc.selector(
                None, b"webViewDidChangeSelection:", b"v@:@", isRequired=False
            ),
            objc.selector(
                None, b"webView:shouldDeleteDOMRange:", b"Z@:@@", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:shouldChangeTypingStyle:toStyle:",
                b"Z@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:shouldChangeSelectedDOMRange:toDOMRange:affinity:stillSelecting:",
                b"Z@:@@@QZ",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:shouldApplyStyle:toElementsInDOMRange:",
                b"Z@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None, b"webView:doCommandBySelector:", b"Z@:@:", isRequired=False
            ),
            objc.selector(
                None, b"webViewDidChangeTypingStyle:", b"v@:@", isRequired=False
            ),
            objc.selector(None, b"undoManagerForWebView:", b"@@:@", isRequired=False),
            objc.selector(None, b"webViewDidEndEditing:", b"v@:@", isRequired=False),
            objc.selector(
                None,
                b"webView:shouldInsertText:replacingDOMRange:givenAction:",
                b"Z@:@@@q",
                isRequired=False,
            ),
            objc.selector(None, b"webViewDidChange:", b"v@:@", isRequired=False),
            objc.selector(
                None, b"webView:shouldEndEditingInDOMRange:", b"Z@:@@", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:shouldBeginEditingInDOMRange:",
                b"Z@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:shouldInsertNode:replacingDOMRange:givenAction:",
                b"Z@:@@@q",
                isRequired=False,
            ),
        ],
    ),
    "WebPolicyDelegate": objc.informal_protocol(
        "WebPolicyDelegate",
        [
            objc.selector(
                None,
                b"webView:unableToImplementPolicyWithError:frame:",
                b"v@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:decidePolicyForNavigationAction:request:frame:decisionListener:",
                b"v@:@@@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:decidePolicyForNewWindowAction:request:newFrameName:decisionListener:",
                b"v@:@@@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:decidePolicyForMIMEType:request:frame:decisionListener:",
                b"v@:@@@@@",
                isRequired=False,
            ),
        ],
    ),
    "WebDownloadDelegate": objc.informal_protocol(
        "WebDownloadDelegate",
        [
            objc.selector(
                None,
                b"downloadWindowForAuthenticationSheet:",
                b"@@:@",
                isRequired=False,
            )
        ],
    ),
    "WebPlugIn": objc.informal_protocol(
        "WebPlugIn",
        [
            objc.selector(
                None,
                b"webPlugInMainResourceDidReceiveResponse:",
                b"v@:@",
                isRequired=False,
            ),
            objc.selector(None, b"objectForWebScript", b"@@:", isRequired=False),
            objc.selector(
                None, b"webPlugInMainResourceDidFinishLoading", b"v@:", isRequired=False
            ),
            objc.selector(
                None,
                b"webPlugInMainResourceDidFailWithError:",
                b"v@:@",
                isRequired=False,
            ),
            objc.selector(
                None, b"webPlugInMainResourceDidReceiveData:", b"v@:@", isRequired=False
            ),
            objc.selector(None, b"webPlugInDestroy", b"v@:", isRequired=False),
            objc.selector(None, b"webPlugInStop", b"v@:", isRequired=False),
            objc.selector(None, b"webPlugInSetIsSelected:", b"v@:Z", isRequired=False),
            objc.selector(None, b"webPlugInInitialize", b"v@:", isRequired=False),
            objc.selector(None, b"webPlugInStart", b"v@:", isRequired=False),
        ],
    ),
    "WebJavaPlugIn": objc.informal_protocol(
        "WebJavaPlugIn",
        [
            objc.selector(
                None,
                b"webPlugInCallJava:isStatic:returnType:method:arguments:callingURL:exceptionDescription:",
                b"(jvalue=CcSsiqfd^{_jobject=})@:^{_jobject=}Zi^{_jmethodID=}^(jvalue=CcSsiqfd^{_jobject=})@^@",
                isRequired=False,
            ),
            objc.selector(
                None, b"webPlugInGetApplet", b"^{_jobject=}@:", isRequired=False
            ),
        ],
    ),
    "WebResourceLoadDelegate": objc.informal_protocol(
        "WebResourceLoadDelegate",
        [
            objc.selector(
                None,
                b"webView:resource:didCancelAuthenticationChallenge:fromDataSource:",
                b"v@:@@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:resource:didFinishLoadingFromDataSource:",
                b"v@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:identifierForInitialRequest:fromDataSource:",
                b"@@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:resource:willSendRequest:redirectResponse:fromDataSource:",
                b"@@:@@@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:plugInFailedWithError:dataSource:",
                b"v@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:resource:didReceiveResponse:fromDataSource:",
                b"v@:@@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:resource:didReceiveContentLength:fromDataSource:",
                b"v@:@@q@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:resource:didFailLoadingWithError:fromDataSource:",
                b"v@:@@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:resource:didReceiveAuthenticationChallenge:fromDataSource:",
                b"v@:@@@@",
                isRequired=False,
            ),
        ],
    ),
    "WebFrameLoadDelegate": objc.informal_protocol(
        "WebFrameLoadDelegate",
        [
            objc.selector(
                None,
                b"webView:didCancelClientRedirectForFrame:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:didClearWindowObject:forFrame:",
                b"v@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None, b"webView:didReceiveTitle:forFrame:", b"v@:@@@", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:didStartProvisionalLoadForFrame:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(
                None, b"webView:didCommitLoadForFrame:", b"v@:@@", isRequired=False
            ),
            objc.selector(
                None, b"webView:didFinishLoadForFrame:", b"v@:@@", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:didFailProvisionalLoadWithError:forFrame:",
                b"v@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:didFailLoadWithError:forFrame:",
                b"v@:@@@",
                isRequired=False,
            ),
            objc.selector(
                None, b"webView:didReceiveIcon:forFrame:", b"v@:@@@", isRequired=False
            ),
            objc.selector(
                None,
                b"webView:didReceiveServerRedirectForProvisionalLoadForFrame:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:willPerformClientRedirectToURL:delay:fireDate:forFrame:",
                b"v@:@@d@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:windowScriptObjectAvailable:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"webView:didChangeLocationWithinPageForFrame:",
                b"v@:@@",
                isRequired=False,
            ),
            objc.selector(None, b"webView:willCloseFrame:", b"v@:@@", isRequired=False),
        ],
    ),
    "WebPlugInContainer": objc.informal_protocol(
        "WebPlugInContainer",
        [
            objc.selector(
                None, b"webPlugInContainerShowStatus:", b"v@:@", isRequired=False
            ),
            objc.selector(
                None, b"webPlugInContainerSelectionColor", b"@@:", isRequired=False
            ),
            objc.selector(None, b"webFrame", b"@@:", isRequired=False),
            objc.selector(
                None,
                b"webPlugInContainerLoadRequest:inFrame:",
                b"v@:@@",
                isRequired=False,
            ),
        ],
    ),
    "WebScripting": objc.informal_protocol(
        "WebScripting",
        [
            objc.selector(None, b"finalizeForWebScript", b"v@:", isRequired=False),
            objc.selector(
                None,
                b"invokeUndefinedMethodFromWebScript:withArguments:",
                b"@@:@@",
                isRequired=False,
            ),
            objc.selector(None, b"webScriptNameForKey:", b"@@:^c", isRequired=False),
            objc.selector(
                None, b"webScriptNameForSelector:", b"@@::", isRequired=False
            ),
            objc.selector(
                None, b"invokeDefaultMethodWithArguments:", b"@@:@", isRequired=False
            ),
            objc.selector(
                None, b"isSelectorExcludedFromWebScript:", b"Z@::", isRequired=False
            ),
            objc.selector(
                None, b"isKeyExcludedFromWebScript:", b"Z@:^c", isRequired=False
            ),
        ],
    ),
}
expressions = {}

# END OF FILE
