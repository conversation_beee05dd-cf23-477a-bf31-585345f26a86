# This file is generated by objective.metadata
#
# Last update: Sat Mar  1 11:27:20 2025
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "MPSNDArrayOffsets": objc.createStructType(
            "MetalPerformanceShaders.MPSNDArrayOffsets",
            b"{MPSNDArrayOffsets=[16q]}",
            ["dimensions"],
        ),
        "MPSRayOriginMinDistanceDirectionMaxDistance": objc.createStructType(
            "MetalPerformanceShaders.MPSRayOriginMinDistanceDirectionMaxDistance",
            b"{MPSRayOriginMinDistanceDirectionMaxDistance={_MPSPackedFloat3=fff}f{_MPSPackedFloat3=fff}f}",
            ["origin", "minDistance", "direction", "maxDistance"],
        ),
        "MPSImageHistogramInfo": objc.createStructType(
            "MetalPerformanceShaders.MPSImageHistogramInfo",
            b"{MPSImageHistogramInfo=QZ<4f><4f>}",
            [
                "numberOfHistogramEntries",
                "histogramForAlpha",
                "minPixelValue",
                "maxPixelValue",
            ],
        ),
        "MPSMatrixCopyOffsets": objc.createStructType(
            "MetalPerformanceShaders.MPSMatrixCopyOffsets",
            b"{MPSMatrixCopyOffsets=IIII}",
            [
                "sourceRowOffset",
                "sourceColumnOffset",
                "destinationRowOffset",
                "destinationColumnOffset",
            ],
        ),
        "MPSIntersectionDistance": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistance",
            b"{MPSIntersectionDistance=f}",
            ["distance"],
        ),
        "MPSRayOriginDirection": objc.createStructType(
            "MetalPerformanceShaders.MPSRayOriginDirection",
            b"{MPSRayOriginDirection=<3f><3f>}",
            ["origin", "direction"],
        ),
        "MPSImageKeypointRangeInfo": objc.createStructType(
            "MetalPerformanceShaders.MPSImageKeypointRangeInfo",
            b"{MPSImageKeypointRangeInfo=Qf}",
            ["maximumKeypoints", "minimumThresholdValue"],
        ),
        "MPSIntersectionDistancePrimitiveIndex": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistancePrimitiveIndex",
            b"{MPSIntersectionDistancePrimitiveIndex=fI}",
            ["distance", "primitiveIndex"],
        ),
        "MPSIntersectionDistancePrimitiveIndexBufferIndexInstanceIndexCoordinates": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistancePrimitiveIndexBufferIndexInstanceIndexCoordinates",
            b"{MPSIntersectionDistancePrimitiveIndexBufferIndexInstanceIndexCoordinates=fIII<2f>}",
            [
                "distance",
                "primitiveIndex",
                "bufferIndex",
                "instanceIndex",
                "coordinates",
            ],
        ),
        "MPSIntersectionDistancePrimitiveIndexBufferIndexInstanceIndex": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistancePrimitiveIndexBufferIndexInstanceIndex",
            b"{MPSIntersectionDistancePrimitiveIndexBufferIndexInstanceIndex=fIII}",
            ["distance", "primitiveIndex", "bufferIndex", "instanceIndex"],
        ),
        "MPSNDArraySizes": objc.createStructType(
            "MetalPerformanceShaders.MPSNDArraySizes",
            b"{MPSNDArraySizes=[16Q]}",
            ["dimensions"],
        ),
        "MPSScaleTransform": objc.createStructType(
            "MetalPerformanceShaders.MPSScaleTransform",
            b"{MPSScaleTransform=dddd}",
            ["scaleX", "scaleY", "translateX", "translateY"],
        ),
        "MPSRayPackedOriginDirection": objc.createStructType(
            "MetalPerformanceShaders.MPSRayPackedOriginDirection",
            b"{MPSRayPackedOriginDirection={_MPSPackedFloat3=fff}{_MPSPackedFloat3=fff}}",
            ["origin", "direction"],
        ),
        "MPSStateTextureInfo": objc.createStructType(
            "MetalPerformanceShaders.MPSStateTextureInfo",
            b"{MPSStateTextureInfo=QQQQQQQ[4Q]}",
            [
                "width",
                "height",
                "depth",
                "arrayLength",
                "pixelFormat",
                "textureType",
                "usage",
                "_reserved",
            ],
        ),
        "MPSMatrixOffset": objc.createStructType(
            "MetalPerformanceShaders.MPSMatrixOffset",
            b"{MPSMatrixOffset=II}",
            ["rowOffset", "columnOffset"],
        ),
        "MPSSize": objc.createStructType(
            "MetalPerformanceShaders.MPSSize",
            b"{MPSSize=ddd}",
            ["width", "height", "depth"],
        ),
        "MPSIntegerDivisionParams": objc.createStructType(
            "MetalPerformanceShaders.MPSIntegerDivisionParams",
            b"{MPSIntegerDivisionParams=SSSS}",
            ["divisor", "recip", "addend", "shift"],
        ),
        "MPSCustomKernelSourceInfo": objc.createStructType(
            "MetalPerformanceShaders.MPSCustomKernelSourceInfo",
            b"{MPSCustomKernelSourceInfo=<2s><2S><2S><2s><2S><2S>SSSS}",
            [
                "kernelOrigin",
                "kernelPhase",
                "kernelSize",
                "offset",
                "stride",
                "dilationRate",
                "featureChannelOffset",
                "featureChannels",
                "imageArrayOffset",
                "imageArraySize",
            ],
        ),
        "MPSImageReadWriteParams": objc.createStructType(
            "MetalPerformanceShaders.MPSImageReadWriteParams",
            b"{MPSImageReadWriteParams=QQ}",
            ["featureChannelOffset", "numberOfFeatureChannelsToReadWrite"],
        ),
        "MPSRayOriginMaskDirectionMaxDistance": objc.createStructType(
            "MetalPerformanceShaders.MPSRayOriginMaskDirectionMaxDistance",
            b"{MPSRayOriginMaskDirectionMaxDistance={_MPSPackedFloat3=fff}I{_MPSPackedFloat3=fff}f}",
            ["origin", "mask", "direction", "maxDistance"],
        ),
        "MPSCustomKernelArgumentCount": objc.createStructType(
            "MetalPerformanceShaders.MPSCustomKernelArgumentCount",
            b"{MPSCustomKernelArgumentCount=QQQ}",
            ["destinationTextureCount", "sourceTextureCount", "broadcastTextureCount"],
        ),
        "MPSOffset": objc.createStructType(
            "MetalPerformanceShaders.MPSOffset", b"{MPSOffset=qqq}", ["x", "y", "z"]
        ),
        "MPSImageRegion": objc.createStructType(
            "MetalPerformanceShaders.MPSImageRegion",
            b"{MPSImageRegion={MPSImageCoordinate=QQQ}{MPSImageCoordinate=QQQ}}",
            ["offset", "size"],
        ),
        "MPSDimensionSlice": objc.createStructType(
            "MetalPerformanceShaders.MPSDimensionSlice",
            b"{MPSDimensionSlice=QQ}",
            ["start", "length"],
        ),
        "MPSAxisAlignedBoundingBox": objc.createStructType(
            "MetalPerformanceShaders.MPSAxisAlignedBoundingBox",
            b"{_MPSAxisAlignedBoundingBox=<3f><3f>}",
            ["min", "max"],
        ),
        "MPSIntersectionDistancePrimitiveIndexCoordinates": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistancePrimitiveIndexCoordinates",
            b"{MPSIntersectionDistancePrimitiveIndexCoordinates=fI<2f>}",
            ["distance", "primitiveIndex", "coordinates"],
        ),
        "MPSRegion": objc.createStructType(
            "MetalPerformanceShaders.MPSRegion",
            b"{MPSRegion={MPSOrigin=ddd}{MPSSize=ddd}}",
            ["origin", "size"],
        ),
        "MPSIntersectionDistancePrimitiveIndexInstanceIndex": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistancePrimitiveIndexInstanceIndex",
            b"{MPSIntersectionDistancePrimitiveIndexInstanceIndex=fII}",
            ["distance", "primitiveIndex", "instanceIndex"],
        ),
        "MPSImageKeypointData": objc.createStructType(
            "MetalPerformanceShaders.MPSImageKeypointData",
            b"{MPSImageKeypointData=<2S>f}",
            ["keypointCoordinate", "keypointColorValue"],
        ),
        "MPSIntersectionDistancePrimitiveIndexInstanceIndexCoordinates": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistancePrimitiveIndexInstanceIndexCoordinates",
            b"{MPSIntersectionDistancePrimitiveIndexInstanceIndexCoordinates=fII<2f>}",
            ["distance", "primitiveIndex", "instanceIndex", "coordinates"],
        ),
        "MPSImageCoordinate": objc.createStructType(
            "MetalPerformanceShaders.MPSImageCoordinate",
            b"{MPSImageCoordinate=QQQ}",
            ["x", "y", "channel"],
        ),
        "MPSCustomKernelInfo": objc.createStructType(
            "MetalPerformanceShaders.MPSCustomKernelInfo",
            b"{MPSCustomKernelInfo=<4S><4S>SSSSSS{MPSIntegerDivisionParams=SSSS}}",
            [
                "clipOrigin",
                "clipSize",
                "destinationFeatureChannels",
                "destImageArraySize",
                "sourceImageCount",
                "threadgroupSize",
                "subbatchIndex",
                "subbatchStride",
                "idiv",
            ],
        ),
        "MPSPackedFloat3": objc.createStructType(
            "MetalPerformanceShaders.MPSPackedFloat3",
            b"{_MPSPackedFloat3=fff}",
            ["x", "y", "z"],
        ),
        "MPSIntersectionDistancePrimitiveIndexBufferIndexCoordinates": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistancePrimitiveIndexBufferIndexCoordinates",
            b"{MPSIntersectionDistancePrimitiveIndexBufferIndexCoordinates=fII<2f>}",
            ["distance", "primitiveIndex", "bufferIndex", "coordinates"],
        ),
        "MPSIntersectionDistancePrimitiveIndexBufferIndex": objc.createStructType(
            "MetalPerformanceShaders.MPSIntersectionDistancePrimitiveIndexBufferIndex",
            b"{MPSIntersectionDistancePrimitiveIndexBufferIndex=fII}",
            ["distance", "primitiveIndex", "bufferIndex"],
        ),
        "MPSOrigin": objc.createStructType(
            "MetalPerformanceShaders.MPSOrigin", b"{MPSOrigin=ddd}", ["x", "y", "z"]
        ),
    }
)
constants = """$MPSRectNoClip@{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}$"""
enums = """$MPSAccelerationStructureStatusBuilt@1$MPSAccelerationStructureStatusUnbuilt@0$MPSAccelerationStructureUsageFrequentRebuild@2$MPSAccelerationStructureUsageNone@0$MPSAccelerationStructureUsagePreferCPUBuild@8$MPSAccelerationStructureUsagePreferGPUBuild@4$MPSAccelerationStructureUsageRefit@1$MPSAliasingStrategyAliasingReserved@3$MPSAliasingStrategyDefault@0$MPSAliasingStrategyDontCare@0$MPSAliasingStrategyPreferNonTemporaryMemory@8$MPSAliasingStrategyPreferTemporaryMemory@4$MPSAliasingStrategyShallAlias@1$MPSAliasingStrategyShallNotAlias@2$MPSAlphaTypeAlphaIsOne@1$MPSAlphaTypeNonPremultiplied@0$MPSAlphaTypePremultiplied@2$MPSBoundingBoxIntersectionTestTypeAxisAligned@1$MPSBoundingBoxIntersectionTestTypeDefault@0$MPSBoundingBoxIntersectionTestTypeFast@2$MPSCNNBatchNormalizationFlagsCalculateStatisticsAlways@1$MPSCNNBatchNormalizationFlagsCalculateStatisticsAutomatic@0$MPSCNNBatchNormalizationFlagsCalculateStatisticsMask@3$MPSCNNBatchNormalizationFlagsCalculateStatisticsNever@2$MPSCNNBatchNormalizationFlagsDefault@0$MPSCNNBinaryConvolutionFlagsNone@0$MPSCNNBinaryConvolutionFlagsUseBetaScaling@1$MPSCNNBinaryConvolutionTypeAND@2$MPSCNNBinaryConvolutionTypeBinaryWeights@0$MPSCNNBinaryConvolutionTypeXNOR@1$MPSCNNConvolutionFlagsNone@0$MPSCNNConvolutionGradientOptionAll@3$MPSCNNConvolutionGradientOptionGradientWithData@1$MPSCNNConvolutionGradientOptionGradientWithWeightsAndBias@2$MPSCNNConvolutionWeightsLayoutOHWI@0$MPSCNNLossTypeCategoricalCrossEntropy@4$MPSCNNLossTypeCosineDistance@7$MPSCNNLossTypeCount@10$MPSCNNLossTypeHinge@5$MPSCNNLossTypeHuber@6$MPSCNNLossTypeKullbackLeiblerDivergence@9$MPSCNNLossTypeLog@8$MPSCNNLossTypeMeanAbsoluteError@0$MPSCNNLossTypeMeanSquaredError@1$MPSCNNLossTypeSigmoidCrossEntropy@3$MPSCNNLossTypeSoftMaxCrossEntropy@2$MPSCNNNeuronTypeAbsolute@6$MPSCNNNeuronTypeCount@16$MPSCNNNeuronTypeELU@9$MPSCNNNeuronTypeExponential@13$MPSCNNNeuronTypeGeLU@15$MPSCNNNeuronTypeHardSigmoid@4$MPSCNNNeuronTypeLinear@2$MPSCNNNeuronTypeLogarithm@14$MPSCNNNeuronTypeNone@0$MPSCNNNeuronTypePReLU@10$MPSCNNNeuronTypePower@12$MPSCNNNeuronTypeReLU@1$MPSCNNNeuronTypeReLUN@11$MPSCNNNeuronTypeSigmoid@3$MPSCNNNeuronTypeSoftPlus@7$MPSCNNNeuronTypeSoftSign@8$MPSCNNNeuronTypeTanH@5$MPSCNNReductionTypeCount@4$MPSCNNReductionTypeMean@2$MPSCNNReductionTypeNone@0$MPSCNNReductionTypeSum@1$MPSCNNReductionTypeSumByNonZeroWeights@3$MPSCNNWeightsQuantizationTypeLinear@1$MPSCNNWeightsQuantizationTypeLookupTable@2$MPSCNNWeightsQuantizationTypeNone@0$MPSCustomKernelIndexDestIndex@0$MPSCustomKernelIndexSrc0Index@0$MPSCustomKernelIndexSrc1Index@1$MPSCustomKernelIndexSrc2Index@2$MPSCustomKernelIndexSrc3Index@3$MPSCustomKernelIndexSrc4Index@4$MPSCustomKernelIndexUserDataIndex@30$MPSDataLayoutFeatureChannelsxHeightxWidth@1$MPSDataLayoutHeightxWidthxFeatureChannels@0$MPSDataTypeAlternateEncodingBit@2147483648$MPSDataTypeBFloat16@2415919120$MPSDataTypeBool@2147483656$MPSDataTypeComplexBit@16777216$MPSDataTypeComplexFloat16@285212704$MPSDataTypeComplexFloat32@285212736$MPSDataTypeFloat16@268435472$MPSDataTypeFloat32@268435488$MPSDataTypeFloatBit@268435456$MPSDataTypeInt16@536870928$MPSDataTypeInt2@536870914$MPSDataTypeInt32@536870944$MPSDataTypeInt4@536870916$MPSDataTypeInt64@536870976$MPSDataTypeInt8@536870920$MPSDataTypeIntBit@536870912$MPSDataTypeInvalid@0$MPSDataTypeNormalizedBit@1073741824$MPSDataTypeSignedBit@536870912$MPSDataTypeUInt16@16$MPSDataTypeUInt2@2$MPSDataTypeUInt32@32$MPSDataTypeUInt4@4$MPSDataTypeUInt64@64$MPSDataTypeUInt8@8$MPSDataTypeUnorm1@1073741825$MPSDataTypeUnorm8@1073741832$MPSDeviceCapsIndex@127$MPSDeviceCapsNull@0$MPSDeviceIsAppleDevice@1024$MPSDeviceOptionsDefault@0$MPSDeviceOptionsLowPower@1$MPSDeviceOptionsSkipRemovable@2$MPSDeviceSupportsBFloat16Arithmetic@4096$MPSDeviceSupportsFloat16BicubicFiltering@512$MPSDeviceSupportsFloat32Filtering@128$MPSDeviceSupportsNorm16BicubicFiltering@256$MPSDeviceSupportsQuadShuffle@16$MPSDeviceSupportsReadWriteTextures@4$MPSDeviceSupportsReadableArrayOfTextures@1$MPSDeviceSupportsSimdReduction@64$MPSDeviceSupportsSimdShuffle@32$MPSDeviceSupportsSimdShuffleAndFill@2048$MPSDeviceSupportsSimdgroupBarrier@8$MPSDeviceSupportsWritableArrayOfTextures@2$MPSFloatDataTypeExponentBit@8126464$MPSFloatDataTypeExponentShift@18$MPSFloatDataTypeMantissaBit@261120$MPSFloatDataTypeMantissaShift@10$MPSFloatDataTypeSignBit@8388608$MPSFloatDataTypeSignShift@23$MPSFunctionConstantNone@-1$MPSImageEdgeModeClamp@1$MPSImageEdgeModeConstant@4$MPSImageEdgeModeMirror@2$MPSImageEdgeModeMirrorWithEdge@3$MPSImageEdgeModeZero@0$MPSImageFeatureChannelFormatCount@6$MPSImageFeatureChannelFormatFloat16@3$MPSImageFeatureChannelFormatFloat32@4$MPSImageFeatureChannelFormatNone@0$MPSImageFeatureChannelFormatUnorm16@2$MPSImageFeatureChannelFormatUnorm8@1$MPSImageFeatureChannelFormat_reserved0@5$MPSImageType2d@0$MPSImageType2d_array@1$MPSImageType2d_array_noAlpha@5$MPSImageType2d_noAlpha@4$MPSImageTypeArray2d@2$MPSImageTypeArray2d_array@3$MPSImageTypeArray2d_array_noAlpha@7$MPSImageTypeArray2d_noAlpha@6$MPSImageType_ArrayMask@1$MPSImageType_BatchMask@2$MPSImageType_bitCount@6$MPSImageType_mask@63$MPSImageType_noAlpha@4$MPSImageType_texelFormatBFloat16@24$MPSImageType_texelFormatFloat16@16$MPSImageType_texelFormatMask@56$MPSImageType_texelFormatShift@3$MPSImageType_texelFormatStandard@0$MPSImageType_texelFormatUnorm8@8$MPSImageType_typeMask@3$MPSIntersectionDataTypeDistance@0$MPSIntersectionDataTypeDistancePrimitiveIndex@1$MPSIntersectionDataTypeDistancePrimitiveIndexBufferIndex@5$MPSIntersectionDataTypeDistancePrimitiveIndexBufferIndexCoordinates@6$MPSIntersectionDataTypeDistancePrimitiveIndexBufferIndexInstanceIndex@7$MPSIntersectionDataTypeDistancePrimitiveIndexBufferIndexInstanceIndexCoordinates@8$MPSIntersectionDataTypeDistancePrimitiveIndexCoordinates@2$MPSIntersectionDataTypeDistancePrimitiveIndexInstanceIndex@3$MPSIntersectionDataTypeDistancePrimitiveIndexInstanceIndexCoordinates@4$MPSIntersectionTypeAny@1$MPSIntersectionTypeNearest@0$MPSKernelOptionsAllowReducedPrecision@2$MPSKernelOptionsDisableInternalTiling@4$MPSKernelOptionsInsertDebugGroups@8$MPSKernelOptionsNone@0$MPSKernelOptionsSkipAPIValidation@1$MPSKernelOptionsVerbose@16$MPSKernelTypes_h@1$MPSMatrixDecompositionStatusFailure@-1$MPSMatrixDecompositionStatusNonPositiveDefinite@-3$MPSMatrixDecompositionStatusSingular@-2$MPSMatrixDecompositionStatusSuccess@0$MPSMatrixRandomDistributionDefault@1$MPSMatrixRandomDistributionNormal@3$MPSMatrixRandomDistributionUniform@2$MPSNDArrayQuantizationTypeAffine@1$MPSNDArrayQuantizationTypeLUT@2$MPSNDArrayQuantizationTypeNone@0$MPSNNComparisonTypeEqual@0$MPSNNComparisonTypeGreater@4$MPSNNComparisonTypeGreaterOrEqual@5$MPSNNComparisonTypeLess@2$MPSNNComparisonTypeLessOrEqual@3$MPSNNComparisonTypeNotEqual@1$MPSNNConvolutionAccumulatorPrecisionOptionFloat@1$MPSNNConvolutionAccumulatorPrecisionOptionHalf@0$MPSNNPaddingMethodAddRemainderToBottomLeft@8$MPSNNPaddingMethodAddRemainderToBottomRight@12$MPSNNPaddingMethodAddRemainderToMask@12$MPSNNPaddingMethodAddRemainderToTopLeft@0$MPSNNPaddingMethodAddRemainderToTopRight@4$MPSNNPaddingMethodAlignBottomRight@2$MPSNNPaddingMethodAlignCentered@0$MPSNNPaddingMethodAlignMask@3$MPSNNPaddingMethodAlignTopLeft@1$MPSNNPaddingMethodAlign_reserved@3$MPSNNPaddingMethodCustom@16384$MPSNNPaddingMethodCustomAllowForNodeFusion@8192$MPSNNPaddingMethodCustomWhitelistForNodeFusion@8192$MPSNNPaddingMethodExcludeEdges@32768$MPSNNPaddingMethodSizeFull@32$MPSNNPaddingMethodSizeMask@2032$MPSNNPaddingMethodSizeSame@16$MPSNNPaddingMethodSizeValidOnly@0$MPSNNPaddingMethodSize_reserved@48$MPSNNRegularizationTypeL1@1$MPSNNRegularizationTypeL2@2$MPSNNRegularizationTypeNone@0$MPSNNTrainingStyleUpdateDeviceCPU@1$MPSNNTrainingStyleUpdateDeviceGPU@2$MPSNNTrainingStyleUpdateDeviceNone@0$MPSPolygonTypeQuadrilateral@1$MPSPolygonTypeTriangle@0$MPSPurgeableStateAllocationDeferred@0$MPSPurgeableStateEmpty@4$MPSPurgeableStateKeepCurrent@1$MPSPurgeableStateNonVolatile@2$MPSPurgeableStateVolatile@3$MPSRNNBidirectionalCombineModeAdd@1$MPSRNNBidirectionalCombineModeConcatenate@2$MPSRNNBidirectionalCombineModeNone@0$MPSRNNMatrixIdGRUInputGateBiasTerms@21$MPSRNNMatrixIdGRUInputGateInputWeights@19$MPSRNNMatrixIdGRUInputGateRecurrentWeights@20$MPSRNNMatrixIdGRUOutputGateBiasTerms@28$MPSRNNMatrixIdGRUOutputGateInputGateWeights@27$MPSRNNMatrixIdGRUOutputGateInputWeights@25$MPSRNNMatrixIdGRUOutputGateRecurrentWeights@26$MPSRNNMatrixIdGRURecurrentGateBiasTerms@24$MPSRNNMatrixIdGRURecurrentGateInputWeights@22$MPSRNNMatrixIdGRURecurrentGateRecurrentWeights@23$MPSRNNMatrixIdLSTMForgetGateBiasTerms@10$MPSRNNMatrixIdLSTMForgetGateInputWeights@7$MPSRNNMatrixIdLSTMForgetGateMemoryWeights@9$MPSRNNMatrixIdLSTMForgetGateRecurrentWeights@8$MPSRNNMatrixIdLSTMInputGateBiasTerms@6$MPSRNNMatrixIdLSTMInputGateInputWeights@3$MPSRNNMatrixIdLSTMInputGateMemoryWeights@5$MPSRNNMatrixIdLSTMInputGateRecurrentWeights@4$MPSRNNMatrixIdLSTMMemoryGateBiasTerms@14$MPSRNNMatrixIdLSTMMemoryGateInputWeights@11$MPSRNNMatrixIdLSTMMemoryGateMemoryWeights@13$MPSRNNMatrixIdLSTMMemoryGateRecurrentWeights@12$MPSRNNMatrixIdLSTMOutputGateBiasTerms@18$MPSRNNMatrixIdLSTMOutputGateInputWeights@15$MPSRNNMatrixIdLSTMOutputGateMemoryWeights@17$MPSRNNMatrixIdLSTMOutputGateRecurrentWeights@16$MPSRNNMatrixIdSingleGateBiasTerms@2$MPSRNNMatrixIdSingleGateInputWeights@0$MPSRNNMatrixIdSingleGateRecurrentWeights@1$MPSRNNMatrixId_count@29$MPSRNNSequenceDirectionBackward@1$MPSRNNSequenceDirectionForward@0$MPSRayDataTypeOriginDirection@0$MPSRayDataTypeOriginMaskDirectionMaxDistance@2$MPSRayDataTypeOriginMinDistanceDirectionMaxDistance@1$MPSRayDataTypePackedOriginDirection@3$MPSRayMaskOperatorAnd@0$MPSRayMaskOperatorEqual@10$MPSRayMaskOperatorGreaterThan@8$MPSRayMaskOperatorGreaterThanOrEqualTo@9$MPSRayMaskOperatorLessThan@6$MPSRayMaskOperatorLessThanOrEqualTo@7$MPSRayMaskOperatorNotAnd@1$MPSRayMaskOperatorNotEqual@11$MPSRayMaskOperatorNotOr@3$MPSRayMaskOperatorNotXor@5$MPSRayMaskOperatorOr@2$MPSRayMaskOperatorXor@4$MPSRayMaskOptionInstance@2$MPSRayMaskOptionNone@0$MPSRayMaskOptionPrimitive@1$MPSStateResourceTypeBuffer@1$MPSStateResourceTypeNone@0$MPSStateResourceTypeTexture@2$MPSTemporalWeightingAverage@0$MPSTemporalWeightingExponentialMovingAverage@1$MPSTransformTypeFloat4x4@0$MPSTransformTypeIdentity@1$MPSTriangleIntersectionTestTypeDefault@0$MPSTriangleIntersectionTestTypeWatertight@1$MetalPerformanceShaders_h@1$"""
misc.update(
    {
        "MPSNNRegularizationType": NewType("MPSNNRegularizationType", int),
        "MPSRayDataType": NewType("MPSRayDataType", int),
        "MPSCNNBinaryConvolutionFlags": NewType("MPSCNNBinaryConvolutionFlags", int),
        "MPSTriangleIntersectionTestType": NewType(
            "MPSTriangleIntersectionTestType", int
        ),
        "MPSNNConvolutionAccumulatorPrecisionOption": NewType(
            "MPSNNConvolutionAccumulatorPrecisionOption", int
        ),
        "MPSAliasingStrategy": NewType("MPSAliasingStrategy", int),
        "MPSDeviceOptions": NewType("MPSDeviceOptions", int),
        "MPSStateResourceType": NewType("MPSStateResourceType", int),
        "MPSCNNBatchNormalizationFlags": NewType("MPSCNNBatchNormalizationFlags", int),
        "MPSAccelerationStructureUsage": NewType("MPSAccelerationStructureUsage", int),
        "MPSNDArrayQuantizationScheme": NewType("MPSNDArrayQuantizationScheme", int),
        "MPSBoundingBoxIntersectionTestType": NewType(
            "MPSBoundingBoxIntersectionTestType", int
        ),
        "MPSFloatDataTypeBit": NewType("MPSFloatDataTypeBit", int),
        "MPSMatrixRandomDistribution": NewType("MPSMatrixRandomDistribution", int),
        "MPSNNComparisonType": NewType("MPSNNComparisonType", int),
        "MPSNNPaddingMethod": NewType("MPSNNPaddingMethod", int),
        "MPSAccelerationStructureStatus": NewType(
            "MPSAccelerationStructureStatus", int
        ),
        "MPSRNNSequenceDirection": NewType("MPSRNNSequenceDirection", int),
        "MPSPolygonType": NewType("MPSPolygonType", int),
        "MPSImageFeatureChannelFormat": NewType("MPSImageFeatureChannelFormat", int),
        "MPSFloatDataTypeShift": NewType("MPSFloatDataTypeShift", int),
        "MPSCNNBinaryConvolutionType": NewType("MPSCNNBinaryConvolutionType", int),
        "MPSCNNLossType": NewType("MPSCNNLossType", int),
        "MPSCNNConvolutionWeightsLayout": NewType(
            "MPSCNNConvolutionWeightsLayout", int
        ),
        "MPSAlphaType": NewType("MPSAlphaType", int),
        "MPSPurgeableState": NewType("MPSPurgeableState", int),
        "MPSCNNReductionType": NewType("MPSCNNReductionType", int),
        "MPSCNNConvolutionFlags": NewType("MPSCNNConvolutionFlags", int),
        "MPSCustomKernelIndex": NewType("MPSCustomKernelIndex", int),
        "MPSDataType": NewType("MPSDataType", int),
        "MPSRNNMatrixId": NewType("MPSRNNMatrixId", int),
        "MPSRayMaskOptions": NewType("MPSRayMaskOptions", int),
        "MPSDataLayout": NewType("MPSDataLayout", int),
        "MPSRayMaskOperator": NewType("MPSRayMaskOperator", int),
        "MPSMatrixDecompositionStatus": NewType("MPSMatrixDecompositionStatus", int),
        "MPSIntersectionType": NewType("MPSIntersectionType", int),
        "MPSDeviceCapsValues": NewType("MPSDeviceCapsValues", int),
        "MPSTemporalWeighting": NewType("MPSTemporalWeighting", int),
        "MPSTransformType": NewType("MPSTransformType", int),
        "MPSNNTrainingStyle": NewType("MPSNNTrainingStyle", int),
        "MPSCNNNeuronType": NewType("MPSCNNNeuronType", int),
        "MPSIntersectionDataType": NewType("MPSIntersectionDataType", int),
        "MPSCNNConvolutionGradientOption": NewType(
            "MPSCNNConvolutionGradientOption", int
        ),
        "MPSCNNWeightsQuantizationType": NewType("MPSCNNWeightsQuantizationType", int),
        "MPSImageEdgeMode": NewType("MPSImageEdgeMode", int),
        "MPSRNNBidirectionalCombineMode": NewType(
            "MPSRNNBidirectionalCombineMode", int
        ),
        "MPSImageType": NewType("MPSImageType", int),
        "MPSKernelOptions": NewType("MPSKernelOptions", int),
    }
)
misc.update({})
misc.update({})
functions = {
    "MPSImageBatchResourceSize": (b"Q@",),
    "MPSStateBatchSynchronize": (b"v@@",),
    "MPSStateBatchResourceSize": (b"Q@",),
    "MPSGetImageType": (b"I@",),
    "MPSSizeofMPSDataType": (b"QI",),
    "MPSFindIntegerDivisionParams": (b"{MPSIntegerDivisionParams=SSSS}S",),
    "MPSHintTemporaryMemoryHighWaterMark": (b"v@Q",),
    "MPSSupportsMTLDevice": (b"Z@",),
    "MPSGetCustomKernelBatchedSourceIndex": (b"Q{MPSCustomKernelArgumentCount=QQQ}QQ",),
    "MPSStateBatchIncrementReadCount": (b"Q@q",),
    "MPSDataTypeBitsCount": (b"QI",),
    "MPSGetCustomKernelBroadcastSourceIndex": (
        b"Q{MPSCustomKernelArgumentCount=QQQ}QQ",
    ),
    "MPSGetCustomKernelBatchedDestinationIndex": (
        b"Q{MPSCustomKernelArgumentCount=QQQ}",
    ),
    "MPSSetHeapCacheDuration": (b"v@d",),
    "MPSGetCustomKernelMaxBatchSize": (b"Q{MPSCustomKernelArgumentCount=QQQ}Q",),
    "MPSGetPreferredDevice": (b"@Q",),
    "MPSImageBatchSynchronize": (b"v@@",),
    "MPSImageBatchIncrementReadCount": (b"Q@q",),
    "MPSImageBatchIterate": (
        b"q@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                        },
                    }
                }
            }
        },
    ),
}
aliases = {
    "MPSNNPaddingMethodAlignMask": "MPSNNPaddingMethodAlign_reserved",
    "MPSPurgeableStateVolatile": "MTLPurgeableStateVolatile",
    "MPSPurgeableStateEmpty": "MTLPurgeableStateEmpty",
    "MPSPurgeableStateKeepCurrent": "MTLPurgeableStateKeepCurrent",
    "MPSDataTypeIntBit": "MPSDataTypeSignedBit",
    "MPSAliasingStrategyDontCare": "MPSAliasingStrategyDefault",
    "MPSCNNBatchNormalizationFlagsCalculateStatisticsAutomatic": "MPSCNNBatchNormalizationFlagsDefault",
    "MPSNNPaddingMethodAddRemainderToMask": "MPSNNPaddingMethodAddRemainderToBottomRight",
    "MPSPurgeableStateNonVolatile": "MTLPurgeableStateNonVolatile",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"MPSAccelerationStructure",
        b"boundingBox",
        {
            "full_signature": b"{_MPSAxisAlignedBoundingBox=<3f><3f>}@:",
            "retval": {"type": b"{_MPSAxisAlignedBoundingBox=<3f><3f>}"},
        },
    )
    r(
        b"MPSAccelerationStructure",
        b"rebuildWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"MPSBinaryImageKernel",
        b"clipRect",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSBinaryImageKernel",
        b"encodeToCommandBuffer:inPlacePrimaryTexture:secondaryTexture:fallbackCopyAllocator:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            },
        },
    )
    r(
        b"MPSBinaryImageKernel",
        b"encodeToCommandBuffer:primaryTexture:inPlaceSecondaryTexture:fallbackCopyAllocator:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            },
        },
    )
    r(
        b"MPSBinaryImageKernel",
        b"primaryOffset",
        {"retval": {"type": b"{MPSOffset=qqq}"}},
    )
    r(
        b"MPSBinaryImageKernel",
        b"primarySourceRegionForDestinationSize:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSBinaryImageKernel",
        b"secondaryOffset",
        {"retval": {"type": b"{MPSOffset=qqq}"}},
    )
    r(
        b"MPSBinaryImageKernel",
        b"secondarySourceRegionForDestinationSize:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSBinaryImageKernel",
        b"setClipRect:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSBinaryImageKernel",
        b"setPrimaryOffset:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSBinaryImageKernel",
        b"setSecondaryOffset:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSCNNAddGradient",
        b"initWithDevice:isSecondarySourceFilter:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNArithmeticGradient",
        b"initWithDevice:isSecondarySourceFilter:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNArithmeticGradient",
        b"isSecondarySourceFilter",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSCNNBatchNormalization",
        b"encodeBatchToCommandBuffer:sourceImages:destinationStates:destinationStateIsTemporary:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNBatchNormalization",
        b"encodeToCommandBuffer:sourceImage:destinationState:destinationStateIsTemporary:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNBinaryConvolution",
        b"initWithDevice:convolutionData:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
        {
            "arguments": {
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
                6: {"type_modifier": b"n", "c_array_of_variable_length": True},
                7: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSCNNBinaryConvolutionNode",
        b"initWithSource:weights:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
        {
            "arguments": {
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
                6: {"type_modifier": b"n", "c_array_of_variable_length": True},
                7: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSCNNBinaryConvolutionNode",
        b"nodeWithSource:weights:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
        {
            "arguments": {
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
                6: {"type_modifier": b"n", "c_array_of_variable_length": True},
                7: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSCNNBinaryFullyConnected",
        b"initWithDevice:convolutionData:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
        {
            "arguments": {
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
                6: {"type_modifier": b"n", "c_array_of_variable_length": True},
                7: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSCNNBinaryFullyConnectedNode",
        b"initWithSource:weights:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
        {
            "arguments": {
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
                6: {"type_modifier": b"n", "c_array_of_variable_length": True},
                7: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSCNNBinaryFullyConnectedNode",
        b"nodeWithSource:weights:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
        {
            "arguments": {
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
                6: {"type_modifier": b"n", "c_array_of_variable_length": True},
                7: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(b"MPSCNNBinaryKernel", b"appendBatchBarrier", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNBinaryKernel",
        b"clipRect",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSCNNBinaryKernel",
        b"encodeBatchToCommandBuffer:primaryImages:secondaryImages:destinationStates:destinationStateIsTemporary:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNBinaryKernel",
        b"encodeToCommandBuffer:primaryImage:secondaryImage:destinationState:destinationStateIsTemporary:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(b"MPSCNNBinaryKernel", b"isBackwards", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNBinaryKernel",
        b"isResultStateReusedAcrossBatch",
        {"retval": {"type": b"Z"}},
    )
    r(b"MPSCNNBinaryKernel", b"isStateModified", {"retval": {"type": b"Z"}})
    r(b"MPSCNNBinaryKernel", b"primaryOffset", {"retval": {"type": b"{MPSOffset=qqq}"}})
    r(
        b"MPSCNNBinaryKernel",
        b"secondaryOffset",
        {"retval": {"type": b"{MPSOffset=qqq}"}},
    )
    r(
        b"MPSCNNBinaryKernel",
        b"setClipRect:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSCNNBinaryKernel",
        b"setPrimaryOffset:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSCNNBinaryKernel",
        b"setSecondaryOffset:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSCNNConvolution",
        b"exportWeightsAndBiasesWithCommandBuffer:resultStateCanBeTemporary:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNConvolution",
        b"initWithDevice:convolutionDescriptor:kernelWeights:biasTerms:flags:",
        {
            "arguments": {
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSCNNConvolutionDescriptor",
        b"setBatchNormalizationParametersForInferenceWithMean:variance:gamma:beta:epsilon:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_of_variable_length": True},
                3: {"type_modifier": b"n", "c_array_of_variable_length": True},
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSCNNConvolutionDescriptor",
        b"supportsSecureCoding",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSCNNConvolutionGradient",
        b"serializeWeightsAndBiases",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSCNNConvolutionGradient",
        b"setSerializeWeightsAndBiases:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNConvolutionTranspose",
        b"encodeBatchToCommandBuffer:sourceImages:convolutionGradientStates:destinationStates:destinationStateIsTemporary:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNConvolutionTranspose",
        b"encodeToCommandBuffer:sourceImage:convolutionGradientState:destinationState:destinationStateIsTemporary:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNConvolutionTranspose",
        b"exportWeightsAndBiasesWithCommandBuffer:resultStateCanBeTemporary:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNDropout",
        b"initWithDevice:keepProbability:seed:maskStrideInPixels:",
        {"arguments": {5: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(b"MPSCNNDropout", b"maskStrideInPixels", {"retval": {"type": b"{MTLSize=QQQ}"}})
    r(
        b"MPSCNNDropoutGradient",
        b"initWithDevice:keepProbability:seed:maskStrideInPixels:",
        {"arguments": {5: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSCNNDropoutGradient",
        b"maskStrideInPixels",
        {"retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"MPSCNNDropoutGradientNode",
        b"initWithSourceGradient:sourceImage:gradientState:keepProbability:seed:maskStrideInPixels:",
        {"arguments": {7: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSCNNDropoutGradientNode",
        b"maskStrideInPixels",
        {"retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"MPSCNNDropoutGradientNode",
        b"nodeWithSourceGradient:sourceImage:gradientState:keepProbability:seed:maskStrideInPixels:",
        {"arguments": {7: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSCNNDropoutNode",
        b"initWithSource:keepProbability:seed:maskStrideInPixels:",
        {"arguments": {5: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSCNNDropoutNode",
        b"maskStrideInPixels",
        {"retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"MPSCNNDropoutNode",
        b"nodeWithSource:keepProbability:seed:maskStrideInPixels:",
        {"arguments": {5: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSCNNFullyConnected",
        b"initWithDevice:convolutionDescriptor:kernelWeights:biasTerms:flags:",
        {
            "arguments": {
                4: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(b"MPSCNNKernel", b"appendBatchBarrier", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNKernel",
        b"clipRect",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSCNNKernel",
        b"encodeBatchToCommandBuffer:sourceImages:destinationStates:destinationStateIsTemporary:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNKernel",
        b"encodeToCommandBuffer:sourceImage:destinationState:destinationStateIsTemporary:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(b"MPSCNNKernel", b"isBackwards", {"retval": {"type": b"Z"}})
    r(b"MPSCNNKernel", b"isResultStateReusedAcrossBatch", {"retval": {"type": b"Z"}})
    r(b"MPSCNNKernel", b"isStateModified", {"retval": {"type": b"Z"}})
    r(b"MPSCNNKernel", b"offset", {"retval": {"type": b"{MPSOffset=qqq}"}})
    r(
        b"MPSCNNKernel",
        b"setClipRect:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(b"MPSCNNKernel", b"setOffset:", {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}})
    r(b"MPSCNNLoss", b"reduceAcrossBatch", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNLossDataDescriptor",
        b"cnnLossDataDescriptorWithData:layout:size:",
        {"arguments": {4: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(b"MPSCNNLossDataDescriptor", b"size", {"retval": {"type": b"{MTLSize=QQQ}"}})
    r(b"MPSCNNLossDescriptor", b"reduceAcrossBatch", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNLossDescriptor",
        b"setReduceAcrossBatch:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNLossLabels",
        b"initWithDevice:lossImageSize:labelsDescriptor:weightsDescriptor:",
        {"arguments": {3: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSCNNLossLabels",
        b"initWithDevice:lossImageSize:labelsImage:weightsImage:",
        {"arguments": {3: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(b"MPSCNNMultiaryKernel", b"appendBatchBarrier", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNMultiaryKernel",
        b"clipRect",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSCNNMultiaryKernel",
        b"encodeBatchToCommandBuffer:sourceImages:destinationStates:destinationStateIsTemporary:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNMultiaryKernel",
        b"encodeToCommandBuffer:sourceImages:destinationState:destinationStateIsTemporary:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(b"MPSCNNMultiaryKernel", b"isBackwards", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNMultiaryKernel",
        b"isResultStateReusedAcrossBatch",
        {"retval": {"type": b"Z"}},
    )
    r(b"MPSCNNMultiaryKernel", b"isStateModified", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNMultiaryKernel",
        b"offsetAtIndex:",
        {"retval": {"type": b"{MPSOffset=qqq}"}},
    )
    r(
        b"MPSCNNMultiaryKernel",
        b"setClipRect:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSCNNMultiaryKernel",
        b"setOffset:atIndex:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSCNNMultiplyGradient",
        b"initWithDevice:isSecondarySourceFilter:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNPoolingGradient",
        b"setSourceSize:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(b"MPSCNNPoolingGradient", b"sourceSize", {"retval": {"type": b"{MTLSize=QQQ}"}})
    r(
        b"MPSCNNSubtractGradient",
        b"initWithDevice:isSecondarySourceFilter:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"MPSCNNUpsampling", b"alignCorners", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNUpsamplingBilinear",
        b"initWithDevice:integerScaleFactorX:integerScaleFactorY:alignCorners:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(b"MPSCNNUpsamplingBilinearNode", b"alignCorners", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNUpsamplingBilinearNode",
        b"initWithSource:integerScaleFactorX:integerScaleFactorY:alignCorners:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSCNNUpsamplingBilinearNode",
        b"nodeWithSource:integerScaleFactorX:integerScaleFactorY:alignCorners:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(b"MPSCNNYOLOLoss", b"reduceAcrossBatch", {"retval": {"type": b"Z"}})
    r(b"MPSCNNYOLOLossDescriptor", b"reduceAcrossBatch", {"retval": {"type": b"Z"}})
    r(b"MPSCNNYOLOLossDescriptor", b"rescore", {"retval": {"type": b"Z"}})
    r(
        b"MPSCNNYOLOLossDescriptor",
        b"setReduceAcrossBatch:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MPSCNNYOLOLossDescriptor", b"setRescore:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MPSGRUDescriptor", b"flipOutputGates", {"retval": {"type": b"Z"}})
    r(b"MPSGRUDescriptor", b"setFlipOutputGates:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MPSImage",
        b"readBytes:dataLayout:bytesPerRow:bytesPerImage:region:featureChannelInfo:imageIndex:",
        {
            "arguments": {
                2: {"type_modifier": b"o", "c_array_of_variable_length": True},
                6: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                7: {"type": b"{MPSImageReadWriteParams=QQ}"},
            }
        },
    )
    r(
        b"MPSImage",
        b"readBytes:dataLayout:bytesPerRow:region:featureChannelInfo:imageIndex:",
        {
            "arguments": {
                2: {"type_modifier": b"o", "c_array_of_variable_length": True},
                5: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                6: {"type": b"{MPSImageReadWriteParams=QQ}"},
            }
        },
    )
    r(
        b"MPSImage",
        b"readBytes:dataLayout:imageIndex:",
        {"arguments": {2: {"type_modifier": b"o", "c_array_of_variable_length": True}}},
    )
    r(
        b"MPSImage",
        b"writeBytes:dataLayout:bytesPerColumn:bytesPerRow:bytesPerImage:region:featureChannelInfo:imageIndex:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_of_variable_length": True},
                7: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                8: {"type": b"{MPSImageReadWriteParams=QQ}"},
            }
        },
    )
    r(
        b"MPSImage",
        b"writeBytes:dataLayout:bytesPerRow:bytesPerImage:region:featureChannelInfo:imageIndex:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_of_variable_length": True},
                6: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                7: {"type": b"{MPSImageReadWriteParams=QQ}"},
            }
        },
    )
    r(
        b"MPSImage",
        b"writeBytes:dataLayout:bytesPerRow:region:featureChannelInfo:imageIndex:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_of_variable_length": True},
                5: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                6: {"type": b"{MPSImageReadWriteParams=QQ}"},
            }
        },
    )
    r(
        b"MPSImage",
        b"writeBytes:dataLayout:imageIndex:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_of_variable_length": True}}},
    )
    r(
        b"MPSImageArithmetic",
        b"primaryStrideInPixels",
        {"retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"MPSImageArithmetic",
        b"secondaryStrideInPixels",
        {"retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"MPSImageArithmetic",
        b"setPrimaryStrideInPixels:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSImageArithmetic",
        b"setSecondaryStrideInPixels:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(b"MPSImageCanny", b"colorTransform", {"retval": {"c_array_of_fixed_length": 3}})
    r(
        b"MPSImageCanny",
        b"initWithDevice:linearToGrayScaleTransform:sigma:",
        {"arguments": {3: {"c_array_of_fixed_length": 3, "type_modifier": b"n"}}},
    )
    r(b"MPSImageCanny", b"setUseFastMode:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MPSImageCanny", b"useFastMode", {"retval": {"type": b"Z"}})
    r(
        b"MPSImageConvolution",
        b"initWithDevice:kernelWidth:kernelHeight:weights:",
        {"arguments": {5: {"type_modifier": b"n", "c_array_of_variable_length": True}}},
    )
    r(
        b"MPSImageCopyToMatrix",
        b"destinationMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSImageCopyToMatrix",
        b"setDestinationMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSImageDilate",
        b"initWithDevice:kernelWidth:kernelHeight:values:",
        {"arguments": {5: {"type_modifier": b"n", "c_array_of_variable_length": True}}},
    )
    r(
        b"MPSImageEDLines",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSImageEDLines",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSImageFindKeypoints",
        b"encodeToCommandBuffer:sourceTexture:regions:numberOfRegions:keypointCountBuffer:keypointCountBufferOffset:keypointDataBuffer:keypointDataBufferOffset:",
        {"arguments": {4: {"type": b"^{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSImageFindKeypoints",
        b"initWithDevice:info:",
        {
            "arguments": {
                3: {"type": b"^{MPSImageKeypointRangeInfo=Qf}", "type_modifier": b"n"}
            }
        },
    )
    r(
        b"MPSImageFindKeypoints",
        b"keypointRangeInfo",
        {"retval": {"type": b"{MPSImageKeypointRangeInfo=Qf}"}},
    )
    r(
        b"MPSImageHistogram",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSImageHistogram",
        b"histogramInfo",
        {
            "full_signature": b"{MPSImageHistogramInfo=QZ<4f><4f>}@:",
            "retval": {"type": b"{MPSImageHistogramInfo=QZ<4f><4f>}"},
        },
    )
    r(
        b"MPSImageHistogram",
        b"initWithDevice:histogramInfo:",
        {
            "full_signature": b"@@:@^{MPSImageHistogramInfo=QZ<4f><4f>}",
            "arguments": {
                3: {
                    "type": b"^{MPSImageHistogramInfo=QZ<4f><4f>}",
                    "type_modifier": b"n",
                }
            },
        },
    )
    r(
        b"MPSImageHistogram",
        b"minPixelThresholdValue",
        {"full_signature": b"<4f>@:", "retval": {"type": b"<4f>"}},
    )
    r(
        b"MPSImageHistogram",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSImageHistogram",
        b"setMinPixelThresholdValue:",
        {"full_signature": b"v@:<4f>", "arguments": {2: {"type": b"<4f>"}}},
    )
    r(b"MPSImageHistogram", b"setZeroHistogram:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MPSImageHistogram", b"zeroHistogram", {"retval": {"type": b"Z"}})
    r(
        b"MPSImageHistogramEqualization",
        b"histogramInfo",
        {
            "full_signature": b"{MPSImageHistogramInfo=QZ<4f><4f>}@:",
            "retval": {"type": b"{MPSImageHistogramInfo=QZ<4f><4f>}"},
        },
    )
    r(
        b"MPSImageHistogramEqualization",
        b"initWithDevice:histogramInfo:",
        {
            "full_signature": b"@@:@^{MPSImageHistogramInfo=QZ<4f><4f>}",
            "arguments": {
                3: {
                    "type": b"^{MPSImageHistogramInfo=QZ<4f><4f>}",
                    "type_modifier": b"n",
                }
            },
        },
    )
    r(
        b"MPSImageHistogramSpecification",
        b"histogramInfo",
        {
            "full_signature": b"{MPSImageHistogramInfo=QZ<4f><4f>}@:",
            "retval": {"type": b"{MPSImageHistogramInfo=QZ<4f><4f>}"},
        },
    )
    r(
        b"MPSImageHistogramSpecification",
        b"initWithDevice:histogramInfo:",
        {
            "full_signature": b"@@:@^{MPSImageHistogramInfo=QZ<4f><4f>}",
            "arguments": {
                3: {
                    "type": b"^{MPSImageHistogramInfo=QZ<4f><4f>}",
                    "type_modifier": b"n",
                }
            },
        },
    )
    r(
        b"MPSImageNormalizedHistogram",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSImageNormalizedHistogram",
        b"histogramInfo",
        {
            "full_signature": b"{MPSImageHistogramInfo=QZ<4f><4f>}@:",
            "retval": {"type": b"{MPSImageHistogramInfo=QZ<4f><4f>}"},
        },
    )
    r(
        b"MPSImageNormalizedHistogram",
        b"initWithDevice:histogramInfo:",
        {
            "full_signature": b"@@:@^{MPSImageHistogramInfo=QZ<4f><4f>}",
            "arguments": {
                3: {
                    "type": b"^{MPSImageHistogramInfo=QZ<4f><4f>}",
                    "type_modifier": b"n",
                }
            },
        },
    )
    r(
        b"MPSImageNormalizedHistogram",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSImageNormalizedHistogram",
        b"setZeroHistogram:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MPSImageNormalizedHistogram", b"zeroHistogram", {"retval": {"type": b"Z"}})
    r(
        b"MPSImagePyramid",
        b"initWithDevice:kernelWidth:kernelHeight:weights:",
        {"arguments": {5: {"type_modifier": b"n", "c_array_of_variable_length": True}}},
    )
    r(
        b"MPSImageReduceUnary",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSImageReduceUnary",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSImageSobel",
        b"initWithDevice:linearGrayColorTransform:",
        {"arguments": {3: {"c_array_of_fixed_length": 3, "type_modifier": b"n"}}},
    )
    r(
        b"MPSImageStatisticsMean",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSImageStatisticsMean",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSImageStatisticsMeanAndVariance",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSImageStatisticsMeanAndVariance",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSImageStatisticsMinAndMax",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSImageStatisticsMinAndMax",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSImageThresholdBinary",
        b"initWithDevice:thresholdValue:maximumValue:linearGrayColorTransform:",
        {"arguments": {5: {"c_array_of_fixed_length": 3, "type_modifier": b"n"}}},
    )
    r(
        b"MPSImageThresholdBinaryInverse",
        b"initWithDevice:thresholdValue:maximumValue:linearGrayColorTransform:",
        {"arguments": {5: {"c_array_of_fixed_length": 3, "type_modifier": b"n"}}},
    )
    r(
        b"MPSImageThresholdToZero",
        b"initWithDevice:thresholdValue:linearGrayColorTransform:",
        {"arguments": {4: {"c_array_of_fixed_length": 3, "type_modifier": b"n"}}},
    )
    r(
        b"MPSImageThresholdToZeroInverse",
        b"initWithDevice:thresholdValue:linearGrayColorTransform:",
        {"arguments": {4: {"c_array_of_fixed_length": 3, "type_modifier": b"n"}}},
    )
    r(
        b"MPSImageThresholdTruncate",
        b"initWithDevice:thresholdValue:linearGrayColorTransform:",
        {"arguments": {4: {"c_array_of_fixed_length": 3, "type_modifier": b"n"}}},
    )
    r(
        b"MPSKeyedUnarchiver",
        b"initForReadingFromData:device:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"MPSKeyedUnarchiver",
        b"initForReadingFromData:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"MPSKeyedUnarchiver",
        b"unarchiveTopLevelObjectWithData:device:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"MPSKeyedUnarchiver",
        b"unarchiveTopLevelObjectWithData:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"MPSKeyedUnarchiver",
        b"unarchivedObjectOfClass:fromData:device:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"MPSKeyedUnarchiver",
        b"unarchivedObjectOfClass:fromData:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"MPSKeyedUnarchiver",
        b"unarchivedObjectOfClasses:fromData:device:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"MPSKeyedUnarchiver",
        b"unarchivedObjectOfClasses:fromData:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"MPSLSTMDescriptor", b"memoryWeightsAreDiagonal", {"retval": {"type": b"Z"}})
    r(
        b"MPSLSTMDescriptor",
        b"setMemoryWeightsAreDiagonal:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MPSMatrixBatchNormalization", b"computeStatistics", {"retval": {"type": b"Z"}})
    r(
        b"MPSMatrixBatchNormalization",
        b"setComputeStatistics:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSMatrixBinaryKernel",
        b"primarySourceMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixBinaryKernel",
        b"resultMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixBinaryKernel",
        b"secondarySourceMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixBinaryKernel",
        b"setPrimarySourceMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSMatrixBinaryKernel",
        b"setResultMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSMatrixBinaryKernel",
        b"setSecondarySourceMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(b"MPSMatrixCopy", b"destinationsAreTransposed", {"retval": {"type": b"Z"}})
    r(
        b"MPSMatrixCopy",
        b"initWithDevice:copyRows:copyColumns:sourcesAreTransposed:destinationsAreTransposed:",
        {"arguments": {5: {"type": b"Z"}, 6: {"type": b"Z"}}},
    )
    r(b"MPSMatrixCopy", b"sourcesAreTransposed", {"retval": {"type": b"Z"}})
    r(
        b"MPSMatrixCopyDescriptor",
        b"descriptorWithSourceMatrix:destinationMatrix:offsets:",
        {"arguments": {4: {"type": b"{MPSMatrixCopyOffsets=IIII}"}}},
    )
    r(
        b"MPSMatrixCopyDescriptor",
        b"setCopyOperationAtIndex:sourceMatrix:destinationMatrix:offsets:",
        {"arguments": {5: {"type": b"{MPSMatrixCopyOffsets=IIII}"}}},
    )
    r(
        b"MPSMatrixCopyToImage",
        b"setSourceMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSMatrixCopyToImage",
        b"sourceMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixDecompositionCholesky",
        b"initWithDevice:lower:order:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSMatrixMultiplication",
        b"initWithDevice:transposeLeft:transposeRight:resultRows:resultColumns:interiorColumns:alpha:beta:",
        {"arguments": {3: {"type": b"Z"}, 4: {"type": b"Z"}}},
    )
    r(
        b"MPSMatrixMultiplication",
        b"leftMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixMultiplication",
        b"resultMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixMultiplication",
        b"rightMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixMultiplication",
        b"setLeftMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSMatrixMultiplication",
        b"setResultMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSMatrixMultiplication",
        b"setRightMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSMatrixSolveCholesky",
        b"initWithDevice:upper:order:numberOfRightHandSides:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSMatrixSolveLU",
        b"initWithDevice:transpose:order:numberOfRightHandSides:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSMatrixSolveTriangular",
        b"initWithDevice:right:upper:transpose:unit:order:numberOfRightHandSides:alpha:",
        {
            "arguments": {
                3: {"type": b"Z"},
                4: {"type": b"Z"},
                5: {"type": b"Z"},
                6: {"type": b"Z"},
            }
        },
    )
    r(
        b"MPSMatrixSum",
        b"initWithDevice:count:rows:columns:transpose:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(b"MPSMatrixSum", b"resultMatrixOrigin", {"retval": {"type": b"{MTLOrigin=QQQ}"}})
    r(
        b"MPSMatrixSum",
        b"setResultMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(b"MPSMatrixSum", b"transpose", {"retval": {"type": b"Z"}})
    r(
        b"MPSMatrixUnaryKernel",
        b"resultMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixUnaryKernel",
        b"setResultMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSMatrixUnaryKernel",
        b"setSourceMatrixOrigin:",
        {"arguments": {2: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSMatrixUnaryKernel",
        b"sourceMatrixOrigin",
        {"retval": {"type": b"{MTLOrigin=QQQ}"}},
    )
    r(
        b"MPSMatrixVectorMultiplication",
        b"initWithDevice:transpose:rows:columns:alpha:beta:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MPSNDArray",
        b"arrayViewWithDimensionCount:dimensionSizes:strides:",
        {
            "arguments": {
                3: {"type_modifier": b"n", "c_array_length_in_arg": 2},
                4: {"type_modifier": b"n", "c_array_length_in_arg": 2},
            }
        },
    )
    r(
        b"MPSNDArray",
        b"exportDataWithCommandBuffer:toBuffer:destinationDataType:offset:rowStrides:",
        {"arguments": {6: {"type_modifier": b"n", "c_array_of_variable_length": True}}},
    )
    r(
        b"MPSNDArray",
        b"importDataWithCommandBuffer:fromBuffer:sourceDataType:offset:rowStrides:",
        {"arguments": {6: {"type_modifier": b"n", "c_array_of_variable_length": True}}},
    )
    r(
        b"MPSNDArray",
        b"readBytes:strideBytes:",
        {
            "arguments": {
                2: {"type_modifier": b"o", "c_array_of_variable_length": True},
                3: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSNDArray",
        b"writeBytes:strideBytes:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_of_variable_length": True},
                3: {"type_modifier": b"n", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"MPSNDArrayAffineQuantizationDescriptor",
        b"hasMinValue",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSNDArrayAffineQuantizationDescriptor",
        b"hasZeroPoint",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSNDArrayAffineQuantizationDescriptor",
        b"initWithDataType:hasZeroPoint:hasMinValue:",
        {"arguments": {3: {"type": b"Z"}, 4: {"type": b"Z"}}},
    )
    r(
        b"MPSNDArrayAffineQuantizationDescriptor",
        b"setHasMinValue:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSNDArrayAffineQuantizationDescriptor",
        b"setHasZeroPoint:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"encodeToCommandBuffer:primarySourceArray:secondarySourceArray:resultState:outputStateIsTemporary:",
        {"arguments": {6: {"type": b"Z"}}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"primaryDilationRates",
        {"retval": {"type": b"{MPSNDArraySizes=[16Q]}"}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"primaryKernelSizes",
        {"retval": {"type": b"{MPSNDArraySizes=[16Q]}"}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"primaryOffsets",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"primaryStrides",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"secondaryDilationRates",
        {"retval": {"type": b"{MPSNDArraySizes=[16Q]}"}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"secondaryKernelSizes",
        {"retval": {"type": b"{MPSNDArraySizes=[16Q]}"}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"secondaryOffsets",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNDArrayBinaryKernel",
        b"secondaryStrides",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNDArrayDescriptor",
        b"descriptorWithDataType:dimensionCount:dimensionSizes:",
        {"arguments": {4: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"MPSNDArrayDescriptor",
        b"descriptorWithDataType:dimensionSizes:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"MPSNDArrayDescriptor",
        b"dimensionOrder",
        {"full_signature": b"<16C>@:", "retval": {"type": b"<16C>"}},
    )
    r(
        b"MPSNDArrayDescriptor",
        b"permuteWithDimensionOrder:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_of_variable_length": True}}},
    )
    r(b"MPSNDArrayDescriptor", b"preferPackedRows", {"retval": {"type": b"Z"}})
    r(
        b"MPSNDArrayDescriptor",
        b"reshapeWithDimensionCount:dimensionSizes:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 2}}},
    )
    r(
        b"MPSNDArrayDescriptor",
        b"setPreferPackedRows:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSNDArrayIdentity",
        b"reshapeWithCommandBuffer:sourceArray:dimensionCount:dimensionSizes:destinationArray:",
        {"arguments": {5: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"MPSNDArrayIdentity",
        b"reshapeWithCommandEncoder:commandBuffer:sourceArray:dimensionCount:dimensionSizes:destinationArray:",
        {"arguments": {6: {"type_modifier": b"n", "c_array_length_in_arg": 5}}},
    )
    r(
        b"MPSNDArrayMultiaryBase",
        b"dilationRatesForSourceIndex:",
        {"retval": {"type": b"{MPSNDArraySizes=[16Q]}"}},
    )
    r(
        b"MPSNDArrayMultiaryBase",
        b"kernelSizesForSourceIndex:",
        {"retval": {"type": b"{MPSNDArraySizes=[16Q]}"}},
    )
    r(
        b"MPSNDArrayMultiaryBase",
        b"offsetsAtSourceIndex:",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNDArrayMultiaryBase",
        b"stridesForSourceIndex:",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNDArrayMultiaryKernel",
        b"encodeToCommandBuffer:sourceArrays:resultState:outputStateIsTemporary:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSNDArrayStridedSlice",
        b"setStrides:",
        {"arguments": {2: {"type": b"{MPSNDArrayOffsets=[16q]}"}}},
    )
    r(
        b"MPSNDArrayStridedSlice",
        b"strides",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNDArrayUnaryKernel",
        b"dilationRates",
        {"retval": {"type": b"{MPSNDArraySizes=[16Q]}"}},
    )
    r(
        b"MPSNDArrayUnaryKernel",
        b"encodeToCommandBuffer:sourceArray:resultState:outputStateIsTemporary:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSNDArrayUnaryKernel",
        b"kernelSizes",
        {"retval": {"type": b"{MPSNDArraySizes=[16Q]}"}},
    )
    r(
        b"MPSNDArrayUnaryKernel",
        b"offsets",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNDArrayUnaryKernel",
        b"strides",
        {"retval": {"type": b"{MPSNDArrayOffsets=[16q]}"}},
    )
    r(
        b"MPSNNArithmeticGradientNode",
        b"initWithGradientImages:forwardFilter:isSecondarySourceFilter:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MPSNNArithmeticGradientNode",
        b"initWithSourceGradient:sourceImage:gradientState:isSecondarySourceFilter:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSNNArithmeticGradientNode",
        b"isSecondarySourceFilter",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSNNArithmeticGradientNode",
        b"nodeWithSourceGradient:sourceImage:gradientState:isSecondarySourceFilter:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSNNFilterNode",
        b"trainingGraphWithSourceGradient:nodeHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"MPSNNForwardLoss",
        b"encodeBatchToCommandBuffer:sourceImages:labels:weights:destinationStates:destinationStateIsTemporary:",
        {"arguments": {7: {"type": b"Z"}}},
    )
    r(b"MPSNNForwardLoss", b"reduceAcrossBatch", {"retval": {"type": b"Z"}})
    r(b"MPSNNForwardLossNode", b"reduceAcrossBatch", {"retval": {"type": b"Z"}})
    r(
        b"MPSNNGraph",
        b"executeAsyncWithSourceImages:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"MPSNNGraph",
        b"graphWithDevice:resultImage:resultImageIsNeeded:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MPSNNGraph",
        b"graphWithDevice:resultImages:resultsAreNeeded:",
        {
            "arguments": {
                4: {
                    "type": b"^Z",
                    "type_modifier": b"o",
                    "c_array_of_variable_length": True,
                }
            }
        },
    )
    r(
        b"MPSNNGraph",
        b"initWithDevice:resultImage:resultImageIsNeeded:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MPSNNGraph",
        b"initWithDevice:resultImages:resultsAreNeeded:",
        {
            "arguments": {
                4: {
                    "type": b"^Z",
                    "type_modifier": b"o",
                    "c_array_of_variable_length": True,
                }
            }
        },
    )
    r(b"MPSNNGraph", b"outputStateIsTemporary", {"retval": {"type": b"Z"}})
    r(b"MPSNNGraph", b"resultImageIsNeeded", {"retval": {"type": b"Z"}})
    r(b"MPSNNGraph", b"setOutputStateIsTemporary:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MPSNNGridSample",
        b"setUseGridValueAsInputCoordinate:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MPSNNGridSample", b"useGridValueAsInputCoordinate", {"retval": {"type": b"Z"}})
    r(b"MPSNNImageNode", b"exportFromGraph", {"retval": {"type": b"Z"}})
    r(b"MPSNNImageNode", b"setExportFromGraph:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MPSNNImageNode", b"setStopGradient:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MPSNNImageNode", b"setSynchronizeResource:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MPSNNImageNode", b"stopGradient", {"retval": {"type": b"Z"}})
    r(b"MPSNNImageNode", b"synchronizeResource", {"retval": {"type": b"Z"}})
    r(b"MPSNNLossGradient", b"computeLabelGradients", {"retval": {"type": b"Z"}})
    r(b"MPSNNLossGradient", b"reduceAcrossBatch", {"retval": {"type": b"Z"}})
    r(
        b"MPSNNLossGradient",
        b"setComputeLabelGradients:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSNNLossGradientNode",
        b"initWithSourceGradient:sourceImage:labels:gradientState:lossDescriptor:isLabelsGradientFilter:",
        {"arguments": {7: {"type": b"Z"}}},
    )
    r(
        b"MPSNNLossGradientNode",
        b"initWithSourceGradient:sourceImage:labels:weights:gradientState:lossDescriptor:isLabelsGradientFilter:",
        {"arguments": {8: {"type": b"Z"}}},
    )
    r(
        b"MPSNNLossGradientNode",
        b"initWithSources:gradientState:lossDescriptor:isLabelsGradientFilter:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(b"MPSNNLossGradientNode", b"isLabelsGradientFilter", {"retval": {"type": b"Z"}})
    r(
        b"MPSNNLossGradientNode",
        b"nodeWithSourceGradient:sourceImage:labels:gradientState:lossDescriptor:isLabelsGradientFilter:",
        {"arguments": {7: {"type": b"Z"}}},
    )
    r(
        b"MPSNNLossGradientNode",
        b"nodeWithSourceGradient:sourceImage:labels:weights:gradientState:lossDescriptor:isLabelsGradientFilter:",
        {"arguments": {8: {"type": b"Z"}}},
    )
    r(
        b"MPSNNLossGradientNode",
        b"nodeWithSources:gradientState:lossDescriptor:isLabelsGradientFilter:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(b"MPSNNLossGradientNode", b"reduceAcrossBatch", {"retval": {"type": b"Z"}})
    r(b"MPSNNOptimizer", b"applyGradientClipping", {"retval": {"type": b"Z"}})
    r(
        b"MPSNNOptimizer",
        b"setApplyGradientClipping:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MPSNNOptimizerDescriptor", b"applyGradientClipping", {"retval": {"type": b"Z"}})
    r(
        b"MPSNNOptimizerDescriptor",
        b"initWithLearningRate:gradientRescale:applyGradientClipping:gradientClipMax:gradientClipMin:regularizationType:regularizationScale:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MPSNNOptimizerDescriptor",
        b"optimizerDescriptorWithLearningRate:gradientRescale:applyGradientClipping:gradientClipMax:gradientClipMin:regularizationType:regularizationScale:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MPSNNOptimizerDescriptor",
        b"setApplyGradientClipping:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSNNOptimizerStochasticGradientDescent",
        b"initWithDevice:momentumScale:useNesterovMomentum:optimizerDescriptor:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MPSNNOptimizerStochasticGradientDescent",
        b"initWithDevice:momentumScale:useNestrovMomentum:optimizerDescriptor:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"MPSNNOptimizerStochasticGradientDescent",
        b"useNesterovMomentum",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSNNOptimizerStochasticGradientDescent",
        b"useNestrovMomentum",
        {"retval": {"type": b"Z"}},
    )
    r(b"MPSNNReduceBinary", b"primaryOffset", {"retval": {"type": b"{MPSOffset=qqq}"}})
    r(
        b"MPSNNReduceBinary",
        b"primarySourceClipRect",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSNNReduceBinary",
        b"secondaryOffset",
        {"retval": {"type": b"{MPSOffset=qqq}"}},
    )
    r(
        b"MPSNNReduceBinary",
        b"secondarySourceClipRect",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSNNReduceBinary",
        b"setPrimaryOffset:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSNNReduceBinary",
        b"setPrimarySourceClipRect:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSNNReduceBinary",
        b"setSecondaryOffset:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSNNReduceBinary",
        b"setSecondarySourceClipRect:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSNNReduceUnary",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(b"MPSNNReduceUnary", b"offset", {"retval": {"type": b"{MPSOffset=qqq}"}})
    r(
        b"MPSNNReduceUnary",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSNNReduceUnary",
        b"setOffset:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSNNReshape",
        b"encodeBatchToCommandBuffer:sourceImages:destinationStates:destinationStateIsTemporary:reshapedWidth:reshapedHeight:reshapedFeatureChannels:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSNNReshape",
        b"encodeToCommandBuffer:sourceImage:destinationState:destinationStateIsTemporary:reshapedWidth:reshapedHeight:reshapedFeatureChannels:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(b"MPSNNResizeBilinear", b"alignCorners", {"retval": {"type": b"Z"}})
    r(
        b"MPSNNResizeBilinear",
        b"initWithDevice:resizeWidth:resizeHeight:alignCorners:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MPSNNScaleNode",
        b"initWithSource:outputSize:",
        {"arguments": {3: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSNNScaleNode",
        b"initWithSource:transformProvider:outputSize:",
        {"arguments": {4: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSNNScaleNode",
        b"nodeWithSource:outputSize:",
        {"arguments": {3: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MPSNNScaleNode",
        b"nodeWithSource:transformProvider:outputSize:",
        {"arguments": {4: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(b"MPSNNStateNode", b"exportFromGraph", {"retval": {"type": b"Z"}})
    r(b"MPSNNStateNode", b"setExportFromGraph:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MPSNNStateNode", b"setSynchronizeResource:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MPSNNStateNode", b"synchronizeResource", {"retval": {"type": b"Z"}})
    r(
        b"MPSNNUnaryReductionNode",
        b"clipRectSource",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSNNUnaryReductionNode",
        b"setClipRectSource:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(b"MPSRNNDescriptor", b"setUseFloat32Weights:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MPSRNNDescriptor",
        b"setUseLayerInputUnitTransformMode:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MPSRNNDescriptor", b"useFloat32Weights", {"retval": {"type": b"Z"}})
    r(
        b"MPSRNNDescriptor",
        b"useLayerInputUnitTransformMode",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSRNNImageInferenceLayer",
        b"recurrentOutputIsTemporary",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSRNNImageInferenceLayer",
        b"setRecurrentOutputIsTemporary:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSRNNImageInferenceLayer",
        b"setStoreAllIntermediateStates:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSRNNImageInferenceLayer",
        b"storeAllIntermediateStates",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSRNNMatrixInferenceLayer",
        b"recurrentOutputIsTemporary",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSRNNMatrixInferenceLayer",
        b"setRecurrentOutputIsTemporary:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSRNNMatrixInferenceLayer",
        b"setStoreAllIntermediateStates:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSRNNMatrixInferenceLayer",
        b"storeAllIntermediateStates",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"accumulateWeightGradients",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"encodeCopyWeightsToCommandBuffer:weights:matrixId:matrix:copyFromWeightsToMatrix:matrixOffset:",
        {"arguments": {6: {"type": b"Z"}, 7: {"type": b"{MTLOrigin=QQQ}"}}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"recurrentOutputIsTemporary",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"setAccumulateWeightGradients:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"setRecurrentOutputIsTemporary:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"setStoreAllIntermediateStates:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"setTrainingStateIsTemporary:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"storeAllIntermediateStates",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MPSRNNMatrixTrainingLayer",
        b"trainingStateIsTemporary",
        {"retval": {"type": b"Z"}},
    )
    r(b"MPSState", b"isTemporary", {"retval": {"type": b"Z"}})
    r(
        b"MPSState",
        b"resourceAtIndex:allocateMemory:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"MPSStateResourceList", b"resourceListWithBufferSizes:", {"variadic": True})
    r(
        b"MPSStateResourceList",
        b"resourceListWithTextureDescriptors:",
        {"variadic": True},
    )
    r(
        b"MPSUnaryImageKernel",
        b"clipRect",
        {"retval": {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
    )
    r(
        b"MPSUnaryImageKernel",
        b"encodeToCommandBuffer:inPlaceTexture:fallbackCopyAllocator:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            },
        },
    )
    r(b"MPSUnaryImageKernel", b"offset", {"retval": {"type": b"{MPSOffset=qqq}"}})
    r(
        b"MPSUnaryImageKernel",
        b"setClipRect:",
        {"arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}}},
    )
    r(
        b"MPSUnaryImageKernel",
        b"setOffset:",
        {"arguments": {2: {"type": b"{MPSOffset=qqq}"}}},
    )
    r(
        b"MPSUnaryImageKernel",
        b"sourceRegionForDestinationSize:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"NSObject",
        b"alphaForSourceImage:destinationImage:",
        {
            "required": True,
            "retval": {"type": b"f"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"arrayForCommandBuffer:arrayDescriptor:kernel:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"beta",
        {
            "required": True,
            "retval": {"type": b"^f", "c_array_of_variable_length": True},
        },
    )
    r(
        b"NSObject",
        b"biasTerms",
        {
            "required": True,
            "retval": {"type": b"^f", "c_array_of_variable_length": True},
        },
    )
    r(
        b"NSObject",
        b"copyWithZone:device:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"^{_NSZone=}"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"dataType", {"required": True, "retval": {"type": b"I"}})
    r(b"NSObject", b"descriptor", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"destinationImageDescriptorForSourceImages:sourceStates:forKernel:suggestedDescriptor:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"encodeWithCoder:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"epsilon", {"required": False, "retval": {"type": b"f"}})
    r(
        b"NSObject",
        b"gamma",
        {
            "required": True,
            "retval": {"type": b"^f", "c_array_of_variable_length": True},
        },
    )
    r(
        b"NSObject",
        b"imageBatchForCommandBuffer:imageDescriptor:kernel:count:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"imageForCommandBuffer:imageDescriptor:kernel:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"initWithCoder:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"inverse", {"required": False, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"kernelWeightsDataType",
        {"required": False, "retval": {"type": b"I"}},
    )
    r(b"NSObject", b"label", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"load", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"lookupTableForUInt8Kernel",
        {"required": False, "retval": {"type": b"^f"}},
    )
    r(
        b"NSObject",
        b"mean",
        {
            "required": True,
            "retval": {"type": b"^f", "c_array_of_variable_length": True},
        },
    )
    r(b"NSObject", b"mpsMTLDevice", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"newHeapWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"numberOfFeatureChannels",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(b"NSObject", b"numberOfGroups", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"paddingMethod", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"purge", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"rangesForUInt8Kernel",
        {
            "full_signature": b"^<2f>@:",
            "required": False,
            "retval": {"type": b"^<2f>", "c_array_of_variable_length": True},
        },
    )
    r(
        b"NSObject",
        b"retireHeap:cacheDelay:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"d"}},
        },
    )
    r(
        b"NSObject",
        b"returnTexture:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"scalarWeightForSourceImage:destinationImage:",
        {
            "required": True,
            "retval": {"type": b"f"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"setNumberOfGroups:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setTrainingStyle:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(b"NSObject", b"sourceHeight", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"sourceWidth", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"supportsSecureCoding",
        {"required": False, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"textureWithPixelFormat:width:height:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"trainingStyle", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"transformForSourceImage:handle:",
        {
            "required": True,
            "retval": {"type": b"{MPSScaleTransform=dddd}"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"updateGammaAndBetaWithBatchNormalizationState:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"updateGammaAndBetaWithCommandBuffer:batchNormalizationState:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"updateGammaAndBetaWithCommandBuffer:groupNormalizationStateBatch:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"updateGammaAndBetaWithCommandBuffer:instanceNormalizationStateBatch:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"updateGammaAndBetaWithGroupNormalizationStateBatch:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"updateGammaAndBetaWithInstanceNormalizationStateBatch:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"updateMeanAndVarianceWithBatchNormalizationState:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"updateMeanAndVarianceWithCommandBuffer:batchNormalizationState:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"updateWithCommandBuffer:gradientState:sourceState:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"updateWithGradientState:sourceState:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"variance",
        {
            "required": True,
            "retval": {"type": b"^f", "c_array_of_variable_length": True},
        },
    )
    r(
        b"NSObject",
        b"weights",
        {
            "required": True,
            "retval": {"type": b"^v", "c_array_of_variable_length": True},
        },
    )
    r(b"NSObject", b"weightsLayout", {"required": False, "retval": {"type": b"I"}})
    r(
        b"NSObject",
        b"weightsQuantizationType",
        {"required": False, "retval": {"type": b"I"}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "MPSAccelerationStructure", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSAccelerationStructure", b"initWithCoder:group:"
)
objc.registerNewKeywordsFromSelector("MPSAccelerationStructure", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSAccelerationStructure", b"initWithGroup:")
objc.registerNewKeywordsFromSelector(
    "MPSAccelerationStructureGroup", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector("MPSBinaryImageKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSBinaryImageKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNAdd", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNAddGradient", b"initWithDevice:isSecondarySourceFilter:"
)
objc.registerNewKeywordsFromSelector("MPSCNNArithmetic", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNArithmeticGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNArithmeticGradient", b"initWithDevice:isSecondarySourceFilter:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalization", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNBatchNormalization", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalization", b"initWithDevice:dataSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalization", b"initWithDevice:dataSource:fusedNeuronDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationGradient", b"initWithDevice:fusedNeuronDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationNode", b"initWithSource:dataSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationState", b"initWithResource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationStatistics", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationStatistics", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationStatisticsGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBatchNormalizationStatisticsGradient",
    b"initWithDevice:fusedNeuronDescriptor:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryConvolution", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNBinaryConvolution", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryConvolution",
    b"initWithDevice:convolutionData:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryConvolution", b"initWithDevice:convolutionData:scaleValue:type:flags:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryConvolutionNode",
    b"initWithSource:weights:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryConvolutionNode", b"initWithSource:weights:scaleValue:type:flags:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryFullyConnected", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNBinaryFullyConnected", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryFullyConnected",
    b"initWithDevice:convolutionData:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryFullyConnected",
    b"initWithDevice:convolutionData:scaleValue:type:flags:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryFullyConnectedNode",
    b"initWithSource:weights:outputBiasTerms:outputScaleTerms:inputBiasTerms:inputScaleTerms:type:flags:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNBinaryFullyConnectedNode", b"initWithSource:weights:scaleValue:type:flags:"
)
objc.registerNewKeywordsFromSelector("MPSCNNBinaryKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNBinaryKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNConvolution", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNConvolution", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolution",
    b"initWithDevice:convolutionDescriptor:kernelWeights:biasTerms:flags:",
)
objc.registerNewKeywordsFromSelector("MPSCNNConvolution", b"initWithDevice:weights:")
objc.registerNewKeywordsFromSelector("MPSCNNConvolutionDescriptor", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNConvolutionGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionGradient", b"initWithDevice:weights:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionGradientNode",
    b"initWithSourceGradient:sourceImage:convolutionGradientState:weights:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionNode", b"initWithSource:weights:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionTranspose", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNConvolutionTranspose", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionTranspose", b"initWithDevice:weights:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionTransposeGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionTransposeGradient", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionTransposeGradient", b"initWithDevice:weights:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionTransposeGradientNode",
    b"initWithSourceGradient:sourceImage:convolutionTransposeGradientState:weights:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionTransposeNode",
    b"initWithSource:convolutionGradientState:weights:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionWeightsAndBiasesState",
    b"initWithDevice:cnnConvolutionDescriptor:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionWeightsAndBiasesState", b"initWithWeights:biases:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNConvolutionWeightsAndBiasesState",
    b"initWithWeights:weightsOffset:biases:biasesOffset:cnnConvolutionDescriptor:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNCrossChannelNormalization", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNCrossChannelNormalization", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNCrossChannelNormalization", b"initWithDevice:kernelSize:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNCrossChannelNormalizationGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNCrossChannelNormalizationGradient", b"initWithDevice:kernelSize:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNCrossChannelNormalizationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:kernelSize:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNCrossChannelNormalizationNode", b"initWithSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNCrossChannelNormalizationNode", b"initWithSource:kernelSize:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMax", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMax",
    b"initWithDevice:kernelWidth:kernelHeight:dilationRateX:dilationRateY:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMaxGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMaxGradient",
    b"initWithDevice:kernelWidth:kernelHeight:dilationRateX:dilationRateY:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMaxGradient",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMaxGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:dilationRateX:dilationRateY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMaxNode", b"initWithSource:filterSize:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMaxNode", b"initWithSource:filterSize:stride:dilationRate:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDilatedPoolingMaxNode",
    b"initWithSource:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:dilationRateX:dilationRateY:",
)
objc.registerNewKeywordsFromSelector("MPSCNNDivide", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNDropout", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNDropout", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNDropout", b"initWithDevice:keepProbability:seed:maskStrideInPixels:"
)
objc.registerNewKeywordsFromSelector("MPSCNNDropoutGradient", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNDropoutGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNDropoutGradient", b"initWithDevice:keepProbability:seed:maskStrideInPixels:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDropoutGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:keepProbability:seed:maskStrideInPixels:",
)
objc.registerNewKeywordsFromSelector("MPSCNNDropoutNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNDropoutNode", b"initWithSource:keepProbability:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNDropoutNode", b"initWithSource:keepProbability:seed:maskStrideInPixels:"
)
objc.registerNewKeywordsFromSelector("MPSCNNFullyConnected", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNFullyConnected", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNFullyConnected",
    b"initWithDevice:convolutionDescriptor:kernelWeights:biasTerms:flags:",
)
objc.registerNewKeywordsFromSelector("MPSCNNFullyConnected", b"initWithDevice:weights:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNFullyConnectedGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNFullyConnectedGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNFullyConnectedGradient", b"initWithDevice:weights:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNFullyConnectedGradientNode",
    b"initWithSourceGradient:sourceImage:convolutionGradientState:weights:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNFullyConnectedNode", b"initWithSource:weights:"
)
objc.registerNewKeywordsFromSelector("MPSCNNGradientKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNGradientKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNGroupNormalization", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNGroupNormalization", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNGroupNormalization", b"initWithDevice:dataSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNGroupNormalizationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNGroupNormalizationGradientState", b"initWithDevice:bufferSize:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNGroupNormalizationGradientState", b"initWithDevice:textureDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNGroupNormalizationGradientState", b"initWithResource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNGroupNormalizationNode", b"initWithSource:dataSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNInstanceNormalization", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNInstanceNormalization", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNInstanceNormalization", b"initWithDevice:dataSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNInstanceNormalizationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNInstanceNormalizationGradientState", b"initWithDevice:bufferSize:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNInstanceNormalizationGradientState", b"initWithDevice:textureDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNInstanceNormalizationGradientState", b"initWithResource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNInstanceNormalizationNode", b"initWithSource:dataSource:"
)
objc.registerNewKeywordsFromSelector("MPSCNNKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNLocalContrastNormalization", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLocalContrastNormalization", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLocalContrastNormalization", b"initWithDevice:kernelWidth:kernelHeight:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLocalContrastNormalizationGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLocalContrastNormalizationGradient",
    b"initWithDevice:kernelWidth:kernelHeight:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLocalContrastNormalizationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:kernelWidth:kernelHeight:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLocalContrastNormalizationNode", b"initWithSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLocalContrastNormalizationNode", b"initWithSource:kernelSize:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLogSoftMaxGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNLogSoftMaxGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNLogSoftMaxGradientNode", b"initWithSourceGradient:sourceImage:gradientState:"
)
objc.registerNewKeywordsFromSelector("MPSCNNLogSoftMaxNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNLoss", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNLoss", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNLoss", b"initWithDevice:lossDescriptor:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNLossLabels", b"initWithDevice:labelsDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLossLabels",
    b"initWithDevice:lossImageSize:labelsDescriptor:weightsDescriptor:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLossLabels", b"initWithDevice:lossImageSize:labelsImage:weightsImage:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNLossNode", b"initWithSource:lossDescriptor:"
)
objc.registerNewKeywordsFromSelector("MPSCNNMultiaryKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNMultiaryKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNMultiaryKernel", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector("MPSCNNMultiply", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNMultiplyGradient", b"initWithDevice:isSecondarySourceFilter:"
)
objc.registerNewKeywordsFromSelector("MPSCNNNeuron", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuron", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNNeuron", b"initWithDevice:neuronDescriptor:"
)
objc.registerNewKeywordsFromSelector("MPSCNNNeuronAbsolute", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronAbsoluteNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronELU", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronELU", b"initWithDevice:a:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronELUNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronELUNode", b"initWithSource:a:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronExponential", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNNeuronExponential", b"initWithDevice:a:b:c:"
)
objc.registerNewKeywordsFromSelector("MPSCNNNeuronExponentialNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNNeuronExponentialNode", b"initWithSource:a:b:c:"
)
objc.registerNewKeywordsFromSelector("MPSCNNNeuronGeLUNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronGradient", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNNeuronGradient", b"initWithDevice:neuronDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNNeuronGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:descriptor:",
)
objc.registerNewKeywordsFromSelector("MPSCNNNeuronHardSigmoid", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronHardSigmoid", b"initWithDevice:a:b:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronHardSigmoidNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNNeuronHardSigmoidNode", b"initWithSource:a:b:"
)
objc.registerNewKeywordsFromSelector("MPSCNNNeuronLinear", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronLinear", b"initWithDevice:a:b:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronLinearNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronLinearNode", b"initWithSource:a:b:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronLogarithm", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronLogarithm", b"initWithDevice:a:b:c:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronLogarithmNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNNeuronLogarithmNode", b"initWithSource:a:b:c:"
)
objc.registerNewKeywordsFromSelector("MPSCNNNeuronPReLU", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronPReLU", b"initWithDevice:a:count:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronPReLUNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronPReLUNode", b"initWithSource:aData:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronPower", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronPower", b"initWithDevice:a:b:c:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronPowerNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronPowerNode", b"initWithSource:a:b:c:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronReLU", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronReLU", b"initWithDevice:a:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronReLUN", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronReLUN", b"initWithDevice:a:b:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronReLUNNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronReLUNNode", b"initWithSource:a:b:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronReLUNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronReLUNode", b"initWithSource:a:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronSigmoid", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronSigmoidNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronSoftPlus", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronSoftPlus", b"initWithDevice:a:b:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronSoftPlusNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronSoftPlusNode", b"initWithSource:a:b:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronSoftSign", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronSoftSignNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronTanH", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronTanH", b"initWithDevice:a:b:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronTanHNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNNeuronTanHNode", b"initWithSource:a:b:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNNormalizationGammaAndBetaState", b"initWithGamma:beta:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNNormalizationMeanAndVarianceState", b"initWithMean:variance:"
)
objc.registerNewKeywordsFromSelector("MPSCNNNormalizationNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSCNNPooling", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNPooling", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNPooling", b"initWithDevice:kernelWidth:kernelHeight:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPooling",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector("MPSCNNPoolingAverage", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingAverage",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingAverageGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingAverageGradient",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector("MPSCNNPoolingGradient", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNPoolingGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingGradient", b"initWithDevice:kernelWidth:kernelHeight:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingGradient",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:paddingPolicy:",
)
objc.registerNewKeywordsFromSelector("MPSCNNPoolingL2Norm", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingL2Norm",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingL2NormGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingL2NormGradient",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector("MPSCNNPoolingMax", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingMax",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingMaxGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingMaxGradient",
    b"initWithDevice:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector("MPSCNNPoolingNode", b"initWithSource:filterSize:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingNode", b"initWithSource:filterSize:stride:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNPoolingNode",
    b"initWithSource:kernelWidth:kernelHeight:strideInPixelsX:strideInPixelsY:",
)
objc.registerNewKeywordsFromSelector("MPSCNNSoftMaxGradient", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNSoftMaxGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNSoftMaxGradientNode", b"initWithSourceGradient:sourceImage:gradientState:"
)
objc.registerNewKeywordsFromSelector("MPSCNNSoftMaxNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNSpatialNormalization", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSCNNSpatialNormalization", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNSpatialNormalization", b"initWithDevice:kernelWidth:kernelHeight:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNSpatialNormalizationGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNSpatialNormalizationGradient", b"initWithDevice:kernelWidth:kernelHeight:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNSpatialNormalizationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:kernelSize:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNSpatialNormalizationNode", b"initWithSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNSpatialNormalizationNode", b"initWithSource:kernelSize:"
)
objc.registerNewKeywordsFromSelector("MPSCNNSubtract", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNSubtractGradient", b"initWithDevice:isSecondarySourceFilter:"
)
objc.registerNewKeywordsFromSelector("MPSCNNUpsampling", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingBilinear",
    b"initWithDevice:integerScaleFactorX:integerScaleFactorY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingBilinear",
    b"initWithDevice:integerScaleFactorX:integerScaleFactorY:alignCorners:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingBilinearGradient",
    b"initWithDevice:integerScaleFactorX:integerScaleFactorY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingBilinearGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:scaleFactorX:scaleFactorY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingBilinearNode",
    b"initWithSource:integerScaleFactorX:integerScaleFactorY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingBilinearNode",
    b"initWithSource:integerScaleFactorX:integerScaleFactorY:alignCorners:",
)
objc.registerNewKeywordsFromSelector("MPSCNNUpsamplingGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingNearest",
    b"initWithDevice:integerScaleFactorX:integerScaleFactorY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingNearestGradient",
    b"initWithDevice:integerScaleFactorX:integerScaleFactorY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingNearestGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:scaleFactorX:scaleFactorY:",
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNUpsamplingNearestNode",
    b"initWithSource:integerScaleFactorX:integerScaleFactorY:",
)
objc.registerNewKeywordsFromSelector("MPSCNNYOLOLoss", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSCNNYOLOLoss", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSCNNYOLOLoss", b"initWithDevice:lossDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSCNNYOLOLossNode", b"initWithSource:lossDescriptor:"
)
objc.registerNewKeywordsFromSelector("MPSCommandBuffer", b"initWithCommandBuffer:")
objc.registerNewKeywordsFromSelector("MPSImage", b"initWithDevice:imageDescriptor:")
objc.registerNewKeywordsFromSelector(
    "MPSImage", b"initWithParentImage:sliceRange:featureChannels:"
)
objc.registerNewKeywordsFromSelector("MPSImage", b"initWithTexture:featureChannels:")
objc.registerNewKeywordsFromSelector("MPSImageAdd", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageAreaMax", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageAreaMax", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageAreaMax", b"initWithDevice:kernelWidth:kernelHeight:"
)
objc.registerNewKeywordsFromSelector("MPSImageArithmetic", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageBilinearScale", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageBilinearScale", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageBox", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageBox", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageBox", b"initWithDevice:kernelWidth:kernelHeight:"
)
objc.registerNewKeywordsFromSelector("MPSImageCanny", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageCanny", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageCanny", b"initWithDevice:linearToGrayScaleTransform:sigma:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageConversion",
    b"initWithDevice:srcAlpha:destAlpha:backgroundColor:conversionInfo:",
)
objc.registerNewKeywordsFromSelector("MPSImageConvolution", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector(
    "MPSImageConvolution", b"initWithDevice:kernelWidth:kernelHeight:weights:"
)
objc.registerNewKeywordsFromSelector("MPSImageCopyToMatrix", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector(
    "MPSImageCopyToMatrix", b"initWithDevice:dataLayout:"
)
objc.registerNewKeywordsFromSelector("MPSImageDilate", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageDilate", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageDilate", b"initWithDevice:kernelWidth:kernelHeight:values:"
)
objc.registerNewKeywordsFromSelector("MPSImageDivide", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageEDLines", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector(
    "MPSImageEDLines",
    b"initWithDevice:gaussianSigma:minLineLength:maxLines:detailRatio:gradientThreshold:lineErrorThreshold:mergeLocalityThreshold:",
)
objc.registerNewKeywordsFromSelector(
    "MPSImageEuclideanDistanceTransform", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageEuclideanDistanceTransform", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector("MPSImageFindKeypoints", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageFindKeypoints", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageFindKeypoints", b"initWithDevice:info:")
objc.registerNewKeywordsFromSelector("MPSImageGaussianBlur", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageGaussianBlur", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageGaussianBlur", b"initWithDevice:sigma:")
objc.registerNewKeywordsFromSelector("MPSImageGuidedFilter", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageGuidedFilter", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageGuidedFilter", b"initWithDevice:kernelDiameter:"
)
objc.registerNewKeywordsFromSelector("MPSImageHistogram", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector(
    "MPSImageHistogram", b"initWithDevice:histogramInfo:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageHistogramEqualization", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageHistogramEqualization", b"initWithDevice:histogramInfo:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageHistogramSpecification", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageHistogramSpecification", b"initWithDevice:histogramInfo:"
)
objc.registerNewKeywordsFromSelector("MPSImageLanczosScale", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageLanczosScale", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageMedian", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageMedian", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageMedian", b"initWithDevice:kernelDiameter:"
)
objc.registerNewKeywordsFromSelector("MPSImageMultiply", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageNormalizedHistogram", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageNormalizedHistogram", b"initWithDevice:histogramInfo:"
)
objc.registerNewKeywordsFromSelector("MPSImagePyramid", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImagePyramid", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImagePyramid", b"initWithDevice:centerWeight:")
objc.registerNewKeywordsFromSelector(
    "MPSImagePyramid", b"initWithDevice:kernelWidth:kernelHeight:weights:"
)
objc.registerNewKeywordsFromSelector("MPSImageReduceColumnMax", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageReduceColumnMean", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageReduceColumnMin", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageReduceColumnSum", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageReduceRowMax", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageReduceRowMean", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageReduceRowMin", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageReduceRowSum", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageReduceUnary", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageScale", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageScale", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageSobel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageSobel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageSobel", b"initWithDevice:linearGrayColorTransform:"
)
objc.registerNewKeywordsFromSelector("MPSImageStatisticsMean", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSImageStatisticsMean", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageStatisticsMeanAndVariance", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageStatisticsMeanAndVariance", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageStatisticsMinAndMax", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSImageStatisticsMinAndMax", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSImageSubtract", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdBinary", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSImageThresholdBinary", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdBinary",
    b"initWithDevice:thresholdValue:maximumValue:linearGrayColorTransform:",
)
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdBinaryInverse", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdBinaryInverse", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdBinaryInverse",
    b"initWithDevice:thresholdValue:maximumValue:linearGrayColorTransform:",
)
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdToZero", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSImageThresholdToZero", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdToZero",
    b"initWithDevice:thresholdValue:linearGrayColorTransform:",
)
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdToZeroInverse", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdToZeroInverse", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdToZeroInverse",
    b"initWithDevice:thresholdValue:linearGrayColorTransform:",
)
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdTruncate", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSImageThresholdTruncate", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSImageThresholdTruncate",
    b"initWithDevice:thresholdValue:linearGrayColorTransform:",
)
objc.registerNewKeywordsFromSelector("MPSKernel", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("MPSKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSKeyedUnarchiver", b"initForReadingFromData:device:error:"
)
objc.registerNewKeywordsFromSelector(
    "MPSKeyedUnarchiver", b"initForReadingFromData:error:"
)
objc.registerNewKeywordsFromSelector("MPSKeyedUnarchiver", b"initForReadingWithData:")
objc.registerNewKeywordsFromSelector(
    "MPSKeyedUnarchiver", b"initForReadingWithData:device:"
)
objc.registerNewKeywordsFromSelector("MPSKeyedUnarchiver", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSMatrix", b"initWithBuffer:descriptor:")
objc.registerNewKeywordsFromSelector("MPSMatrix", b"initWithBuffer:offset:descriptor:")
objc.registerNewKeywordsFromSelector("MPSMatrix", b"initWithDevice:descriptor:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixBatchNormalization", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSMatrixBatchNormalization", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixBatchNormalizationGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixBatchNormalizationGradient", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector("MPSMatrixCopy", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSMatrixCopy", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixCopy",
    b"initWithDevice:copyRows:copyColumns:sourcesAreTransposed:destinationsAreTransposed:",
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixCopyDescriptor", b"initWithDevice:count:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixCopyDescriptor",
    b"initWithSourceMatrices:destinationMatrices:offsetVector:offset:",
)
objc.registerNewKeywordsFromSelector("MPSMatrixCopyToImage", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixCopyToImage", b"initWithDevice:dataLayout:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixDecompositionCholesky", b"initWithDevice:lower:order:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixDecompositionLU", b"initWithDevice:rows:columns:"
)
objc.registerNewKeywordsFromSelector("MPSMatrixFindTopK", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSMatrixFindTopK", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixFindTopK", b"initWithDevice:numberOfTopKValues:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixFullyConnected", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSMatrixFullyConnected", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixFullyConnectedGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixFullyConnectedGradient", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector("MPSMatrixMultiplication", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixMultiplication",
    b"initWithDevice:resultRows:resultColumns:interiorColumns:",
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixMultiplication",
    b"initWithDevice:transposeLeft:transposeRight:resultRows:resultColumns:interiorColumns:alpha:beta:",
)
objc.registerNewKeywordsFromSelector("MPSMatrixNeuron", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSMatrixNeuron", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixNeuronGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSMatrixNeuronGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSMatrixRandom", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSMatrixRandomMTGP32", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSMatrixRandomMTGP32", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixRandomMTGP32", b"initWithDevice:destinationDataType:seed:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixRandomMTGP32",
    b"initWithDevice:destinationDataType:seed:distributionDescriptor:",
)
objc.registerNewKeywordsFromSelector("MPSMatrixRandomPhilox", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSMatrixRandomPhilox", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixRandomPhilox", b"initWithDevice:destinationDataType:seed:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixRandomPhilox",
    b"initWithDevice:destinationDataType:seed:distributionDescriptor:",
)
objc.registerNewKeywordsFromSelector("MPSMatrixSoftMax", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSMatrixSoftMax", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixSoftMaxGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSMatrixSoftMaxGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixSolveCholesky", b"initWithDevice:upper:order:numberOfRightHandSides:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixSolveLU", b"initWithDevice:transpose:order:numberOfRightHandSides:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixSolveTriangular",
    b"initWithDevice:right:upper:transpose:unit:order:numberOfRightHandSides:alpha:",
)
objc.registerNewKeywordsFromSelector("MPSMatrixSum", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSMatrixSum", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSMatrixSum", b"initWithDevice:count:rows:columns:transpose:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixVectorMultiplication", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixVectorMultiplication", b"initWithDevice:rows:columns:"
)
objc.registerNewKeywordsFromSelector(
    "MPSMatrixVectorMultiplication",
    b"initWithDevice:transpose:rows:columns:alpha:beta:",
)
objc.registerNewKeywordsFromSelector("MPSNDArray", b"initWithBuffer:offset:descriptor:")
objc.registerNewKeywordsFromSelector("MPSNDArray", b"initWithDevice:descriptor:")
objc.registerNewKeywordsFromSelector("MPSNDArray", b"initWithDevice:scalar:")
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayAffineInt4Dequantize", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayAffineInt4Dequantize", b"initWithDevice:quantizationDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayAffineInt4Dequantize", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayAffineQuantizationDescriptor",
    b"initWithDataType:hasZeroPoint:hasMinValue:",
)
objc.registerNewKeywordsFromSelector("MPSNDArrayBinaryKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNDArrayBinaryKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayBinaryKernel", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayBinaryPrimaryGradientKernel", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayBinaryPrimaryGradientKernel", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayBinaryPrimaryGradientKernel",
    b"initWithDevice:sourceCount:sourceGradientIndex:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayBinarySecondaryGradientKernel", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayBinarySecondaryGradientKernel", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayBinarySecondaryGradientKernel",
    b"initWithDevice:sourceCount:sourceGradientIndex:",
)
objc.registerNewKeywordsFromSelector("MPSNDArrayIdentity", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNDArrayLUTDequantize", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayLUTDequantize", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayLUTQuantizationDescriptor", b"initWithDataType:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayLUTQuantizationDescriptor", b"initWithDataType:vectorAxis:"
)
objc.registerNewKeywordsFromSelector("MPSNDArrayMultiaryBase", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNDArrayMultiaryBase", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayMultiaryBase", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayMultiaryGradientKernel", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayMultiaryGradientKernel", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayMultiaryGradientKernel",
    b"initWithDevice:sourceCount:sourceGradientIndex:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayMultiaryKernel", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayMultiaryKernel", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayQuantizedMatrixMultiplication",
    b"initWithDevice:leftQuantizationDescriptor:rightQuantizationDescriptor:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayQuantizedMatrixMultiplication", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayUnaryGradientKernel", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayUnaryGradientKernel", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayUnaryGradientKernel", b"initWithDevice:sourceCount:sourceGradientIndex:"
)
objc.registerNewKeywordsFromSelector("MPSNDArrayUnaryKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNDArrayUnaryKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayUnaryKernel", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayVectorLUTDequantize", b"initWithDevice:axis:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNDArrayVectorLUTDequantize", b"initWithDevice:sourceCount:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNArithmeticGradientNode",
    b"initWithGradientImages:forwardFilter:isSecondarySourceFilter:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNArithmeticGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:isSecondarySourceFilter:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNBinaryArithmeticNode", b"initWithLeftSource:rightSource:"
)
objc.registerNewKeywordsFromSelector("MPSNNBinaryArithmeticNode", b"initWithSources:")
objc.registerNewKeywordsFromSelector("MPSNNCompare", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNConcatenationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:",
)
objc.registerNewKeywordsFromSelector("MPSNNConcatenationNode", b"initWithSources:")
objc.registerNewKeywordsFromSelector(
    "MPSNNCropAndResizeBilinear", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSNNCropAndResizeBilinear", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNCropAndResizeBilinear",
    b"initWithDevice:resizeWidth:resizeHeight:numberOfRegions:regions:",
)
objc.registerNewKeywordsFromSelector("MPSNNForwardLoss", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNForwardLoss", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNForwardLoss", b"initWithDevice:lossDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNForwardLossNode", b"initWithSource:labels:lossDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNForwardLossNode", b"initWithSource:labels:weights:lossDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNForwardLossNode", b"initWithSources:lossDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculation", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSNNGramMatrixCalculation", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculation", b"initWithDevice:alpha:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculationGradient", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculationGradient", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculationGradient", b"initWithDevice:alpha:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculationGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:alpha:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculationNode", b"initWithSource:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGramMatrixCalculationNode", b"initWithSource:alpha:"
)
objc.registerNewKeywordsFromSelector("MPSNNGraph", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNGraph", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNGraph", b"initWithDevice:resultImage:")
objc.registerNewKeywordsFromSelector(
    "MPSNNGraph", b"initWithDevice:resultImage:resultImageIsNeeded:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNGraph", b"initWithDevice:resultImages:resultsAreNeeded:"
)
objc.registerNewKeywordsFromSelector("MPSNNGridSample", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNGridSample", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNImageNode", b"initWithHandle:")
objc.registerNewKeywordsFromSelector("MPSNNInitialGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNInitialGradientNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSNNLocalCorrelation", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNLocalCorrelation", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNLocalCorrelation", b"initWithDevice:windowInX:windowInY:strideInX:strideInY:"
)
objc.registerNewKeywordsFromSelector("MPSNNLossGradient", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNLossGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNLossGradient", b"initWithDevice:lossDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNLossGradientNode",
    b"initWithSourceGradient:sourceImage:labels:gradientState:lossDescriptor:isLabelsGradientFilter:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNLossGradientNode",
    b"initWithSourceGradient:sourceImage:labels:weights:gradientState:lossDescriptor:isLabelsGradientFilter:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNLossGradientNode",
    b"initWithSources:gradientState:lossDescriptor:isLabelsGradientFilter:",
)
objc.registerNewKeywordsFromSelector("MPSNNOptimizer", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNOptimizerAdam", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerAdam",
    b"initWithDevice:beta1:beta2:epsilon:timeStep:optimizerDescriptor:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerAdam", b"initWithDevice:learningRate:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerDescriptor",
    b"initWithLearningRate:gradientRescale:applyGradientClipping:gradientClipMax:gradientClipMin:regularizationType:regularizationScale:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerDescriptor",
    b"initWithLearningRate:gradientRescale:regularizationType:regularizationScale:",
)
objc.registerNewKeywordsFromSelector("MPSNNOptimizerRMSProp", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerRMSProp", b"initWithDevice:decay:epsilon:optimizerDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerRMSProp", b"initWithDevice:learningRate:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerStochasticGradientDescent", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerStochasticGradientDescent", b"initWithDevice:learningRate:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerStochasticGradientDescent",
    b"initWithDevice:momentumScale:useNesterovMomentum:optimizerDescriptor:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNOptimizerStochasticGradientDescent",
    b"initWithDevice:momentumScale:useNestrovMomentum:optimizerDescriptor:",
)
objc.registerNewKeywordsFromSelector("MPSNNPad", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNPad", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNPad", b"initWithDevice:paddingSizeBefore:paddingSizeAfter:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNPad", b"initWithDevice:paddingSizeBefore:paddingSizeAfter:fillValueArray:"
)
objc.registerNewKeywordsFromSelector("MPSNNPadGradient", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNPadGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNPadGradientNode", b"initWithSourceGradient:sourceImage:gradientState:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNPadNode", b"initWithSource:paddingSizeBefore:paddingSizeAfter:edgeMode:"
)
objc.registerNewKeywordsFromSelector("MPSNNReduceBinary", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReduceColumnMax", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReduceColumnMax", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReduceColumnMean", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReduceColumnMean", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReduceColumnMin", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReduceColumnMin", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReduceColumnSum", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReduceColumnSum", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsAndWeightsMean", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsAndWeightsMean", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsAndWeightsSum", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsAndWeightsSum", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsAndWeightsSum",
    b"initWithDevice:doWeightedSumByNonZeroWeights:",
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsArgumentMax", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsArgumentMax", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsArgumentMin", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsArgumentMin", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsMax", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsMax", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsMean", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsMean", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsMin", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsMin", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsSum", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReduceFeatureChannelsSum", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector("MPSNNReduceRowMax", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReduceRowMax", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReduceRowMean", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReduceRowMean", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReduceRowMin", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReduceRowMin", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReduceRowSum", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReduceRowSum", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReduceUnary", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNReductionSpatialMeanGradientNode",
    b"initWithSourceGradient:sourceImage:gradientState:",
)
objc.registerNewKeywordsFromSelector("MPSNNReshape", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReshape", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNReshapeGradient", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNReshapeGradient", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNReshapeGradientNode", b"initWithSourceGradient:sourceImage:gradientState:"
)
objc.registerNewKeywordsFromSelector(
    "MPSNNReshapeNode",
    b"initWithSource:resultWidth:resultHeight:resultFeatureChannels:",
)
objc.registerNewKeywordsFromSelector("MPSNNResizeBilinear", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNResizeBilinear", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSNNResizeBilinear", b"initWithDevice:resizeWidth:resizeHeight:alignCorners:"
)
objc.registerNewKeywordsFromSelector("MPSNNScaleNode", b"initWithSource:outputSize:")
objc.registerNewKeywordsFromSelector(
    "MPSNNScaleNode", b"initWithSource:transformProvider:outputSize:"
)
objc.registerNewKeywordsFromSelector("MPSNNSlice", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSNNSlice", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSNNUnaryReductionNode", b"initWithSource:")
objc.registerNewKeywordsFromSelector("MPSPolygonBuffer", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("MPSPredicate", b"initWithBuffer:offset:")
objc.registerNewKeywordsFromSelector("MPSPredicate", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSRNNImageInferenceLayer", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSRNNImageInferenceLayer", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSRNNImageInferenceLayer", b"initWithDevice:rnnDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSRNNImageInferenceLayer", b"initWithDevice:rnnDescriptors:"
)
objc.registerNewKeywordsFromSelector(
    "MPSRNNMatrixInferenceLayer", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSRNNMatrixInferenceLayer", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSRNNMatrixInferenceLayer", b"initWithDevice:rnnDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSRNNMatrixInferenceLayer", b"initWithDevice:rnnDescriptors:"
)
objc.registerNewKeywordsFromSelector(
    "MPSRNNMatrixTrainingLayer", b"initWithCoder:device:"
)
objc.registerNewKeywordsFromSelector("MPSRNNMatrixTrainingLayer", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSRNNMatrixTrainingLayer", b"initWithDevice:rnnDescriptor:trainableWeights:"
)
objc.registerNewKeywordsFromSelector("MPSRayIntersector", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSRayIntersector", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSSVGF", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSSVGF", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSSVGFDefaultTextureAllocator", b"initWithDevice:"
)
objc.registerNewKeywordsFromSelector("MPSSVGFDenoiser", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSSVGFDenoiser", b"initWithSVGF:textureAllocator:"
)
objc.registerNewKeywordsFromSelector("MPSState", b"initWithDevice:bufferSize:")
objc.registerNewKeywordsFromSelector("MPSState", b"initWithDevice:resourceList:")
objc.registerNewKeywordsFromSelector("MPSState", b"initWithDevice:textureDescriptor:")
objc.registerNewKeywordsFromSelector("MPSState", b"initWithResource:")
objc.registerNewKeywordsFromSelector("MPSState", b"initWithResources:")
objc.registerNewKeywordsFromSelector("MPSTemporalAA", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSTemporalAA", b"initWithDevice:")
objc.registerNewKeywordsFromSelector(
    "MPSTemporaryImage", b"initWithDevice:imageDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSTemporaryImage", b"initWithTexture:featureChannels:"
)
objc.registerNewKeywordsFromSelector(
    "MPSTemporaryMatrix", b"initWithBuffer:descriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSTemporaryNDArray", b"initWithDevice:descriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MPSTemporaryVector", b"initWithBuffer:descriptor:"
)
objc.registerNewKeywordsFromSelector("MPSUnaryImageKernel", b"initWithCoder:device:")
objc.registerNewKeywordsFromSelector("MPSUnaryImageKernel", b"initWithDevice:")
objc.registerNewKeywordsFromSelector("MPSVector", b"initWithBuffer:descriptor:")
objc.registerNewKeywordsFromSelector("MPSVector", b"initWithBuffer:offset:descriptor:")
objc.registerNewKeywordsFromSelector("MPSVector", b"initWithDevice:descriptor:")
expressions = {
    "MPSUserAvailableFunctionConstantStartIndex": "MPSDeviceCapsIndex-12",
    "MPSNDArrayConstantMultiDestSrcAddressingIndex": "MPSDeviceCapsIndex-10",
    "MPSNDArrayConstantMultiDestIndex": "MPSDeviceCapsIndex-7",
    "MPSNDArrayConstantMultiDestDstAddressingIndex": "MPSDeviceCapsIndex-11",
    "MPSTextureLinkingConstantIndex": "MPSDeviceCapsIndex-6",
    "MPSFunctionConstantIndex": "MPSDeviceCapsIndex-1",
    "MPSNDArrayConstantMultiDestIndex0": "MPSDeviceCapsIndex-8",
    "MPSNDArrayConstantIndex": "MPSDeviceCapsIndex-4",
    "MPSNDArrayConstantMultiDestIndex1": "MPSDeviceCapsIndex-9",
    "MPSBatchSizeIndex": "MPSDeviceCapsIndex-2",
    "MPSFunctionConstantIndexReserved": "MPSDeviceCapsIndex-5",
    "MPSFunctionConstantNoneArray": "(-1, -1)",
    "MPSUserConstantIndex": "MPSDeviceCapsIndex-3",
}

# END OF FILE
