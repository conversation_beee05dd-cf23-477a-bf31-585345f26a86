# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:14:32 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$$"""
enums = """$kMTAudioProcessingTapCallbacksVersion_0@0$kMTAudioProcessingTapCreationFlag_PostEffects@2$kMTAudioProcessingTapCreationFlag_PreEffects@1$kMTAudioProcessingTapFlag_EndOfStream@512$kMTAudioProcessingTapFlag_StartOfStream@256$"""
misc.update({})
misc.update({})
misc.update({})
functions = {
    "MTAudioProcessingTapGetSourceAudio": (
        b"i^{opaqueMTAudioProcessingTap=}q^{AudioBufferList=I[1{AudioBuffer=II^v}]}^I^{CMTimeRange={CMTime=qiIq}{CMTime=qiIq}}^q",
        "",
        {
            "arguments": {
                3: {"type_modifier": "o"},
                4: {"type_modifier": "o"},
                5: {"type_modifier": "o"},
            }
        },
    ),
    "MTAudioProcessingTapGetTypeID": (b"Q",),
    "MTRegisterProfessionalVideoWorkflowFormatReaders": (b"v",),
    "MTCopyLocalizedNameForMediaType": (
        b"^{__CFString=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "MTCopyLocalizedNameForMediaSubType": (
        b"^{__CFString=}II",
        "",
        {"retval": {"already_cfretained": True}},
    ),
}
cftypes = [
    (
        "MTAudioProcessingTapRef",
        b"^{opaqueMTAudioProcessingTap=}",
        "MTAudioProcessingTapGetTypeID",
        None,
    )
]
expressions = {}

# END OF FILE
