"""
Python mapping for the CoreAudio framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import Foundation
    import objc
    from . import _metadata, _CoreAudio
    from ._inlines import _inline_list_

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="CoreAudio",
        frameworkIdentifier="com.apple.audio.CoreAudio",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/CoreAudio.framework"
        ),
        globals_dict=globals(),
        inline_list=_inline_list_,
        parents=(
            _CoreAudio,
            Foundation,
        ),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    del sys.modules["CoreAudio._metadata"]


globals().pop("_setup")()
