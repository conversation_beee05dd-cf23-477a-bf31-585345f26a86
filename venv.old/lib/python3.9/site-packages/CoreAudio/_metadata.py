# This file is generated by objective.metadata
#
# Last update: Sun Nov 17 10:44:41 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "AudioClassDescription": objc.createStructType(
            "CoreAudio.AudioClassDescription",
            b"{AudioClassDescription=III}",
            ["mType", "mSubType", "mManufacturer"],
        ),
        "AudioStreamPacketDescription": objc.createStructType(
            "CoreAudio.AudioStreamPacketDescription",
            b"{AudioStreamPacketDescription=qII}",
            ["mStartOffset", "mVariableFramesInPacket", "mDataByteSize"],
        ),
        "AudioStreamRangedDescription": objc.createStructType(
            "CoreAudio.AudioStreamRangedDescription",
            b"{AudioStreamRangedDescription={AudioStreamBasicDescription=dIIIIIIII}{AudioValueRange=dd}}",
            ["mFormat", "mSampleRateRange"],
        ),
        "AudioFormatListItem": objc.createStructType(
            "CoreAudio.AudioFormatListItem",
            b"{AudioFormatListItem={AudioStreamBasicDescription=dIIIIIIII}I}",
            ["mASBD", "mChannelLayoutTag"],
        ),
        "SMPTETime": objc.createStructType(
            "CoreAudio.SMPTETime",
            b"{SMPTETime=ssIIIssss}",
            [
                "mSubframes",
                "mSubframeDivisor",
                "mCounter",
                "mType",
                "mFlags",
                "mHours",
                "mMinutes",
                "mSeconds",
                "mFrames",
            ],
        ),
        "AudioValueRange": objc.createStructType(
            "CoreAudio.AudioValueRange",
            b"{AudioValueRange=dd}",
            ["mMinimum", "mMaximum"],
        ),
        "AudioTimeStamp": objc.createStructType(
            "CoreAudio.AudioTimeStamp",
            b"{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}",
            [
                "mSampleTime",
                "mHostTime",
                "mRateScalar",
                "mWordClockTime",
                "mSMPTETime",
                "mFlags",
                "mReserved",
            ],
        ),
        "AudioHardwareIOProcStreamUsage": objc.createStructType(
            "CoreAudio.AudioHardwareIOProcStreamUsage",
            b"{AudioHardwareIOProcStreamUsage=^vI[1I]}",
            ["mIOProc", "mNumberStreams", "mStreamIsOn"],
        ),
        "AudioStreamBasicDescription": objc.createStructType(
            "CoreAudio.AudioStreamBasicDescription",
            b"{AudioStreamBasicDescription=dIIIIIIII}",
            [
                "mSampleRate",
                "mFormatID",
                "mFormatFlags",
                "mBytesPerPacket",
                "mFramesPerPacket",
                "mBytesPerFrame",
                "mChannelsPerFrame",
                "mBitsPerChannel",
                "mReserved",
            ],
        ),
        "AudioObjectPropertyAddress": objc.createStructType(
            "CoreAudio.AudioObjectPropertyAddress",
            b"{AudioObjectPropertyAddress=III}",
            ["mSelector", "mScope", "mElement"],
        ),
    }
)
constants = """$$"""
enums = """$AVAudioSessionErrorCodeBadParam@-50$AVAudioSessionErrorCodeCannotInterruptOthers@560557684$AVAudioSessionErrorCodeCannotStartPlaying@561015905$AVAudioSessionErrorCodeCannotStartRecording@561145187$AVAudioSessionErrorCodeExpiredSession@561210739$AVAudioSessionErrorCodeIncompatibleCategory@560161140$AVAudioSessionErrorCodeInsufficientPriority@561017449$AVAudioSessionErrorCodeIsBusy@560030580$AVAudioSessionErrorCodeMediaServicesFailed@1836282486$AVAudioSessionErrorCodeMissingEntitlement@1701737535$AVAudioSessionErrorCodeNone@0$AVAudioSessionErrorCodeResourceNotAvailable@561145203$AVAudioSessionErrorCodeSessionNotActive@1768841571$AVAudioSessionErrorCodeSiriIsRecording@1936290409$AVAudioSessionErrorCodeUnspecified@2003329396$CATapMuted@1$CATapMutedWhenTapped@2$CATapUnmuted@0$CA_PREFER_FIXED_POINT@0$COREAUDIOTYPES_VERSION@20150414$kAppleLosslessFormatFlag_16BitSourceData@1$kAppleLosslessFormatFlag_20BitSourceData@2$kAppleLosslessFormatFlag_24BitSourceData@3$kAppleLosslessFormatFlag_32BitSourceData@4$kAudioAggregateDeviceClassID@1633773415$kAudioAggregateDevicePropertyActiveSubDeviceList@1634169456$kAudioAggregateDevicePropertyClockDevice@1634755428$kAudioAggregateDevicePropertyComposition@1633906541$kAudioAggregateDevicePropertyFullSubDeviceList@1735554416$kAudioAggregateDevicePropertyMainSubDevice@1634562932$kAudioAggregateDevicePropertyMasterSubDevice@1634562932$kAudioAggregateDevicePropertySubTapList@1635017072$kAudioAggregateDevicePropertyTapList@1952542755$kAudioAggregateDriftCompensationHighQuality@96$kAudioAggregateDriftCompensationLowQuality@32$kAudioAggregateDriftCompensationMaxQuality@127$kAudioAggregateDriftCompensationMediumQuality@64$kAudioAggregateDriftCompensationMinQuality@0$kAudioBooleanControlClassID@1953458028$kAudioBooleanControlPropertyValue@1650685548$kAudioBootChimeVolumeControlClassID@1886544237$kAudioBoxClassID@1633841016$kAudioBoxPropertyAcquired@1652060014$kAudioBoxPropertyAcquisitionFailed@1652060006$kAudioBoxPropertyBoxUID@1651861860$kAudioBoxPropertyClockDeviceList@1650682915$kAudioBoxPropertyDeviceList@1650751011$kAudioBoxPropertyHasAudio@1651007861$kAudioBoxPropertyHasMIDI@1651010921$kAudioBoxPropertyHasVideo@1651013225$kAudioBoxPropertyIsProtected@1651536495$kAudioBoxPropertyTransportType@1953653102$kAudioChannelBit_Center@4$kAudioChannelBit_CenterSurround@256$kAudioChannelBit_CenterTopRear@33554432$kAudioChannelBit_LFEScreen@8$kAudioChannelBit_Left@1$kAudioChannelBit_LeftCenter@64$kAudioChannelBit_LeftSurround@16$kAudioChannelBit_LeftSurroundDirect@512$kAudioChannelBit_LeftTopMiddle@2097152$kAudioChannelBit_LeftTopRear@16777216$kAudioChannelBit_Right@2$kAudioChannelBit_RightCenter@128$kAudioChannelBit_RightSurround@32$kAudioChannelBit_RightSurroundDirect@1024$kAudioChannelBit_RightTopMiddle@8388608$kAudioChannelBit_RightTopRear@67108864$kAudioChannelBit_TopBackCenter@65536$kAudioChannelBit_TopBackLeft@32768$kAudioChannelBit_TopBackRight@131072$kAudioChannelBit_TopCenterSurround@2048$kAudioChannelBit_VerticalHeightCenter@8192$kAudioChannelBit_VerticalHeightLeft@4096$kAudioChannelBit_VerticalHeightRight@16384$kAudioChannelCoordinates_Azimuth@0$kAudioChannelCoordinates_BackFront@1$kAudioChannelCoordinates_Distance@2$kAudioChannelCoordinates_DownUp@2$kAudioChannelCoordinates_Elevation@1$kAudioChannelCoordinates_LeftRight@0$kAudioChannelFlags_AllOff@0$kAudioChannelFlags_Meters@4$kAudioChannelFlags_RectangularCoordinates@1$kAudioChannelFlags_SphericalCoordinates@2$kAudioChannelLabel_Ambisonic_W@200$kAudioChannelLabel_Ambisonic_X@201$kAudioChannelLabel_Ambisonic_Y@202$kAudioChannelLabel_Ambisonic_Z@203$kAudioChannelLabel_BeginReserved@4026531840$kAudioChannelLabel_BinauralLeft@208$kAudioChannelLabel_BinauralRight@209$kAudioChannelLabel_Center@3$kAudioChannelLabel_CenterBottom@59$kAudioChannelLabel_CenterSurround@9$kAudioChannelLabel_CenterSurroundDirect@44$kAudioChannelLabel_CenterTopRear@53$kAudioChannelLabel_ClickTrack@304$kAudioChannelLabel_DialogCentricMix@43$kAudioChannelLabel_Discrete@400$kAudioChannelLabel_Discrete_0@65536$kAudioChannelLabel_Discrete_1@65537$kAudioChannelLabel_Discrete_10@65546$kAudioChannelLabel_Discrete_11@65547$kAudioChannelLabel_Discrete_12@65548$kAudioChannelLabel_Discrete_13@65549$kAudioChannelLabel_Discrete_14@65550$kAudioChannelLabel_Discrete_15@65551$kAudioChannelLabel_Discrete_2@65538$kAudioChannelLabel_Discrete_3@65539$kAudioChannelLabel_Discrete_4@65540$kAudioChannelLabel_Discrete_5@65541$kAudioChannelLabel_Discrete_6@65542$kAudioChannelLabel_Discrete_65535@131071$kAudioChannelLabel_Discrete_7@65543$kAudioChannelLabel_Discrete_8@65544$kAudioChannelLabel_Discrete_9@65545$kAudioChannelLabel_EndReserved@4294967294$kAudioChannelLabel_ForeignLanguage@305$kAudioChannelLabel_HOA_ACN@500$kAudioChannelLabel_HOA_ACN_0@131072$kAudioChannelLabel_HOA_ACN_1@131073$kAudioChannelLabel_HOA_ACN_10@131082$kAudioChannelLabel_HOA_ACN_11@131083$kAudioChannelLabel_HOA_ACN_12@131084$kAudioChannelLabel_HOA_ACN_13@131085$kAudioChannelLabel_HOA_ACN_14@131086$kAudioChannelLabel_HOA_ACN_15@131087$kAudioChannelLabel_HOA_ACN_2@131074$kAudioChannelLabel_HOA_ACN_3@131075$kAudioChannelLabel_HOA_ACN_4@131076$kAudioChannelLabel_HOA_ACN_5@131077$kAudioChannelLabel_HOA_ACN_6@131078$kAudioChannelLabel_HOA_ACN_65024@196096$kAudioChannelLabel_HOA_ACN_7@131079$kAudioChannelLabel_HOA_ACN_8@131080$kAudioChannelLabel_HOA_ACN_9@131081$kAudioChannelLabel_HOA_N3D@196608$kAudioChannelLabel_Haptic@45$kAudioChannelLabel_HeadphonesLeft@301$kAudioChannelLabel_HeadphonesRight@302$kAudioChannelLabel_HearingImpaired@40$kAudioChannelLabel_LFE2@37$kAudioChannelLabel_LFE3@62$kAudioChannelLabel_LFEScreen@4$kAudioChannelLabel_Left@1$kAudioChannelLabel_LeftBackSurround@63$kAudioChannelLabel_LeftBottom@57$kAudioChannelLabel_LeftCenter@7$kAudioChannelLabel_LeftEdgeOfScreen@65$kAudioChannelLabel_LeftSideSurround@55$kAudioChannelLabel_LeftSurround@5$kAudioChannelLabel_LeftSurroundDirect@10$kAudioChannelLabel_LeftTopMiddle@49$kAudioChannelLabel_LeftTopRear@52$kAudioChannelLabel_LeftTopSurround@60$kAudioChannelLabel_LeftTotal@38$kAudioChannelLabel_LeftWide@35$kAudioChannelLabel_MS_Mid@204$kAudioChannelLabel_MS_Side@205$kAudioChannelLabel_Mono@42$kAudioChannelLabel_Narration@41$kAudioChannelLabel_Object@262144$kAudioChannelLabel_RearSurroundLeft@33$kAudioChannelLabel_RearSurroundRight@34$kAudioChannelLabel_Right@2$kAudioChannelLabel_RightBackSurround@64$kAudioChannelLabel_RightBottom@58$kAudioChannelLabel_RightCenter@8$kAudioChannelLabel_RightEdgeOfScreen@66$kAudioChannelLabel_RightSideSurround@56$kAudioChannelLabel_RightSurround@6$kAudioChannelLabel_RightSurroundDirect@11$kAudioChannelLabel_RightTopMiddle@51$kAudioChannelLabel_RightTopRear@54$kAudioChannelLabel_RightTopSurround@61$kAudioChannelLabel_RightTotal@39$kAudioChannelLabel_RightWide@36$kAudioChannelLabel_TopBackCenter@17$kAudioChannelLabel_TopBackLeft@16$kAudioChannelLabel_TopBackRight@18$kAudioChannelLabel_TopCenterSurround@12$kAudioChannelLabel_Unknown@4294967295$kAudioChannelLabel_Unused@0$kAudioChannelLabel_UseCoordinates@100$kAudioChannelLabel_VerticalHeightCenter@14$kAudioChannelLabel_VerticalHeightLeft@13$kAudioChannelLabel_VerticalHeightRight@15$kAudioChannelLabel_XY_X@206$kAudioChannelLabel_XY_Y@207$kAudioChannelLayoutTag_AAC_3_0@7471107$kAudioChannelLayoutTag_AAC_4_0@7602180$kAudioChannelLayoutTag_AAC_5_0@7864325$kAudioChannelLayoutTag_AAC_5_1@8126470$kAudioChannelLayoutTag_AAC_6_0@9240582$kAudioChannelLayoutTag_AAC_6_1@9306119$kAudioChannelLayoutTag_AAC_7_0@9371655$kAudioChannelLayoutTag_AAC_7_1@8323080$kAudioChannelLayoutTag_AAC_7_1_B@11993096$kAudioChannelLayoutTag_AAC_7_1_C@12058632$kAudioChannelLayoutTag_AAC_Octagonal@9437192$kAudioChannelLayoutTag_AAC_Quadraphonic@7077892$kAudioChannelLayoutTag_AC3_1_0_1@9764866$kAudioChannelLayoutTag_AC3_2_1_1@10027012$kAudioChannelLayoutTag_AC3_3_0@9830403$kAudioChannelLayoutTag_AC3_3_0_1@9961476$kAudioChannelLayoutTag_AC3_3_1@9895940$kAudioChannelLayoutTag_AC3_3_1_1@10092549$kAudioChannelLayoutTag_Ambisonic_B_Format@7012356$kAudioChannelLayoutTag_Atmos_5_1_2@12713992$kAudioChannelLayoutTag_Atmos_5_1_4@12779530$kAudioChannelLayoutTag_Atmos_7_1_2@12845066$kAudioChannelLayoutTag_Atmos_7_1_4@12582924$kAudioChannelLayoutTag_Atmos_9_1_6@12648464$kAudioChannelLayoutTag_AudioUnit_4@7077892$kAudioChannelLayoutTag_AudioUnit_5@7143429$kAudioChannelLayoutTag_AudioUnit_5_0@7733253$kAudioChannelLayoutTag_AudioUnit_5_1@7929862$kAudioChannelLayoutTag_AudioUnit_6@7208966$kAudioChannelLayoutTag_AudioUnit_6_0@9109510$kAudioChannelLayoutTag_AudioUnit_6_1@8192007$kAudioChannelLayoutTag_AudioUnit_7_0@9175047$kAudioChannelLayoutTag_AudioUnit_7_0_Front@9699335$kAudioChannelLayoutTag_AudioUnit_7_1@8388616$kAudioChannelLayoutTag_AudioUnit_7_1_Front@8257544$kAudioChannelLayoutTag_AudioUnit_8@7274504$kAudioChannelLayoutTag_BeginReserved@4026531840$kAudioChannelLayoutTag_Binaural@6946818$kAudioChannelLayoutTag_CICP_13@13369368$kAudioChannelLayoutTag_CICP_14@13434888$kAudioChannelLayoutTag_CICP_15@13500428$kAudioChannelLayoutTag_CICP_16@13565962$kAudioChannelLayoutTag_CICP_17@13631500$kAudioChannelLayoutTag_CICP_18@13697038$kAudioChannelLayoutTag_CICP_19@13762572$kAudioChannelLayoutTag_CICP_20@13828110$kAudioChannelLayoutTag_Cube@7340040$kAudioChannelLayoutTag_DTS_3_1@11010052$kAudioChannelLayoutTag_DTS_4_1@11075589$kAudioChannelLayoutTag_DTS_6_0_A@11141126$kAudioChannelLayoutTag_DTS_6_0_B@11206662$kAudioChannelLayoutTag_DTS_6_0_C@11272198$kAudioChannelLayoutTag_DTS_6_1_A@11337735$kAudioChannelLayoutTag_DTS_6_1_B@11403271$kAudioChannelLayoutTag_DTS_6_1_C@11468807$kAudioChannelLayoutTag_DTS_6_1_D@11927559$kAudioChannelLayoutTag_DTS_7_0@11534343$kAudioChannelLayoutTag_DTS_7_1@11599880$kAudioChannelLayoutTag_DTS_8_0_A@11665416$kAudioChannelLayoutTag_DTS_8_0_B@11730952$kAudioChannelLayoutTag_DTS_8_1_A@11796489$kAudioChannelLayoutTag_DTS_8_1_B@11862025$kAudioChannelLayoutTag_DVD_0@6553601$kAudioChannelLayoutTag_DVD_1@6619138$kAudioChannelLayoutTag_DVD_10@8912900$kAudioChannelLayoutTag_DVD_11@8978437$kAudioChannelLayoutTag_DVD_12@7929862$kAudioChannelLayoutTag_DVD_13@7536644$kAudioChannelLayoutTag_DVD_14@7667717$kAudioChannelLayoutTag_DVD_15@8912900$kAudioChannelLayoutTag_DVD_16@8978437$kAudioChannelLayoutTag_DVD_17@7929862$kAudioChannelLayoutTag_DVD_18@9043973$kAudioChannelLayoutTag_DVD_19@7733253$kAudioChannelLayoutTag_DVD_2@8585219$kAudioChannelLayoutTag_DVD_20@7995398$kAudioChannelLayoutTag_DVD_3@8650756$kAudioChannelLayoutTag_DVD_4@8716291$kAudioChannelLayoutTag_DVD_5@8781828$kAudioChannelLayoutTag_DVD_6@8847365$kAudioChannelLayoutTag_DVD_7@7405571$kAudioChannelLayoutTag_DVD_8@7536644$kAudioChannelLayoutTag_DVD_9@7667717$kAudioChannelLayoutTag_DiscreteInOrder@9633792$kAudioChannelLayoutTag_EAC3_6_1_A@10289159$kAudioChannelLayoutTag_EAC3_6_1_B@10354695$kAudioChannelLayoutTag_EAC3_6_1_C@10420231$kAudioChannelLayoutTag_EAC3_7_1_A@10485768$kAudioChannelLayoutTag_EAC3_7_1_B@10551304$kAudioChannelLayoutTag_EAC3_7_1_C@10616840$kAudioChannelLayoutTag_EAC3_7_1_D@10682376$kAudioChannelLayoutTag_EAC3_7_1_E@10747912$kAudioChannelLayoutTag_EAC3_7_1_F@10813448$kAudioChannelLayoutTag_EAC3_7_1_G@10878984$kAudioChannelLayoutTag_EAC3_7_1_H@10944520$kAudioChannelLayoutTag_EAC_6_0_A@10158086$kAudioChannelLayoutTag_EAC_7_0_A@10223623$kAudioChannelLayoutTag_Emagic_Default_7_1@8454152$kAudioChannelLayoutTag_EndReserved@4294901759$kAudioChannelLayoutTag_HOA_ACN_N3D@12517376$kAudioChannelLayoutTag_HOA_ACN_SN3D@12451840$kAudioChannelLayoutTag_Hexagonal@7208966$kAudioChannelLayoutTag_ITU_1_0@6553601$kAudioChannelLayoutTag_ITU_2_0@6619138$kAudioChannelLayoutTag_ITU_2_1@8585219$kAudioChannelLayoutTag_ITU_2_2@8650756$kAudioChannelLayoutTag_ITU_3_0@7405571$kAudioChannelLayoutTag_ITU_3_1@7536644$kAudioChannelLayoutTag_ITU_3_2@7667717$kAudioChannelLayoutTag_ITU_3_2_1@7929862$kAudioChannelLayoutTag_ITU_3_4_1@8388616$kAudioChannelLayoutTag_Logic_4_0_C@12910596$kAudioChannelLayoutTag_Logic_6_0_B@12976134$kAudioChannelLayoutTag_Logic_6_1_B@13041671$kAudioChannelLayoutTag_Logic_6_1_D@13107207$kAudioChannelLayoutTag_Logic_7_1_B@13172744$kAudioChannelLayoutTag_Logic_Atmos_7_1_4_B@13238284$kAudioChannelLayoutTag_Logic_Atmos_7_1_6@13303822$kAudioChannelLayoutTag_MPEG_1_0@6553601$kAudioChannelLayoutTag_MPEG_2_0@6619138$kAudioChannelLayoutTag_MPEG_3_0_A@7405571$kAudioChannelLayoutTag_MPEG_3_0_B@7471107$kAudioChannelLayoutTag_MPEG_4_0_A@7536644$kAudioChannelLayoutTag_MPEG_4_0_B@7602180$kAudioChannelLayoutTag_MPEG_5_0_A@7667717$kAudioChannelLayoutTag_MPEG_5_0_B@7733253$kAudioChannelLayoutTag_MPEG_5_0_C@7798789$kAudioChannelLayoutTag_MPEG_5_0_D@7864325$kAudioChannelLayoutTag_MPEG_5_0_E@14155781$kAudioChannelLayoutTag_MPEG_5_1_A@7929862$kAudioChannelLayoutTag_MPEG_5_1_B@7995398$kAudioChannelLayoutTag_MPEG_5_1_C@8060934$kAudioChannelLayoutTag_MPEG_5_1_D@8126470$kAudioChannelLayoutTag_MPEG_5_1_E@14221318$kAudioChannelLayoutTag_MPEG_6_1_A@8192007$kAudioChannelLayoutTag_MPEG_6_1_B@14286855$kAudioChannelLayoutTag_MPEG_7_1_A@8257544$kAudioChannelLayoutTag_MPEG_7_1_B@8323080$kAudioChannelLayoutTag_MPEG_7_1_C@8388616$kAudioChannelLayoutTag_MPEG_7_1_D@14352392$kAudioChannelLayoutTag_MatrixStereo@6750210$kAudioChannelLayoutTag_MidSide@6815746$kAudioChannelLayoutTag_Mono@6553601$kAudioChannelLayoutTag_Octagonal@7274504$kAudioChannelLayoutTag_Ogg_5_0@13893637$kAudioChannelLayoutTag_Ogg_5_1@13959174$kAudioChannelLayoutTag_Ogg_6_1@14024711$kAudioChannelLayoutTag_Ogg_7_1@14090248$kAudioChannelLayoutTag_Pentagonal@7143429$kAudioChannelLayoutTag_Quadraphonic@7077892$kAudioChannelLayoutTag_SMPTE_DTV@8519688$kAudioChannelLayoutTag_Stereo@6619138$kAudioChannelLayoutTag_StereoHeadphones@6684674$kAudioChannelLayoutTag_TMH_10_2_full@9568277$kAudioChannelLayoutTag_TMH_10_2_std@9502736$kAudioChannelLayoutTag_Unknown@4294901760$kAudioChannelLayoutTag_UseChannelBitmap@65536$kAudioChannelLayoutTag_UseChannelDescriptions@0$kAudioChannelLayoutTag_WAVE_4_0_B@12124164$kAudioChannelLayoutTag_WAVE_5_0_B@12189701$kAudioChannelLayoutTag_WAVE_5_1_B@12255238$kAudioChannelLayoutTag_WAVE_6_1@12320775$kAudioChannelLayoutTag_WAVE_7_1@12386312$kAudioChannelLayoutTag_XY@6881282$kAudioClipLightControlClassID@1668049264$kAudioClockDeviceClassID@1633905771$kAudioClockDevicePropertyAvailableNominalSampleRates@1853059619$kAudioClockDevicePropertyClockDomain@1668049764$kAudioClockDevicePropertyControlList@1668575852$kAudioClockDevicePropertyDeviceIsAlive@1818850926$kAudioClockDevicePropertyDeviceIsRunning@1735354734$kAudioClockDevicePropertyDeviceUID@1668639076$kAudioClockDevicePropertyLatency@1819569763$kAudioClockDevicePropertyNominalSampleRate@1853059700$kAudioClockDevicePropertyTransportType@1953653102$kAudioClockSourceControlClassID@1668047723$kAudioClockSourceControlPropertyItemKind@1668049771$kAudioClockSourceItemKindInternal@1768846368$kAudioControlClassID@1633907820$kAudioControlPropertyElement@1667591277$kAudioControlPropertyScope@1668506480$kAudioControlPropertyVariant@1668702578$kAudioDataDestinationControlClassID@1684370292$kAudioDataSourceControlClassID@1685287523$kAudioDeviceClassID@1633969526$kAudioDevicePermissionsError@560492391$kAudioDeviceProcessorOverload@1870030194$kAudioDevicePropertyActualSampleRate@1634955892$kAudioDevicePropertyAvailableNominalSampleRates@1853059619$kAudioDevicePropertyBufferFrameSize@1718839674$kAudioDevicePropertyBufferFrameSizeRange@1718843939$kAudioDevicePropertyBufferSize@1651730810$kAudioDevicePropertyBufferSizeRange@1651735075$kAudioDevicePropertyChannelCategoryName@1667460717$kAudioDevicePropertyChannelCategoryNameCFString@1818452846$kAudioDevicePropertyChannelName@1667788397$kAudioDevicePropertyChannelNameCFString@1818454126$kAudioDevicePropertyChannelNominalLineLevel@1852601964$kAudioDevicePropertyChannelNominalLineLevelNameForID@1668181110$kAudioDevicePropertyChannelNominalLineLevelNameForIDCFString@1818455660$kAudioDevicePropertyChannelNominalLineLevels@1852601891$kAudioDevicePropertyChannelNumberName@1668181613$kAudioDevicePropertyChannelNumberNameCFString@1818455662$kAudioDevicePropertyClipLight@1668049264$kAudioDevicePropertyClockDevice@1634755428$kAudioDevicePropertyClockDomain@1668049764$kAudioDevicePropertyClockSource@1668510307$kAudioDevicePropertyClockSourceKindForID@1668506475$kAudioDevicePropertyClockSourceNameForID@1668506478$kAudioDevicePropertyClockSourceNameForIDCFString@1818456942$kAudioDevicePropertyClockSources@1668506403$kAudioDevicePropertyConfigurationApplication@1667330160$kAudioDevicePropertyDataSource@1936945763$kAudioDevicePropertyDataSourceKindForID@1936941931$kAudioDevicePropertyDataSourceNameForID@1936941934$kAudioDevicePropertyDataSourceNameForIDCFString@1819501422$kAudioDevicePropertyDataSources@1936941859$kAudioDevicePropertyDeviceCanBeDefaultDevice@1684434036$kAudioDevicePropertyDeviceCanBeDefaultSystemDevice@1936092276$kAudioDevicePropertyDeviceHasChanged@1684629094$kAudioDevicePropertyDeviceIsAlive@1818850926$kAudioDevicePropertyDeviceIsRunning@1735354734$kAudioDevicePropertyDeviceIsRunningSomewhere@1735356005$kAudioDevicePropertyDeviceManufacturer@1835101042$kAudioDevicePropertyDeviceManufacturerCFString@1819107691$kAudioDevicePropertyDeviceName@1851878757$kAudioDevicePropertyDeviceNameCFString@1819173229$kAudioDevicePropertyDeviceUID@1969841184$kAudioDevicePropertyDriverShouldOwniSub@1769174370$kAudioDevicePropertyHighPassFilterSetting@1751740518$kAudioDevicePropertyHighPassFilterSettingNameForID@1667787120$kAudioDevicePropertyHighPassFilterSettingNameForIDCFString@1751740524$kAudioDevicePropertyHighPassFilterSettings@1751740451$kAudioDevicePropertyHogMode@1869180523$kAudioDevicePropertyIOCycleUsage@1852012899$kAudioDevicePropertyIOProcStreamUsage@1937077093$kAudioDevicePropertyIOStoppedAbnormally@1937010788$kAudioDevicePropertyIOThreadOSWorkgroup@1869838183$kAudioDevicePropertyIcon@1768124270$kAudioDevicePropertyIsHidden@1751737454$kAudioDevicePropertyJackIsConnected@1784767339$kAudioDevicePropertyLatency@1819569763$kAudioDevicePropertyListenback@1819504226$kAudioDevicePropertyModelUID@1836411236$kAudioDevicePropertyMute@1836414053$kAudioDevicePropertyNominalSampleRate@1853059700$kAudioDevicePropertyPhantomPower@1885888878$kAudioDevicePropertyPhaseInvert@1885893481$kAudioDevicePropertyPlayThru@1953002101$kAudioDevicePropertyPlayThruDestination@1835295859$kAudioDevicePropertyPlayThruDestinationNameForID@1835295854$kAudioDevicePropertyPlayThruDestinationNameForIDCFString@1835295843$kAudioDevicePropertyPlayThruDestinations@1835295779$kAudioDevicePropertyPlayThruSolo@1953002099$kAudioDevicePropertyPlayThruStereoPan@1836281966$kAudioDevicePropertyPlayThruStereoPanChannels@1836281891$kAudioDevicePropertyPlayThruVolumeDecibels@1836475490$kAudioDevicePropertyPlayThruVolumeDecibelsToScalar@1836462707$kAudioDevicePropertyPlayThruVolumeDecibelsToScalarTransferFunction@1836479590$kAudioDevicePropertyPlayThruVolumeRangeDecibels@1836475427$kAudioDevicePropertyPlayThruVolumeScalar@1836479331$kAudioDevicePropertyPlayThruVolumeScalarToDecibels@1836462692$kAudioDevicePropertyPlugIn@1886156135$kAudioDevicePropertyPreferredChannelLayout@1936879204$kAudioDevicePropertyPreferredChannelsForStereo@1684236338$kAudioDevicePropertyProcessMute@1634758765$kAudioDevicePropertyRegisterBufferList@1919055206$kAudioDevicePropertyRelatedDevices@1634429294$kAudioDevicePropertySafetyOffset@1935763060$kAudioDevicePropertyScopeInput@1768845428$kAudioDevicePropertyScopeOutput@1869968496$kAudioDevicePropertyScopePlayThrough@1886679669$kAudioDevicePropertySolo@1936682095$kAudioDevicePropertyStereoPan@1936744814$kAudioDevicePropertyStereoPanChannels@1936748067$kAudioDevicePropertyStreamConfiguration@1936482681$kAudioDevicePropertyStreamFormat@1936092532$kAudioDevicePropertyStreamFormatMatch@1936092525$kAudioDevicePropertyStreamFormatSupported@1936092479$kAudioDevicePropertyStreamFormats@1936092451$kAudioDevicePropertyStreams@1937009955$kAudioDevicePropertySubMute@1936553332$kAudioDevicePropertySubVolumeDecibels@1937140836$kAudioDevicePropertySubVolumeDecibelsToScalar@1935946358$kAudioDevicePropertySubVolumeDecibelsToScalarTransferFunction@1937142886$kAudioDevicePropertySubVolumeRangeDecibels@1937138723$kAudioDevicePropertySubVolumeScalar@1937140845$kAudioDevicePropertySubVolumeScalarToDecibels@1937125988$kAudioDevicePropertySupportsMixing@1835628607$kAudioDevicePropertyTalkback@1952541794$kAudioDevicePropertyTransportType@1953653102$kAudioDevicePropertyUsesVariableBufferFrameSizes@1986425722$kAudioDevicePropertyVoiceActivityDetectionEnable@1983996971$kAudioDevicePropertyVoiceActivityDetectionState@1983997011$kAudioDevicePropertyVolumeDecibels@1987013732$kAudioDevicePropertyVolumeDecibelsToScalar@1684157046$kAudioDevicePropertyVolumeDecibelsToScalarTransferFunction@1986229350$kAudioDevicePropertyVolumeRangeDecibels@1986290211$kAudioDevicePropertyVolumeScalar@1987013741$kAudioDevicePropertyVolumeScalarToDecibels@1983013986$kAudioDeviceStartTimeDontConsultDeviceFlag@2$kAudioDeviceStartTimeDontConsultHALFlag@4$kAudioDeviceStartTimeIsInputFlag@1$kAudioDeviceTransportTypeAVB@1700886114$kAudioDeviceTransportTypeAggregate@1735554416$kAudioDeviceTransportTypeAirPlay@1634300528$kAudioDeviceTransportTypeAutoAggregate@1718055536$kAudioDeviceTransportTypeBluetooth@1651275109$kAudioDeviceTransportTypeBluetoothLE@1651271009$kAudioDeviceTransportTypeBuiltIn@1651274862$kAudioDeviceTransportTypeContinuityCapture@1667457392$kAudioDeviceTransportTypeContinuityCaptureWired@1667463012$kAudioDeviceTransportTypeContinuityCaptureWireless@1667463020$kAudioDeviceTransportTypeDisplayPort@1685090932$kAudioDeviceTransportTypeFireWire@825440564$kAudioDeviceTransportTypeHDMI@1751412073$kAudioDeviceTransportTypePCI@1885563168$kAudioDeviceTransportTypeThunderbolt@1953002862$kAudioDeviceTransportTypeUSB@1970496032$kAudioDeviceTransportTypeUnknown@0$kAudioDeviceTransportTypeVirtual@1986622068$kAudioDeviceUnknown@0$kAudioDeviceUnsupportedFormatError@560226676$kAudioEndPointClassID@1701733488$kAudioEndPointDeviceClassID@1701078390$kAudioEndPointDevicePropertyComposition@1633906541$kAudioEndPointDevicePropertyEndPointList@1634169456$kAudioEndPointDevicePropertyIsPrivate@1886546294$kAudioFormat60958AC3@1667326771$kAudioFormatAC3@1633889587$kAudioFormatAES3@1634038579$kAudioFormatALaw@1634492791$kAudioFormatAMR@1935764850$kAudioFormatAMR_WB@1935767394$kAudioFormatAPAC@1634754915$kAudioFormatAppleIMA4@1768775988$kAudioFormatAppleLossless@1634492771$kAudioFormatAudible@1096107074$kAudioFormatDVIIntelIMA@1836253201$kAudioFormatEnhancedAC3@1700998451$kAudioFormatFLAC@1718378851$kAudioFormatFlagIsAlignedHigh@16$kAudioFormatFlagIsBigEndian@2$kAudioFormatFlagIsFloat@1$kAudioFormatFlagIsNonInterleaved@32$kAudioFormatFlagIsNonMixable@64$kAudioFormatFlagIsPacked@8$kAudioFormatFlagIsSignedInteger@4$kAudioFormatFlagsAreAllClear@2147483648$kAudioFormatFlagsAudioUnitCanonical@41$kAudioFormatFlagsCanonical@9$kAudioFormatFlagsNativeEndian@0$kAudioFormatFlagsNativeFloatPacked@9$kAudioFormatLinearPCM@1819304813$kAudioFormatMACE3@1296122675$kAudioFormatMACE6@1296122678$kAudioFormatMIDIStream@1835623529$kAudioFormatMPEG4AAC@1633772320$kAudioFormatMPEG4AAC_ELD@1633772389$kAudioFormatMPEG4AAC_ELD_SBR@1633772390$kAudioFormatMPEG4AAC_ELD_V2@1633772391$kAudioFormatMPEG4AAC_HE@1633772392$kAudioFormatMPEG4AAC_HE_V2@1633772400$kAudioFormatMPEG4AAC_LD@1633772396$kAudioFormatMPEG4AAC_Spatial@1633772403$kAudioFormatMPEG4CELP@1667591280$kAudioFormatMPEG4HVXC@1752594531$kAudioFormatMPEG4TwinVQ@1953986161$kAudioFormatMPEGD_USAC@1970495843$kAudioFormatMPEGLayer1@778924081$kAudioFormatMPEGLayer2@778924082$kAudioFormatMPEGLayer3@778924083$kAudioFormatMicrosoftGSM@1836253233$kAudioFormatOpus@1869641075$kAudioFormatParameterValueStream@1634760307$kAudioFormatQDesign@1363430723$kAudioFormatQDesign2@1363430706$kAudioFormatQUALCOMM@1365470320$kAudioFormatTimeCode@1953066341$kAudioFormatULaw@1970037111$kAudioFormatiLBC@1768710755$kAudioHardwareBadDeviceError@560227702$kAudioHardwareBadObjectError@560947818$kAudioHardwareBadPropertySizeError@561211770$kAudioHardwareBadStreamError@561214578$kAudioHardwareIllegalOperationError@1852797029$kAudioHardwareNoError@0$kAudioHardwareNotReadyError@1852990585$kAudioHardwareNotRunningError@1937010544$kAudioHardwarePowerHintFavorSavingPower@1$kAudioHardwarePowerHintNone@0$kAudioHardwarePropertyBootChimeVolumeDecibels@1650620004$kAudioHardwarePropertyBootChimeVolumeDecibelsToScalar@1650733686$kAudioHardwarePropertyBootChimeVolumeDecibelsToScalarTransferFunction@1651930214$kAudioHardwarePropertyBootChimeVolumeRangeDecibels@1650615331$kAudioHardwarePropertyBootChimeVolumeScalar@1650620019$kAudioHardwarePropertyBootChimeVolumeScalarToDecibels@1651913316$kAudioHardwarePropertyBoxList@1651472419$kAudioHardwarePropertyClockDeviceList@1668049699$kAudioHardwarePropertyDefaultInputDevice@1682533920$kAudioHardwarePropertyDefaultOutputDevice@1682929012$kAudioHardwarePropertyDefaultSystemOutputDevice@1934587252$kAudioHardwarePropertyDeviceForUID@1685416292$kAudioHardwarePropertyDevices@1684370979$kAudioHardwarePropertyHogModeIsAllowed@1752131442$kAudioHardwarePropertyIsInitingOrExiting@1768845172$kAudioHardwarePropertyMixStereoToMono@1937010031$kAudioHardwarePropertyPlugInForBundleID@1885954665$kAudioHardwarePropertyPlugInList@1886152483$kAudioHardwarePropertyPowerHint@1886353256$kAudioHardwarePropertyProcessInputMute@1886218606$kAudioHardwarePropertyProcessIsAudible@1886221684$kAudioHardwarePropertyProcessIsMain@1835100526$kAudioHardwarePropertyProcessIsMaster@1835103092$kAudioHardwarePropertyProcessObjectList@1886548771$kAudioHardwarePropertyRunLoop@1919839344$kAudioHardwarePropertyServiceRestarted@1936880500$kAudioHardwarePropertySleepingIsAllowed@1936483696$kAudioHardwarePropertyTapList@1953526563$kAudioHardwarePropertyTranslateBundleIDToPlugIn@1651074160$kAudioHardwarePropertyTranslateBundleIDToTransportManager@1953325673$kAudioHardwarePropertyTranslatePIDToProcessObject@1768174192$kAudioHardwarePropertyTranslateUIDToBox@1969841250$kAudioHardwarePropertyTranslateUIDToClockDevice@1969841251$kAudioHardwarePropertyTranslateUIDToDevice@1969841252$kAudioHardwarePropertyTranslateUIDToTap@1969841268$kAudioHardwarePropertyTransportManagerList@1953326883$kAudioHardwarePropertyUnloadingIsAllowed@1970170980$kAudioHardwarePropertyUserIDChanged@1702193508$kAudioHardwarePropertyUserSessionIsActiveOrHeadless@1970496882$kAudioHardwareUnknownPropertyError@2003332927$kAudioHardwareUnspecifiedError@2003329396$kAudioHardwareUnsupportedOperationError@1970171760$kAudioHighPassFilterControlClassID@1751740518$kAudioISubOwnerControlClassID@1635017576$kAudioJackControlClassID@1784767339$kAudioLFEMuteControlClassID@1937072749$kAudioLFEVolumeControlClassID@1937072758$kAudioLevelControlClassID@1818588780$kAudioLevelControlPropertyConvertDecibelsToScalar@1818453107$kAudioLevelControlPropertyConvertScalarToDecibels@1818456932$kAudioLevelControlPropertyDecibelRange@1818453106$kAudioLevelControlPropertyDecibelValue@1818453110$kAudioLevelControlPropertyDecibelsToScalarTransferFunction@1818457190$kAudioLevelControlPropertyScalarValue@1818456950$kAudioLevelControlTranferFunction10Over1@13$kAudioLevelControlTranferFunction11Over1@14$kAudioLevelControlTranferFunction12Over1@15$kAudioLevelControlTranferFunction1Over2@2$kAudioLevelControlTranferFunction1Over3@1$kAudioLevelControlTranferFunction2Over1@5$kAudioLevelControlTranferFunction3Over1@6$kAudioLevelControlTranferFunction3Over2@4$kAudioLevelControlTranferFunction3Over4@3$kAudioLevelControlTranferFunction4Over1@7$kAudioLevelControlTranferFunction5Over1@8$kAudioLevelControlTranferFunction6Over1@9$kAudioLevelControlTranferFunction7Over1@10$kAudioLevelControlTranferFunction8Over1@11$kAudioLevelControlTranferFunction9Over1@12$kAudioLevelControlTranferFunctionLinear@0$kAudioLineLevelControlClassID@1852601964$kAudioListenbackControlClassID@1819504226$kAudioMuteControlClassID@1836414053$kAudioObjectClassID@1634689642$kAudioObjectClassIDWildcard@707406378$kAudioObjectPropertyBaseClass@1650682995$kAudioObjectPropertyClass@1668047219$kAudioObjectPropertyControlList@1668575852$kAudioObjectPropertyCreator@1869638759$kAudioObjectPropertyElementCategoryName@1818452846$kAudioObjectPropertyElementMain@0$kAudioObjectPropertyElementMaster@0$kAudioObjectPropertyElementName@1818454126$kAudioObjectPropertyElementNumberName@1818455662$kAudioObjectPropertyElementWildcard@4294967295$kAudioObjectPropertyFirmwareVersion@1719105134$kAudioObjectPropertyIdentify@1768187246$kAudioObjectPropertyListenerAdded@1818850145$kAudioObjectPropertyListenerRemoved@1818850162$kAudioObjectPropertyManufacturer@1819107691$kAudioObjectPropertyModelName@1819111268$kAudioObjectPropertyName@1819173229$kAudioObjectPropertyOwnedObjects@1870098020$kAudioObjectPropertyOwner@1937007734$kAudioObjectPropertyScopeGlobal@1735159650$kAudioObjectPropertyScopeInput@1768845428$kAudioObjectPropertyScopeOutput@1869968496$kAudioObjectPropertyScopePlayThrough@1886679669$kAudioObjectPropertyScopeWildcard@707406378$kAudioObjectPropertySelectorWildcard@707406378$kAudioObjectPropertySerialNumber@1936618861$kAudioObjectSystemObject@1$kAudioObjectUnknown@0$kAudioPhantomPowerControlClassID@1885888878$kAudioPhaseInvertControlClassID@1885893481$kAudioPlugInClassID@1634757735$kAudioPlugInCreateAggregateDevice@1667327847$kAudioPlugInDestroyAggregateDevice@1684105063$kAudioPlugInPropertyBoxList@1651472419$kAudioPlugInPropertyBundleID@1885956452$kAudioPlugInPropertyClockDeviceList@1668049699$kAudioPlugInPropertyDeviceList@1684370979$kAudioPlugInPropertyTranslateUIDToBox@1969841250$kAudioPlugInPropertyTranslateUIDToClockDevice@1969841251$kAudioPlugInPropertyTranslateUIDToDevice@1969841252$kAudioProcessClassID@1668050548$kAudioProcessPropertyBundleID@1885497700$kAudioProcessPropertyDevices@1885632035$kAudioProcessPropertyIsRunning@1885958719$kAudioProcessPropertyIsRunningInput@1885958761$kAudioProcessPropertyIsRunningOutput@1885958767$kAudioProcessPropertyPID@1886415204$kAudioPropertyWildcardChannel@4294967295$kAudioPropertyWildcardPropertyID@707406378$kAudioPropertyWildcardSection@255$kAudioSelectorControlClassID@1936483188$kAudioSelectorControlItemKindSpacer@1936745330$kAudioSelectorControlPropertyAvailableItems@1935892841$kAudioSelectorControlPropertyCurrentItem@1935893353$kAudioSelectorControlPropertyItemKind@1668049771$kAudioSelectorControlPropertyItemName@1935894894$kAudioSliderControlClassID@1936483442$kAudioSliderControlPropertyRange@1935962738$kAudioSliderControlPropertyValue@1935962742$kAudioSoloControlClassID@1936682095$kAudioStereoPanControlClassID@1936744814$kAudioStereoPanControlPropertyPanningChannels@1936745315$kAudioStereoPanControlPropertyValue@1936745334$kAudioStreamClassID@1634956402$kAudioStreamPropertyAvailablePhysicalFormats@1885762657$kAudioStreamPropertyAvailableVirtualFormats@1936092513$kAudioStreamPropertyDirection@1935960434$kAudioStreamPropertyIsActive@1935762292$kAudioStreamPropertyLatency@1819569763$kAudioStreamPropertyOwningDevice@1937007734$kAudioStreamPropertyPhysicalFormat@1885762592$kAudioStreamPropertyPhysicalFormatMatch@1885762669$kAudioStreamPropertyPhysicalFormatSupported@1885762623$kAudioStreamPropertyPhysicalFormats@1885762595$kAudioStreamPropertyStartingChannel@1935894638$kAudioStreamPropertyTerminalType@1952805485$kAudioStreamPropertyVirtualFormat@1936092532$kAudioStreamTerminalTypeDigitalAudioInterface@1936745574$kAudioStreamTerminalTypeDisplayPort@1685090932$kAudioStreamTerminalTypeHDMI@1751412073$kAudioStreamTerminalTypeHeadphones@1751412840$kAudioStreamTerminalTypeHeadsetMicrophone@1752000867$kAudioStreamTerminalTypeLFESpeaker@1818649971$kAudioStreamTerminalTypeLine@1818848869$kAudioStreamTerminalTypeMicrophone@1835623282$kAudioStreamTerminalTypeReceiverMicrophone@1919773027$kAudioStreamTerminalTypeReceiverSpeaker@1920168043$kAudioStreamTerminalTypeSpeaker@1936747378$kAudioStreamTerminalTypeTTY@1953790303$kAudioStreamTerminalTypeUnknown@0$kAudioStreamUnknown@0$kAudioSubDeviceClassID@1634956642$kAudioSubDeviceDriftCompensationHighQuality@96$kAudioSubDeviceDriftCompensationLowQuality@32$kAudioSubDeviceDriftCompensationMaxQuality@127$kAudioSubDeviceDriftCompensationMediumQuality@64$kAudioSubDeviceDriftCompensationMinQuality@0$kAudioSubDevicePropertyDriftCompensation@1685218932$kAudioSubDevicePropertyDriftCompensationQuality@1685218929$kAudioSubDevicePropertyExtraLatency@2020373603$kAudioSubTapClassID@1937006960$kAudioSubTapPropertyDriftCompensation@1685218932$kAudioSubTapPropertyDriftCompensationQuality@1685218929$kAudioSubTapPropertyExtraLatency@2020373603$kAudioSystemObjectClassID@1634957683$kAudioTalkbackControlClassID@1952541794$kAudioTapClassID@1952672883$kAudioTapPropertyDescription@1952740195$kAudioTapPropertyFormat@1952869748$kAudioTapPropertyUID@1953851748$kAudioTimeStampHostTimeValid@2$kAudioTimeStampNothingValid@0$kAudioTimeStampRateScalarValid@4$kAudioTimeStampSMPTETimeValid@16$kAudioTimeStampSampleHostTimeValid@3$kAudioTimeStampSampleTimeValid@1$kAudioTimeStampWordClockTimeValid@8$kAudioTransportManagerClassID@1953656941$kAudioTransportManagerCreateEndPointDevice@1667523958$kAudioTransportManagerDestroyEndPointDevice@1684301174$kAudioTransportManagerPropertyEndPointList@1701733411$kAudioTransportManagerPropertyTranslateUIDToEndPoint@1969841253$kAudioTransportManagerPropertyTransportType@1953653102$kAudioVolumeControlClassID@1986817381$kAudio_BadFilePathError@561017960$kAudio_FileNotFoundError@-43$kAudio_FilePermissionError@-54$kAudio_MemFullError@-108$kAudio_NoError@0$kAudio_ParamError@-50$kAudio_TooManyFilesOpenError@-42$kAudio_UnimplementedError@-4$kLinearPCMFormatFlagIsAlignedHigh@16$kLinearPCMFormatFlagIsBigEndian@2$kLinearPCMFormatFlagIsFloat@1$kLinearPCMFormatFlagIsNonInterleaved@32$kLinearPCMFormatFlagIsNonMixable@64$kLinearPCMFormatFlagIsPacked@8$kLinearPCMFormatFlagIsSignedInteger@4$kLinearPCMFormatFlagsAreAllClear@2147483648$kLinearPCMFormatFlagsSampleFractionMask@8064$kLinearPCMFormatFlagsSampleFractionShift@7$kMPEG4Object_AAC_LC@2$kMPEG4Object_AAC_LTP@4$kMPEG4Object_AAC_Main@1$kMPEG4Object_AAC_SBR@5$kMPEG4Object_AAC_SSR@3$kMPEG4Object_AAC_Scalable@6$kMPEG4Object_CELP@8$kMPEG4Object_HVXC@9$kMPEG4Object_TwinVQ@7$kSMPTETimeRunning@2$kSMPTETimeType2398@11$kSMPTETimeType24@0$kSMPTETimeType25@1$kSMPTETimeType2997@4$kSMPTETimeType2997Drop@5$kSMPTETimeType30@3$kSMPTETimeType30Drop@2$kSMPTETimeType50@10$kSMPTETimeType5994@7$kSMPTETimeType5994Drop@9$kSMPTETimeType60@6$kSMPTETimeType60Drop@8$kSMPTETimeUnknown@0$kSMPTETimeValid@1$"""
misc.update(
    {
        "CATapMuteBehavior": NewType("CATapMuteBehavior", int),
        "AudioHardwarePowerHint": NewType("AudioHardwarePowerHint", int),
        "AudioLevelControlTransferFunction": NewType(
            "AudioLevelControlTransferFunction", int
        ),
    }
)
misc.update({})
misc.update(
    {
        "kAudioAggregateDeviceMainSubDeviceKey": b"master",
        "kAudioSubTapDriftCompensationQualityKey": b"drift quality",
        "kAudioEndPointInputChannelsKey": b"channels-in",
        "kAudioSubTapExtraOutputLatencyKey": b"latency-out",
        "kAudioEndPointOutputChannelsKey": b"channels-out",
        "kAudioAggregateDeviceNameKey": b"name",
        "kAudioSubDeviceExtraOutputLatencyKey": b"latency-out",
        "kAudioAggregateDeviceMasterSubDeviceKey": b"master",
        "kAudioSubDeviceUIDKey": b"uid",
        "kAudioSubDeviceExtraInputLatencyKey": b"latency-in",
        "kAudioEndPointDeviceEndPointListKey": b"endpoints",
        "kAudioEndPointDeviceMainEndPointKey": b"main",
        "kAudioEndPointDeviceUIDKey": b"uid",
        "kAudioAggregateDeviceClockDeviceKey": b"clock",
        "kAudioAggregateDeviceTapAutoStartKey": b"tapautostart",
        "kAudioEndPointDeviceNameKey": b"name",
        "kAudioEndPointDeviceIsPrivateKey": b"private",
        "kAudioAggregateDeviceTapListKey": b"taps",
        "kAudioSubTapDriftCompensationKey": b"drift",
        "kAudioSubDeviceOutputChannelsKey": b"channels-out",
        "kAudioEndPointNameKey": b"name",
        "kAudioSubDeviceDriftCompensationQualityKey": b"drift quality",
        "kAudioSubTapExtraInputLatencyKey": b"latency-in",
        "kAudioSubDeviceDriftCompensationKey": b"drift",
        "kAudioEndPointDeviceMasterEndPointKey": b"master",
        "kAudioSubDeviceNameKey": b"name",
        "kAudioHardwareRunLoopMode": b"com.apple.audio.CoreAudio",
        "kAudioAggregateDeviceUIDKey": b"uid",
        "kAudioSubDeviceInputChannelsKey": b"channels-in",
        "kAudioAggregateDeviceSubDeviceListKey": b"subdevices",
        "kAudioAggregateDeviceIsStackedKey": b"stacked",
        "kAudioAggregateDeviceIsPrivateKey": b"private",
        "kAudioSubTapUIDKey": b"uid",
        "kAudioEndPointUIDKey": b"uid",
    }
)
functions = {
    "AudioDeviceSetProperty": (
        b"iI^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}IZII^v",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                6: {"c_array_length_in_arg": 5, "type_modifier": "n"},
            }
        },
    ),
    "AudioDeviceRemovePropertyListener": (
        b"iIIZI^?",
        "",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"I"},
                            2: {"type": b"Z"},
                            3: {"type": b"I"},
                            4: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "FillOutAudioTimeStampWithSampleAndHostTime": (
        b"vo^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}dQ",
    ),
    "AudioObjectRemovePropertyListenerBlock": (
        b"iI^{AudioObjectPropertyAddress=III}@@?",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "I"},
                            2: {
                                "type": "n^{AudioObjectPropertyAddress=III}",
                                "c_array_length_in_arg": 1,
                            },
                        },
                    }
                },
            }
        },
    ),
    "AudioGetHostClockMinimumTimeDelta": (b"I", "", {"variadic": True}),
    "AudioHardwareGetProperty": (
        b"iI^I^v",
        "",
        {
            "arguments": {
                1: {"type_modifier": "N"},
                2: {"c_array_length_in_arg": 1, "type_modifier": "o"},
            }
        },
    ),
    "AudioDeviceStartAtTime": (
        b"iI^?^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}I",
        "",
        {"arguments": {2: {"type_modifier": "N"}}},
    ),
    "AudioConvertHostTimeToNanos": (b"QQ",),
    "AudioDeviceGetProperty": (
        b"iIIZI^I^v",
        "",
        {
            "arguments": {
                4: {"type_modifier": "N"},
                5: {"c_array_length_in_arg": 4, "type_modifier": "o"},
            }
        },
    ),
    "AudioHardwareDevicesDied": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}I^I",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "AudioHardwareCreateProcessTap": (
        b"i@^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "AudioHardwareClaimAudioDeviceID": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "AudioObjectSetPropertyData": (
        b"iI^{AudioObjectPropertyAddress=III}I^vI^v",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                3: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                5: {"c_array_length_in_arg": 4, "type_modifier": "n"},
            }
        },
    ),
    "AudioObjectAddPropertyListener": (
        b"iI^{AudioObjectPropertyAddress=III}^?^v",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                2: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"I"},
                            2: {"type": b"n^{AudioObjectPropertyAddress=III}"},
                            3: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                },
            }
        },
    ),
    "AudioHardwareAddRunLoopSource": (b"i^{__CFRunLoopSource=}",),
    "AudioStreamSetProperty": (
        b"iI^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}III^v",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                5: {"c_array_length_in_arg": 4, "type_modifier": "n"},
            }
        },
    ),
    "AudioStreamAddPropertyListener": (
        b"iIII^?^v",
        "",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"I"},
                            2: {"type": b"I"},
                            3: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "AudioHardwareDevicePropertyChanged": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}IIZI",
    ),
    "AudioStreamGetPropertyInfo": (
        b"iIII^I^Z",
        "",
        {"arguments": {3: {"type_modifier": "o"}, 4: {"type_modifier": "o"}}},
    ),
    "AudioHardwareDevicesCreated": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}I^I",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "AudioGetCurrentHostTime": (b"Q", "", {"variadic": True}),
    "AudioObjectCreate": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}II^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}},
        },
    ),
    "AudioConvertNanosToHostTime": (b"QQ",),
    "AudioObjectRemovePropertyListener": (
        b"iI^{AudioObjectPropertyAddress=III}^?^v",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                2: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"I"},
                            2: {"type": b"n^{AudioObjectPropertyAddress=III}"},
                            3: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                },
            }
        },
    ),
    "AudioDeviceGetNearestStartTime": (
        b"iI^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}I",
        "",
        {"arguments": {1: {"type_modifier": "N"}}},
    ),
    "AudioHardwareDestroyProcessTap": (b"iI",),
    "AudioStreamRemovePropertyListener": (
        b"iIII^?",
        "",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"I"},
                            2: {"type": b"I"},
                            3: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "AudioDeviceStop": (b"iI^?",),
    "AudioHardwareGetPropertyInfo": (
        b"iI^I^Z",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "AudioHardwareStreamPropertyChanged": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}IIII",
    ),
    "AudioHardwareUnload": (b"i", "", {"variadic": True}),
    "AudioStreamGetProperty": (
        b"iIII^I^v",
        "",
        {
            "arguments": {
                3: {"type_modifier": "N"},
                4: {"c_array_length_in_arg": 3, "type_modifier": "o"},
            }
        },
    ),
    "AudioHardwareClaimAudioStreamID": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}I^I",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "FillOutAudioTimeStampWithSampleTime": (
        b"vo^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}d",
    ),
    "CalculateLPCMFlags": (b"IIIBBB",),
    "AudioHardwareCreateAggregateDevice": (
        b"i^{__CFDictionary=}^I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "AudioHardwareDestroyAggregateDevice": (b"iI",),
    "AudioDeviceAddIOProc": (
        b"iI^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            2: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                            3: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            4: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                            5: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            6: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "FillOutAudioTimeStampWithHostTime": (
        b"vo^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}Q",
    ),
    "AudioObjectGetPropertyData": (
        b"iI^{AudioObjectPropertyAddress=III}I^v^I^v",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                3: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {"type_modifier": "N"},
                5: {"c_array_length_in_arg": 4, "type_modifier": "o"},
            }
        },
    ),
    "FillOutASBDForLPCM": (b"vo^{AudioStreamBasicDescription=dIIIIIIII}dIIIBBB",),
    "AudioObjectShow": (b"vI",),
    "AudioObjectPropertiesChanged": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}II^{AudioObjectPropertyAddress=III}",
        "",
        {"arguments": {3: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "AudioHardwareSetProperty": (
        b"iII^v",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "IsAudioFormatNativeEndian": (b"Bn^{AudioStreamBasicDescription=dIIIIIIII}",),
    "AudioHardwareRemoveRunLoopSource": (b"i^{__CFRunLoopSource=}",),
    "AudioDeviceGetCurrentTime": (
        b"iI^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "AudioChannelLayoutTag_GetNumberOfChannels": (b"II",),
    "AudioHardwareStreamsCreated": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}II^I",
        "",
        {"arguments": {3: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "AudioGetHostClockFrequency": (b"d", "", {"variadic": True}),
    "AudioDeviceStart": (b"iI^?",),
    "AudioObjectAddPropertyListenerBlock": (
        b"iI^{AudioObjectPropertyAddress=III}@@?",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "I"},
                            2: {
                                "type": "n^{AudioObjectPropertyAddress=III}",
                                "c_array_length_in_arg": 1,
                            },
                        },
                    }
                },
            }
        },
    ),
    "AudioDeviceCreateIOProcIDWithBlock": (
        b"i^^?I@@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                0: {"type_modifier": "o"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {
                                "type": "n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            2: {"type": "^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                            3: {
                                "type": "n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            4: {"type": "^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                            5: {
                                "type": "N^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                        },
                    }
                },
            },
        },
    ),
    "AudioDeviceRemoveIOProc": (
        b"iI^?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            2: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                            3: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            4: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                            5: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            6: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "AudioDeviceAddPropertyListener": (
        b"iIIZI^?^v",
        "",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"I"},
                            2: {"type": b"Z"},
                            3: {"type": b"I"},
                            4: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "AudioObjectHasProperty": (
        b"ZI^{AudioObjectPropertyAddress=III}",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "AudioHardwareRemovePropertyListener": (
        b"iI^?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {0: {"type": b"I"}, 1: {"type": b"^v"}},
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "AudioDeviceGetPropertyInfo": (
        b"iIIZI^I^Z",
        "",
        {"arguments": {4: {"type_modifier": "o"}, 5: {"type_modifier": "o"}}},
    ),
    "AudioObjectsPublishedAndDied": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}II^II^I",
        "",
        {
            "arguments": {
                3: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                5: {"c_array_length_in_arg": 4, "type_modifier": "n"},
            }
        },
    ),
    "AudioDeviceRead": (
        b"iI^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}^{AudioBufferList=I[1{AudioBuffer=II^v}]}",
        "",
        {"arguments": {1: {"type_modifier": "n"}, 2: {"type_modifier": "o"}}},
    ),
    "AudioHardwareStreamsDied": (
        b"i^^{AudioHardwarePlugInInterface=^v^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?^?}II^I",
        "",
        {"arguments": {3: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "AudioObjectIsPropertySettable": (
        b"iI^{AudioObjectPropertyAddress=III}^Z",
        "",
        {"arguments": {1: {"type_modifier": "n"}, 2: {"type_modifier": "o"}}},
    ),
    "AudioHardwareAddPropertyListener": (
        b"iI^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {0: {"type": b"I"}, 1: {"type": b"^v"}},
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "AudioDeviceTranslateTime": (
        b"iI^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}",
        "",
        {"arguments": {1: {"type_modifier": "n"}, 2: {"type_modifier": "o"}}},
    ),
    "AudioDeviceCreateIOProcID": (
        b"iI^?^v^^?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            2: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                            3: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            4: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                            5: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            6: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                },
                3: {"type_modifier": "o"},
            },
        },
    ),
    "AudioObjectGetPropertyDataSize": (
        b"iI^{AudioObjectPropertyAddress=III}I^v^I",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                3: {"c_array_length_in_arg": (2, 4), "type_modifier": "n"},
                4: {"type_modifier": "o"},
            }
        },
    ),
    "AudioDeviceDestroyIOProcID": (b"iI^?",),
}
aliases = {
    "kAudioChannelLayoutTag_ITU_2_0": "kAudioChannelLayoutTag_Stereo",
    "kAudioChannelLayoutTag_Logic_Stereo": "kAudioChannelLayoutTag_Stereo",
    "kAudioChannelLayoutTag_WAVE_5_0_A": "kAudioChannelLayoutTag_MPEG_5_0_A",
    "kAudioChannelLayoutTag_DVD_19": "kAudioChannelLayoutTag_MPEG_5_0_B",
    "kAudioChannelLayoutTag_DVD_12": "kAudioChannelLayoutTag_MPEG_5_1_A",
    "kAudioChannelLayoutTag_DVD_13": "kAudioChannelLayoutTag_DVD_8",
    "kAudioChannelLayoutTag_DVD_16": "kAudioChannelLayoutTag_DVD_11",
    "kAudioChannelLayoutTag_DVD_17": "kAudioChannelLayoutTag_DVD_12",
    "kAudioChannelLayoutTag_DVD_14": "kAudioChannelLayoutTag_DVD_9",
    "kAudioChannelLayoutTag_DVD_15": "kAudioChannelLayoutTag_DVD_10",
    "kAudioChannelLayoutTag_MPEG_1_0": "kAudioChannelLayoutTag_Mono",
    "kAudioChannelLayoutTag_Ogg_4_0": "kAudioChannelLayoutTag_WAVE_4_0_B",
    "kAudioChannelLayoutTag_DVD_0": "kAudioChannelLayoutTag_Mono",
    "kAudioChannelBit_CenterTopMiddle": "kAudioChannelBit_TopCenterSurround",
    "kAudioObjectPropertyElementMaster": "kAudioObjectPropertyElementMain",
    "kAudioChannelLayoutTag_Logic_7_1_SDDS_B": "kAudioChannelLayoutTag_MPEG_7_1_B",
    "kAudioChannelLayoutTag_Logic_7_1_SDDS_C": "kAudioChannelLayoutTag_Emagic_Default_7_1",
    "kAudioDeviceUnknown": "kAudioObjectUnknown",
    "kAudioChannelLayoutTag_Logic_7_1_SDDS_A": "kAudioChannelLayoutTag_MPEG_7_1_A",
    "kAudioStreamUnknown": "kAudioObjectUnknown",
    "kAudioChannelLayoutTag_AAC_Quadraphonic": "kAudioChannelLayoutTag_Quadraphonic",
    "kAudioChannelLayoutTag_DVD_20": "kAudioChannelLayoutTag_MPEG_5_1_B",
    "kAudioChannelLayoutTag_Logic_7_1_A": "kAudioChannelLayoutTag_AudioUnit_7_1",
    "kLinearPCMFormatFlagIsNonMixable": "kAudioFormatFlagIsNonMixable",
    "kAudioChannelLayoutTag_Logic_7_1_C": "kAudioChannelLayoutTag_MPEG_7_1_C",
    "kAudioChannelLayoutTag_Logic_Quadraphonic": "kAudioChannelLayoutTag_Quadraphonic",
    "kAudioChannelLayoutTag_MPEG_2_0": "kAudioChannelLayoutTag_Stereo",
    "kAudioClockSourceControlPropertyItemKind": "kAudioSelectorControlPropertyItemKind",
    "kAudioChannelLayoutTag_ITU_3_2_1": "kAudioChannelLayoutTag_MPEG_5_1_A",
    "kAudioChannelLayoutTag_Logic_6_0_A": "kAudioChannelLayoutTag_AAC_6_0",
    "kAudioChannelLayoutTag_Logic_6_0_C": "kAudioChannelLayoutTag_AudioUnit_6_0",
    "kAudioChannelLayoutTag_ITU_3_4_1": "kAudioChannelLayoutTag_MPEG_7_1_C",
    "kAudioChannelLayoutTag_AudioUnit_7_1": "kAudioChannelLayoutTag_MPEG_7_1_C",
    "kAudioChannelBit_CenterTopFront": "kAudioChannelBit_VerticalHeightCenter",
    "kAudioChannelLayoutTag_AudioUnit_4": "kAudioChannelLayoutTag_Quadraphonic",
    "kAudioChannelLayoutTag_AudioUnit_5": "kAudioChannelLayoutTag_Pentagonal",
    "kAudioChannelLayoutTag_AudioUnit_6": "kAudioChannelLayoutTag_Hexagonal",
    "kAudioChannelLayoutTag_WAVE_5_1_A": "kAudioChannelLayoutTag_MPEG_5_1_A",
    "kAudioChannelLayoutTag_AudioUnit_8": "kAudioChannelLayoutTag_Octagonal",
    "kAudioChannelLayoutTag_Logic_5_1_C": "kAudioChannelLayoutTag_MPEG_5_1_C",
    "kAudioChannelLayoutTag_Logic_5_1_A": "kAudioChannelLayoutTag_MPEG_5_1_A",
    "kAudioChannelLayoutTag_AudioUnit_6_1": "kAudioChannelLayoutTag_MPEG_6_1_A",
    "kAudioChannelLayoutTag_Logic_5_1_D": "kAudioChannelLayoutTag_MPEG_5_1_D",
    "kAudioAggregateDevicePropertyMasterSubDevice": "kAudioAggregateDevicePropertyMainSubDevice",
    "kAudioStreamPropertyOwningDevice": "kAudioObjectPropertyOwner",
    "kAudioChannelLayoutTag_Logic_Atmos_7_1_2": "kAudioChannelLayoutTag_Atmos_7_1_2",
    "kAudioChannelLabel_CenterTopMiddle": "kAudioChannelLabel_TopCenterSurround",
    "kLinearPCMFormatFlagIsPacked": "kAudioFormatFlagIsPacked",
    "kAudioChannelLayoutTag_Logic_5_0_A": "kAudioChannelLayoutTag_MPEG_5_0_A",
    "kAudioChannelLayoutTag_ITU_1_0": "kAudioChannelLayoutTag_Mono",
    "kAudioChannelLayoutTag_DVD_1": "kAudioChannelLayoutTag_Stereo",
    "kAudioDevicePropertyScopeOutput": "kAudioObjectPropertyScopeOutput",
    "kAudioChannelLayoutTag_DVD_3": "kAudioChannelLayoutTag_ITU_2_2",
    "kAudioChannelLayoutTag_DVD_7": "kAudioChannelLayoutTag_MPEG_3_0_A",
    "kAudioChannelLayoutTag_DVD_8": "kAudioChannelLayoutTag_MPEG_4_0_A",
    "kAudioChannelLayoutTag_DVD_9": "kAudioChannelLayoutTag_MPEG_5_0_A",
    "kAudioChannelLayoutTag_WAVE_4_0_A": "kAudioChannelLayoutTag_ITU_2_2",
    "kAudioChannelLayoutTag_Logic_Mono": "kAudioChannelLayoutTag_Mono",
    "kLinearPCMFormatFlagIsNonInterleaved": "kAudioFormatFlagIsNonInterleaved",
    "kAudioChannelLayoutTag_CICP_4": "kAudioChannelLayoutTag_MPEG_4_0_A",
    "kAudioChannelLayoutTag_CICP_5": "kAudioChannelLayoutTag_MPEG_5_0_A",
    "kAudioChannelLayoutTag_CICP_6": "kAudioChannelLayoutTag_MPEG_5_1_A",
    "kAudioChannelLayoutTag_CICP_7": "kAudioChannelLayoutTag_MPEG_7_1_B",
    "kAudioChannelLayoutTag_CICP_1": "kAudioChannelLayoutTag_MPEG_1_0",
    "kAudioChannelLayoutTag_CICP_2": "kAudioChannelLayoutTag_MPEG_2_0",
    "kAudioChannelLayoutTag_CICP_3": "kAudioChannelLayoutTag_MPEG_3_0_A",
    "kAudioChannelLayoutTag_CICP_9": "kAudioChannelLayoutTag_ITU_2_1",
    "kLinearPCMFormatFlagsAreAllClear": "kAudioFormatFlagsAreAllClear",
    "kAudioDevicePropertyScopePlayThrough": "kAudioObjectPropertyScopePlayThrough",
    "kAudioChannelLayoutTag_Logic_Atmos_5_1_4": "kAudioChannelLayoutTag_Atmos_5_1_4",
    "kAudioChannelLayoutTag_Logic_Atmos_5_1_2": "kAudioChannelLayoutTag_Atmos_5_1_2",
    "kAudioChannelLayoutTag_Logic_4_0_B": "kAudioChannelLayoutTag_MPEG_4_0_B",
    "kAudioChannelLabel_CenterTopFront": "kAudioChannelLabel_VerticalHeightCenter",
    "kAudioChannelLayoutTag_Logic_4_0_A": "kAudioChannelLayoutTag_MPEG_4_0_A",
    "kAudioChannelLayoutTag_CICP_12": "kAudioChannelLayoutTag_MPEG_7_1_C",
    "kAudioChannelLayoutTag_CICP_10": "kAudioChannelLayoutTag_ITU_2_2",
    "kAudioChannelLayoutTag_CICP_11": "kAudioChannelLayoutTag_MPEG_6_1_A",
    "kAudioChannelLayoutTag_AAC_4_0": "kAudioChannelLayoutTag_MPEG_4_0_B",
    "kAudioChannelLayoutTag_Logic_5_0_C": "kAudioChannelLayoutTag_MPEG_5_0_C",
    "kAudioChannelLayoutTag_AAC_3_0": "kAudioChannelLayoutTag_MPEG_3_0_B",
    "kAudioStreamPropertyLatency": "kAudioDevicePropertyLatency",
    "kAudioChannelLayoutTag_Logic_5_0_B": "kAudioChannelLayoutTag_MPEG_5_0_B",
    "kAudioChannelLabel_HOA_SN3D": "kAudioChannelLabel_HOA_ACN_0",
    "kAudioChannelLayoutTag_DVD_2": "kAudioChannelLayoutTag_ITU_2_1",
    "kAudioDevicePropertyChannelCategoryNameCFString": "kAudioObjectPropertyElementCategoryName",
    "kAudioChannelLayoutTag_WAVE_2_1": "kAudioChannelLayoutTag_DVD_4",
    "kAudioChannelLayoutTag_AudioUnit_7_1_Front": "kAudioChannelLayoutTag_MPEG_7_1_A",
    "kAudioChannelLabel_RightTopFront": "kAudioChannelLabel_VerticalHeightRight",
    "kAudioDevicePropertyDeviceNameCFString": "kAudioObjectPropertyName",
    "kAudioChannelLayoutTag_AAC_7_1": "kAudioChannelLayoutTag_MPEG_7_1_B",
    "kAudioPropertyWildcardChannel": "kAudioObjectPropertyElementWildcard",
    "kAudioChannelLayoutTag_Logic_5_0_D": "kAudioChannelLayoutTag_MPEG_5_0_D",
    "kAudioDevicePropertyScopeInput": "kAudioObjectPropertyScopeInput",
    "kAudioDevicePropertyChannelNameCFString": "kAudioObjectPropertyElementName",
    "kAudioChannelBit_RightTopFront": "kAudioChannelBit_VerticalHeightRight",
    "kLinearPCMFormatFlagIsAlignedHigh": "kAudioFormatFlagIsAlignedHigh",
    "kAudioDevicePropertyDeviceManufacturerCFString": "kAudioObjectPropertyManufacturer",
    "kAudioChannelLayoutTag_ITU_3_2": "kAudioChannelLayoutTag_MPEG_5_0_A",
    "kAudioChannelLayoutTag_ITU_3_0": "kAudioChannelLayoutTag_MPEG_3_0_A",
    "kAudioChannelLayoutTag_ITU_3_1": "kAudioChannelLayoutTag_MPEG_4_0_A",
    "kAudioChannelLayoutTag_Logic_5_1_B": "kAudioChannelLayoutTag_MPEG_5_1_B",
    "kLinearPCMFormatFlagIsFloat": "kAudioFormatFlagIsFloat",
    "kAudioDevicePropertyChannelNumberNameCFString": "kAudioObjectPropertyElementNumberName",
    "kAudioPropertyWildcardPropertyID": "kAudioObjectPropertySelectorWildcard",
    "kAudioChannelLayoutTag_AudioUnit_5_0": "kAudioChannelLayoutTag_MPEG_5_0_B",
    "kAudioChannelLayoutTag_AudioUnit_5_1": "kAudioChannelLayoutTag_MPEG_5_1_A",
    "kAudioChannelLayoutTag_Logic_Atmos_7_1_4_A": "kAudioChannelLayoutTag_Atmos_7_1_4",
    "kLinearPCMFormatFlagIsSignedInteger": "kAudioFormatFlagIsSignedInteger",
    "kAudioChannelLayoutTag_WAVE_3_0": "kAudioChannelLayoutTag_MPEG_3_0_A",
    "kLinearPCMFormatFlagIsBigEndian": "kAudioFormatFlagIsBigEndian",
    "kAudioChannelLayoutTag_AAC_5_1": "kAudioChannelLayoutTag_MPEG_5_1_D",
    "kAudioChannelLayoutTag_AAC_5_0": "kAudioChannelLayoutTag_MPEG_5_0_D",
    "kAudioChannelLayoutTag_Ogg_3_0": "kAudioChannelLayoutTag_AC3_3_0",
    "kAudioChannelBit_LeftTopFront": "kAudioChannelBit_VerticalHeightLeft",
    "kAudioChannelLabel_LeftTopFront": "kAudioChannelLabel_VerticalHeightLeft",
    "kAudioChannelLayoutTag_Logic_6_1_A": "kAudioChannelLayoutTag_AAC_6_1",
    "kAudioChannelLayoutTag_Logic_6_1_C": "kAudioChannelLayoutTag_MPEG_6_1_A",
}
misc.update(
    {
        "AudioHardwarePlugInRef": objc.createOpaquePointerType(
            "AudioHardwarePlugInRef", b"^{AudioHardwarePlugInInterface=}"
        )
    }
)
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"CATapDescription", b"isExclusive", {"retval": {"type": "Z"}})
    r(b"CATapDescription", b"isMixdown", {"retval": {"type": "Z"}})
    r(b"CATapDescription", b"isMono", {"retval": {"type": "Z"}})
    r(b"CATapDescription", b"isPrivate", {"retval": {"type": "Z"}})
    r(b"CATapDescription", b"setExclusive:", {"arguments": {2: {"type": "Z"}}})
    r(b"CATapDescription", b"setMixdown:", {"arguments": {2: {"type": "Z"}}})
    r(b"CATapDescription", b"setMono:", {"arguments": {2: {"type": "Z"}}})
    r(b"CATapDescription", b"setPrivate:", {"arguments": {2: {"type": "Z"}}})
finally:
    objc._updatingMetadata(False)
expressions = {}

# END OF FILE
