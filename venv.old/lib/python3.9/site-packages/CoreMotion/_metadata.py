# This file is generated by objective.metadata
#
# Last update: Fri Jun 28 12:41:14 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "CMQuaternion": objc.createStructType(
            "CoreMotion.CMQuaternion", b"{CMQuaternion=dddd}", ["x", "y", "z", "w"]
        ),
        "CMMagneticField": objc.createStructType(
            "CoreMotion.CMMagneticField", b"{CMMagneticField=ddd}", ["x", "y", "z"]
        ),
        "CMCalibratedMagneticField": objc.createStructType(
            "CoreMotion.CMCalibratedMagneticField",
            b"{CMCalibratedMagneticField={CMMagneticField=ddd}i}",
            ["field", "accuracy"],
        ),
        "CMRotationRate": objc.createStructType(
            "CoreMotion.CMRotationRate", b"{CMRotationRate=ddd}", ["x", "y", "z"]
        ),
        "CMRotationMatrix": objc.createStructType(
            "CoreMotion.CMRotationMatrix",
            b"{CMRotationMatrix=ddddddddd}",
            ["m11", "m12", "m13", "m21", "m22", "m23", "m31", "m32", "m33"],
        ),
        "CMAcceleration": objc.createStructType(
            "CoreMotion.CMAcceleration", b"{CMAcceleration=ddd}", ["x", "y", "z"]
        ),
    }
)
constants = """$CMErrorDomain$"""
enums = """$CMAttitudeReferenceFrameXArbitraryCorrectedZVertical@2$CMAttitudeReferenceFrameXArbitraryZVertical@1$CMAttitudeReferenceFrameXMagneticNorthZVertical@4$CMAttitudeReferenceFrameXTrueNorthZVertical@8$CMAuthorizationStatusAuthorized@3$CMAuthorizationStatusDenied@2$CMAuthorizationStatusNotDetermined@0$CMAuthorizationStatusRestricted@1$CMDeviceMotionSensorLocationDefault@0$CMDeviceMotionSensorLocationHeadphoneLeft@1$CMDeviceMotionSensorLocationHeadphoneRight@2$CMErrorDeviceRequiresMovement@101$CMErrorInvalidAction@108$CMErrorInvalidParameter@107$CMErrorMotionActivityNotAuthorized@105$CMErrorMotionActivityNotAvailable@104$CMErrorMotionActivityNotEntitled@106$CMErrorNULL@100$CMErrorNilData@112$CMErrorNotAuthorized@111$CMErrorNotAvailable@109$CMErrorNotEntitled@110$CMErrorSize@113$CMErrorTrueNorthNotAvailable@102$CMErrorUnknown@103$CMFallDetectionEventUserResolutionConfirmed@0$CMFallDetectionEventUserResolutionDismissed@1$CMFallDetectionEventUserResolutionRejected@2$CMFallDetectionEventUserResolutionUnresponsive@3$CMHeadphoneActivityStatusConnected@1$CMHeadphoneActivityStatusDisconnected@0$CMHighFrequencyHeartRateDataConfidenceHigh@2$CMHighFrequencyHeartRateDataConfidenceHighest@3$CMHighFrequencyHeartRateDataConfidenceLow@0$CMHighFrequencyHeartRateDataConfidenceMedium@1$CMMagneticFieldCalibrationAccuracyHigh@2$CMMagneticFieldCalibrationAccuracyLow@0$CMMagneticFieldCalibrationAccuracyMedium@1$CMMagneticFieldCalibrationAccuracyUncalibrated@-1$CMMotionActivityConfidenceHigh@2$CMMotionActivityConfidenceLow@0$CMMotionActivityConfidenceMedium@1$CMOdometerOriginDeviceLocal@1$CMOdometerOriginDeviceRemote@2$CMOdometerOriginDeviceUnknown@0$CMPedometerEventTypePause@0$CMPedometerEventTypeResume@1$CMWaterSubmersionDepthStateApproachingMaxDepth@400$CMWaterSubmersionDepthStateNotSubmerged@100$CMWaterSubmersionDepthStatePastMaxDepth@500$CMWaterSubmersionDepthStateSensorDepthError@600$CMWaterSubmersionDepthStateSubmergedDeep@300$CMWaterSubmersionDepthStateSubmergedShallow@200$CMWaterSubmersionDepthStateUnknown@0$CMWaterSubmersionStateNotSubmerged@1$CMWaterSubmersionStateSubmerged@2$CMWaterSubmersionStateUnknown@0$"""
misc.update(
    {
        "CMPedometerEventType": NewType("CMPedometerEventType", int),
        "CMAttitudeReferenceFrame": NewType("CMAttitudeReferenceFrame", int),
        "CMError": NewType("CMError", int),
        "CMFallDetectionEventUserResolution": NewType(
            "CMFallDetectionEventUserResolution", int
        ),
        "CMWaterSubmersionDepthState": NewType("CMWaterSubmersionDepthState", int),
        "CMHighFrequencyHeartRateDataConfidence": NewType(
            "CMHighFrequencyHeartRateDataConfidence", int
        ),
        "CMMagneticFieldCalibrationAccuracy": NewType(
            "CMMagneticFieldCalibrationAccuracy", int
        ),
        "CMAuthorizationStatus": NewType("CMAuthorizationStatus", int),
        "CMWaterSubmersionState": NewType("CMWaterSubmersionState", int),
        "CMMotionActivityConfidence": NewType("CMMotionActivityConfidence", int),
        "CMOdometerOriginDevice": NewType("CMOdometerOriginDevice", int),
        "CMHeadphoneActivityStatus": NewType("CMHeadphoneActivityStatus", int),
        "CMDeviceMotionSensorLocation": NewType("CMDeviceMotionSensorLocation", int),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"CMAccelerometerData",
        b"acceleration",
        {"retval": {"type": b"{CMAcceleration=ddd}"}},
    )
    r(b"CMAltimeter", b"isAbsoluteAltitudeAvailable", {"retval": {"type": b"Z"}})
    r(b"CMAltimeter", b"isRelativeAltitudeAvailable", {"retval": {"type": b"Z"}})
    r(
        b"CMAltimeter",
        b"startAbsoluteAltitudeUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMAltimeter",
        b"startRelativeAltitudeUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"CMAttitude", b"quaternion", {"retval": {"type": b"{CMQuaternion=dddd}"}})
    r(
        b"CMAttitude",
        b"rotationMatrix",
        {"retval": {"type": b"{CMRotationMatrix=ddddddddd}"}},
    )
    r(b"CMBatchedSensorManager", b"isAccelerometerActive", {"retval": {"type": b"Z"}})
    r(
        b"CMBatchedSensorManager",
        b"isAccelerometerSupported",
        {"retval": {"type": b"Z"}},
    )
    r(b"CMBatchedSensorManager", b"isDeviceMotionActive", {"retval": {"type": b"Z"}})
    r(b"CMBatchedSensorManager", b"isDeviceMotionSupported", {"retval": {"type": b"Z"}})
    r(
        b"CMBatchedSensorManager",
        b"startAccelerometerUpdatesWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMBatchedSensorManager",
        b"startDeviceMotionUpdatesWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"CMDeviceMotion", b"gravity", {"retval": {"type": b"{CMAcceleration=ddd}"}})
    r(
        b"CMDeviceMotion",
        b"magneticField",
        {"retval": {"type": b"{CMCalibratedMagneticField={CMMagneticField=ddd}i}"}},
    )
    r(b"CMDeviceMotion", b"rotationRate", {"retval": {"type": b"{CMRotationRate=ddd}"}})
    r(
        b"CMDeviceMotion",
        b"userAcceleration",
        {"retval": {"type": b"{CMAcceleration=ddd}"}},
    )
    r(b"CMFallDetectionManager", b"isAvailable", {"retval": {"type": b"Z"}})
    r(
        b"CMFallDetectionManager",
        b"requestAuthorizationWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(b"CMGyroData", b"rotationRate", {"retval": {"type": b"{CMRotationRate=ddd}"}})
    r(b"CMHeadphoneActivityManager", b"isActivityActive", {"retval": {"type": b"Z"}})
    r(b"CMHeadphoneActivityManager", b"isActivityAvailable", {"retval": {"type": b"Z"}})
    r(b"CMHeadphoneActivityManager", b"isStatusActive", {"retval": {"type": b"Z"}})
    r(b"CMHeadphoneActivityManager", b"isStatusAvailable", {"retval": {"type": b"Z"}})
    r(
        b"CMHeadphoneActivityManager",
        b"startActivityUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMHeadphoneActivityManager",
        b"startStatusUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMHeadphoneMotionManager",
        b"isConnectionStatusActive",
        {"retval": {"type": b"Z"}},
    )
    r(b"CMHeadphoneMotionManager", b"isDeviceMotionActive", {"retval": {"type": b"Z"}})
    r(
        b"CMHeadphoneMotionManager",
        b"isDeviceMotionAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"CMHeadphoneMotionManager",
        b"startDeviceMotionUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMMagnetometerData",
        b"magneticField",
        {"retval": {"type": b"{CMMagneticField=ddd}"}},
    )
    r(b"CMMotionActivity", b"automotive", {"retval": {"type": b"Z"}})
    r(b"CMMotionActivity", b"cycling", {"retval": {"type": b"Z"}})
    r(b"CMMotionActivity", b"running", {"retval": {"type": b"Z"}})
    r(b"CMMotionActivity", b"stationary", {"retval": {"type": b"Z"}})
    r(b"CMMotionActivity", b"unknown", {"retval": {"type": b"Z"}})
    r(b"CMMotionActivity", b"walking", {"retval": {"type": b"Z"}})
    r(b"CMMotionActivityManager", b"isActivityAvailable", {"retval": {"type": b"Z"}})
    r(
        b"CMMotionActivityManager",
        b"queryActivityStartingFromDate:toDate:toQueue:withHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMMotionActivityManager",
        b"startActivityUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"CMMotionManager", b"isAccelerometerActive", {"retval": {"type": b"Z"}})
    r(b"CMMotionManager", b"isAccelerometerAvailable", {"retval": {"type": b"Z"}})
    r(b"CMMotionManager", b"isDeviceMotionActive", {"retval": {"type": b"Z"}})
    r(b"CMMotionManager", b"isDeviceMotionAvailable", {"retval": {"type": b"Z"}})
    r(b"CMMotionManager", b"isGyroActive", {"retval": {"type": b"Z"}})
    r(b"CMMotionManager", b"isGyroAvailable", {"retval": {"type": b"Z"}})
    r(b"CMMotionManager", b"isMagnetometerActive", {"retval": {"type": b"Z"}})
    r(b"CMMotionManager", b"isMagnetometerAvailable", {"retval": {"type": b"Z"}})
    r(
        b"CMMotionManager",
        b"setShowsDeviceMovementDisplay:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"CMMotionManager", b"showsDeviceMovementDisplay", {"retval": {"type": b"Z"}})
    r(
        b"CMMotionManager",
        b"startAccelerometerUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMMotionManager",
        b"startDeviceMotionUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMMotionManager",
        b"startDeviceMotionUpdatesUsingReferenceFrame:toQueue:withHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMMotionManager",
        b"startGyroUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMMotionManager",
        b"startMagnetometerUpdatesToQueue:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"CMMovementDisorderManager", b"isAvailable", {"retval": {"type": b"Z"}})
    r(
        b"CMMovementDisorderManager",
        b"queryDyskineticSymptomFromDate:toDate:withHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMMovementDisorderManager",
        b"queryTremorFromDate:toDate:withHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"CMPedometer", b"isCadenceAvailable", {"retval": {"type": b"Z"}})
    r(b"CMPedometer", b"isDistanceAvailable", {"retval": {"type": b"Z"}})
    r(b"CMPedometer", b"isFloorCountingAvailable", {"retval": {"type": b"Z"}})
    r(b"CMPedometer", b"isPaceAvailable", {"retval": {"type": b"Z"}})
    r(b"CMPedometer", b"isPedometerEventTrackingAvailable", {"retval": {"type": b"Z"}})
    r(b"CMPedometer", b"isStepCountingAvailable", {"retval": {"type": b"Z"}})
    r(
        b"CMPedometer",
        b"queryPedometerDataFromDate:toDate:withHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CMPedometer",
        b"startPedometerEventUpdatesWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMPedometer",
        b"startPedometerUpdatesFromDate:withHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"CMRotationRateData",
        b"rotationRate",
        {"retval": {"type": b"{CMRotationRate=ddd}"}},
    )
    r(
        b"CMSensorRecorder",
        b"isAccelerometerRecordingAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(b"CMSensorRecorder", b"isAuthorizedForRecording", {"retval": {"type": b"Z"}})
    r(b"CMStepCounter", b"isStepCountingAvailable", {"retval": {"type": b"Z"}})
    r(
        b"CMStepCounter",
        b"queryStepCountStartingFrom:to:toQueue:withHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMStepCounter",
        b"startStepCountingUpdatesToQueue:updateOn:withHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CMWaterSubmersionManager",
        b"waterSubmersionAvailable",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"fallDetectionManager:didDetectEvent:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"fallDetectionManagerDidChangeAuthorization:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"headphoneMotionManagerDidConnect:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"headphoneMotionManagerDidDisconnect:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"manager:didUpdateEvent:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"manager:didUpdateMeasurement:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"manager:didUpdateTemperature:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"manager:errorOccurred:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
finally:
    objc._updatingMetadata(False)
expressions = {}

# END OF FILE
