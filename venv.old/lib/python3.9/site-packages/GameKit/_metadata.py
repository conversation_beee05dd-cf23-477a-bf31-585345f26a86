# This file is generated by objective.metadata
#
# Last update: Sun Mar  2 12:00:36 2025
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$GKErrorDomain$GKExchangeTimeoutDefault@d$GKExchangeTimeoutNone@d$GKGameSessionErrorDomain$GKPlayerAuthenticationDidChangeNotificationName$GKPlayerDidChangeNotificationName$GKPlayerIDNoLongerAvailable$GKSessionErrorDomain$GKTurnTimeoutDefault@d$GKTurnTimeoutNone@d$GKVoiceChatServiceErrorDomain$"""
enums = """$GKAccessPointLocationBottomLeading@2$GKAccessPointLocationBottomTrailing@3$GKAccessPointLocationTopLeading@0$GKAccessPointLocationTopTrailing@1$GKAuthenticatingWithAuthKitInvocation@2$GKAuthenticatingWithGreenBuddyUI@1$GKAuthenticatingWithoutUI@0$GKChallengeStateCompleted@2$GKChallengeStateDeclined@3$GKChallengeStateInvalid@0$GKChallengeStatePending@1$GKConnectionStateConnected@1$GKConnectionStateNotConnected@0$GKErrorAPINotAvailable@31$GKErrorAPIObsolete@34$GKErrorAppUnlisted@37$GKErrorAuthenticationInProgress@7$GKErrorCancelled@2$GKErrorChallengeInvalid@19$GKErrorCommunicationsFailure@3$GKErrorConnectionTimeout@33$GKErrorDebugMode@38$GKErrorFriendListDenied@102$GKErrorFriendListDescriptionMissing@100$GKErrorFriendListRestricted@101$GKErrorFriendRequestNotAvailable@103$GKErrorGameSessionRequestInvalid@29$GKErrorGameUnrecognized@15$GKErrorICloudUnavailable@35$GKErrorInvalidCredentials@5$GKErrorInvalidParameter@17$GKErrorInvalidPlayer@8$GKErrorInvitationsDisabled@25$GKErrorLockdownMode@36$GKErrorMatchNotConnected@28$GKErrorMatchRequestInvalid@13$GKErrorNotAuthenticated@6$GKErrorNotAuthorized@32$GKErrorNotSupported@16$GKErrorParentalControlsBlocked@10$GKErrorPlayerPhotoFailure@26$GKErrorPlayerStatusExceedsMaximumLength@11$GKErrorPlayerStatusInvalid@12$GKErrorRestrictedToAutomatch@30$GKErrorScoreNotSet@9$GKErrorTurnBasedInvalidParticipant@22$GKErrorTurnBasedInvalidState@24$GKErrorTurnBasedInvalidTurn@23$GKErrorTurnBasedMatchDataTooLarge@20$GKErrorTurnBasedTooManySessions@21$GKErrorUbiquityContainerUnavailable@27$GKErrorUnderage@14$GKErrorUnexpectedConnection@18$GKErrorUnknown@1$GKErrorUserDenied@4$GKFriendsAuthorizationStatusAuthorized@3$GKFriendsAuthorizationStatusDenied@2$GKFriendsAuthorizationStatusNotDetermined@0$GKFriendsAuthorizationStatusRestricted@1$GKGameCenterViewControllerStateAchievements@1$GKGameCenterViewControllerStateChallenges@2$GKGameCenterViewControllerStateDashboard@4$GKGameCenterViewControllerStateDefault@-1$GKGameCenterViewControllerStateLeaderboards@0$GKGameCenterViewControllerStateLocalPlayerFriendsList@5$GKGameCenterViewControllerStateLocalPlayerProfile@3$GKGameSessionErrorBadContainer@12$GKGameSessionErrorCloudDriveDisabled@15$GKGameSessionErrorCloudQuotaExceeded@13$GKGameSessionErrorConnectionCancelledByUser@5$GKGameSessionErrorConnectionFailed@6$GKGameSessionErrorInvalidSession@16$GKGameSessionErrorNetworkFailure@14$GKGameSessionErrorNotAuthenticated@2$GKGameSessionErrorSendDataNoRecipients@9$GKGameSessionErrorSendDataNotConnected@8$GKGameSessionErrorSendDataNotReachable@10$GKGameSessionErrorSendRateLimitReached@11$GKGameSessionErrorSessionConflict@3$GKGameSessionErrorSessionHasMaxConnectedPlayers@7$GKGameSessionErrorSessionNotShared@4$GKGameSessionErrorUnknown@1$GKInviteRecipientResponseAccepted@0$GKInviteRecipientResponseDeclined@1$GKInviteRecipientResponseFailed@2$GKInviteRecipientResponseIncompatible@3$GKInviteRecipientResponseNoAnswer@5$GKInviteRecipientResponseUnableToConnect@4$GKInviteeResponseAccepted@0$GKInviteeResponseDeclined@1$GKInviteeResponseFailed@2$GKInviteeResponseIncompatible@3$GKInviteeResponseNoAnswer@5$GKInviteeResponseUnableToConnect@4$GKLeaderboardPlayerScopeFriendsOnly@1$GKLeaderboardPlayerScopeGlobal@0$GKLeaderboardTimeScopeAllTime@2$GKLeaderboardTimeScopeToday@0$GKLeaderboardTimeScopeWeek@1$GKLeaderboardTypeClassic@0$GKLeaderboardTypeRecurring@1$GKMatchSendDataReliable@0$GKMatchSendDataUnreliable@1$GKMatchTypeHosted@1$GKMatchTypePeerToPeer@0$GKMatchTypeTurnBased@2$GKMatchmakingModeAutomatchOnly@2$GKMatchmakingModeDefault@0$GKMatchmakingModeInviteOnly@3$GKMatchmakingModeNearbyOnly@1$GKPeerStateAvailable@0$GKPeerStateConnected@2$GKPeerStateConnectedRelay@5$GKPeerStateConnecting@4$GKPeerStateDisconnected@3$GKPeerStateUnavailable@1$GKPhotoSizeNormal@1$GKPhotoSizeSmall@0$GKPlayerStateConnected@1$GKPlayerStateDisconnected@2$GKPlayerStateUnknown@0$GKReleaseStatePrereleased@2$GKReleaseStateReleased@1$GKReleaseStateUnknown@0$GKSendDataReliable@0$GKSendDataUnreliable@1$GKSessionCancelledError@30504$GKSessionCannotEnableError@30509$GKSessionConnectionClosedError@30506$GKSessionConnectionFailedError@30505$GKSessionConnectivityError@30201$GKSessionDataTooBigError@30507$GKSessionDeclinedError@30502$GKSessionInProgressError@30510$GKSessionInternalError@30203$GKSessionInvalidParameterError@30500$GKSessionModeClient@1$GKSessionModePeer@2$GKSessionModeServer@0$GKSessionNotConnectedError@30508$GKSessionPeerNotFoundError@30501$GKSessionSystemError@30205$GKSessionTimedOutError@30503$GKSessionTransportError@30202$GKSessionUnknownError@30204$GKTransportTypeReliable@1$GKTransportTypeUnreliable@0$GKTurnBasedExchangeStatusActive@1$GKTurnBasedExchangeStatusCanceled@4$GKTurnBasedExchangeStatusComplete@2$GKTurnBasedExchangeStatusResolved@3$GKTurnBasedExchangeStatusUnknown@0$GKTurnBasedMatchOutcomeCustomRange@16711680$GKTurnBasedMatchOutcomeFirst@6$GKTurnBasedMatchOutcomeFourth@9$GKTurnBasedMatchOutcomeLost@3$GKTurnBasedMatchOutcomeNone@0$GKTurnBasedMatchOutcomeQuit@1$GKTurnBasedMatchOutcomeSecond@7$GKTurnBasedMatchOutcomeThird@8$GKTurnBasedMatchOutcomeTied@4$GKTurnBasedMatchOutcomeTimeExpired@5$GKTurnBasedMatchOutcomeWon@2$GKTurnBasedMatchStatusEnded@2$GKTurnBasedMatchStatusMatching@3$GKTurnBasedMatchStatusOpen@1$GKTurnBasedMatchStatusUnknown@0$GKTurnBasedParticipantStatusActive@4$GKTurnBasedParticipantStatusDeclined@2$GKTurnBasedParticipantStatusDone@5$GKTurnBasedParticipantStatusInvited@1$GKTurnBasedParticipantStatusMatching@3$GKTurnBasedParticipantStatusUnknown@0$GKVoiceChatPlayerConnected@0$GKVoiceChatPlayerConnecting@4$GKVoiceChatPlayerDisconnected@1$GKVoiceChatPlayerSilent@3$GKVoiceChatPlayerSpeaking@2$GKVoiceChatServiceAudioUnavailableError@32005$GKVoiceChatServiceClientMissingRequiredMethodsError@32007$GKVoiceChatServiceInternalError@32000$GKVoiceChatServiceInvalidCallIDError@32004$GKVoiceChatServiceInvalidParameterError@32016$GKVoiceChatServiceMethodCurrentlyInvalidError@32012$GKVoiceChatServiceNetworkConfigurationError@32013$GKVoiceChatServiceNoRemotePacketsError@32001$GKVoiceChatServiceOutOfMemoryError@32015$GKVoiceChatServiceRemoteParticipantBusyError@32008$GKVoiceChatServiceRemoteParticipantCancelledError@32009$GKVoiceChatServiceRemoteParticipantDeclinedInviteError@32011$GKVoiceChatServiceRemoteParticipantHangupError@32003$GKVoiceChatServiceRemoteParticipantResponseInvalidError@32010$GKVoiceChatServiceUnableToConnectError@32002$GKVoiceChatServiceUninitializedClientError@32006$GKVoiceChatServiceUnsupportedRemoteVersionError@32014$"""
misc.update(
    {
        "GKSessionMode": NewType("GKSessionMode", int),
        "GKVoiceChatPlayerState": NewType("GKVoiceChatPlayerState", int),
        "GKTurnBasedMatchOutcome": NewType("GKTurnBasedMatchOutcome", int),
        "GKVoiceChatServiceError": NewType("GKVoiceChatServiceError", int),
        "GKTurnBasedParticipantStatus": NewType("GKTurnBasedParticipantStatus", int),
        "GKTransportType": NewType("GKTransportType", int),
        "GKTurnBasedMatchStatus": NewType("GKTurnBasedMatchStatus", int),
        "GKInviteRecipientResponse": NewType("GKInviteRecipientResponse", int),
        "GKMatchSendDataMode": NewType("GKMatchSendDataMode", int),
        "GKSendDataMode": NewType("GKSendDataMode", int),
        "GKPhotoSize": NewType("GKPhotoSize", int),
        "GKChallengeState": NewType("GKChallengeState", int),
        "GKSessionError": NewType("GKSessionError", int),
        "GKMatchmakingMode": NewType("GKMatchmakingMode", int),
        "GKPeerConnectionState": NewType("GKPeerConnectionState", int),
        "GKErrorCode": NewType("GKErrorCode", int),
        "GKPlayerConnectionState": NewType("GKPlayerConnectionState", int),
        "GKLeaderboardPlayerScope": NewType("GKLeaderboardPlayerScope", int),
        "GKLeaderboardTimeScope": NewType("GKLeaderboardTimeScope", int),
        "GKConnectionState": NewType("GKConnectionState", int),
        "GKLeaderboardType": NewType("GKLeaderboardType", int),
        "GKTurnBasedExchangeStatus": NewType("GKTurnBasedExchangeStatus", int),
        "GKReleaseState": NewType("GKReleaseState", int),
        "GKGameSessionErrorCode": NewType("GKGameSessionErrorCode", int),
        "GKGameCenterViewControllerState": NewType(
            "GKGameCenterViewControllerState", int
        ),
        "GKMatchType": NewType("GKMatchType", int),
        "GKFriendsAuthorizationStatus": NewType("GKFriendsAuthorizationStatus", int),
        "GKAccessPointLocation": NewType("GKAccessPointLocation", int),
    }
)
misc.update({})
misc.update({})
aliases = {
    "GKInviteeResponseAccepted": "GKInviteRecipientResponseAccepted",
    "GKInviteeResponseNoAnswer": "GKInviteRecipientResponseNoAnswer",
    "GKInviteeResponseFailed": "GKInviteRecipientResponseFailed",
    "GKInviteeResponseIncompatible": "GKInviteRecipientResponseIncompatible",
    "GKInviteeResponseDeclined": "GKInviteRecipientResponseDeclined",
    "GKInviteeResponseUnableToConnect": "GKInviteRecipientResponseUnableToConnect",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"GKAccessPoint", b"isActive", {"retval": {"type": b"Z"}})
    r(b"GKAccessPoint", b"isFocused", {"retval": {"type": b"Z"}})
    r(b"GKAccessPoint", b"isPresentingGameCenter", {"retval": {"type": b"Z"}})
    r(b"GKAccessPoint", b"isVisible", {"retval": {"type": b"Z"}})
    r(b"GKAccessPoint", b"setActive:", {"arguments": {2: {"type": b"Z"}}})
    r(b"GKAccessPoint", b"setFocused:", {"arguments": {2: {"type": b"Z"}}})
    r(b"GKAccessPoint", b"setShowHighlights:", {"arguments": {2: {"type": b"Z"}}})
    r(b"GKAccessPoint", b"showHighlights", {"retval": {"type": b"Z"}})
    r(
        b"GKAccessPoint",
        b"triggerAccessPointWithAchievementID:handler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAccessPoint",
        b"triggerAccessPointWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAccessPoint",
        b"triggerAccessPointWithLeaderboardID:playerScope:timeScope:handler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAccessPoint",
        b"triggerAccessPointWithLeaderboardSetID:handler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAccessPoint",
        b"triggerAccessPointWithPlayer:handler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAccessPoint",
        b"triggerAccessPointWithState:handler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"challengeComposeControllerWithMessage:players:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"challengeComposeControllerWithMessage:players:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"challengeComposeControllerWithPlayers:message:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"GKAchievement", b"isCompleted", {"retval": {"type": b"Z"}})
    r(b"GKAchievement", b"isHidden", {"retval": {"type": b"Z"}})
    r(
        b"GKAchievement",
        b"loadAchievementsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"reportAchievementWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"reportAchievements:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"reportAchievements:withEligibleChallenges:withCompletionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"resetAchievementsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"selectChallengeablePlayerIDs:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"selectChallengeablePlayers:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKAchievement",
        b"setShowsCompletionBanner:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"GKAchievement", b"showsCompletionBanner", {"retval": {"type": b"Z"}})
    r(b"GKAchievementDescription", b"isHidden", {"retval": {"type": b"Z"}})
    r(b"GKAchievementDescription", b"isReplayable", {"retval": {"type": b"Z"}})
    r(
        b"GKAchievementDescription",
        b"loadAchievementDescriptionsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKAchievementDescription",
        b"loadImageWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKChallenge",
        b"challengeComposeControllerWithMessage:players:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKChallenge",
        b"loadReceivedChallengesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKChallenge",
        b"reportScores:withEligibleChallenges:withCompletionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKCloudPlayer",
        b"getCurrentSignedInPlayerForContainer:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"GKDialogController", b"presentViewController:", {"retval": {"type": b"Z"}})
    r(
        b"GKGameSession",
        b"clearBadgeForPlayers:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"createSessionInContainer:withTitle:maxConnectedPlayers:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"getShareURLWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"loadDataWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"loadSessionWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"loadSessionsInContainer:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"removeSessionWithIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"saveData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"sendData:withTransportType:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKGameSession",
        b"sendMessageWithLocalizedFormatKey:arguments:data:toPlayers:badgePlayers:completionHandler:",
        {
            "arguments": {
                6: {"type": b"Z"},
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"GKGameSession",
        b"setConnectionState:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"GKInvite", b"isHosted", {"retval": {"type": b"Z"}})
    r(b"GKLeaderboard", b"isLoading", {"retval": {"type": b"Z"}})
    r(
        b"GKLeaderboard",
        b"loadCategoriesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadEntriesForPlayerScope:timeScope:range:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"q"},
                            4: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadEntriesForPlayers:timeScope:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadImageWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadLeaderboardsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadLeaderboardsWithIDs:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadPreviousOccurrenceWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"loadScoresWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"setDefaultLeaderboard:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"submitScore:context:player:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboard",
        b"submitScore:context:player:leaderboardIDs:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboardEntry",
        b"challengeComposeControllerWithMessage:players:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboardEntry",
        b"challengeComposeControllerWithMessage:players:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboardSet",
        b"loadImageWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"GKLeaderboardSet",
        b"loadLeaderboardSetsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboardSet",
        b"loadLeaderboardsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLeaderboardSet",
        b"loadLeaderboardsWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"authenticateHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"authenticateWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"deleteSavedGamesWithName:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"fetchItemsForIdentityVerificationSignature:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"Q"},
                            5: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"fetchSavedGamesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"generateIdentityVerificationSignatureWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"Q"},
                            5: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"GKLocalPlayer", b"isAuthenticated", {"retval": {"type": b"Z"}})
    r(b"GKLocalPlayer", b"isMultiplayerGamingRestricted", {"retval": {"type": b"Z"}})
    r(
        b"GKLocalPlayer",
        b"isPersonalizedCommunicationRestricted",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"GKLocalPlayer",
        b"isPresentingFriendRequestViewController",
        {"retval": {"type": b"Z"}},
    )
    r(b"GKLocalPlayer", b"isUnderage", {"retval": {"type": b"Z"}})
    r(
        b"GKLocalPlayer",
        b"loadChallengableFriendsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadDefaultLeaderboardCategoryIDWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadDefaultLeaderboardIdentifierWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadFriendPlayersWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadFriends:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadFriendsAuthorizationStatus:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadFriendsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadFriendsWithIdentifiers:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"loadRecentPlayersWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"presentFriendRequestCreatorFromViewController:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"GKLocalPlayer",
        b"presentFriendRequestCreatorFromWindow:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"GKLocalPlayer",
        b"resolveConflictingSavedGames:withData:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"saveGameData:withName:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"setAuthenticateHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"setDefaultLeaderboardCategoryID:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKLocalPlayer",
        b"setDefaultLeaderboardIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKMatch",
        b"chooseBestHostPlayerWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKMatch",
        b"chooseBestHostingPlayerWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKMatch",
        b"rematchWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatch",
        b"sendData:toPlayers:dataMode:error:",
        {"retval": {"type": b"Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"GKMatch",
        b"sendData:toPlayers:withDataMode:error:",
        {"retval": {"type": b"Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"GKMatch",
        b"sendDataToAllPlayers:withDataMode:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"GKMatchRequest",
        b"inviteeResponseHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"q"},
                    },
                }
            }
        },
    )
    r(
        b"GKMatchRequest",
        b"recipientResponseHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"q"},
                    },
                }
            }
        },
    )
    r(b"GKMatchRequest", b"restrictToAutomatch", {"retval": {"type": b"Z"}})
    r(
        b"GKMatchRequest",
        b"setInviteeResponseHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"q"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchRequest",
        b"setRecipientResponseHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"q"},
                        },
                    }
                }
            }
        },
    )
    r(b"GKMatchRequest", b"setRestrictToAutomatch:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"GKMatchmaker",
        b"addPlayersToMatch:matchRequest:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"findMatchForRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"findMatchedPlayers:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"findPlayersForHostedMatchRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"findPlayersForHostedRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"inviteHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"matchForInvite:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"queryActivityWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"queryPlayerGroupActivity:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"queryQueueActivity:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"setInviteHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"startBrowsingForNearbyPlayersWithHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"startBrowsingForNearbyPlayersWithReachableHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmaker",
        b"startGroupActivityWithPlayerHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKMatchmakerViewController",
        b"canStartWithMinimumPlayers",
        {"retval": {"type": b"Z"}},
    )
    r(b"GKMatchmakerViewController", b"isHosted", {"retval": {"type": b"Z"}})
    r(
        b"GKMatchmakerViewController",
        b"setCanStartWithMinimumPlayers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"GKMatchmakerViewController", b"setHosted:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"GKMatchmakerViewController",
        b"setHostedPlayer:connected:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"GKMatchmakerViewController",
        b"setHostedPlayer:didConnect:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"GKNotificationBanner",
        b"showBannerWithTitle:message:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"GKNotificationBanner",
        b"showBannerWithTitle:message:duration:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"GKPlayer", b"isFriend", {"retval": {"type": b"Z"}})
    r(b"GKPlayer", b"isInvitable", {"retval": {"type": b"Z"}})
    r(
        b"GKPlayer",
        b"loadPhotoForSize:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKPlayer",
        b"loadPlayersForIdentifiers:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"GKPlayer", b"scopedIDsArePersistent", {"retval": {"type": b"Z"}})
    r(
        b"GKSavedGame",
        b"loadDataWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKScore",
        b"challengeComposeControllerWithMessage:players:completion:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKScore",
        b"challengeComposeControllerWithMessage:players:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKScore",
        b"challengeComposeControllerWithPlayers:message:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKScore",
        b"reportLeaderboardScores:withEligibleChallenges:withCompletionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKScore",
        b"reportScoreWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKScore",
        b"reportScores:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKScore",
        b"reportScores:withEligibleChallenges:withCompletionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKScore",
        b"setShouldSetDefaultLeaderboard:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"GKScore", b"shouldSetDefaultLeaderboard", {"retval": {"type": b"Z"}})
    r(
        b"GKSession",
        b"acceptConnectionFromPeer:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"GKSession", b"isAvailable", {"retval": {"type": b"Z"}})
    r(
        b"GKSession",
        b"sendData:toPeers:withDataMode:error:",
        {"retval": {"type": b"Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"GKSession",
        b"sendDataToAllPeers:withDataMode:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"GKSession", b"setAvailable:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"GKSession",
        b"setDataReceiveHandler:withContext:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"GKTurnBasedExchange",
        b"cancelWithLocalizableMessageKey:arguments:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedExchange",
        b"replyWithLocalizableMessageKey:arguments:data:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"acceptInviteWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"cancelWithLocalizableMessageKey:arguments:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"declineInviteWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endMatchInTurnWithMatchData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endMatchInTurnWithMatchData:leaderboardScores:achievements:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endMatchInTurnWithMatchData:scores:achievements:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endTurnWithNextParticipant:matchData:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"endTurnWithNextParticipants:turnTimeout:matchData:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"findMatchForRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"loadMatchDataWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"loadMatchWithID:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"loadMatchesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"participantQuitInTurnWithOutcome:nextParticipant:matchData:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"participantQuitInTurnWithOutcome:nextParticipants:turnTimeout:matchData:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"participantQuitOutOfTurnWithOutcome:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"rematchWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"removeWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"replyWithLocalizableMessageKey:arguments:data:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"saveCurrentTurnWithMatchData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"saveMergedMatchData:withResolvedExchanges:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"sendExchangeToParticipants:data:localizableMessageKey:arguments:timeout:completionHandler:",
        {
            "arguments": {
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatch",
        b"sendReminderToParticipants:localizableMessageKey:arguments:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"GKTurnBasedMatchmakerViewController",
        b"setShowExistingMatches:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"GKTurnBasedMatchmakerViewController",
        b"showExistingMatches",
        {"retval": {"type": b"Z"}},
    )
    r(b"GKVoiceChat", b"isActive", {"retval": {"type": b"Z"}})
    r(b"GKVoiceChat", b"isVoIPAllowed", {"retval": {"type": b"Z"}})
    r(
        b"GKVoiceChat",
        b"playerStateUpdateHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"q"},
                    },
                }
            }
        },
    )
    r(
        b"GKVoiceChat",
        b"playerVoiceChatStateDidChangeHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"q"},
                    },
                }
            }
        },
    )
    r(b"GKVoiceChat", b"setActive:", {"arguments": {2: {"type": b"Z"}}})
    r(b"GKVoiceChat", b"setMute:forPlayer:", {"arguments": {2: {"type": b"Z"}}})
    r(b"GKVoiceChat", b"setPlayer:muted:", {"arguments": {3: {"type": b"Z"}}})
    r(
        b"GKVoiceChat",
        b"setPlayerStateUpdateHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"q"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKVoiceChat",
        b"setPlayerVoiceChatStateDidChangeHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"q"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"GKVoiceChatService",
        b"acceptCallID:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"GKVoiceChatService", b"isInputMeteringEnabled", {"retval": {"type": b"Z"}})
    r(b"GKVoiceChatService", b"isMicrophoneMuted", {"retval": {"type": b"Z"}})
    r(b"GKVoiceChatService", b"isOutputMeteringEnabled", {"retval": {"type": b"Z"}})
    r(b"GKVoiceChatService", b"isVoIPAllowed", {"retval": {"type": b"Z"}})
    r(
        b"GKVoiceChatService",
        b"setInputMeteringEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"GKVoiceChatService", b"setMicrophoneMuted:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"GKVoiceChatService",
        b"setOutputMeteringEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"GKVoiceChatService",
        b"startVoiceChatWithParticipantID:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSObject",
        b"achievementViewControllerDidFinish:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"challengesViewControllerDidFinish:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"friendRequestComposeViewControllerDidFinish:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"gameCenterViewControllerDidFinish:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"handleInviteFromGameCenter:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"handleMatchEnded:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"handleTurnEventForMatch:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"handleTurnEventForMatch:didBecomeActive:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"leaderboardViewControllerDidFinish:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"localPlayerDidCompleteChallenge:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"localPlayerDidReceiveChallenge:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"localPlayerDidSelectChallenge:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"match:didFailWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"match:didReceiveData:forRecipient:fromRemotePlayer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"match:didReceiveData:fromPlayer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"match:didReceiveData:fromRemotePlayer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"match:player:didChangeConnectionState:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"match:player:didChangeState:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"match:shouldReinviteDisconnectedPlayer:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"match:shouldReinvitePlayer:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"matchmakerViewController:didFailWithError:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"matchmakerViewController:didFindHostedPlayers:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"matchmakerViewController:didFindMatch:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"matchmakerViewController:didFindPlayers:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"matchmakerViewController:didReceiveAcceptFromHostedPlayer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"matchmakerViewController:getMatchPropertiesForRecipient:withCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"matchmakerViewController:hostedPlayerDidAccept:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"matchmakerViewControllerWasCancelled:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"participantID", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"player:didAcceptInvite:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:didCompleteChallenge:issuedByFriend:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:didModifySavedGame:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:didReceiveChallenge:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:didRequestMatchWithOtherPlayers:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:didRequestMatchWithPlayers:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:didRequestMatchWithRecipients:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:hasConflictingSavedGames:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:issuedChallengeWasCompleted:byFriend:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:matchEnded:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:receivedExchangeCancellation:forMatch:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:receivedExchangeReplies:forCompletedExchange:forMatch:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"player:receivedExchangeRequest:forMatch:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:receivedTurnEventForMatch:didBecomeActive:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"player:wantsToPlayChallenge:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"player:wantsToQuitMatch:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"remotePlayerDidCompleteChallenge:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"session:connectionWithPeerFailed:withError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"session:didAddPlayer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"session:didFailWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"session:didReceiveConnectionRequestFromPeer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"session:didReceiveData:fromPlayer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"session:didReceiveMessage:withData:fromPlayer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"session:didRemovePlayer:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"session:peer:didChangeState:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"i"}},
        },
    )
    r(
        b"NSObject",
        b"session:player:didChangeConnectionState:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"session:player:didSaveData:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"shouldShowBannerForLocallyCompletedChallenge:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"shouldShowBannerForLocallyReceivedChallenge:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"shouldShowBannerForRemotelyCompletedChallenge:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"turnBasedMatchmakerViewController:didFailWithError:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"turnBasedMatchmakerViewController:didFindMatch:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"turnBasedMatchmakerViewController:playerQuitForMatch:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"turnBasedMatchmakerViewControllerWasCancelled:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"voiceChatService:didNotStartWithParticipantID:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"voiceChatService:didReceiveInvitationFromParticipantID:callID:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"voiceChatService:didStartWithParticipantID:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"voiceChatService:didStopWithParticipantID:error:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"voiceChatService:sendData:toParticipantID:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"voiceChatService:sendRealTimeData:toParticipantID:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"null",
        b"chooseBestHostPlayerWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"findPlayersForHostedMatchRequest:withCompletionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"sendData:toPlayers:withDataMode:error:",
        {"retval": {"type": b"Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"null",
        b"setInviteHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"null", b"setMute:forPlayer:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"null",
        b"setPlayerStateUpdateHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"startBrowsingForNearbyPlayersWithReachableHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                        },
                    }
                }
            }
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("GKAchievement", b"initWithIdentifier:")
objc.registerNewKeywordsFromSelector("GKAchievement", b"initWithIdentifier:forPlayer:")
objc.registerNewKeywordsFromSelector("GKAchievement", b"initWithIdentifier:player:")
objc.registerNewKeywordsFromSelector(
    "GKGameCenterViewController", b"initWithAchievementID:"
)
objc.registerNewKeywordsFromSelector(
    "GKGameCenterViewController", b"initWithLeaderboard:playerScope:"
)
objc.registerNewKeywordsFromSelector(
    "GKGameCenterViewController", b"initWithLeaderboardID:playerScope:timeScope:"
)
objc.registerNewKeywordsFromSelector(
    "GKGameCenterViewController", b"initWithLeaderboardSetID:"
)
objc.registerNewKeywordsFromSelector("GKGameCenterViewController", b"initWithPlayer:")
objc.registerNewKeywordsFromSelector("GKGameCenterViewController", b"initWithState:")
objc.registerNewKeywordsFromSelector("GKLeaderboard", b"initWithPlayerIDs:")
objc.registerNewKeywordsFromSelector("GKLeaderboard", b"initWithPlayers:")
objc.registerNewKeywordsFromSelector("GKMatchmakerViewController", b"initWithInvite:")
objc.registerNewKeywordsFromSelector(
    "GKMatchmakerViewController", b"initWithMatchRequest:"
)
objc.registerNewKeywordsFromSelector("GKScore", b"initWithCategory:")
objc.registerNewKeywordsFromSelector("GKScore", b"initWithLeaderboardIdentifier:")
objc.registerNewKeywordsFromSelector(
    "GKScore", b"initWithLeaderboardIdentifier:forPlayer:"
)
objc.registerNewKeywordsFromSelector(
    "GKScore", b"initWithLeaderboardIdentifier:player:"
)
objc.registerNewKeywordsFromSelector(
    "GKSession", b"initWithSessionID:displayName:sessionMode:"
)
objc.registerNewKeywordsFromSelector(
    "GKTurnBasedMatchmakerViewController", b"initWithMatchRequest:"
)
expressions = {}

# END OF FILE
