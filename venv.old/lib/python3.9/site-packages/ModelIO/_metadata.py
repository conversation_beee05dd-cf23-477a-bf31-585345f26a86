# This file is generated by objective.metadata
#
# Last update: Tue <PERSON> 11 10:15:38 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "MDLAxisAlignedBoundingBox": objc.createStructType(
            "ModelIO.MDLAxisAlignedBoundingBox",
            b"{MDLAxisAlignedBoundingBox=<3f><3f>}",
            ["maxBounds", "minBounds"],
        ),
        "MDLVoxelIndexExtent": objc.createStructType(
            "ModelIO.MDLVoxelIndexExtent",
            b"{MDLVoxelIndexExtent=<4i><4i>}",
            ["minimumExtent", "maximumExtent"],
        ),
    }
)
constants = """$MDLVertexAttributeAnisotropy$MDLVertexAttributeBinormal$MDLVertexAttributeBitangent$MDLVertexAttributeColor$MDLVertexAttributeEdgeCrease$MDLVertexAttributeJointIndices$MDLVertexAttributeJointWeights$MDLVertexAttributeNormal$MDLVertexAttributeOcclusionValue$MDLVertexAttributePosition$MDLVertexAttributeShadingBasisU$MDLVertexAttributeShadingBasisV$MDLVertexAttributeSubdivisionStencil$MDLVertexAttributeTangent$MDLVertexAttributeTextureCoordinate$kUTType3dObject$kUTTypeAlembic$kUTTypePolygon$kUTTypeStereolithography$kUTTypeUniversalSceneDescription$kUTTypeUniversalSceneDescriptionMobile$"""
enums = """$MDLAnimatedValueInterpolationConstant@0$MDLAnimatedValueInterpolationLinear@1$MDLCameraProjectionOrthographic@1$MDLCameraProjectionPerspective@0$MDLDataPrecisionDouble@2$MDLDataPrecisionFloat@1$MDLDataPrecisionUndefined@0$MDLGeometryTypeLines@1$MDLGeometryTypePoints@0$MDLGeometryTypeQuads@4$MDLGeometryTypeTriangleStrips@3$MDLGeometryTypeTriangles@2$MDLGeometryTypeVariableTopology@5$MDLIndexBitDepthInvalid@0$MDLIndexBitDepthUInt16@16$MDLIndexBitDepthUInt32@32$MDLIndexBitDepthUInt8@8$MDLIndexBitDepthUint16@16$MDLIndexBitDepthUint32@32$MDLIndexBitDepthUint8@8$MDLLightTypeAmbient@1$MDLLightTypeDirectional@2$MDLLightTypeDiscArea@6$MDLLightTypeEnvironment@11$MDLLightTypeLinear@5$MDLLightTypePhotometric@9$MDLLightTypePoint@4$MDLLightTypeProbe@10$MDLLightTypeRectangularArea@7$MDLLightTypeSpot@3$MDLLightTypeSuperElliptical@8$MDLLightTypeUnknown@0$MDLMaterialFaceBack@1$MDLMaterialFaceDoubleSided@2$MDLMaterialFaceFront@0$MDLMaterialMipMapFilterModeLinear@1$MDLMaterialMipMapFilterModeNearest@0$MDLMaterialPropertyTypeBuffer@10$MDLMaterialPropertyTypeColor@4$MDLMaterialPropertyTypeFloat@5$MDLMaterialPropertyTypeFloat2@6$MDLMaterialPropertyTypeFloat3@7$MDLMaterialPropertyTypeFloat4@8$MDLMaterialPropertyTypeMatrix44@9$MDLMaterialPropertyTypeNone@0$MDLMaterialPropertyTypeString@1$MDLMaterialPropertyTypeTexture@3$MDLMaterialPropertyTypeURL@2$MDLMaterialSemanticAmbientOcclusion@22$MDLMaterialSemanticAmbientOcclusionScale@23$MDLMaterialSemanticAnisotropic@7$MDLMaterialSemanticAnisotropicRotation@8$MDLMaterialSemanticBaseColor@0$MDLMaterialSemanticBump@14$MDLMaterialSemanticClearcoat@11$MDLMaterialSemanticClearcoatGloss@12$MDLMaterialSemanticDisplacement@20$MDLMaterialSemanticDisplacementScale@21$MDLMaterialSemanticEmission@13$MDLMaterialSemanticInterfaceIndexOfRefraction@16$MDLMaterialSemanticMaterialIndexOfRefraction@17$MDLMaterialSemanticMetallic@2$MDLMaterialSemanticNone@32768$MDLMaterialSemanticObjectSpaceNormal@18$MDLMaterialSemanticOpacity@15$MDLMaterialSemanticRoughness@6$MDLMaterialSemanticSheen@9$MDLMaterialSemanticSheenTint@10$MDLMaterialSemanticSpecular@3$MDLMaterialSemanticSpecularExponent@4$MDLMaterialSemanticSpecularTint@5$MDLMaterialSemanticSubsurface@1$MDLMaterialSemanticTangentSpaceNormal@19$MDLMaterialSemanticUserDefined@32769$MDLMaterialTextureFilterModeLinear@1$MDLMaterialTextureFilterModeNearest@0$MDLMaterialTextureWrapModeClamp@0$MDLMaterialTextureWrapModeMirror@2$MDLMaterialTextureWrapModeRepeat@1$MDLMeshBufferTypeCustom@3$MDLMeshBufferTypeIndex@2$MDLMeshBufferTypeVertex@1$MDLPrimitiveTypeCapsule@3$MDLPrimitiveTypeCone@2$MDLPrimitiveTypeCube@0$MDLPrimitiveTypeCylinder@4$MDLPrimitiveTypeNone@5$MDLPrimitiveTypeSphere@1$MDLProbePlacementIrradianceDistribution@1$MDLProbePlacementUniformGrid@0$MDLTextureChannelEncodingFloat16@258$MDLTextureChannelEncodingFloat16SR@770$MDLTextureChannelEncodingFloat32@260$MDLTextureChannelEncodingUInt16@2$MDLTextureChannelEncodingUInt24@3$MDLTextureChannelEncodingUInt32@4$MDLTextureChannelEncodingUInt8@1$MDLTextureChannelEncodingUint16@2$MDLTextureChannelEncodingUint24@3$MDLTextureChannelEncodingUint32@4$MDLTextureChannelEncodingUint8@1$MDLTransformOpRotationOrderXYZ@1$MDLTransformOpRotationOrderXZY@2$MDLTransformOpRotationOrderYXZ@3$MDLTransformOpRotationOrderYZX@4$MDLTransformOpRotationOrderZXY@5$MDLTransformOpRotationOrderZYX@6$MDLVertexFormatChar@131073$MDLVertexFormatChar2@131074$MDLVertexFormatChar2Normalized@262146$MDLVertexFormatChar3@131075$MDLVertexFormatChar3Normalized@262147$MDLVertexFormatChar4@131076$MDLVertexFormatChar4Normalized@262148$MDLVertexFormatCharBits@131072$MDLVertexFormatCharNormalized@262145$MDLVertexFormatCharNormalizedBits@262144$MDLVertexFormatFloat@786433$MDLVertexFormatFloat2@786434$MDLVertexFormatFloat3@786435$MDLVertexFormatFloat4@786436$MDLVertexFormatFloatBits@786432$MDLVertexFormatHalf@720897$MDLVertexFormatHalf2@720898$MDLVertexFormatHalf3@720899$MDLVertexFormatHalf4@720900$MDLVertexFormatHalfBits@720896$MDLVertexFormatInt@655361$MDLVertexFormatInt1010102Normalized@659460$MDLVertexFormatInt2@655362$MDLVertexFormatInt3@655363$MDLVertexFormatInt4@655364$MDLVertexFormatIntBits@655360$MDLVertexFormatInvalid@0$MDLVertexFormatPackedBit@4096$MDLVertexFormatShort@393217$MDLVertexFormatShort2@393218$MDLVertexFormatShort2Normalized@524290$MDLVertexFormatShort3@393219$MDLVertexFormatShort3Normalized@524291$MDLVertexFormatShort4@393220$MDLVertexFormatShort4Normalized@524292$MDLVertexFormatShortBits@393216$MDLVertexFormatShortNormalized@524289$MDLVertexFormatShortNormalizedBits@524288$MDLVertexFormatUChar@65537$MDLVertexFormatUChar2@65538$MDLVertexFormatUChar2Normalized@196610$MDLVertexFormatUChar3@65539$MDLVertexFormatUChar3Normalized@196611$MDLVertexFormatUChar4@65540$MDLVertexFormatUChar4Normalized@196612$MDLVertexFormatUCharBits@65536$MDLVertexFormatUCharNormalized@196609$MDLVertexFormatUCharNormalizedBits@196608$MDLVertexFormatUInt@589825$MDLVertexFormatUInt1010102Normalized@593924$MDLVertexFormatUInt2@589826$MDLVertexFormatUInt3@589827$MDLVertexFormatUInt4@589828$MDLVertexFormatUIntBits@589824$MDLVertexFormatUShort@327681$MDLVertexFormatUShort2@327682$MDLVertexFormatUShort2Normalized@458754$MDLVertexFormatUShort3@327683$MDLVertexFormatUShort3Normalized@458755$MDLVertexFormatUShort4@327684$MDLVertexFormatUShort4Normalized@458756$MDLVertexFormatUShortBits@327680$MDLVertexFormatUShortNormalized@458753$MDLVertexFormatUShortNormalizedBits@458752$"""
misc.update(
    {
        "MDLMaterialTextureFilterMode": NewType("MDLMaterialTextureFilterMode", int),
        "MDLMaterialTextureWrapMode": NewType("MDLMaterialTextureWrapMode", int),
        "MDLTextureChannelEncoding": NewType("MDLTextureChannelEncoding", int),
        "MDLLightType": NewType("MDLLightType", int),
        "MDLGeometryType": NewType("MDLGeometryType", int),
        "MDLProbePlacement": NewType("MDLProbePlacement", int),
        "MDLCameraProjection": NewType("MDLCameraProjection", int),
        "MDLMeshBufferType": NewType("MDLMeshBufferType", int),
        "MDLVertexFormat": NewType("MDLVertexFormat", int),
        "MDLMaterialSemantic": NewType("MDLMaterialSemantic", int),
        "MDLMaterialMipMapFilterMode": NewType("MDLMaterialMipMapFilterMode", int),
        "MDLDataPrecision": NewType("MDLDataPrecision", int),
        "MDLAnimatedValueInterpolation": NewType("MDLAnimatedValueInterpolation", int),
        "MDLIndexBitDepth": NewType("MDLIndexBitDepth", int),
        "MDLMaterialFace": NewType("MDLMaterialFace", int),
        "MDLMaterialPropertyType": NewType("MDLMaterialPropertyType", int),
        "MDLTransformOpRotationOrder": NewType("MDLTransformOpRotationOrder", int),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"MDLAnimatedMatrix4x4",
        b"copyDouble4x4ArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"copyFloat4x4ArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"double4x4AtTime:",
        {
            "full_signature": b"{simd_double4x4=[4<4d>]}@:d",
            "retval": {"type": b"{simd_double4x4=[4<4d>]}"},
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"float4x4AtTime:",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:d",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"getDouble4x4Array:maxCount:",
        {
            "full_signature": b"Q@:^{simd_double4x4=[4<4d>]}Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{simd_double4x4=[4<4d>]}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"getFloat4x4Array:maxCount:",
        {
            "full_signature": b"Q@:^{simd_float4x4=[4<4f>]}Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{simd_float4x4=[4<4f>]}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"resetWithDouble4x4Array:atTimes:count:",
        {
            "full_signature": b"v@:^{simd_double4x4=[4<4d>]}^dQ",
            "arguments": {
                2: {
                    "type": b"^{simd_double4x4=[4<4d>]}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 4,
                },
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            },
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"resetWithFloat4x4Array:atTimes:count:",
        {
            "full_signature": b"v@:^{simd_float4x4=[4<4f>]}^dQ",
            "arguments": {
                2: {
                    "type": b"^{simd_float4x4=[4<4f>]}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 4,
                },
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            },
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"setDouble4x4:atTime:",
        {
            "full_signature": b"v@:{simd_double4x4=[4<4d>]}d",
            "arguments": {2: {"type": b"{simd_double4x4=[4<4d>]}"}},
        },
    )
    r(
        b"MDLAnimatedMatrix4x4",
        b"setFloat4x4:atTime:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}d",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"MDLAnimatedQuaternion",
        b"doubleQuaternionAtTime:",
        {
            "full_signature": b"{simd_quatd=<4d>}@:d",
            "retval": {"type": b"{simd_quatd=<4d>}"},
        },
    )
    r(
        b"MDLAnimatedQuaternion",
        b"floatQuaternionAtTime:",
        {
            "full_signature": b"{simd_quatf=<4f>}@:d",
            "retval": {"type": b"{simd_quatf=<4f>}"},
        },
    )
    r(
        b"MDLAnimatedQuaternion",
        b"getDoubleQuaternionArray:maxCount:",
        {
            "full_signature": b"Q@:^{simd_quatd=<4d>}Q",
            "arguments": {2: {"type": b"^{simd_quatd=<4d>}"}},
        },
    )
    r(
        b"MDLAnimatedQuaternion",
        b"getFloatQuaternionArray:maxCount:",
        {
            "full_signature": b"Q@:^{simd_quatf=<4f>}Q",
            "arguments": {2: {"type": b"^{simd_quatf=<4f>}"}},
        },
    )
    r(
        b"MDLAnimatedQuaternion",
        b"resetWithDoubleQuaternionArray:atTimes:count:",
        {
            "full_signature": b"v@:^{simd_quatd=<4d>}^dQ",
            "arguments": {2: {"type": b"^{simd_quatd=<4d>}"}},
        },
    )
    r(
        b"MDLAnimatedQuaternion",
        b"resetWithFloatQuaternionArray:atTimes:count:",
        {
            "full_signature": b"v@:^{simd_quatf=<4f>}^dQ",
            "arguments": {2: {"type": b"^{simd_quatf=<4f>}"}},
        },
    )
    r(
        b"MDLAnimatedQuaternion",
        b"setDoubleQuaternion:atTime:",
        {
            "full_signature": b"v@:{simd_quatd=<4d>}d",
            "arguments": {2: {"type": b"{simd_quatd=<4d>}"}},
        },
    )
    r(
        b"MDLAnimatedQuaternion",
        b"setFloatQuaternion:atTime:",
        {
            "full_signature": b"v@:{simd_quatf=<4f>}d",
            "arguments": {2: {"type": b"{simd_quatf=<4f>}"}},
        },
    )
    r(
        b"MDLAnimatedQuaternionArray",
        b"getDoubleQuaternionArray:maxCount:",
        {
            "full_signature": b"Q@:^{simd_quatd=<4d>}Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{simd_quatd=<4d>}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedQuaternionArray",
        b"getDoubleQuaternionArray:maxCount:atTime:",
        {
            "full_signature": b"Q@:^{simd_quatd=<4d>}Qd",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{simd_quatd=<4d>}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedQuaternionArray",
        b"getFloatQuaternionArray:maxCount:",
        {
            "full_signature": b"Q@:^{simd_quatf=<4f>}Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{simd_quatf=<4f>}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedQuaternionArray",
        b"getFloatQuaternionArray:maxCount:atTime:",
        {
            "full_signature": b"Q@:^{simd_quatf=<4f>}Qd",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{simd_quatf=<4f>}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedQuaternionArray",
        b"resetWithDoubleQuaternionArray:count:atTimes:count:",
        {
            "full_signature": b"v@:^{simd_quatd=<4d>}Q^dQ",
            "arguments": {
                2: {
                    "type": b"^{simd_quatd=<4d>}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                },
                4: {"type_modifier": b"n", "c_array_length_in_arg": 5},
            },
        },
    )
    r(
        b"MDLAnimatedQuaternionArray",
        b"resetWithFloatQuaternionArray:count:atTimes:count:",
        {
            "full_signature": b"v@:^{simd_quatf=<4f>}Q^dQ",
            "arguments": {
                2: {
                    "type": b"^{simd_quatf=<4f>}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                },
                4: {"type_modifier": b"n", "c_array_length_in_arg": 5},
            },
        },
    )
    r(
        b"MDLAnimatedQuaternionArray",
        b"setDoubleQuaternionArray:count:atTime:",
        {
            "full_signature": b"v@:^{simd_quatd=<4d>}Qd",
            "arguments": {
                2: {
                    "type": b"^{simd_quatd=<4d>}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            },
        },
    )
    r(
        b"MDLAnimatedQuaternionArray",
        b"setFloatQuaternionArray:count:atTime:",
        {
            "full_signature": b"v@:^{simd_quatf=<4f>}Qd",
            "arguments": {
                2: {
                    "type": b"^{simd_quatf=<4f>}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            },
        },
    )
    r(
        b"MDLAnimatedScalar",
        b"copyDoubleArrayInto:maxCount:atTime:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalar",
        b"copyFloatArrayInto:maxCount:atTime:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalar",
        b"getDoubleArray:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalar",
        b"getFloatArray:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalar",
        b"resetWithDoubleArray:atTimes:count:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            }
        },
    )
    r(
        b"MDLAnimatedScalar",
        b"resetWithFloatArray:atTimes:count:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"copyDoubleArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"copyDoubleArrayInto:maxCount:atTime:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"copyFloatArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"copyFloatArrayInto:maxCount:atTime:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"getDoubleArray:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"getDoubleArray:maxCount:atTime:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"getFloatArray:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"getFloatArray:maxCount:atTime:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"resetWithDoubleArray:count:atTimes:count:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type_modifier": b"n", "c_array_length_in_arg": 5},
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"resetWithFloatArray:atTimes:count:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type_modifier": b"n", "c_array_length_in_arg": 5},
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"resetWithFloatArray:count:atTimes:count:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type_modifier": b"n", "c_array_length_in_arg": 5},
            }
        },
    )
    r(
        b"MDLAnimatedScalarArray",
        b"setDoubleArray:count:atTime:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"MDLAnimatedScalarArray",
        b"setFloatArray:count:atTime:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"MDLAnimatedValue",
        b"copyTimesInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedValue",
        b"getTimes:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            }
        },
    )
    r(b"MDLAnimatedValue", b"isAnimated", {"retval": {"type": "Z"}})
    r(
        b"MDLAnimatedVector2",
        b"copyDouble2ArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedVector2",
        b"copyFloat2ArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedVector2",
        b"double2AtTime:",
        {"full_signature": b"<2d>@:d", "retval": {"type": b"<2d>"}},
    )
    r(
        b"MDLAnimatedVector2",
        b"float2AtTime:",
        {"full_signature": b"<2f>@:d", "retval": {"type": b"<2f>"}},
    )
    r(
        b"MDLAnimatedVector2",
        b"getDouble2Array:maxCount:",
        {
            "full_signature": b"Q@:^<2d>Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<2d>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector2",
        b"getFloat2Array:maxCount:",
        {
            "full_signature": b"Q@:^<2f>Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<2f>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector2",
        b"resetWithDouble2Array:atTimes:count:",
        {
            "full_signature": b"v@:^<2d>^dQ",
            "arguments": {
                2: {
                    "type": b"^<2d>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 4,
                },
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            },
        },
    )
    r(
        b"MDLAnimatedVector2",
        b"resetWithFloat2Array:atTimes:count:",
        {
            "full_signature": b"v@:^<2f>^dQ",
            "arguments": {
                2: {
                    "type": b"^<2f>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 4,
                },
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            },
        },
    )
    r(
        b"MDLAnimatedVector2",
        b"setDouble2:atTime:",
        {"full_signature": b"v@:<2d>d", "arguments": {2: {"type": b"<2d>"}}},
    )
    r(
        b"MDLAnimatedVector2",
        b"setFloat2:atTime:",
        {"full_signature": b"v@:<2f>d", "arguments": {2: {"type": b"<2f>"}}},
    )
    r(
        b"MDLAnimatedVector3",
        b"copyDouble3ArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedVector3",
        b"copyFloat3ArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedVector3",
        b"double3AtTime:",
        {"full_signature": b"<3d>@:d", "retval": {"type": b"<3d>"}},
    )
    r(
        b"MDLAnimatedVector3",
        b"float3AtTime:",
        {"full_signature": b"<3f>@:d", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLAnimatedVector3",
        b"getDouble3Array:maxCount:",
        {
            "full_signature": b"Q@:^<3d>Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<3d>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector3",
        b"getFloat3Array:maxCount:",
        {
            "full_signature": b"Q@:^<3f>Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<3f>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector3",
        b"resetWithDouble3Array:atTimes:count:",
        {
            "full_signature": b"v@:^<3d>^dQ",
            "arguments": {
                2: {
                    "type": b"^<3d>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 4,
                },
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            },
        },
    )
    r(
        b"MDLAnimatedVector3",
        b"resetWithFloat3Array:atTimes:count:",
        {
            "full_signature": b"v@:^<3f>^dQ",
            "arguments": {
                2: {
                    "type": b"^<3f>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 4,
                },
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            },
        },
    )
    r(
        b"MDLAnimatedVector3",
        b"setDouble3:atTime:",
        {"full_signature": b"v@:<3d>d", "arguments": {2: {"type": b"<3d>"}}},
    )
    r(
        b"MDLAnimatedVector3",
        b"setFloat3:atTime:",
        {"full_signature": b"v@:<3f>d", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLAnimatedVector3Array",
        b"getDouble3Array:maxCount:",
        {
            "full_signature": b"Q@:^<3d>Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<3d>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector3Array",
        b"getDouble3Array:maxCount:atTime:",
        {
            "full_signature": b"Q@:^<3d>Qd",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<3d>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector3Array",
        b"getFloat3Array:maxCount:",
        {
            "full_signature": b"Q@:^<3f>Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<3f>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector3Array",
        b"getFloat3Array:maxCount:atTime:",
        {
            "full_signature": b"Q@:^<3f>Qd",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<3f>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector3Array",
        b"resetWithDouble3Array:count:atTimes:count:",
        {
            "full_signature": b"v@:^<3d>Q^dQ",
            "arguments": {
                2: {
                    "type": b"^<3d>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                },
                4: {"type_modifier": b"n", "c_array_length_in_arg": 5},
            },
        },
    )
    r(
        b"MDLAnimatedVector3Array",
        b"resetWithFloat3Array:count:atTimes:count:",
        {
            "full_signature": b"v@:^<3f>Q^dQ",
            "arguments": {
                2: {
                    "type": b"^<3f>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                },
                4: {"type_modifier": b"n", "c_array_length_in_arg": 5},
            },
        },
    )
    r(
        b"MDLAnimatedVector3Array",
        b"setDouble3Array:count:atTime:",
        {
            "full_signature": b"v@:^<3d>Qd",
            "arguments": {
                2: {"type": b"^<3d>", "type_modifier": b"n", "c_array_length_in_arg": 3}
            },
        },
    )
    r(
        b"MDLAnimatedVector3Array",
        b"setFloat3Array:count:atTime:",
        {
            "full_signature": b"v@:^<3f>Qd",
            "arguments": {
                2: {"type": b"^<3f>", "type_modifier": b"n", "c_array_length_in_arg": 3}
            },
        },
    )
    r(
        b"MDLAnimatedVector4",
        b"copyDouble4ArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedVector4",
        b"copyFloat4ArrayInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLAnimatedVector4",
        b"double4AtTime:",
        {"full_signature": b"<4d>@:d", "retval": {"type": b"<4d>"}},
    )
    r(
        b"MDLAnimatedVector4",
        b"float4AtTime:",
        {"full_signature": b"<4f>@:d", "retval": {"type": b"<4f>"}},
    )
    r(
        b"MDLAnimatedVector4",
        b"getDouble4Array:maxCount:",
        {
            "full_signature": b"Q@:^<4d>Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<4d>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector4",
        b"getFloat4Array:maxCount:",
        {
            "full_signature": b"Q@:^<4f>Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^<4f>",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLAnimatedVector4",
        b"resetWithDouble4Array:atTimes:count:",
        {
            "full_signature": b"v@:^<4d>^dQ",
            "arguments": {
                2: {
                    "type": b"^<4d>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 4,
                },
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            },
        },
    )
    r(
        b"MDLAnimatedVector4",
        b"resetWithFloat4Array:atTimes:count:",
        {
            "full_signature": b"v@:^<4f>^dQ",
            "arguments": {
                2: {
                    "type": b"^<4f>",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 4,
                },
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            },
        },
    )
    r(
        b"MDLAnimatedVector4",
        b"setDouble4:atTime:",
        {"full_signature": b"v@:<4d>d", "arguments": {2: {"type": b"<4d>"}}},
    )
    r(
        b"MDLAnimatedVector4",
        b"setFloat4:atTime:",
        {"full_signature": b"v@:<4f>d", "arguments": {2: {"type": b"<4f>"}}},
    )
    r(
        b"MDLAnimationBindComponent",
        b"geometryBindTransform",
        {
            "full_signature": b"{simd_double4x4=[4<4d>]}@:",
            "retval": {"type": b"{simd_double4x4=[4<4d>]}"},
        },
    )
    r(
        b"MDLAnimationBindComponent",
        b"setGeometryBindTransform:",
        {
            "full_signature": b"v@:{simd_double4x4=[4<4d>]}",
            "arguments": {2: {"type": b"{simd_double4x4=[4<4d>]}"}},
        },
    )
    r(
        b"MDLAreaLight",
        b"setSuperEllipticPower:",
        {"full_signature": b"v@:<2f>", "arguments": {2: {"type": b"<2f>"}}},
    )
    r(
        b"MDLAreaLight",
        b"superEllipticPower",
        {"full_signature": b"<2f>@:", "retval": {"type": b"<2f>"}},
    )
    r(
        b"MDLAsset",
        b"boundingBox",
        {
            "full_signature": b"{MDLAxisAlignedBoundingBox=<3f><3f>}@:",
            "retval": {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"},
        },
    )
    r(
        b"MDLAsset",
        b"boundingBoxAtTime:",
        {
            "full_signature": b"{MDLAxisAlignedBoundingBox=<3f><3f>}@:d",
            "retval": {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"},
        },
    )
    r(b"MDLAsset", b"canExportFileExtension:", {"retval": {"type": b"Z"}})
    r(b"MDLAsset", b"canImportFileExtension:", {"retval": {"type": "Z"}})
    r(b"MDLAsset", b"exportAssetToURL:", {"retval": {"type": "Z"}})
    r(
        b"MDLAsset",
        b"exportAssetToURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"MDLAsset",
        b"initWithURL:bufferAllocator:preserveIndexing:error:",
        {"arguments": {4: {"type": "Z"}, 5: {"type_modifier": b"o"}}},
    )
    r(
        b"MDLAsset",
        b"initWithURL:vertexDescriptor:bufferAllocator:preserveTopology:error:",
        {"arguments": {5: {"type": "Z"}, 6: {"type_modifier": b"o"}}},
    )
    r(
        b"MDLAsset",
        b"setUpAxis:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLAsset",
        b"upAxis",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLCamera",
        b"bokehKernelWithSize:",
        {"full_signature": b"@@:<2i>", "arguments": {2: {"type": b"<2i>"}}},
    )
    r(
        b"MDLCamera",
        b"exposure",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLCamera",
        b"exposureCompression",
        {"full_signature": b"<2f>@:", "retval": {"type": b"<2f>"}},
    )
    r(
        b"MDLCamera",
        b"flash",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLCamera",
        b"frameBoundingBox:setNearAndFar:",
        {
            "full_signature": b"v@:{MDLAxisAlignedBoundingBox=<3f><3f>}Z",
            "arguments": {
                2: {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"},
                3: {"type": "Z"},
            },
        },
    )
    r(
        b"MDLCamera",
        b"lookAt:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLCamera",
        b"lookAt:from:",
        {
            "full_signature": b"v@:<3f><3f>",
            "arguments": {2: {"type": b"<3f>"}, 3: {"type": b"<3f>"}},
        },
    )
    r(
        b"MDLCamera",
        b"projectionMatrix",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLCamera",
        b"rayTo:forViewPort:",
        {
            "full_signature": b"<3f>@:<2i><2i>",
            "retval": {"type": b"<3f>"},
            "arguments": {2: {"type": b"<2i>"}, 3: {"type": b"<2i>"}},
        },
    )
    r(
        b"MDLCamera",
        b"sensorEnlargement",
        {"full_signature": b"<2f>@:", "retval": {"type": b"<2f>"}},
    )
    r(
        b"MDLCamera",
        b"sensorShift",
        {"full_signature": b"<2f>@:", "retval": {"type": b"<2f>"}},
    )
    r(
        b"MDLCamera",
        b"setExposure:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLCamera",
        b"setExposureCompression:",
        {"full_signature": b"v@:<2f>", "arguments": {2: {"type": b"<2f>"}}},
    )
    r(
        b"MDLCamera",
        b"setFlash:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLCamera",
        b"setSensorEnlargement:",
        {"full_signature": b"v@:<2f>", "arguments": {2: {"type": b"<2f>"}}},
    )
    r(
        b"MDLCamera",
        b"setSensorShift:",
        {"full_signature": b"v@:<2f>", "arguments": {2: {"type": b"<2f>"}}},
    )
    r(
        b"MDLCheckerboardTexture",
        b"initWithDivisions:name:dimensions:channelCount:channelEncoding:color1:color2:",
        {
            "full_signature": b"@@:f@<2i>iq^{CGColor=}^{CGColor=}",
            "arguments": {4: {"type": b"<2i>"}},
        },
    )
    r(
        b"MDLColorSwatchTexture",
        b"initWithColorGradientFrom:toColor:name:textureDimensions:",
        {
            "full_signature": b"@@:^{CGColor=}^{CGColor=}@<2i>",
            "arguments": {5: {"type": b"<2i>"}},
        },
    )
    r(
        b"MDLColorSwatchTexture",
        b"initWithColorTemperatureGradientFrom:toColorTemperature:name:textureDimensions:",
        {"full_signature": b"@@:ff@<2i>", "arguments": {5: {"type": b"<2i>"}}},
    )
    r(
        b"MDLLight",
        b"irradianceAtPoint:",
        {"full_signature": b"^{CGColor=}@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLLight",
        b"irradianceAtPoint:colorSpace:",
        {
            "full_signature": b"^{CGColor=}@:<3f>^{CGColorSpace=}",
            "arguments": {2: {"type": b"<3f>"}},
        },
    )
    r(
        b"MDLMaterialProperty",
        b"float2Value",
        {"full_signature": b"<2f>@:", "retval": {"type": b"<2f>"}},
    )
    r(
        b"MDLMaterialProperty",
        b"float3Value",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLMaterialProperty",
        b"float4Value",
        {"full_signature": b"<4f>@:", "retval": {"type": b"<4f>"}},
    )
    r(
        b"MDLMaterialProperty",
        b"initWithName:semantic:float2:",
        {"full_signature": b"@@:@Q<2f>", "arguments": {4: {"type": b"<2f>"}}},
    )
    r(
        b"MDLMaterialProperty",
        b"initWithName:semantic:float3:",
        {"full_signature": b"@@:@Q<3f>", "arguments": {4: {"type": b"<3f>"}}},
    )
    r(
        b"MDLMaterialProperty",
        b"initWithName:semantic:float4:",
        {"full_signature": b"@@:@Q<4f>", "arguments": {4: {"type": b"<4f>"}}},
    )
    r(
        b"MDLMaterialProperty",
        b"initWithName:semantic:matrix4x4:",
        {
            "full_signature": b"@@:@Q{simd_float4x4=[4<4f>]}",
            "arguments": {4: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"MDLMaterialProperty",
        b"matrix4x4",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLMaterialProperty",
        b"setFloat2Value:",
        {"full_signature": b"v@:<2f>", "arguments": {2: {"type": b"<2f>"}}},
    )
    r(
        b"MDLMaterialProperty",
        b"setFloat3Value:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLMaterialProperty",
        b"setFloat4Value:",
        {"full_signature": b"v@:<4f>", "arguments": {2: {"type": b"<4f>"}}},
    )
    r(
        b"MDLMaterialProperty",
        b"setMatrix4x4:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"MDLMaterialPropertyNode",
        b"evaluationFunction",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"MDLMaterialPropertyNode",
        b"initWithInputs:outputs:evaluationFunction:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"MDLMaterialPropertyNode",
        b"setEvaluationFunction:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"MDLMatrix4x4Array",
        b"getDouble4x4Array:maxCount:",
        {
            "full_signature": b"Q@:^{simd_double4x4=[4<4d>]}Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{simd_double4x4=[4<4d>]}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLMatrix4x4Array",
        b"getFloat4x4Array:maxCount:",
        {
            "full_signature": b"Q@:^{simd_float4x4=[4<4f>]}Q",
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{simd_float4x4=[4<4f>]}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            },
        },
    )
    r(
        b"MDLMatrix4x4Array",
        b"setDouble4x4Array:count:",
        {
            "full_signature": b"v@:^{simd_double4x4=[4<4d>]}Q",
            "arguments": {
                2: {
                    "type": b"^{simd_double4x4=[4<4d>]}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            },
        },
    )
    r(
        b"MDLMatrix4x4Array",
        b"setFloat4x4Array:count:",
        {
            "full_signature": b"v@:^{simd_float4x4=[4<4f>]}Q",
            "arguments": {
                2: {
                    "type": b"^{simd_float4x4=[4<4f>]}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            },
        },
    )
    r(
        b"MDLMesh",
        b"boundingBox",
        {
            "full_signature": b"{MDLAxisAlignedBoundingBox=<3f><3f>}@:",
            "retval": {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"},
        },
    )
    r(
        b"MDLMesh",
        b"generateAmbientOcclusionTextureWithQuality:attenuationFactor:objectsToConsider:vertexAttributeNamed:materialPropertyNamed:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"MDLMesh",
        b"generateAmbientOcclusionTextureWithSize:raysPerSample:attenuationFactor:objectsToConsider:vertexAttributeNamed:materialPropertyNamed:",
        {
            "full_signature": b"Z@:<2i>qf@@@",
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"<2i>"}},
        },
    )
    r(
        b"MDLMesh",
        b"generateAmbientOcclusionVertexColorsWithQuality:attenuationFactor:objectsToConsider:vertexAttributeNamed:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"MDLMesh",
        b"generateAmbientOcclusionVertexColorsWithRaysPerSample:attenuationFactor:objectsToConsider:vertexAttributeNamed:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"MDLMesh",
        b"generateLightMapTextureWithQuality:lightsToCondider:objectsToConsider:vertexAttributeNamed:materialPropertyNamed:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"MDLMesh",
        b"generateLightMapTextureWithQuality:lightsToConsider:objectsToConsider:vertexAttributeNamed:materialPropertyNamed:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MDLMesh",
        b"generateLightMapTextureWithTextureSize:lightsToCondider:objectsToConsider:vertexAttributeNamed:materialPropertyNamed:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"MDLMesh",
        b"generateLightMapTextureWithTextureSize:lightsToConsider:objectsToConsider:vertexAttributeNamed:materialPropertyNamed:",
        {
            "full_signature": b"Z@:<2i>@@@@",
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"<2i>"}},
        },
    )
    r(
        b"MDLMesh",
        b"generateLightMapVertexColorsWithLightsToConsider:objectsToConsider:vertexAttributeNamed:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"MDLMesh",
        b"initBoxWithExtent:segments:inwardNormals:geometryType:allocator:",
        {
            "full_signature": b"@@:<3f><3I>Zq@",
            "arguments": {2: {"type": b"<3f>"}, 3: {"type": b"<3I>"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"initCapsuleWithExtent:cylinderSegments:hemisphereSegments:inwardNormals:geometryType:allocator:",
        {
            "full_signature": b"@@:<3f><2I>iZq@",
            "arguments": {2: {"type": b"<3f>"}, 3: {"type": b"<2I>"}, 5: {"type": "Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"initConeWithExtent:segments:inwardNormals:cap:geometryType:allocator:",
        {
            "full_signature": b"@@:<3f><2I>ZZq@",
            "arguments": {
                2: {"type": b"<3f>"},
                3: {"type": b"<2I>"},
                4: {"type": "Z"},
                5: {"type": "Z"},
            },
        },
    )
    r(
        b"MDLMesh",
        b"initCylinderWithExtent:segments:inwardNormals:topCap:bottomCap:cap:geometryType:allocator:",
        {"arguments": {4: {"type": "Z"}, 5: {"type": "Z"}, 6: {"type": "Z"}}},
    )
    r(
        b"MDLMesh",
        b"initCylinderWithExtent:segments:inwardNormals:topCap:bottomCap:geometryType:allocator:",
        {
            "full_signature": b"@@:<3f><2I>ZZZq@",
            "arguments": {
                2: {"type": b"<3f>"},
                3: {"type": b"<2I>"},
                4: {"type": b"Z"},
                5: {"type": b"Z"},
                6: {"type": b"Z"},
            },
        },
    )
    r(
        b"MDLMesh",
        b"initHemisphereWithExtent:segments:inwardNormals:cap:geometryType:allocator:",
        {
            "full_signature": b"@@:<3f><2I>ZZq@",
            "arguments": {
                2: {"type": b"<3f>"},
                3: {"type": b"<2I>"},
                4: {"type": "Z"},
                5: {"type": "Z"},
            },
        },
    )
    r(
        b"MDLMesh",
        b"initIcosahedronWithExtent:inwardNormals:geometryType:allocator:",
        {
            "full_signature": b"@@:<3f>Zq@",
            "arguments": {2: {"type": b"<3f>"}, 3: {"type": b"Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"initIcosahedronWithExtent:inwardNormals:segments:geometryType:allocator:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"MDLMesh",
        b"initMeshWithPrimitive:segments:inwardNormals:geometryType:allocator:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"MDLMesh",
        b"initPlaneWithExtent:segments:geometryType:allocator:",
        {
            "full_signature": b"@@:<3f><2I>q@",
            "arguments": {2: {"type": b"<3f>"}, 3: {"type": b"<2I>"}},
        },
    )
    r(
        b"MDLMesh",
        b"initSphereWithExtent:segments:inwardNormals:geometryType:allocator:",
        {
            "full_signature": b"@@:<3f><2I>Zq@",
            "arguments": {2: {"type": b"<3f>"}, 3: {"type": b"<2I>"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"makeVerticesUniqueAndReturnError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"MDLMesh",
        b"newBoxWithDimensions:segments:geometryType:inwardNormals:allocator:",
        {
            "full_signature": b"@@:<3f><3I>qZ@",
            "arguments": {2: {"type": b"<3f>"}, 3: {"type": b"<3I>"}, 5: {"type": "Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"newCapsuleWithHeight:radii:radialSegments:verticalSegments:hemisphereSegments:geometryType:inwardNormals:allocator:",
        {
            "full_signature": b"@@:f<2f>QQQqZ@",
            "arguments": {3: {"type": b"<2f>"}, 8: {"type": "Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"newCylinderWithHeight:radii:radialSegments:verticalSegments:geometryType:inwardNormals:allocator:",
        {
            "full_signature": b"@@:f<2f>QQqZ@",
            "arguments": {3: {"type": b"<2f>"}, 7: {"type": "Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"newEllipsoidWithRadii:radialSegments:verticalSegments:geometryType:inwardNormals:hemisphere:allocator:",
        {
            "full_signature": b"@@:<3f>QQqZZ@",
            "arguments": {2: {"type": b"<3f>"}, 6: {"type": "Z"}, 7: {"type": "Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"newEllipticalConeWithHeight:radii:radialSegments:verticalSegments:geometryType:inwardNormals:allocator:",
        {
            "full_signature": b"@@:f<2f>QQqZ@",
            "arguments": {3: {"type": b"<2f>"}, 7: {"type": "Z"}},
        },
    )
    r(
        b"MDLMesh",
        b"newIcosahedronWithRadius:inwardNormals:allocator:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"MDLMesh",
        b"newIcosahedronWithRadius:inwardNormals:geometryType:allocator:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"MDLMesh",
        b"newMeshWithPrimitive:segments:inwardNormals:geometryType:allocator:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"MDLMesh",
        b"newPlaneWithDimensions:segments:geometryType:allocator:",
        {
            "full_signature": b"@@:<2f><2I>q@",
            "arguments": {2: {"type": b"<2f>"}, 3: {"type": b"<2I>"}},
        },
    )
    r(b"MDLMeshBufferMap", b"bytes", {"retval": {"c_array_of_variable_length": True}})
    r(
        b"MDLMeshBufferMap",
        b"initWithBytes:deallocator:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_of_variable_length": True},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                },
            }
        },
    )
    r(
        b"MDLMorphDeformer",
        b"copyShapeSetTargetCountsInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLMorphDeformer",
        b"copyShapeSetTargetWeightsInto:maxCount:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MDLMorphDeformer",
        b"initWithTargetShapes:shapeSetTargetWeights:count:shapeSetTargetCounts:count:",
        {
            "arguments": {
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                5: {"type_modifier": b"n", "c_array_length_in_arg": 6},
            }
        },
    )
    r(
        b"MDLNoiseTexture",
        b"initCellularNoiseWithFrequency:name:textureDimensions:channelEncoding:",
        {"full_signature": b"@@:f@<2i>q", "arguments": {4: {"type": b"<2i>"}}},
    )
    r(
        b"MDLNoiseTexture",
        b"initScalarNoiseWithSmoothness:name:textureDimensions:channelCount:channelEncoding:grayScale:",
        {"arguments": {7: {"type": "Z"}}},
    )
    r(
        b"MDLNoiseTexture",
        b"initScalarNoiseWithSmoothness:name:textureDimensions:channelCount:channelEncoding:grayscale:",
        {
            "full_signature": b"@@:f@<2i>iqZ",
            "arguments": {4: {"type": b"<2i>"}, 7: {"type": b"Z"}},
        },
    )
    r(
        b"MDLNoiseTexture",
        b"initVectorNoiseWithSmoothness:name:textureDimensions:channelEncoding:",
        {"full_signature": b"@@:f@<2i>q", "arguments": {4: {"type": b"<2i>"}}},
    )
    r(
        b"MDLObject",
        b"boundingBoxAtTime:",
        {
            "full_signature": b"{MDLAxisAlignedBoundingBox=<3f><3f>}@:d",
            "retval": {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"},
        },
    )
    r(
        b"MDLObject",
        b"enumerateChildObjectsOfClass:root:usingBlock:stopPointer:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"o^Z"},
                        },
                    }
                },
                5: {"type": "N^Z"},
            }
        },
    )
    r(b"MDLObject", b"hidden", {"retval": {"type": "Z"}})
    r(b"MDLObject", b"resetsTransform", {"retval": {"type": "Z"}})
    r(b"MDLObject", b"setHidden:", {"arguments": {2: {"type": "Z"}}})
    r(b"MDLObject", b"setResetsTransform:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"MDLSkinDeformer",
        b"copyJointBindTransformsInto:maxCount:",
        {
            "retval": {"type": "Q"},
            "arguments": {
                2: {"comment": "matrix_float4x4", "type": "?"},
                3: {"type": "Q"},
            },
        },
    )
    r(
        b"MDLSkyCubeTexture",
        b"highDynamicRangeCompression",
        {"full_signature": b"<2f>@:", "retval": {"type": b"<2f>"}},
    )
    r(
        b"MDLSkyCubeTexture",
        b"initWithName:channelEncoding:textureDimensions:turbidity:sunElevation:sunAzimuth:upperAtmosphereScattering:groundAlbedo:",
        {"full_signature": b"@@:@q<2i>fffff", "arguments": {4: {"type": b"<2i>"}}},
    )
    r(
        b"MDLSkyCubeTexture",
        b"initWithName:channelEncoding:textureDimensions:turbidity:sunElevation:upperAtmosphereScattering:groundAlbedo:",
        {"full_signature": b"@@:@q<2i>ffff", "arguments": {4: {"type": b"<2i>"}}},
    )
    r(
        b"MDLSkyCubeTexture",
        b"setHighDynamicRangeCompression:",
        {"full_signature": b"v@:<2f>", "arguments": {2: {"type": b"<2f>"}}},
    )
    r(
        b"MDLStereoscopicCamera",
        b"leftProjectionMatrix",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLStereoscopicCamera",
        b"leftViewMatrix",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLStereoscopicCamera",
        b"rightProjectionMatrix",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLStereoscopicCamera",
        b"rightViewMatrix",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLTexture",
        b"dimensions",
        {"full_signature": b"<2i>@:", "retval": {"type": b"<2i>"}},
    )
    r(b"MDLTexture", b"hasAlphaValues", {"retval": {"type": "Z"}})
    r(
        b"MDLTexture",
        b"initWithData:topLeftOrigin:name:dimensions:rowStride:channelCount:channelEncoding:isCube:",
        {
            "full_signature": b"@@:@Z@<2i>qQqZ",
            "arguments": {3: {"type": "Z"}, 5: {"type": b"<2i>"}, 9: {"type": "Z"}},
        },
    )
    r(
        b"MDLTexture",
        b"irradianceTextureCubeWithTexture:name:dimensions:",
        {"full_signature": b"@@:@@<2i>", "arguments": {4: {"type": b"<2i>"}}},
    )
    r(
        b"MDLTexture",
        b"irradianceTextureCubeWithTexture:name:dimensions:roughness:",
        {"full_signature": b"@@:@@<2i>f", "arguments": {4: {"type": b"<2i>"}}},
    )
    r(b"MDLTexture", b"isCube", {"retval": {"type": "Z"}})
    r(b"MDLTexture", b"setHasAlphaValues:", {"arguments": {2: {"type": "Z"}}})
    r(b"MDLTexture", b"setIsCube:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"MDLTexture",
        b"texelDataWithBottomLeftOriginAtMipLevel:create:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"MDLTexture",
        b"texelDataWithTopLeftOriginAtMipLevel:create:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(b"MDLTexture", b"writeToURL:", {"retval": {"type": "Z"}})
    r(b"MDLTexture", b"writeToURL:level:", {"retval": {"type": "Z"}})
    r(b"MDLTexture", b"writeToURL:type:", {"retval": {"type": "Z"}})
    r(b"MDLTexture", b"writeToURL:type:level:", {"retval": {"type": "Z"}})
    r(
        b"MDLTransform",
        b"initWithMatrix:",
        {
            "full_signature": b"@@:{simd_float4x4=[4<4f>]}",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"MDLTransform",
        b"initWithMatrix:resetsTransform:",
        {
            "full_signature": b"@@:{simd_float4x4=[4<4f>]}Z",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}, 3: {"type": "Z"}},
        },
    )
    r(
        b"MDLTransform",
        b"initWithTransformComponent:resetsTransform:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"MDLTransform",
        b"rotation",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLTransform",
        b"rotationAtTime:",
        {"full_signature": b"<3f>@:d", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLTransform",
        b"rotationMatrixAtTime:",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:d",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLTransform",
        b"scale",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLTransform",
        b"scaleAtTime:",
        {"full_signature": b"<3f>@:d", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLTransform",
        b"setMatrix:forTime:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}d",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"MDLTransform",
        b"setRotation:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLTransform",
        b"setRotation:forTime:",
        {"full_signature": b"v@:<3f>d", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLTransform",
        b"setScale:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLTransform",
        b"setScale:forTime:",
        {"full_signature": b"v@:<3f>d", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLTransform",
        b"setShear:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLTransform",
        b"setShear:forTime:",
        {"full_signature": b"v@:<3f>d", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLTransform",
        b"setTranslation:",
        {"full_signature": b"v@:<3f>", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLTransform",
        b"setTranslation:forTime:",
        {"full_signature": b"v@:<3f>d", "arguments": {2: {"type": b"<3f>"}}},
    )
    r(
        b"MDLTransform",
        b"shear",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLTransform",
        b"shearAtTime:",
        {"full_signature": b"<3f>@:d", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLTransform",
        b"translation",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLTransform",
        b"translationAtTime:",
        {"full_signature": b"<3f>@:d", "retval": {"type": b"<3f>"}},
    )
    r(
        b"MDLTransformStack",
        b"double4x4AtTime:",
        {
            "full_signature": b"{simd_double4x4=[4<4d>]}@:d",
            "retval": {"type": b"{simd_double4x4=[4<4d>]}"},
        },
    )
    r(
        b"MDLTransformStack",
        b"float4x4AtTime:",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:d",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"MDLVertexAttribute",
        b"initializationValue",
        {"full_signature": b"<4f>@:", "retval": {"type": b"<4f>"}},
    )
    r(
        b"MDLVertexAttribute",
        b"setInitializationValue:",
        {"full_signature": b"v@:<4f>", "arguments": {2: {"type": b"<4f>"}}},
    )
    r(
        b"MDLVertexAttributeData",
        b"dataStart",
        {"retval": {"c_array_of_variable_length": True}},
    )
    r(
        b"MDLVertexAttributeData",
        b"setDataStart:",
        {"arguments": {2: {"c_array_of_variable_length": True}}},
    )
    r(
        b"MDLVoxelArray",
        b"boundingBox",
        {
            "full_signature": b"{MDLAxisAlignedBoundingBox=<3f><3f>}@:",
            "retval": {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"},
        },
    )
    r(
        b"MDLVoxelArray",
        b"indexOfSpatialLocation:",
        {
            "full_signature": b"<4i>@:<3f>",
            "retval": {"type": b"<4i>"},
            "arguments": {2: {"type": b"<3f>"}},
        },
    )
    r(
        b"MDLVoxelArray",
        b"initWithData:boundingBox:voxelExtent:",
        {
            "full_signature": b"@@:@{MDLAxisAlignedBoundingBox=<3f><3f>}f",
            "arguments": {3: {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"}},
        },
    )
    r(b"MDLVoxelArray", b"isValidSignedShellField", {"retval": {"type": "Z"}})
    r(
        b"MDLVoxelArray",
        b"setIsValidSignedShellField:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"MDLVoxelArray",
        b"setVoxelAtIndex:",
        {"full_signature": b"v@:<4i>", "arguments": {2: {"type": b"<4i>"}}},
    )
    r(
        b"MDLVoxelArray",
        b"spatialLocationOfIndex:",
        {
            "full_signature": b"<3f>@:<4i>",
            "retval": {"type": b"<3f>"},
            "arguments": {2: {"type": b"<4i>"}},
        },
    )
    r(
        b"MDLVoxelArray",
        b"voxelBoundingBoxAtIndex:",
        {
            "full_signature": b"{MDLAxisAlignedBoundingBox=<3f><3f>}@:<4i>",
            "retval": {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"},
            "arguments": {2: {"type": b"<4i>"}},
        },
    )
    r(
        b"MDLVoxelArray",
        b"voxelExistsAtIndex:allowAnyX:allowAnyY:allowAnyZ:allowAnyShell:",
        {
            "full_signature": b"Z@:<4i>ZZZZ",
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"<4i>"},
                3: {"type": "Z"},
                4: {"type": "Z"},
                5: {"type": "Z"},
                6: {"type": "Z"},
            },
        },
    )
    r(
        b"MDLVoxelArray",
        b"voxelIndexExtent",
        {
            "full_signature": b"{MDLVoxelIndexExtent=<4i><4i>}@:",
            "retval": {"type": b"{MDLVoxelIndexExtent=<4i><4i>}"},
        },
    )
    r(
        b"MDLVoxelArray",
        b"voxelsWithinExtent:",
        {
            "full_signature": b"@@:{MDLVoxelIndexExtent=<4i><4i>}",
            "arguments": {2: {"type": b"{MDLVoxelIndexExtent=<4i><4i>}"}},
        },
    )
    r(b"NSObject", b"IsInverseOp", {"required": True, "retval": {"type": b"B"}})
    r(
        b"NSObject",
        b"addObject:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"allocator", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"boundingBox",
        {
            "full_signature": b"{MDLAxisAlignedBoundingBox=<3f><3f>}@:",
            "required": True,
            "retval": {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"},
        },
    )
    r(
        b"NSObject",
        b"canResolveAssetNamed:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"capacity", {"required": True, "retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"copyJointBindTransformsInto:maxCount:",
        {
            "retval": {"type": "Q"},
            "arguments": {
                2: {"comment": "matrix_float4x4", "type": "?"},
                3: {"type": "Q"},
            },
        },
    )
    r(b"NSObject", b"count", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"double4x4AtTime:",
        {
            "full_signature": b"{simd_double4x4=[4<4d>]}@:d",
            "required": True,
            "retval": {"type": b"{simd_double4x4=[4<4d>]}"},
            "arguments": {2: {"type": b"d"}},
        },
    )
    r(
        b"NSObject",
        b"fillData:offset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"float4x4AtTime:",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:d",
            "required": True,
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
            "arguments": {2: {"type": b"d"}},
        },
    )
    r(
        b"NSObject",
        b"globalTransformWithObject:atTime",
        {"arguments": {3: {"type": "d"}}},
    )
    r(
        b"NSObject",
        b"globalTransformWithObject:atTime:",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:@d",
            "required": False,
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"d"}},
        },
    )
    r(b"NSObject", b"keyTimes", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"length", {"required": True, "retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"localTransformAtTime:",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:d",
            "required": False,
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
            "arguments": {2: {"type": "d"}},
        },
    )
    r(b"NSObject", b"map", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"matrix",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "required": True,
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(b"NSObject", b"maximumTime", {"required": True, "retval": {"type": "d"}})
    r(b"NSObject", b"minimumTime", {"required": True, "retval": {"type": "d"}})
    r(b"NSObject", b"name", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"newBuffer:type:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": "Q"}, 3: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"newBufferFromZone:data:type:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"newBufferFromZone:length:type:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}, 4: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"newBufferWithData:type:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"newZone:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": "Q"}}},
    )
    r(
        b"NSObject",
        b"newZoneForBuffersWithSize:andType:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"objectAtIndexedSubscript:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": "Q"}}},
    )
    r(b"NSObject", b"objects", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"removeObject:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"resetsTransform", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"resolveAssetNamed:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setBoundingBox:",
        {
            "full_signature": b"v@:{MDLAxisAlignedBoundingBox=<3f><3f>}",
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{MDLAxisAlignedBoundingBox=<3f><3f>}"}},
        },
    )
    r(
        b"NSObject",
        b"setLocalTransform:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}",
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"NSObject",
        b"setLocalTransform:forTime:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}d",
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}, 3: {"type": "d"}},
        },
    )
    r(
        b"NSObject",
        b"setMatrix:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}",
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(b"NSObject", b"setMaximumTime:", {"arguments": {2: {"type": "d"}}})
    r(b"NSObject", b"setMinimumTime:", {"arguments": {2: {"type": "d"}}})
    r(
        b"NSObject",
        b"setName:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setResetsTransform:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSObject",
        b"setSphericalHarmonicsLevel:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": "Q"}}},
    )
    r(
        b"NSObject",
        b"sphericalHarmonicsCoefficientsAtPosition:",
        {
            "full_signature": b"@@:<3f>",
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"<3f>"}},
        },
    )
    r(
        b"NSObject",
        b"sphericalHarmonicsLevel",
        {"required": False, "retval": {"type": "Q"}},
    )
    r(b"NSObject", b"type", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"zone", {"required": True, "retval": {"type": b"@"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "MDLAnimatedQuaternionArray", b"initWithElementCount:"
)
objc.registerNewKeywordsFromSelector("MDLAnimatedScalarArray", b"initWithElementCount:")
objc.registerNewKeywordsFromSelector(
    "MDLAnimatedVector3Array", b"initWithElementCount:"
)
objc.registerNewKeywordsFromSelector("MDLAsset", b"initWithBufferAllocator:")
objc.registerNewKeywordsFromSelector("MDLAsset", b"initWithURL:")
objc.registerNewKeywordsFromSelector(
    "MDLAsset", b"initWithURL:vertexDescriptor:bufferAllocator:"
)
objc.registerNewKeywordsFromSelector(
    "MDLAsset", b"initWithURL:vertexDescriptor:bufferAllocator:preserveTopology:error:"
)
objc.registerNewKeywordsFromSelector("MDLBundleAssetResolver", b"initWithBundle:")
objc.registerNewKeywordsFromSelector(
    "MDLCheckerboardTexture",
    b"initWithDivisions:name:dimensions:channelCount:channelEncoding:color1:color2:",
)
objc.registerNewKeywordsFromSelector(
    "MDLColorSwatchTexture",
    b"initWithColorGradientFrom:toColor:name:textureDimensions:",
)
objc.registerNewKeywordsFromSelector(
    "MDLColorSwatchTexture",
    b"initWithColorTemperatureGradientFrom:toColorTemperature:name:textureDimensions:",
)
objc.registerNewKeywordsFromSelector(
    "MDLLightProbe", b"initWithReflectiveTexture:irradianceTexture:"
)
objc.registerNewKeywordsFromSelector("MDLMaterial", b"initWithName:scatteringFunction:")
objc.registerNewKeywordsFromSelector("MDLMaterialProperty", b"initWithName:semantic:")
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:URL:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:color:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:float2:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:float3:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:float4:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:float:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:matrix4x4:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:string:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialProperty", b"initWithName:semantic:textureSampler:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialPropertyConnection", b"initWithOutput:input:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialPropertyGraph", b"initWithNodes:connections:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMaterialPropertyNode", b"initWithInputs:outputs:evaluationFunction:"
)
objc.registerNewKeywordsFromSelector("MDLMatrix4x4Array", b"initWithElementCount:")
objc.registerNewKeywordsFromSelector(
    "MDLMesh", b"initBoxWithExtent:segments:inwardNormals:geometryType:allocator:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh",
    b"initCapsuleWithExtent:cylinderSegments:hemisphereSegments:inwardNormals:geometryType:allocator:",
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh", b"initConeWithExtent:segments:inwardNormals:cap:geometryType:allocator:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh",
    b"initCylinderWithExtent:segments:inwardNormals:topCap:bottomCap:geometryType:allocator:",
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh",
    b"initHemisphereWithExtent:segments:inwardNormals:cap:geometryType:allocator:",
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh", b"initIcosahedronWithExtent:inwardNormals:geometryType:allocator:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh", b"initMeshBySubdividingMesh:submeshIndex:subdivisionLevels:allocator:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh", b"initPlaneWithExtent:segments:geometryType:allocator:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh", b"initSphereWithExtent:segments:inwardNormals:geometryType:allocator:"
)
objc.registerNewKeywordsFromSelector("MDLMesh", b"initWithBufferAllocator:")
objc.registerNewKeywordsFromSelector(
    "MDLMesh", b"initWithVertexBuffer:vertexCount:descriptor:submeshes:"
)
objc.registerNewKeywordsFromSelector(
    "MDLMesh", b"initWithVertexBuffers:vertexCount:descriptor:submeshes:"
)
objc.registerNewKeywordsFromSelector("MDLMeshBufferData", b"initWithType:data:")
objc.registerNewKeywordsFromSelector("MDLMeshBufferData", b"initWithType:length:")
objc.registerNewKeywordsFromSelector("MDLMeshBufferMap", b"initWithBytes:deallocator:")
objc.registerNewKeywordsFromSelector(
    "MDLNoiseTexture",
    b"initCellularNoiseWithFrequency:name:textureDimensions:channelEncoding:",
)
objc.registerNewKeywordsFromSelector(
    "MDLNoiseTexture",
    b"initScalarNoiseWithSmoothness:name:textureDimensions:channelCount:channelEncoding:grayscale:",
)
objc.registerNewKeywordsFromSelector(
    "MDLNoiseTexture",
    b"initVectorNoiseWithSmoothness:name:textureDimensions:channelEncoding:",
)
objc.registerNewKeywordsFromSelector(
    "MDLNormalMapTexture",
    b"initByGeneratingNormalMapWithTexture:name:smoothness:contrast:",
)
objc.registerNewKeywordsFromSelector(
    "MDLPackedJointAnimation", b"initWithName:jointPaths:"
)
objc.registerNewKeywordsFromSelector("MDLPathAssetResolver", b"initWithPath:")
objc.registerNewKeywordsFromSelector("MDLPhotometricLight", b"initWithIESProfile:")
objc.registerNewKeywordsFromSelector("MDLRelativeAssetResolver", b"initWithAsset:")
objc.registerNewKeywordsFromSelector("MDLSkeleton", b"initWithName:jointPaths:")
objc.registerNewKeywordsFromSelector(
    "MDLSkyCubeTexture",
    b"initWithName:channelEncoding:textureDimensions:turbidity:sunElevation:sunAzimuth:upperAtmosphereScattering:groundAlbedo:",
)
objc.registerNewKeywordsFromSelector(
    "MDLSkyCubeTexture",
    b"initWithName:channelEncoding:textureDimensions:turbidity:sunElevation:upperAtmosphereScattering:groundAlbedo:",
)
objc.registerNewKeywordsFromSelector(
    "MDLSubmesh", b"initWithIndexBuffer:indexCount:indexType:geometryType:material:"
)
objc.registerNewKeywordsFromSelector(
    "MDLSubmesh", b"initWithMDLSubmesh:indexType:geometryType:"
)
objc.registerNewKeywordsFromSelector(
    "MDLSubmesh",
    b"initWithName:indexBuffer:indexCount:indexType:geometryType:material:",
)
objc.registerNewKeywordsFromSelector(
    "MDLSubmesh",
    b"initWithName:indexBuffer:indexCount:indexType:geometryType:material:topology:",
)
objc.registerNewKeywordsFromSelector("MDLSubmeshTopology", b"initWithSubmesh:")
objc.registerNewKeywordsFromSelector(
    "MDLTexture",
    b"initWithData:topLeftOrigin:name:dimensions:rowStride:channelCount:channelEncoding:isCube:",
)
objc.registerNewKeywordsFromSelector("MDLTransform", b"initWithMatrix:")
objc.registerNewKeywordsFromSelector("MDLTransform", b"initWithMatrix:resetsTransform:")
objc.registerNewKeywordsFromSelector("MDLTransform", b"initWithTransformComponent:")
objc.registerNewKeywordsFromSelector(
    "MDLTransform", b"initWithTransformComponent:resetsTransform:"
)
objc.registerNewKeywordsFromSelector("MDLURLTexture", b"initWithURL:name:")
objc.registerNewKeywordsFromSelector(
    "MDLVertexAttribute", b"initWithName:format:offset:bufferIndex:"
)
objc.registerNewKeywordsFromSelector("MDLVertexBufferLayout", b"initWithStride:")
objc.registerNewKeywordsFromSelector(
    "MDLVertexDescriptor", b"initWithVertexDescriptor:"
)
objc.registerNewKeywordsFromSelector(
    "MDLVoxelArray",
    b"initWithAsset:divisions:interiorNBWidth:exteriorNBWidth:patchRadius:",
)
objc.registerNewKeywordsFromSelector(
    "MDLVoxelArray",
    b"initWithAsset:divisions:interiorShells:exteriorShells:patchRadius:",
)
objc.registerNewKeywordsFromSelector(
    "MDLVoxelArray", b"initWithAsset:divisions:patchRadius:"
)
objc.registerNewKeywordsFromSelector(
    "MDLVoxelArray", b"initWithData:boundingBox:voxelExtent:"
)
expressions = {}

# END OF FILE
