# This file is generated by objective.metadata
#
# Last update: Wed Jun 19 22:59:30 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$_nw_connection_send_idempotent_content@@?$_nw_content_context_default_message$_nw_content_context_default_stream$_nw_content_context_final_send$_nw_data_transfer_report_all_paths@I$_nw_parameters_configure_protocol_default_configuration@@?$_nw_parameters_configure_protocol_disable@@?$_nw_privacy_context_default_context$kNWErrorDomainDNS$kNWErrorDomainPOSIX$kNWErrorDomainTLS$"""
enums = """$NW_FRAMER_CREATE_FLAGS_DEFAULT@0$NW_FRAMER_WAKEUP_TIME_FOREVER@18446744073709551615$NW_LISTENER_INFINITE_CONNECTION_LIMIT@4294967295$NW_NOT_i386_MAC@1$NW_QUIC_CONNECTION_DEFAULT_KEEPALIVE@65535$nw_browse_result_change_identical@1$nw_browse_result_change_interface_added@8$nw_browse_result_change_interface_removed@16$nw_browse_result_change_invalid@0$nw_browse_result_change_result_added@2$nw_browse_result_change_result_removed@4$nw_browse_result_change_txt_record_changed@32$nw_browser_state_cancelled@3$nw_browser_state_failed@2$nw_browser_state_invalid@0$nw_browser_state_ready@1$nw_browser_state_waiting@4$nw_connection_group_state_cancelled@4$nw_connection_group_state_failed@3$nw_connection_group_state_invalid@0$nw_connection_group_state_ready@2$nw_connection_group_state_waiting@1$nw_connection_state_cancelled@5$nw_connection_state_failed@4$nw_connection_state_invalid@0$nw_connection_state_preparing@2$nw_connection_state_ready@3$nw_connection_state_waiting@1$nw_data_transfer_report_state_collected@2$nw_data_transfer_report_state_collecting@1$nw_endpoint_type_address@1$nw_endpoint_type_bonjour_service@3$nw_endpoint_type_host@2$nw_endpoint_type_invalid@0$nw_endpoint_type_url@4$nw_error_domain_dns@2$nw_error_domain_invalid@0$nw_error_domain_posix@1$nw_error_domain_tls@3$nw_ethernet_channel_state_cancelled@5$nw_ethernet_channel_state_failed@4$nw_ethernet_channel_state_invalid@0$nw_ethernet_channel_state_preparing@2$nw_ethernet_channel_state_ready@3$nw_ethernet_channel_state_waiting@1$nw_framer_start_result_ready@1$nw_framer_start_result_will_mark_ready@2$nw_interface_radio_type_cell_cdma@135$nw_interface_radio_type_cell_endc_mmw@130$nw_interface_radio_type_cell_endc_sub6@129$nw_interface_radio_type_cell_evdo@136$nw_interface_radio_type_cell_gsm@134$nw_interface_radio_type_cell_lte@128$nw_interface_radio_type_cell_nr_sa_mmw@132$nw_interface_radio_type_cell_nr_sa_sub6@131$nw_interface_radio_type_cell_wcdma@133$nw_interface_radio_type_unknown@0$nw_interface_radio_type_wifi_a@2$nw_interface_radio_type_wifi_ac@5$nw_interface_radio_type_wifi_ax@6$nw_interface_radio_type_wifi_b@1$nw_interface_radio_type_wifi_g@3$nw_interface_radio_type_wifi_n@4$nw_interface_type_cellular@2$nw_interface_type_loopback@4$nw_interface_type_other@0$nw_interface_type_wifi@1$nw_interface_type_wired@3$nw_ip_ecn_flag_ce@3$nw_ip_ecn_flag_ect_0@2$nw_ip_ecn_flag_ect_1@1$nw_ip_ecn_flag_non_ect@0$nw_ip_local_address_preference_default@0$nw_ip_local_address_preference_stable@2$nw_ip_local_address_preference_temporary@1$nw_ip_version_4@4$nw_ip_version_6@6$nw_ip_version_any@0$nw_listener_state_cancelled@4$nw_listener_state_failed@3$nw_listener_state_invalid@0$nw_listener_state_ready@2$nw_listener_state_waiting@1$nw_multipath_service_aggregate@3$nw_multipath_service_disabled@0$nw_multipath_service_handover@1$nw_multipath_service_interactive@2$nw_multipath_version_0@0$nw_multipath_version_1@1$nw_multipath_version_unspecified@-1$nw_parameters_attribution_developer@1$nw_parameters_attribution_user@2$nw_parameters_expired_dns_behavior_allow@1$nw_parameters_expired_dns_behavior_default@0$nw_parameters_expired_dns_behavior_persistent@3$nw_parameters_expired_dns_behavior_prohibit@2$nw_path_status_invalid@0$nw_path_status_satisfiable@3$nw_path_status_satisfied@1$nw_path_status_unsatisfied@2$nw_path_unsatisfied_reason_cellular_denied@1$nw_path_unsatisfied_reason_local_network_denied@3$nw_path_unsatisfied_reason_not_available@0$nw_path_unsatisfied_reason_vpn_inactive@4$nw_path_unsatisfied_reason_wifi_denied@2$nw_quic_stream_type_bidirectional@1$nw_quic_stream_type_datagram@3$nw_quic_stream_type_unidirectional@2$nw_quic_stream_type_unknown@0$nw_report_resolution_protocol_https@4$nw_report_resolution_protocol_tcp@2$nw_report_resolution_protocol_tls@3$nw_report_resolution_protocol_udp@1$nw_report_resolution_protocol_unknown@0$nw_report_resolution_source_cache@2$nw_report_resolution_source_expired_cache@3$nw_report_resolution_source_query@1$nw_service_class_background@1$nw_service_class_best_effort@0$nw_service_class_interactive_video@2$nw_service_class_interactive_voice@3$nw_service_class_responsive_data@4$nw_service_class_signaling@5$nw_txt_record_find_key_empty_value@3$nw_txt_record_find_key_invalid@0$nw_txt_record_find_key_no_value@2$nw_txt_record_find_key_non_empty_value@4$nw_txt_record_find_key_not_present@1$nw_ws_close_code_abnormal_closure@1006$nw_ws_close_code_going_away@1001$nw_ws_close_code_internal_server_error@1011$nw_ws_close_code_invalid_frame_payload_data@1007$nw_ws_close_code_mandatory_extension@1010$nw_ws_close_code_message_too_big@1009$nw_ws_close_code_no_status_received@1005$nw_ws_close_code_normal_closure@1000$nw_ws_close_code_policy_violation@1008$nw_ws_close_code_protocol_error@1002$nw_ws_close_code_tls_handshake@1015$nw_ws_close_code_unsupported_data@1003$nw_ws_opcode_binary@2$nw_ws_opcode_close@8$nw_ws_opcode_cont@0$nw_ws_opcode_invalid@-1$nw_ws_opcode_ping@9$nw_ws_opcode_pong@10$nw_ws_opcode_text@1$nw_ws_response_status_accept@1$nw_ws_response_status_invalid@0$nw_ws_response_status_reject@2$nw_ws_version_13@1$nw_ws_version_invalid@0$"""
misc.update(
    {
        "nw_report_resolution_protocol_t": NewType(
            "nw_report_resolution_protocol_t", int
        ),
        "nw_ethernet_channel_state_t": NewType("nw_ethernet_channel_state_t", int),
        "nw_path_status_t": NewType("nw_path_status_t", int),
        "nw_multipath_version_t": NewType("nw_multipath_version_t", int),
        "nw_connection_group_state_t": NewType("nw_connection_group_state_t", int),
        "nw_multipath_service_t": NewType("nw_multipath_service_t", int),
        "nw_ws_version_t": NewType("nw_ws_version_t", int),
        "nw_service_class_t": NewType("nw_service_class_t", int),
        "nw_parameters_attribution_t": NewType("nw_parameters_attribution_t", int),
        "nw_quic_stream_type_t": NewType("nw_quic_stream_type_t", int),
        "nw_ws_response_status_t": NewType("nw_ws_response_status_t", int),
        "nw_txt_record_find_key_t": NewType("nw_txt_record_find_key_t", int),
        "nw_listener_state_t": NewType("nw_listener_state_t", int),
        "nw_ws_opcode_t": NewType("nw_ws_opcode_t", int),
        "nw_data_transfer_report_state_t": NewType(
            "nw_data_transfer_report_state_t", int
        ),
        "nw_error_domain_t": NewType("nw_error_domain_t", int),
        "nw_interface_radio_type_t": NewType("nw_interface_radio_type_t", int),
        "nw_ws_close_code_t": NewType("nw_ws_close_code_t", int),
        "nw_ip_local_address_preference_t": NewType(
            "nw_ip_local_address_preference_t", int
        ),
        "nw_interface_type_t": NewType("nw_interface_type_t", int),
        "nw_ip_version_t": NewType("nw_ip_version_t", int),
        "nw_path_unsatisfied_reason_t": NewType("nw_path_unsatisfied_reason_t", int),
        "nw_connection_state_t": NewType("nw_connection_state_t", int),
        "nw_browser_state_t": NewType("nw_browser_state_t", int),
        "nw_ip_ecn_flag_t": NewType("nw_ip_ecn_flag_t", int),
        "nw_report_resolution_source_t": NewType("nw_report_resolution_source_t", int),
        "nw_endpoint_type_t": NewType("nw_endpoint_type_t", int),
        "nw_framer_start_result_t": NewType("nw_framer_start_result_t", int),
        "nw_parameters_expired_dns_behavior_t": NewType(
            "nw_parameters_expired_dns_behavior_t", int
        ),
    }
)
misc.update({})
misc.update({})
functions = {
    "nw_txt_record_copy": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_framer_set_stop_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_establishment_report_copy_proxy_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_listener_set_state_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "i"},
                            2: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_listener_create": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_connection_group_copy_remote_endpoint_for_message": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_parameters_set_local_only": (b"v@B",),
    "nw_parameters_create_custom_ip": (
        b"@C@?",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_protocol_options_copy_definition": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_browser_set_queue": (b"v@@",),
    "nw_connection_set_queue": (b"v@@",),
    "nw_protocol_metadata_is_quic": (b"B@",),
    "nw_path_is_constrained": (b"B@",),
    "nw_interface_get_name": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_protocol_metadata_copy_definition": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_txt_record_apply": (
        b"B@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"c_array_delimited_by_null": True, "type": "n^t"},
                            2: {"type": "@"},
                            3: {"type": "n^v", "c_array_length_in_arg": 4},
                            4: {"type": "L"},
                        },
                    }
                }
            }
        },
    ),
    "nw_content_context_set_relative_priority": (b"v@d",),
    "nw_parameters_clear_prohibited_interfaces": (b"v@",),
    "nw_connection_start": (b"v@",),
    "nw_listener_set_new_connection_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_endpoint_get_hostname": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_parameters_create_secure_tcp": (
        b"@@?@?",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                },
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                },
            },
        },
    ),
    "nw_quic_set_max_udp_payload_size": (b"v@S",),
    "nw_quic_get_initial_max_data": (b"Q@",),
    "nw_content_context_set_expiration_milliseconds": (b"v@Q",),
    "nw_proxy_config_clear_match_domains": (b"v@",),
    "nw_framer_message_set_object_value": (
        b"v@^t@",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "nw_connection_group_cancel": (b"v@",),
    "nw_path_is_equal": (b"B@@",),
    "nw_connection_force_cancel": (b"v@",),
    "nw_listener_create_with_launchd_key": (
        b"@@^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_ip_metadata_get_service_class": (b"I@",),
    "nw_parameters_create": (b"@", "", {"retval": {"already_retained": True}}),
    "nw_tcp_options_set_disable_ack_stretching": (b"v@B",),
    "nw_framer_message_set_value": (
        b"v@^t^v@?",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "^v"}},
                    }
                },
            }
        },
    ),
    "nw_quic_get_stream_is_unidirectional": (b"B@",),
    "nw_resolution_report_copy_successful_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_content_context_foreach_protocol_metadata": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_content_context_set_is_final": (b"v@B",),
    "nw_tls_copy_sec_protocol_options": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_quic_get_local_max_streams_unidirectional": (b"Q@",),
    "nw_quic_set_initial_max_data": (b"v@Q",),
    "nw_advertise_descriptor_get_no_auto_rename": (b"B@",),
    "nw_connection_group_copy_protocol_metadata": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_protocol_metadata_is_ip": (b"B@",),
    "nw_endpoint_get_type": (b"I@",),
    "nw_path_enumerate_gateways": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_protocol_stack_clear_application_protocols": (b"v@",),
    "nw_udp_create_metadata": (b"@", "", {"retval": {"already_retained": True}}),
    "nw_advertise_descriptor_get_application_service_name": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_quic_get_initial_max_stream_data_bidirectional_local": (b"Q@",),
    "nw_parameters_set_attribution": (b"v@C",),
    "nw_resolver_config_add_server_address": (b"v@@",),
    "nw_advertise_descriptor_set_txt_record": (
        b"v@^vQ",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "nw_tcp_options_set_no_push": (b"v@B",),
    "nw_connection_copy_protocol_metadata": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_ws_create_metadata": (b"@i", "", {"retval": {"already_retained": True}}),
    "nw_connection_get_maximum_datagram_size": (b"I@",),
    "nw_resolution_report_get_endpoint_count": (b"I@",),
    "nw_quic_copy_sec_protocol_metadata": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_connection_group_copy_protocol_metadata_for_message": (
        b"@@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_connection_receive_message": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                            3: {"type": "B"},
                            4: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_ip_options_set_local_address_preference": (b"v@I",),
    "nw_endpoint_copy_address_string": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_listener_set_new_connection_group_handler": (b"v@@?",),
    "nw_tcp_options_set_connection_timeout": (b"v@I",),
    "nw_connection_set_viability_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "B"}},
                    }
                }
            }
        },
    ),
    "nw_ethernet_channel_create_with_parameters": (
        b"@S@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_connection_group_set_queue": (b"v@@",),
    "nw_ip_options_set_disable_fragmentation": (b"v@B",),
    "nw_error_get_error_domain": (b"I@",),
    "nw_framer_options_copy_object_value": (
        b"@@^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_framer_mark_ready": (b"v@",),
    "nw_ws_request_enumerate_additional_headers": (
        b"B@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"c_array_delimited_by_null": True, "type": "n^t"},
                            2: {"c_array_delimited_by_null": True, "type": "n^t"},
                        },
                    }
                }
            }
        },
    ),
    "nw_parameters_get_fast_open_enabled": (b"B@",),
    "nw_parameters_copy_required_interface": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_establishment_report_get_duration_milliseconds": (b"Q@",),
    "nw_browse_descriptor_set_include_txt_record": (b"v@B",),
    "nw_protocol_stack_iterate_application_protocols": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_ip_options_set_hop_limit": (b"v@C",),
    "nw_establishment_report_enumerate_resolution_reports": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_connection_set_state_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "i"},
                            2: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_browser_set_state_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_privacy_context_add_proxy": (b"v@@",),
    "nw_ethernet_channel_cancel": (b"v@",),
    "nw_quic_set_initial_max_stream_data_unidirectional": (b"v@Q",),
    "nw_path_monitor_start": (b"v@",),
    "nw_parameters_clear_prohibited_interface_types": (b"v@",),
    "nw_connection_copy_description": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}, "free_result": True},
    ),
    "nw_parameters_create_quic": (b"@@?", "", {"retval": {"already_retained": True}}),
    "nw_listener_set_queue": (b"v@@",),
    "nw_tcp_create_options": (b"@", "", {"retval": {"already_retained": True}}),
    "nw_proxy_config_clear_excluded_domains": (b"v@",),
    "nw_ip_options_set_version": (b"v@I",),
    "nw_establishment_report_get_proxy_configured": (b"B@",),
    "nw_connection_group_copy_descriptor": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_parameters_iterate_prohibited_interface_types": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "i"}},
                    }
                }
            }
        },
    ),
    "nw_path_has_ipv6": (b"B@",),
    "nw_path_has_ipv4": (b"B@",),
    "nw_connection_cancel_current_endpoint": (b"v@",),
    "nw_quic_get_stream_is_datagram": (b"B@",),
    "nw_framer_message_access_value": (
        b"B@^t@?",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"^v"}},
                    }
                },
            }
        },
    ),
    "nw_framer_copy_remote_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_quic_get_stream_id": (b"Q@",),
    "nw_listener_get_new_connection_limit": (b"I@",),
    "nw_browse_descriptor_get_bonjour_service_type": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_endpoint_copy_txt_record": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_endpoint_get_port": (b"S@",),
    "nw_parameters_get_multipath_service": (b"I@",),
    "nw_parameters_set_local_endpoint": (b"v@@",),
    "nw_ethernet_channel_set_state_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_parameters_get_service_class": (b"I@",),
    "nw_privacy_context_create": (
        b"@^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_framer_create_definition": (
        b"@^tI@?",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {
                0: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                },
            },
        },
    ),
    "nw_ws_options_set_auto_reply_ping": (b"v@B",),
    "nw_protocol_copy_tls_definition": (
        b"@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_proxy_config_create_relay": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_path_monitor_set_update_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_multicast_group_descriptor_get_disable_unicast_traffic": (b"B@",),
    "nw_establishment_report_enumerate_resolutions": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "Q"},
                            3: {"type": "I"},
                            4: {"type": "@"},
                            5: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_framer_copy_parameters": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_txt_record_is_dictionary": (b"B@",),
    "nw_ethernet_channel_set_queue": (b"v@@",),
    "nw_quic_get_local_max_streams_bidirectional": (b"Q@",),
    "nw_parameters_get_attribution": (b"C@",),
    "nw_ip_options_set_disable_multicast_loopback": (b"v@B",),
    "nw_interface_get_type": (b"I@",),
    "nw_endpoint_create_url": (
        b"@^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_endpoint_copy_port_string": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_quic_get_stream_type": (b"C@",),
    "nw_parameters_set_requires_dnssec_validation": (b"v@B",),
    "nw_path_monitor_set_queue": (b"v@@",),
    "nw_ethernet_channel_get_maximum_payload_size": (b"I@",),
    "nw_protocol_stack_copy_internet_protocol": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_privacy_context_disable_logging": (b"v@",),
    "nw_framer_message_create": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_connection_group_copy_parameters": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_path_monitor_set_cancel_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}},
                    }
                }
            }
        },
    ),
    "nw_quic_set_initial_max_stream_data_bidirectional_remote": (b"v@Q",),
    "nw_quic_set_keepalive_interval": (b"v@S",),
    "nw_establishment_report_get_attempt_started_after_milliseconds": (b"Q@",),
    "nw_advertise_descriptor_set_txt_record_object": (b"v@@",),
    "nw_connection_group_set_receive_handler": (
        b"v@IB@?",
        "",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                            3: {"type": "B"},
                        },
                    }
                }
            }
        },
    ),
    "nw_protocol_metadata_is_tls": (b"B@",),
    "nw_protocol_stack_set_transport_protocol": (b"v@@",),
    "nw_quic_get_stream_application_error": (b"Q@",),
    "nw_framer_write_output_no_copy": (b"B@Q",),
    "nw_connection_create": (b"@@@", "", {"retval": {"already_retained": True}}),
    "nw_browser_create": (b"@@@", "", {"retval": {"already_retained": True}}),
    "nw_ip_metadata_get_ecn_flag": (b"I@",),
    "nw_protocol_options_is_quic": (b"B@",),
    "nw_connection_set_better_path_available_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "B"}},
                    }
                }
            }
        },
    ),
    "nw_tcp_options_set_disable_ecn": (b"v@B",),
    "nw_data_transfer_report_get_path_count": (b"I@",),
    "nw_proxy_config_add_match_domain": (
        b"v@^t",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "nw_framer_write_output": (
        b"v@n^vQ",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2}}},
    ),
    "nw_relay_hop_add_additional_http_header_field": (
        b"v@^t^t",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"c_array_delimited_by_null": True, "type_modifier": "n"},
            }
        },
    ),
    "nw_endpoint_create_bonjour_service": (
        b"@^t^t^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {
                0: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"c_array_delimited_by_null": True, "type_modifier": "n"},
            },
        },
    ),
    "nw_browse_result_get_interfaces_count": (b"Q@",),
    "nw_parameters_prohibit_interface_type": (b"v@I",),
    "nw_parameters_copy_local_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_connection_group_start": (b"v@",),
    "nw_ethernet_channel_set_receive_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "S"},
                            3: {"type": "n[6t]"},
                            4: {"type": "n[6t]"},
                        },
                    }
                }
            }
        },
    ),
    "nw_parameters_set_prefer_no_proxy": (b"v@B",),
    "nw_udp_options_set_prefer_no_checksum": (b"v@B",),
    "nw_privacy_context_flush_cache": (b"v@",),
    "nw_establishment_report_get_used_proxy": (b"B@",),
    "nw_content_context_get_is_final": (b"B@",),
    "nw_quic_create_options": (b"@", "", {"retval": {"already_retained": True}}),
    "nw_tcp_options_set_no_options": (b"v@B",),
    "nw_resolution_report_get_source": (b"I@",),
    "nw_data_transfer_report_get_sent_transport_byte_count": (b"Q@I",),
    "nw_connection_access_establishment_report": (
        b"v@@@?",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_proxy_config_enumerate_match_domains": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"c_array_delimited_by_null": True, "type": "n^t"},
                        },
                    }
                }
            }
        },
    ),
    "nw_parameters_set_required_interface_type": (b"v@I",),
    "nw_content_context_set_antecedent": (b"v@@",),
    "nw_parameters_requires_dnssec_validation": (b"B@",),
    "nw_ws_response_get_selected_subprotocol": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_parameters_get_local_only": (b"B@",),
    "nw_framer_options_set_object_value": (
        b"v@^t@",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "nw_proxy_config_create_oblivious_http": (
        b"@@^t^vQ",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
            },
        },
    ),
    "nw_listener_start": (b"v@",),
    "nw_resolution_report_copy_preferred_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_resolver_config_create_https": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_txt_record_create_dictionary": (
        b"@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_ws_create_options": (b"@I", "", {"retval": {"already_retained": True}}),
    "nw_parameters_create_application_service": (
        b"@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_framer_mark_failed_with_error": (b"v@i",),
    "nw_tcp_options_set_keepalive_idle_time": (b"v@I",),
    "nw_error_get_error_code": (b"i@",),
    "nw_quic_set_max_datagram_frame_size": (b"v@S",),
    "nw_quic_add_tls_application_protocol": (
        b"v@^t",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "nw_ip_metadata_set_service_class": (b"v@I",),
    "nw_parameters_iterate_prohibited_interfaces": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_parameters_set_reuse_local_address": (b"v@B",),
    "nw_quic_set_initial_max_streams_unidirectional": (b"v@Q",),
    "nw_ethernet_channel_create": (b"@S@", "", {"retval": {"already_retained": True}}),
    "nw_txt_record_is_equal": (b"B@@",),
    "nw_framer_prepend_application_protocol": (b"B@@",),
    "nw_protocol_copy_quic_definition": (
        b"@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_ws_request_enumerate_subprotocols": (
        b"B@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"c_array_delimited_by_null": True, "type": "n^t"},
                        },
                    }
                }
            }
        },
    ),
    "nw_tls_create_options": (b"@",),
    "nw_advertise_descriptor_copy_txt_record_object": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_txt_record_remove_key": (
        b"B@n^t",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True}}},
    ),
    "nw_data_transfer_report_collect": (
        b"v@@@?",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_content_context_get_relative_priority": (b"d@",),
    "nw_txt_record_access_bytes": (
        b"B@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "n^v", "c_array_length_in_arg": 2},
                            2: {"type": "L"},
                        },
                    }
                }
            }
        },
    ),
    "nw_browse_descriptor_get_include_txt_record": (b"B@",),
    "nw_path_has_dns": (b"B@",),
    "nw_quic_get_remote_max_streams_unidirectional": (b"Q@",),
    "nw_quic_set_idle_timeout": (b"v@I",),
    "nw_proxy_config_set_failover_allowed": (b"v@B",),
    "nw_browser_start": (b"v@",),
    "nw_browse_descriptor_create_bonjour_service": (
        b"@^t^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {
                0: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
            },
        },
    ),
    "nw_framer_set_output_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                            3: {"type": "L"},
                            4: {"type": "B"},
                        },
                    }
                }
            }
        },
    ),
    "nw_quic_set_stream_application_error": (b"v@Q",),
    "nw_parameters_set_multipath_service": (b"v@I",),
    "nw_framer_pass_through_output": (b"v@",),
    "nw_udp_create_options": (b"@", "", {"retval": {"already_retained": True}}),
    "nw_ws_metadata_copy_server_response": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_listener_set_new_connection_limit": (b"v@I",),
    "nw_ws_response_add_additional_header": (
        b"v@n^tn^t",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True},
                2: {"c_array_delimited_by_null": True},
            }
        },
    ),
    "nw_framer_set_input_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"L"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_txt_record_get_key_count": (b"Q@",),
    "nw_quic_set_local_max_streams_bidirectional": (b"v@Q",),
    "nw_protocol_copy_udp_definition": (
        b"@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_ws_metadata_set_close_code": (b"v@I",),
    "nw_quic_get_initial_max_stream_data_unidirectional": (b"Q@",),
    "nw_quic_get_initial_max_streams_unidirectional": (b"Q@",),
    "nw_ip_metadata_get_receive_time": (b"Q@",),
    "nw_ws_options_add_subprotocol": (
        b"v@^t",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "nw_txt_record_create_with_bytes": (
        b"@n^vQ",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {0: {"c_array_length_in_arg": 1}},
        },
    ),
    "nw_tcp_options_set_keepalive_count": (b"v@I",),
    "nw_tcp_options_set_multipath_force_version": (b"v@i",),
    "nw_endpoint_get_address": (
        b"^{sockaddr=CC[14c]}@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_quic_get_max_udp_payload_size": (b"S@",),
    "nw_tcp_options_set_retransmit_connection_drop_time": (b"v@I",),
    "nw_data_transfer_report_get_sent_transport_retransmitted_byte_count": (b"Q@I",),
    "nw_content_context_copy_protocol_metadata": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_data_transfer_report_get_received_transport_out_of_order_byte_count": (b"Q@I",),
    "nw_browse_result_enumerate_interfaces": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_protocol_metadata_is_framer_message": (b"B@",),
    "nw_endpoint_create_address": (
        b"@^{sockaddr=CC[14c]}",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {0: {"type_modifier": "n"}},
        },
    ),
    "nw_quic_get_initial_max_stream_data_bidirectional_remote": (b"Q@",),
    "nw_advertise_descriptor_create_application_service": (
        b"@^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_quic_get_keepalive_interval": (b"S@",),
    "nw_privacy_context_clear_proxies": (b"v@",),
    "nw_connection_batch": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}},
                    }
                }
            }
        },
    ),
    "nw_parameters_get_include_peer_to_peer": (b"B@",),
    "nw_framer_parse_input": (
        b"B@QQ^C@?",
        "",
        {
            "arguments": {
                3: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                4: {
                    "callable": {
                        "retval": {"type": b"L"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "n^v", "c_array_length_in_arg": 2},
                            2: {"type": "L"},
                            3: {"type": "B"},
                        },
                    }
                },
            }
        },
    ),
    "nw_framer_copy_options": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_data_transfer_report_get_state": (b"I@",),
    "nw_quic_get_stream_usable_datagram_frame_size": (b"S@",),
    "nw_ws_metadata_get_opcode": (b"i@",),
    "nw_framer_schedule_wakeup": (b"v@Q",),
    "nw_data_transfer_report_get_received_transport_byte_count": (b"Q@I",),
    "nw_endpoint_get_bonjour_service_domain": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_resolver_config_create_tls": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_path_is_expensive": (b"B@",),
    "nw_quic_get_idle_timeout": (b"I@",),
    "nw_group_descriptor_enumerate_endpoints": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_ws_metadata_set_pong_handler": (
        b"v@@@?",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_path_monitor_prohibit_interface_type": (b"v@I",),
    "nw_endpoint_get_bonjour_service_type": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_connection_copy_endpoint": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_content_context_copy_antecedent": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_tcp_options_set_maximum_segment_size": (b"v@I",),
    "nw_framer_pass_through_input": (b"v@",),
    "nw_browser_copy_browse_descriptor": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_data_transfer_report_get_duration_milliseconds": (b"Q@",),
    "nw_protocol_metadata_is_tcp": (b"B@",),
    "nw_endpoint_get_bonjour_service_name": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_parameters_get_expired_dns_behavior": (b"I@",),
    "nw_framer_async": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}},
                    }
                }
            }
        },
    ),
    "nw_connection_group_set_new_connection_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_connection_copy_current_path": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_path_copy_effective_remote_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_browse_descriptor_get_bonjour_service_domain": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_quic_get_remote_max_streams_bidirectional": (b"Q@",),
    "nw_connection_copy_parameters": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_quic_set_initial_max_stream_data_bidirectional_local": (b"v@Q",),
    "nw_browser_copy_parameters": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_quic_set_application_error": (
        b"v@Q^t",
        "",
        {"arguments": {2: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "nw_quic_get_max_datagram_frame_size": (b"S@",),
    "nw_txt_record_set_key": (
        b"B@n^vn^vQ",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True},
                2: {"c_array_length_in_arg": 3},
            }
        },
    ),
    "nw_ws_response_get_status": (b"I@",),
    "nw_content_context_get_expiration_milliseconds": (b"Q@",),
    "nw_group_descriptor_add_endpoint": (b"B@@",),
    "nw_resolution_report_get_protocol": (
        b"I@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_quic_set_initial_max_streams_bidirectional": (b"v@Q",),
    "nw_group_descriptor_create_multicast": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_framer_set_wakeup_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_listener_set_advertise_descriptor": (b"v@@",),
    "nw_relay_hop_create": (b"@@@@", "", {"retval": {"already_retained": True}}),
    "nw_parameters_prohibit_interface": (b"v@@",),
    "nw_quic_get_initial_max_streams_bidirectional": (b"Q@",),
    "nw_establishment_report_get_previous_attempt_count": (b"I@",),
    "nw_proxy_config_get_failover_allowed": (b"B@",),
    "nw_establishment_report_enumerate_protocols": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "Q"},
                            3: {"type": "Q"},
                        },
                    }
                }
            }
        },
    ),
    "nw_quic_get_application_error_reason": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_endpoint_get_url": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_data_transfer_report_get_sent_ip_packet_count": (b"Q@I",),
    "nw_tcp_get_available_send_buffer": (b"I@",),
    "nw_protocol_stack_copy_transport_protocol": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_data_transfer_report_copy_path_interface": (
        b"@@I",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_protocol_copy_tcp_definition": (
        b"@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_data_transfer_report_get_sent_application_byte_count": (b"Q@I",),
    "nw_connection_group_extract_connection_for_message": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_path_copy_effective_local_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_parameters_get_required_interface_type": (b"I@",),
    "nw_ws_options_set_skip_handshake": (b"v@B",),
    "nw_ws_response_enumerate_additional_headers": (
        b"B@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"c_array_delimited_by_null": True, "type": "n^t"},
                            2: {"c_array_delimited_by_null": True, "type": "n^t"},
                        },
                    }
                }
            }
        },
    ),
    "nw_protocol_stack_prepend_application_protocol": (b"v@@",),
    "nw_tcp_options_set_retransmit_fin_drop": (b"v@B",),
    "nw_listener_get_port": (b"S@",),
    "nw_proxy_config_set_username_and_password": (
        b"v@^t^t",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"c_array_delimited_by_null": True, "type_modifier": "n"},
            }
        },
    ),
    "nw_group_descriptor_create_multiplex": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_browse_descriptor_create_application_service": (
        b"@^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_endpoint_get_signature": (
        b"^t@^Q",
        "",
        {
            "retval": {"c_array_length_in_arg": 1},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "nw_ws_response_create": (
        b"@I^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "nw_connection_set_path_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_protocol_metadata_is_udp": (b"B@",),
    "nw_content_context_get_identifier": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_proxy_config_create_http_connect": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_browser_cancel": (b"v@",),
    "nw_connection_cancel": (b"v@",),
    "nw_ip_options_set_calculate_receive_time": (b"v@B",),
    "nw_interface_get_index": (b"I@",),
    "nw_data_transfer_report_get_received_application_byte_count": (b"Q@I",),
    "nw_framer_deliver_input_no_copy": (b"B@Q@B",),
    "nw_browse_result_copy_txt_record_object": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_connection_restart": (b"v@",),
    "nw_ip_metadata_set_ecn_flag": (b"v@I",),
    "nw_advertise_descriptor_create_bonjour_service": (
        b"@^t^t^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {
                0: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"c_array_delimited_by_null": True, "type_modifier": "n"},
            },
        },
    ),
    "nw_connection_group_create": (b"@@@", "", {"retval": {"already_retained": True}}),
    "nw_parameters_set_expired_dns_behavior": (b"v@I",),
    "nw_parameters_get_reuse_local_address": (b"B@",),
    "nw_connection_create_new_data_transfer_report": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_connection_group_set_state_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "i"},
                            2: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_connection_group_reply": (b"v@@@@",),
    "nw_privacy_context_require_encrypted_name_resolution": (b"v@B@",),
    "nw_parameters_set_include_peer_to_peer": (b"v@B",),
    "nw_framer_set_cleanup_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_content_context_create": (
        b"@^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_browse_descriptor_get_application_service_name": (
        b"^t@",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "nw_ws_options_set_client_request_handler": (
        b"v@@@?",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_advertise_descriptor_set_no_auto_rename": (b"v@B",),
    "nw_data_transfer_report_get_path_radio_type": (b"I@I",),
    "nw_quic_set_stream_is_datagram": (b"v@B",),
    "nw_ip_options_set_use_minimum_mtu": (b"v@B",),
    "nw_ws_options_set_maximum_message_size": (b"v@Q",),
    "nw_error_copy_cf_error": (
        b"^{__CFError=}@",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "nw_tcp_options_set_persist_timeout": (b"v@I",),
    "nw_parameters_require_interface": (b"v@@",),
    "nw_ip_create_metadata": (b"@", "", {"retval": {"already_retained": True}}),
    "nw_tls_copy_sec_protocol_metadata": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_listener_create_with_connection": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_path_monitor_create_with_type": (
        b"@I",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_framer_copy_local_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_parameters_set_service_class": (b"v@I",),
    "nw_connection_group_copy_path_for_message": (b"@@@",),
    "nw_parameters_set_privacy_context": (b"v@@",),
    "nw_framer_parse_output": (
        b"B@QQ^C@?",
        "",
        {
            "arguments": {
                3: {"c_array_length_in_arg": 2, "type_modifier": "o"},
                4: {
                    "callable": {
                        "retval": {"type": b"L"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "n^v", "c_array_length_in_arg": 2},
                            2: {"type": "L"},
                            3: {"type": "B"},
                        },
                    }
                },
            }
        },
    ),
    "nw_framer_message_copy_object_value": (
        b"@@^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_data_transfer_report_get_received_transport_duplicate_byte_count": (b"Q@I",),
    "nw_txt_record_access_key": (
        b"B@^t@?",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"c_array_delimited_by_null": True, "type": "n^t"},
                            2: {"type": "@"},
                            3: {"type": "n^v", "c_array_length_in_arg": 4},
                            4: {"type": "L"},
                        },
                    }
                },
            }
        },
    ),
    "nw_tcp_get_available_receive_buffer": (b"I@",),
    "nw_tcp_options_set_no_delay": (b"v@B",),
    "nw_path_monitor_create_for_ethernet_channel": (
        b"@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_path_enumerate_interfaces": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_connection_group_extract_connection": (
        b"@@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_browser_set_browse_results_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                            3: {"type": "B"},
                        },
                    }
                }
            }
        },
    ),
    "nw_protocol_definition_is_equal": (b"B@@",),
    "nw_parameters_set_prohibit_expensive": (b"v@B",),
    "nw_parameters_set_fast_open_enabled": (b"v@B",),
    "nw_ethernet_channel_send": (
        b"v@@S^C@?",
        "",
        {
            "arguments": {
                3: {"c_array_of_fixed_size": 6, "type_modifier": "n"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                },
            }
        },
    ),
    "nw_data_transfer_report_get_transport_smoothed_rtt_milliseconds": (b"Q@I",),
    "nw_ws_metadata_get_close_code": (b"I@",),
    "nw_tcp_options_set_keepalive_interval": (b"v@I",),
    "nw_path_get_status": (b"I@",),
    "nw_txt_record_find_key": (
        b"I@n^t",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True}}},
    ),
    "nw_resolution_report_get_milliseconds": (b"Q@",),
    "nw_ws_options_add_additional_header": (
        b"v@^t^t",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"c_array_delimited_by_null": True, "type_modifier": "n"},
            }
        },
    ),
    "nw_path_get_unsatisfied_reason": (b"I@",),
    "nw_browse_result_copy_endpoint": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_parameters_get_prohibit_constrained": (b"B@",),
    "nw_endpoint_create_host": (
        b"@^t^t",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {
                0: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
            },
        },
    ),
    "nw_framer_write_output_data": (b"v@@",),
    "nw_connection_group_send_message": (
        b"v@@@@@?",
        "",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_parameters_set_prohibit_constrained": (b"v@B",),
    "nw_connection_group_reinsert_extracted_connection": (b"B@@",),
    "nw_parameters_copy_default_protocol_stack": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_quic_copy_sec_protocol_options": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_path_monitor_cancel": (b"v@",),
    "nw_proxy_config_create_socksv5": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_listener_set_advertised_endpoint_changed_handler": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "B"},
                        },
                    }
                }
            }
        },
    ),
    "nw_quic_get_application_error": (b"Q@",),
    "nw_parameters_get_prohibit_expensive": (b"B@",),
    "nw_content_context_set_metadata_for_protocol": (b"v@@",),
    "nw_framer_create_options": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_parameters_get_prefer_no_proxy": (b"B@",),
    "nw_protocol_metadata_is_ws": (b"B@",),
    "nw_tcp_options_set_enable_keepalive": (b"v@B",),
    "nw_tcp_options_set_enable_fast_open": (b"v@B",),
    "nw_framer_protocol_create_message": (
        b"@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_ethernet_channel_start": (b"v@",),
    "nw_parameters_copy": (b"@@", "", {"retval": {"already_retained": True}}),
    "nw_listener_cancel": (b"v@",),
    "nw_quic_get_remote_idle_timeout": (b"Q@",),
    "nw_browse_result_get_changes": (b"Q@@",),
    "nw_connection_send": (
        b"v@@@B@?",
        "",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                }
            }
        },
    ),
    "nw_multicast_group_descriptor_set_disable_unicast_traffic": (b"v@B",),
    "nw_proxy_config_enumerate_excluded_domains": (
        b"v@@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"c_array_delimited_by_null": True, "type": "n^t"},
                        },
                    }
                }
            }
        },
    ),
    "nw_path_uses_interface_type": (b"B@I",),
    "nw_data_transfer_report_get_transport_rtt_variance": (b"Q@I",),
    "nw_quic_set_local_max_streams_unidirectional": (b"v@Q",),
    "nw_connection_receive": (
        b"v@II@?",
        "",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                            3: {"type": "B"},
                            4: {"type": "@"},
                        },
                    }
                }
            }
        },
    ),
    "nw_proxy_config_add_excluded_domain": (
        b"v@^t",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "nw_protocol_copy_ip_definition": (b"@",),
    "nw_multicast_group_descriptor_set_specific_source": (b"v@@",),
    "nw_connection_group_copy_local_endpoint_for_message": (
        b"@@@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_quic_set_stream_is_unidirectional": (b"v@B",),
    "nw_data_transfer_report_get_received_ip_packet_count": (b"Q@I",),
    "nw_parameters_create_secure_udp": (
        b"@@?@?",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                },
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": "^v"}, 1: {"type": "@"}},
                    }
                },
            },
        },
    ),
    "nw_path_monitor_create": (b"@", "", {"retval": {"already_retained": True}}),
    "nw_listener_create_with_port": (
        b"@^t@",
        "",
        {
            "retval": {"already_retained": True},
            "arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "nw_protocol_copy_ws_definition": (
        b"@",
        "",
        {"retval": {"already_retained": True}},
    ),
    "nw_data_transfer_report_get_transport_minimum_rtt_milliseconds": (b"Q@I",),
    "nw_framer_deliver_input": (
        b"v@n^vQ@B",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2}}},
    ),
}
aliases = {
    "NW_RETURNS_RETAINED": "OS_OBJECT_RETURNS_RETAINED",
    "NW_QUIC_CONNECTION_DEFAULT_KEEPALIVE": "UINT16_MAX",
    "NW_LISTENER_INFINITE_CONNECTION_LIMIT": "UINT32_MAX",
    "NW_EXPORT_PROJECT": "NW_EXPORT",
    "NW_ALL_PATHS": "_nw_data_transfer_report_all_paths",
    "NW_NONNULL_ARRAY": "_Nonnull",
    "NW_UNSAFE_UNRETAINED": "__unsafe_unretained",
}
expressions = {}

# END OF FILE
