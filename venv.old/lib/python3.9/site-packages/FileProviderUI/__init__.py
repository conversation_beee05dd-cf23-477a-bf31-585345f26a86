"""
Python mapping for the FileProviderUI framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import FileProvider
    import objc
    from . import _metadata

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="FileProviderUI",
        frameworkIdentifier="com.apple.FileProviderUI",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/FileProviderUI.framework"
        ),
        globals_dict=globals(),
        inline_list=None,
        parents=(FileProvider,),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    for cls, sel in (
        (
            "FPUIActionExtensionContext",
            b"completeRequestReturningItems:completionHandler:",
        ),
    ):
        objc.registerUnavailableMethod(cls, sel)

    del sys.modules["FileProviderUI._metadata"]


globals().pop("_setup")()
