# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 12:11:56 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "MTLIndirectCommandBufferExecutionRange": objc.createStructType(
            "Metal.MTLIndirectCommandBufferExecutionRange",
            b"{MTLIndirectCommandBufferExecutionRange=II}",
            ["location", "length"],
        ),
        "MTLAccelerationStructureSizes": objc.createStructType(
            "Metal.MTLAccelerationStructureSizes",
            b"{MTLAccelerationStructureSizes=QQQ}",
            [
                "accelerationStructureSize",
                "buildScratchBufferSize",
                "refitScratchBufferSize",
            ],
        ),
        "MTLTextureSwizzleChannels": objc.createStructType(
            "Metal.MTLTextureSwizzleChannels",
            b"{MTLTextureSwizzleChannels=CCCC}",
            ["red", "green", "blue", "alpha"],
        ),
        "MTLPackedFloat3": objc.createStructType(
            "Metal.MTLPackedFloat3", b"{_MTLPackedFloat3=[3f]}", ["elements"]
        ),
        "MTLQuadTessellationFactorsHalf": objc.createStructType(
            "Metal.MTLQuadTessellationFactorsHalf",
            b"{MTLQuadTessellationFactorsHalf=[4S][2S]}",
            ["edgeTessellationFactor", "insideTessellationFactor"],
        ),
        "MTLSize": objc.createStructType(
            "Metal.MTLSize", b"{MTLSize=QQQ}", ["width", "height", "depth"]
        ),
        "MTLDrawPrimitivesIndirectArguments": objc.createStructType(
            "Metal.MTLDrawPrimitivesIndirectArguments",
            b"{MTLDrawPrimitivesIndirectArguments=IIII}",
            ["vertexCount", "instanceCount", "vertexStart", "baseInstance"],
        ),
        "MTLAccelerationStructureUserIDInstanceDescriptor": objc.createStructType(
            "Metal.MTLAccelerationStructureUserIDInstanceDescriptor",
            b"{MTLAccelerationStructureUserIDInstanceDescriptor={_MTLPackedFloat4x3=[4{_MTLPackedFloat3=[3f]}]}IIIII}",
            [
                "transformationMatrix",
                "options",
                "mask",
                "intersectionFunctionTableOffset",
                "accelerationStructureIndex",
                "userID",
            ],
        ),
        "MTLVertexAmplificationViewMapping": objc.createStructType(
            "Metal.MTLVertexAmplificationViewMapping",
            b"{MTLVertexAmplificationViewMapping=II}",
            ["viewportArrayIndexOffset", "renderTargetArrayIndexOffset"],
        ),
        "MTLAccelerationStructureInstanceDescriptor": objc.createStructType(
            "Metal.MTLAccelerationStructureInstanceDescriptor",
            b"{MTLAccelerationStructureInstanceDescriptor={_MTLPackedFloat4x3=[4{_MTLPackedFloat3=[3f]}]}IIII}",
            [
                "transformationMatrix",
                "options",
                "mask",
                "intersectionFunctionTableOffset",
                "accelerationStructureIndex",
            ],
        ),
        "MTLCounterResultStageUtilization": objc.createStructType(
            "Metal.MTLCounterResultStageUtilization",
            b"{MTLCounterResultStageUtilization=QQQQQQ}",
            [
                "totalCycles",
                "vertexCycles",
                "tessellationCycles",
                "postTessellationVertexCycles",
                "fragmentCycles",
                "renderTargetCycles",
            ],
        ),
        "MTLPackedFloat4x3": objc.createStructType(
            "Metal.MTLPackedFloat4x3",
            b"{_MTLPackedFloat4x3=[4{_MTLPackedFloat3=[3f]}]}",
            ["columns"],
        ),
        "MTLDrawPatchIndirectArguments": objc.createStructType(
            "Metal.MTLDrawPatchIndirectArguments",
            b"{MTLDrawPatchIndirectArguments=IIII}",
            ["patchCount", "instanceCount", "patchStart", "baseInstance"],
        ),
        "MTLTriangleTessellationFactorsHalf": objc.createStructType(
            "Metal.MTLTriangleTessellationFactorsHalf",
            b"{MTLTriangleTessellationFactorsHalf=[3S]S}",
            ["edgeTessellationFactor", "insideTessellationFactor"],
        ),
        "MTLAxisAlignedBoundingBox": objc.createStructType(
            "Metal.MTLAxisAlignedBoundingBox",
            b"{_MTLAxisAlignedBoundingBox={_MTLPackedFloat3=[3f]}{_MTLPackedFloat3=[3f]}}",
            ["min", "max"],
        ),
        "MTLCounterResultStatistic": objc.createStructType(
            "Metal.MTLCounterResultStatistic",
            b"{MTLCounterResultStatistic=QQQQQQQQ}",
            [
                "tessellationInputPatches",
                "vertexInvocations",
                "postTessellationVertexInvocations",
                "clipperInvocations",
                "clipperPrimitivesOut",
                "fragmentInvocations",
                "fragmentsPassed",
                "computeKernelInvocations",
            ],
        ),
        "MTLStageInRegionIndirectArguments": objc.createStructType(
            "Metal.MTLStageInRegionIndirectArguments",
            b"{MTLStageInRegionIndirectArguments=[3I][3I]}",
            ["stageInOrigin", "stageInSize"],
        ),
        "MTLOrigin": objc.createStructType(
            "Metal.MTLOrigin", b"{MTLOrigin=QQQ}", ["x", "y", "z"]
        ),
        "MTLAccelerationStructureMotionInstanceDescriptor": objc.createStructType(
            "Metal.MTLAccelerationStructureMotionInstanceDescriptor",
            b"{MTLAccelerationStructureMotionInstanceDescriptor=IIIIIIIIIff}",
            [
                "options",
                "mask",
                "intersectionFunctionTableOffset",
                "accelerationStructureIndex",
                "userID",
                "motionTransformsStartIndex",
                "motionTransformsCount",
                "motionStartBorderMode",
                "motionEndBorderMode",
                "motionStartTime",
                "motionEndTime",
            ],
        ),
        "MTLDispatchThreadgroupsIndirectArguments": objc.createStructType(
            "Metal.MTLDispatchThreadgroupsIndirectArguments",
            b"{MTLDispatchThreadgroupsIndirectArguments=[3I]}",
            ["threadgroupsPerGrid"],
        ),
        "MTLViewport": objc.createStructType(
            "Metal.MTLViewport",
            b"{MTLViewport=dddddd}",
            ["originX", "originY", "width", "height", "znear", "zfar"],
        ),
        "MTLResourceID": objc.createStructType(
            "Metal.MTLResourceID", b"{MTLResourceID=Q}", ["_impl"]
        ),
        "MTLIndirectAccelerationStructureMotionInstanceDescriptor": objc.createStructType(
            "Metal.MTLIndirectAccelerationStructureMotionInstanceDescriptor",
            b"{MTLIndirectAccelerationStructureMotionInstanceDescriptor=IIII{MTLResourceID=Q}IIIIff}",
            [
                "options",
                "mask",
                "intersectionFunctionTableOffset",
                "userID",
                "accelerationStructureID",
                "motionTransformsStartIndex",
                "motionTransformsCount",
                "motionStartBorderMode",
                "motionEndBorderMode",
                "motionStartTime",
                "motionEndTime",
            ],
        ),
        "MTLRegion": objc.createStructType(
            "Metal.MTLRegion",
            b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}",
            ["origin", "size"],
        ),
        "MTLSamplePosition": objc.createStructType(
            "Metal.MTLSamplePosition", b"{MTLSamplePosition=ff}", ["x", "y"]
        ),
        "MTLDrawIndexedPrimitivesIndirectArguments": objc.createStructType(
            "Metal.MTLDrawIndexedPrimitivesIndirectArguments",
            b"{MTLDrawIndexedPrimitivesIndirectArguments=IIIiI}",
            ["indexCount", "instanceCount", "indexStart", "baseVertex", "baseInstance"],
        ),
        "MTLClearColor": objc.createStructType(
            "Metal.MTLClearColor",
            b"{MTLClearColor=dddd}",
            ["red", "green", "blue", "alpha"],
        ),
        "MTLCoordinate2D": objc.createStructType(
            "Metal.MTLCoordinate2D", b"{MTLSamplePosition=ff}", ["x", "y"]
        ),
        "MTLSizeAndAlign": objc.createStructType(
            "Metal.MTLSizeAndAlign", b"{MTLSizeAndAlign=QQ}", ["size", "align"]
        ),
        "MTLScissorRect": objc.createStructType(
            "Metal.MTLScissorRect",
            b"{MTLScissorRect=QQQQ}",
            ["x", "y", "width", "height"],
        ),
        "MTLIndirectAccelerationStructureInstanceDescriptor": objc.createStructType(
            "Metal.MTLIndirectAccelerationStructureInstanceDescriptor",
            b"{MTLIndirectAccelerationStructureInstanceDescriptor={_MTLPackedFloat4x3=[4{_MTLPackedFloat3=[3f]}]}IIII{MTLResourceID=Q}}",
            [
                "transformationMatrix",
                "options",
                "mask",
                "intersectionFunctionTableOffset",
                "userID",
                "accelerationStructureID",
            ],
        ),
        "MTLCounterResultTimestamp": objc.createStructType(
            "Metal.MTLCounterResultTimestamp",
            b"{MTLCounterResultTimestamp=Q}",
            ["timestamp"],
        ),
        "MTLMapIndirectArguments": objc.createStructType(
            "Metal.MTLMapIndirectArguments",
            b"{_MTLMapIndirectArguments=IIIIIIII}",
            [
                "regionOriginX",
                "regionOriginY",
                "regionOriginZ",
                "regionSizeWidth",
                "regionSizeHeight",
                "regionSizeDepth",
                "mipMapLevel",
                "sliceId",
            ],
        ),
        "MTLPackedFloatQuaternion": objc.createStructType(
            "Metal.MTLPackedFloatQuaternion",
            b"{MTLPackedFloatQuaternion=ffff}",
            ["x", "y", "z", "w"],
        ),
        "MTLComponentTransform": objc.createStructType(
            "Metal.MTLComponentTransform",
            b"{MTLComponentTransform={_MTLPackedFloat3=[3f]}{_MTLPackedFloat3=[3f]}{_MTLPackedFloat3=[3f]}{MTLPackedFloatQuaternion=ffff}{_MTLPackedFloat3=[3f]}}",
            ["scale", "shear", "pivot", "rotation", "translation"],
        ),
    }
)
constants = """$MTLBinaryArchiveDomain$MTLCaptureErrorDomain$MTLCommandBufferEncoderInfoErrorKey$MTLCommandBufferErrorDomain$MTLCommonCounterClipperInvocations$MTLCommonCounterClipperPrimitivesOut$MTLCommonCounterComputeKernelInvocations$MTLCommonCounterFragmentCycles$MTLCommonCounterFragmentInvocations$MTLCommonCounterFragmentsPassed$MTLCommonCounterPostTessellationVertexCycles$MTLCommonCounterPostTessellationVertexInvocations$MTLCommonCounterRenderTargetWriteCycles$MTLCommonCounterSetStageUtilization$MTLCommonCounterSetStatistic$MTLCommonCounterSetTimestamp$MTLCommonCounterTessellationCycles$MTLCommonCounterTessellationInputPatches$MTLCommonCounterTimestamp$MTLCommonCounterTotalCycles$MTLCommonCounterVertexCycles$MTLCommonCounterVertexInvocations$MTLCounterErrorDomain$MTLDeviceRemovalRequestedNotification$MTLDeviceWasAddedNotification$MTLDeviceWasRemovedNotification$MTLDynamicLibraryDomain$MTLIOErrorDomain$MTLLibraryErrorDomain$MTLLogStateErrorDomain$NSDeviceCertificationiPhonePerformanceGaming@q$NSProcessInfoPerformanceProfileDidChangeNotification$NSProcessPerformanceProfileDefault@q$NSProcessPerformanceProfileSustained@q$"""
enums = """$MTLAccelerationStructureInstanceDescriptorTypeDefault@0$MTLAccelerationStructureInstanceDescriptorTypeIndirect@3$MTLAccelerationStructureInstanceDescriptorTypeIndirectMotion@4$MTLAccelerationStructureInstanceDescriptorTypeMotion@2$MTLAccelerationStructureInstanceDescriptorTypeUserID@1$MTLAccelerationStructureInstanceOptionDisableTriangleCulling@1$MTLAccelerationStructureInstanceOptionNonOpaque@8$MTLAccelerationStructureInstanceOptionNone@0$MTLAccelerationStructureInstanceOptionOpaque@4$MTLAccelerationStructureInstanceOptionTriangleFrontFacingWindingCounterClockwise@2$MTLAccelerationStructureRefitOptionPerPrimitiveData@2$MTLAccelerationStructureRefitOptionVertexData@1$MTLAccelerationStructureUsageExtendedLimits@4$MTLAccelerationStructureUsageNone@0$MTLAccelerationStructureUsagePreferFastBuild@2$MTLAccelerationStructureUsageRefit@1$MTLArgumentAccessReadOnly@0$MTLArgumentAccessReadWrite@1$MTLArgumentAccessWriteOnly@2$MTLArgumentBuffersTier1@0$MTLArgumentBuffersTier2@1$MTLArgumentTypeBuffer@0$MTLArgumentTypeImageblock@17$MTLArgumentTypeImageblockData@16$MTLArgumentTypeInstanceAccelerationStructure@26$MTLArgumentTypeIntersectionFunctionTable@27$MTLArgumentTypePrimitiveAccelerationStructure@25$MTLArgumentTypeSampler@3$MTLArgumentTypeTexture@2$MTLArgumentTypeThreadgroupMemory@1$MTLArgumentTypeVisibleFunctionTable@24$MTLAttributeFormatChar@46$MTLAttributeFormatChar2@4$MTLAttributeFormatChar2Normalized@10$MTLAttributeFormatChar3@5$MTLAttributeFormatChar3Normalized@11$MTLAttributeFormatChar4@6$MTLAttributeFormatChar4Normalized@12$MTLAttributeFormatCharNormalized@48$MTLAttributeFormatFloat@28$MTLAttributeFormatFloat2@29$MTLAttributeFormatFloat3@30$MTLAttributeFormatFloat4@31$MTLAttributeFormatFloatRG11B10@54$MTLAttributeFormatFloatRGB9E5@55$MTLAttributeFormatHalf@53$MTLAttributeFormatHalf2@25$MTLAttributeFormatHalf3@26$MTLAttributeFormatHalf4@27$MTLAttributeFormatInt@32$MTLAttributeFormatInt1010102Normalized@40$MTLAttributeFormatInt2@33$MTLAttributeFormatInt3@34$MTLAttributeFormatInt4@35$MTLAttributeFormatInvalid@0$MTLAttributeFormatShort@50$MTLAttributeFormatShort2@16$MTLAttributeFormatShort2Normalized@22$MTLAttributeFormatShort3@17$MTLAttributeFormatShort3Normalized@23$MTLAttributeFormatShort4@18$MTLAttributeFormatShort4Normalized@24$MTLAttributeFormatShortNormalized@52$MTLAttributeFormatUChar@45$MTLAttributeFormatUChar2@1$MTLAttributeFormatUChar2Normalized@7$MTLAttributeFormatUChar3@2$MTLAttributeFormatUChar3Normalized@8$MTLAttributeFormatUChar4@3$MTLAttributeFormatUChar4Normalized@9$MTLAttributeFormatUChar4Normalized_BGRA@42$MTLAttributeFormatUCharNormalized@47$MTLAttributeFormatUInt@36$MTLAttributeFormatUInt1010102Normalized@41$MTLAttributeFormatUInt2@37$MTLAttributeFormatUInt3@38$MTLAttributeFormatUInt4@39$MTLAttributeFormatUShort@49$MTLAttributeFormatUShort2@13$MTLAttributeFormatUShort2Normalized@19$MTLAttributeFormatUShort3@14$MTLAttributeFormatUShort3Normalized@20$MTLAttributeFormatUShort4@15$MTLAttributeFormatUShort4Normalized@21$MTLAttributeFormatUShortNormalized@51$MTLBarrierScopeBuffers@1$MTLBarrierScopeRenderTargets@4$MTLBarrierScopeTextures@2$MTLBinaryArchiveErrorCompilationFailure@3$MTLBinaryArchiveErrorInternalError@4$MTLBinaryArchiveErrorInvalidFile@1$MTLBinaryArchiveErrorNone@0$MTLBinaryArchiveErrorUnexpectedElement@2$MTLBindingAccessReadOnly@0$MTLBindingAccessReadWrite@1$MTLBindingAccessWriteOnly@2$MTLBindingTypeBuffer@0$MTLBindingTypeImageblock@17$MTLBindingTypeImageblockData@16$MTLBindingTypeInstanceAccelerationStructure@26$MTLBindingTypeIntersectionFunctionTable@27$MTLBindingTypeObjectPayload@34$MTLBindingTypePrimitiveAccelerationStructure@25$MTLBindingTypeSampler@3$MTLBindingTypeTexture@2$MTLBindingTypeThreadgroupMemory@1$MTLBindingTypeVisibleFunctionTable@24$MTLBlendFactorBlendAlpha@13$MTLBlendFactorBlendColor@11$MTLBlendFactorDestinationAlpha@8$MTLBlendFactorDestinationColor@6$MTLBlendFactorOne@1$MTLBlendFactorOneMinusBlendAlpha@14$MTLBlendFactorOneMinusBlendColor@12$MTLBlendFactorOneMinusDestinationAlpha@9$MTLBlendFactorOneMinusDestinationColor@7$MTLBlendFactorOneMinusSource1Alpha@18$MTLBlendFactorOneMinusSource1Color@16$MTLBlendFactorOneMinusSourceAlpha@5$MTLBlendFactorOneMinusSourceColor@3$MTLBlendFactorSource1Alpha@17$MTLBlendFactorSource1Color@15$MTLBlendFactorSourceAlpha@4$MTLBlendFactorSourceAlphaSaturated@10$MTLBlendFactorSourceColor@2$MTLBlendFactorZero@0$MTLBlendOperationAdd@0$MTLBlendOperationMax@4$MTLBlendOperationMin@3$MTLBlendOperationReverseSubtract@2$MTLBlendOperationSubtract@1$MTLBlitOptionDepthFromDepthStencil@1$MTLBlitOptionNone@0$MTLBlitOptionRowLinearPVRTC@4$MTLBlitOptionStencilFromDepthStencil@2$MTLCPUCacheModeDefaultCache@0$MTLCPUCacheModeWriteCombined@1$MTLCaptureDestinationDeveloperTools@1$MTLCaptureDestinationGPUTraceDocument@2$MTLCaptureErrorAlreadyCapturing@2$MTLCaptureErrorInvalidDescriptor@3$MTLCaptureErrorNotSupported@1$MTLColorWriteMaskAll@15$MTLColorWriteMaskAlpha@1$MTLColorWriteMaskBlue@2$MTLColorWriteMaskGreen@4$MTLColorWriteMaskNone@0$MTLColorWriteMaskRed@8$MTLCommandBufferErrorAccessRevoked@4$MTLCommandBufferErrorBlacklisted@4$MTLCommandBufferErrorDeviceRemoved@11$MTLCommandBufferErrorInternal@1$MTLCommandBufferErrorInvalidResource@9$MTLCommandBufferErrorMemoryless@10$MTLCommandBufferErrorNone@0$MTLCommandBufferErrorNotPermitted@7$MTLCommandBufferErrorOptionEncoderExecutionStatus@1$MTLCommandBufferErrorOptionNone@0$MTLCommandBufferErrorOutOfMemory@8$MTLCommandBufferErrorPageFault@3$MTLCommandBufferErrorStackOverflow@12$MTLCommandBufferErrorTimeout@2$MTLCommandBufferStatusCommitted@2$MTLCommandBufferStatusCompleted@4$MTLCommandBufferStatusEnqueued@1$MTLCommandBufferStatusError@5$MTLCommandBufferStatusNotEnqueued@0$MTLCommandBufferStatusScheduled@3$MTLCommandEncoderErrorStateAffected@2$MTLCommandEncoderErrorStateCompleted@1$MTLCommandEncoderErrorStateFaulted@4$MTLCommandEncoderErrorStatePending@3$MTLCommandEncoderErrorStateUnknown@0$MTLCompareFunctionAlways@7$MTLCompareFunctionEqual@2$MTLCompareFunctionGreater@4$MTLCompareFunctionGreaterEqual@6$MTLCompareFunctionLess@1$MTLCompareFunctionLessEqual@3$MTLCompareFunctionNever@0$MTLCompareFunctionNotEqual@5$MTLCompileSymbolVisibilityDefault@0$MTLCompileSymbolVisibilityHidden@1$MTLCounterDontSample@18446744073709551615$MTLCounterErrorValue@18446744073709551615$MTLCounterSampleBufferErrorInternal@2$MTLCounterSampleBufferErrorInvalid@1$MTLCounterSampleBufferErrorOutOfMemory@0$MTLCounterSamplingPointAtBlitBoundary@4$MTLCounterSamplingPointAtDispatchBoundary@2$MTLCounterSamplingPointAtDrawBoundary@1$MTLCounterSamplingPointAtStageBoundary@0$MTLCounterSamplingPointAtTileDispatchBoundary@3$MTLCullModeBack@2$MTLCullModeFront@1$MTLCullModeNone@0$MTLCurveBasisBSpline@0$MTLCurveBasisBezier@3$MTLCurveBasisCatmullRom@1$MTLCurveBasisLinear@2$MTLCurveEndCapsDisk@1$MTLCurveEndCapsNone@0$MTLCurveEndCapsSphere@2$MTLCurveTypeFlat@1$MTLCurveTypeRound@0$MTLDataTypeArray@2$MTLDataTypeBFloat@121$MTLDataTypeBFloat2@122$MTLDataTypeBFloat3@123$MTLDataTypeBFloat4@124$MTLDataTypeBool@53$MTLDataTypeBool2@54$MTLDataTypeBool3@55$MTLDataTypeBool4@56$MTLDataTypeChar@45$MTLDataTypeChar2@46$MTLDataTypeChar3@47$MTLDataTypeChar4@48$MTLDataTypeComputePipeline@79$MTLDataTypeFloat@3$MTLDataTypeFloat2@4$MTLDataTypeFloat2x2@7$MTLDataTypeFloat2x3@8$MTLDataTypeFloat2x4@9$MTLDataTypeFloat3@5$MTLDataTypeFloat3x2@10$MTLDataTypeFloat3x3@11$MTLDataTypeFloat3x4@12$MTLDataTypeFloat4@6$MTLDataTypeFloat4x2@13$MTLDataTypeFloat4x3@14$MTLDataTypeFloat4x4@15$MTLDataTypeHalf@16$MTLDataTypeHalf2@17$MTLDataTypeHalf2x2@20$MTLDataTypeHalf2x3@21$MTLDataTypeHalf2x4@22$MTLDataTypeHalf3@18$MTLDataTypeHalf3x2@23$MTLDataTypeHalf3x3@24$MTLDataTypeHalf3x4@25$MTLDataTypeHalf4@19$MTLDataTypeHalf4x2@26$MTLDataTypeHalf4x3@27$MTLDataTypeHalf4x4@28$MTLDataTypeIndirectCommandBuffer@80$MTLDataTypeInstanceAccelerationStructure@118$MTLDataTypeInt@29$MTLDataTypeInt2@30$MTLDataTypeInt3@31$MTLDataTypeInt4@32$MTLDataTypeIntersectionFunctionTable@116$MTLDataTypeLong@81$MTLDataTypeLong2@82$MTLDataTypeLong3@83$MTLDataTypeLong4@84$MTLDataTypeNone@0$MTLDataTypePointer@60$MTLDataTypePrimitiveAccelerationStructure@117$MTLDataTypeR16Snorm@65$MTLDataTypeR16Unorm@64$MTLDataTypeR8Snorm@63$MTLDataTypeR8Unorm@62$MTLDataTypeRG11B10Float@76$MTLDataTypeRG16Snorm@69$MTLDataTypeRG16Unorm@68$MTLDataTypeRG8Snorm@67$MTLDataTypeRG8Unorm@66$MTLDataTypeRGB10A2Unorm@75$MTLDataTypeRGB9E5Float@77$MTLDataTypeRGBA16Snorm@74$MTLDataTypeRGBA16Unorm@73$MTLDataTypeRGBA8Snorm@72$MTLDataTypeRGBA8Unorm@70$MTLDataTypeRGBA8Unorm_sRGB@71$MTLDataTypeRenderPipeline@78$MTLDataTypeSampler@59$MTLDataTypeShort@37$MTLDataTypeShort2@38$MTLDataTypeShort3@39$MTLDataTypeShort4@40$MTLDataTypeStruct@1$MTLDataTypeTexture@58$MTLDataTypeUChar@49$MTLDataTypeUChar2@50$MTLDataTypeUChar3@51$MTLDataTypeUChar4@52$MTLDataTypeUInt@33$MTLDataTypeUInt2@34$MTLDataTypeUInt3@35$MTLDataTypeUInt4@36$MTLDataTypeULong@85$MTLDataTypeULong2@86$MTLDataTypeULong3@87$MTLDataTypeULong4@88$MTLDataTypeUShort@41$MTLDataTypeUShort2@42$MTLDataTypeUShort3@43$MTLDataTypeUShort4@44$MTLDataTypeVisibleFunctionTable@115$MTLDepthClipModeClamp@1$MTLDepthClipModeClip@0$MTLDeviceLocationBuiltIn@0$MTLDeviceLocationExternal@2$MTLDeviceLocationSlot@1$MTLDeviceLocationUnspecified@18446744073709551615$MTLDispatchTypeConcurrent@1$MTLDispatchTypeSerial@0$MTLDynamicLibraryErrorCompilationFailure@2$MTLDynamicLibraryErrorDependencyLoadFailure@4$MTLDynamicLibraryErrorInvalidFile@1$MTLDynamicLibraryErrorNone@0$MTLDynamicLibraryErrorUnresolvedInstallName@3$MTLDynamicLibraryErrorUnsupported@5$MTLFeatureSet_OSX_GPUFamily1_v1@10000$MTLFeatureSet_OSX_GPUFamily1_v2@10001$MTLFeatureSet_OSX_ReadWriteTextureTier2@10002$MTLFeatureSet_TVOS_GPUFamily1_v1@30000$MTLFeatureSet_iOS_GPUFamily1_v1@0$MTLFeatureSet_iOS_GPUFamily1_v2@2$MTLFeatureSet_iOS_GPUFamily1_v3@5$MTLFeatureSet_iOS_GPUFamily1_v4@8$MTLFeatureSet_iOS_GPUFamily1_v5@12$MTLFeatureSet_iOS_GPUFamily2_v1@1$MTLFeatureSet_iOS_GPUFamily2_v2@3$MTLFeatureSet_iOS_GPUFamily2_v3@6$MTLFeatureSet_iOS_GPUFamily2_v4@9$MTLFeatureSet_iOS_GPUFamily2_v5@13$MTLFeatureSet_iOS_GPUFamily3_v1@4$MTLFeatureSet_iOS_GPUFamily3_v2@7$MTLFeatureSet_iOS_GPUFamily3_v3@10$MTLFeatureSet_iOS_GPUFamily3_v4@14$MTLFeatureSet_iOS_GPUFamily4_v1@11$MTLFeatureSet_iOS_GPUFamily4_v2@15$MTLFeatureSet_iOS_GPUFamily5_v1@16$MTLFeatureSet_macOS_GPUFamily1_v1@10000$MTLFeatureSet_macOS_GPUFamily1_v2@10001$MTLFeatureSet_macOS_GPUFamily1_v3@10003$MTLFeatureSet_macOS_GPUFamily1_v4@10004$MTLFeatureSet_macOS_GPUFamily2_v1@10005$MTLFeatureSet_macOS_ReadWriteTextureTier2@10002$MTLFeatureSet_tvOS_GPUFamily1_v1@30000$MTLFeatureSet_tvOS_GPUFamily1_v2@30001$MTLFeatureSet_tvOS_GPUFamily1_v3@30002$MTLFeatureSet_tvOS_GPUFamily1_v4@30004$MTLFeatureSet_tvOS_GPUFamily2_v1@30003$MTLFeatureSet_tvOS_GPUFamily2_v2@30005$MTLFunctionLogTypeValidation@0$MTLFunctionOptionCompileToBinary@1$MTLFunctionOptionFailOnBinaryArchiveMiss@4$MTLFunctionOptionNone@0$MTLFunctionOptionStoreFunctionInMetalPipelinesScript@2$MTLFunctionOptionStoreFunctionInMetalScript@2$MTLFunctionTypeFragment@2$MTLFunctionTypeIntersection@6$MTLFunctionTypeKernel@3$MTLFunctionTypeMesh@7$MTLFunctionTypeObject@8$MTLFunctionTypeVertex@1$MTLFunctionTypeVisible@5$MTLGPUFamilyApple1@1001$MTLGPUFamilyApple2@1002$MTLGPUFamilyApple3@1003$MTLGPUFamilyApple4@1004$MTLGPUFamilyApple5@1005$MTLGPUFamilyApple6@1006$MTLGPUFamilyApple7@1007$MTLGPUFamilyApple8@1008$MTLGPUFamilyApple9@1009$MTLGPUFamilyCommon1@3001$MTLGPUFamilyCommon2@3002$MTLGPUFamilyCommon3@3003$MTLGPUFamilyMac1@2001$MTLGPUFamilyMac2@2002$MTLGPUFamilyMacCatalyst1@4001$MTLGPUFamilyMacCatalyst2@4002$MTLGPUFamilyMetal3@5001$MTLHazardTrackingModeDefault@0$MTLHazardTrackingModeTracked@2$MTLHazardTrackingModeUntracked@1$MTLHeapTypeAutomatic@0$MTLHeapTypePlacement@1$MTLHeapTypeSparse@2$MTLIOCommandQueueTypeConcurrent@0$MTLIOCommandQueueTypeSerial@1$MTLIOCompressionMethodLZ4@2$MTLIOCompressionMethodLZBitmap@4$MTLIOCompressionMethodLZFSE@1$MTLIOCompressionMethodLZMA@3$MTLIOCompressionMethodZlib@0$MTLIOCompressionStatusComplete@0$MTLIOCompressionStatusError@1$MTLIOErrorInternal@2$MTLIOErrorURLInvalid@1$MTLIOPriorityHigh@0$MTLIOPriorityLow@2$MTLIOPriorityNormal@1$MTLIOStatusCancelled@1$MTLIOStatusComplete@3$MTLIOStatusError@2$MTLIOStatusPending@0$MTLIndexTypeUInt16@0$MTLIndexTypeUInt32@1$MTLIndirectCommandTypeConcurrentDispatch@32$MTLIndirectCommandTypeConcurrentDispatchThreads@64$MTLIndirectCommandTypeDraw@1$MTLIndirectCommandTypeDrawIndexed@2$MTLIndirectCommandTypeDrawIndexedPatches@8$MTLIndirectCommandTypeDrawMeshThreadgroups@128$MTLIndirectCommandTypeDrawMeshThreads@256$MTLIndirectCommandTypeDrawPatches@4$MTLIntersectionFunctionSignatureCurveData@128$MTLIntersectionFunctionSignatureExtendedLimits@32$MTLIntersectionFunctionSignatureInstanceMotion@8$MTLIntersectionFunctionSignatureInstancing@1$MTLIntersectionFunctionSignatureMaxLevels@64$MTLIntersectionFunctionSignatureNone@0$MTLIntersectionFunctionSignaturePrimitiveMotion@16$MTLIntersectionFunctionSignatureTriangleData@2$MTLIntersectionFunctionSignatureWorldSpaceData@4$MTLLanguageVersion1_0@65536$MTLLanguageVersion1_1@65537$MTLLanguageVersion1_2@65538$MTLLanguageVersion2_0@131072$MTLLanguageVersion2_1@131073$MTLLanguageVersion2_2@131074$MTLLanguageVersion2_3@131075$MTLLanguageVersion2_4@131076$MTLLanguageVersion3_0@196608$MTLLanguageVersion3_1@196609$MTLLanguageVersion3_2@196610$MTLLibraryErrorCompileFailure@3$MTLLibraryErrorCompileWarning@4$MTLLibraryErrorFileNotFound@6$MTLLibraryErrorFunctionNotFound@5$MTLLibraryErrorInternal@2$MTLLibraryErrorUnsupported@1$MTLLibraryOptimizationLevelDefault@0$MTLLibraryOptimizationLevelSize@1$MTLLibraryTypeDynamic@1$MTLLibraryTypeExecutable@0$MTLLoadActionClear@2$MTLLoadActionDontCare@0$MTLLoadActionLoad@1$MTLLogLevelDebug@1$MTLLogLevelError@4$MTLLogLevelFault@5$MTLLogLevelInfo@2$MTLLogLevelNotice@3$MTLLogLevelUndefined@0$MTLLogStateErrorInvalid@2$MTLLogStateErrorInvalidSize@1$MTLMathFloatingPointFunctionsFast@0$MTLMathFloatingPointFunctionsPrecise@1$MTLMathModeFast@2$MTLMathModeRelaxed@1$MTLMathModeSafe@0$MTLMatrixLayoutColumnMajor@0$MTLMatrixLayoutRowMajor@1$MTLMaxBlitPassSampleBuffers@4$MTLMaxComputePassSampleBuffers@4$MTLMaxRenderPassSampleBuffers@4$MTLMaxResourceStatePassSampleBuffers@4$MTLMotionBorderModeClamp@0$MTLMotionBorderModeVanish@1$MTLMultisampleDepthResolveFilterMax@2$MTLMultisampleDepthResolveFilterMin@1$MTLMultisampleDepthResolveFilterSample0@0$MTLMultisampleStencilResolveFilterDepthResolvedSample@1$MTLMultisampleStencilResolveFilterSample0@0$MTLMutabilityDefault@0$MTLMutabilityImmutable@2$MTLMutabilityMutable@1$MTLPatchTypeNone@0$MTLPatchTypeQuad@2$MTLPatchTypeTriangle@1$MTLPipelineOptionArgumentInfo@1$MTLPipelineOptionBindingInfo@1$MTLPipelineOptionBufferTypeInfo@2$MTLPipelineOptionFailOnBinaryArchiveMiss@4$MTLPipelineOptionNone@0$MTLPixelFormatA1BGR5Unorm@41$MTLPixelFormatA8Unorm@1$MTLPixelFormatABGR4Unorm@42$MTLPixelFormatASTC_10x10_HDR@234$MTLPixelFormatASTC_10x10_LDR@216$MTLPixelFormatASTC_10x10_sRGB@198$MTLPixelFormatASTC_10x5_HDR@231$MTLPixelFormatASTC_10x5_LDR@213$MTLPixelFormatASTC_10x5_sRGB@195$MTLPixelFormatASTC_10x6_HDR@232$MTLPixelFormatASTC_10x6_LDR@214$MTLPixelFormatASTC_10x6_sRGB@196$MTLPixelFormatASTC_10x8_HDR@233$MTLPixelFormatASTC_10x8_LDR@215$MTLPixelFormatASTC_10x8_sRGB@197$MTLPixelFormatASTC_12x10_HDR@235$MTLPixelFormatASTC_12x10_LDR@217$MTLPixelFormatASTC_12x10_sRGB@199$MTLPixelFormatASTC_12x12_HDR@236$MTLPixelFormatASTC_12x12_LDR@218$MTLPixelFormatASTC_12x12_sRGB@200$MTLPixelFormatASTC_4x4_HDR@222$MTLPixelFormatASTC_4x4_LDR@204$MTLPixelFormatASTC_4x4_sRGB@186$MTLPixelFormatASTC_5x4_HDR@223$MTLPixelFormatASTC_5x4_LDR@205$MTLPixelFormatASTC_5x4_sRGB@187$MTLPixelFormatASTC_5x5_HDR@224$MTLPixelFormatASTC_5x5_LDR@206$MTLPixelFormatASTC_5x5_sRGB@188$MTLPixelFormatASTC_6x5_HDR@225$MTLPixelFormatASTC_6x5_LDR@207$MTLPixelFormatASTC_6x5_sRGB@189$MTLPixelFormatASTC_6x6_HDR@226$MTLPixelFormatASTC_6x6_LDR@208$MTLPixelFormatASTC_6x6_sRGB@190$MTLPixelFormatASTC_8x5_HDR@228$MTLPixelFormatASTC_8x5_LDR@210$MTLPixelFormatASTC_8x5_sRGB@192$MTLPixelFormatASTC_8x6_HDR@229$MTLPixelFormatASTC_8x6_LDR@211$MTLPixelFormatASTC_8x6_sRGB@193$MTLPixelFormatASTC_8x8_HDR@230$MTLPixelFormatASTC_8x8_LDR@212$MTLPixelFormatASTC_8x8_sRGB@194$MTLPixelFormatB5G6R5Unorm@40$MTLPixelFormatBC1_RGBA@130$MTLPixelFormatBC1_RGBA_sRGB@131$MTLPixelFormatBC2_RGBA@132$MTLPixelFormatBC2_RGBA_sRGB@133$MTLPixelFormatBC3_RGBA@134$MTLPixelFormatBC3_RGBA_sRGB@135$MTLPixelFormatBC4_RSnorm@141$MTLPixelFormatBC4_RUnorm@140$MTLPixelFormatBC5_RGSnorm@143$MTLPixelFormatBC5_RGUnorm@142$MTLPixelFormatBC6H_RGBFloat@150$MTLPixelFormatBC6H_RGBUfloat@151$MTLPixelFormatBC7_RGBAUnorm@152$MTLPixelFormatBC7_RGBAUnorm_sRGB@153$MTLPixelFormatBGR10A2Unorm@94$MTLPixelFormatBGR10_XR@554$MTLPixelFormatBGR10_XR_sRGB@555$MTLPixelFormatBGR5A1Unorm@43$MTLPixelFormatBGRA10_XR@552$MTLPixelFormatBGRA10_XR_sRGB@553$MTLPixelFormatBGRA8Unorm@80$MTLPixelFormatBGRA8Unorm_sRGB@81$MTLPixelFormatBGRG422@241$MTLPixelFormatDepth16Unorm@250$MTLPixelFormatDepth24Unorm_Stencil8@255$MTLPixelFormatDepth32Float@252$MTLPixelFormatDepth32Float_Stencil8@260$MTLPixelFormatEAC_R11Snorm@172$MTLPixelFormatEAC_R11Unorm@170$MTLPixelFormatEAC_RG11Snorm@176$MTLPixelFormatEAC_RG11Unorm@174$MTLPixelFormatEAC_RGBA8@178$MTLPixelFormatEAC_RGBA8_sRGB@179$MTLPixelFormatETC2_RGB8@180$MTLPixelFormatETC2_RGB8A1@182$MTLPixelFormatETC2_RGB8A1_sRGB@183$MTLPixelFormatETC2_RGB8_sRGB@181$MTLPixelFormatGBGR422@240$MTLPixelFormatInvalid@0$MTLPixelFormatPVRTC_RGBA_2BPP@164$MTLPixelFormatPVRTC_RGBA_2BPP_sRGB@165$MTLPixelFormatPVRTC_RGBA_4BPP@166$MTLPixelFormatPVRTC_RGBA_4BPP_sRGB@167$MTLPixelFormatPVRTC_RGB_2BPP@160$MTLPixelFormatPVRTC_RGB_2BPP_sRGB@161$MTLPixelFormatPVRTC_RGB_4BPP@162$MTLPixelFormatPVRTC_RGB_4BPP_sRGB@163$MTLPixelFormatR16Float@25$MTLPixelFormatR16Sint@24$MTLPixelFormatR16Snorm@22$MTLPixelFormatR16Uint@23$MTLPixelFormatR16Unorm@20$MTLPixelFormatR32Float@55$MTLPixelFormatR32Sint@54$MTLPixelFormatR32Uint@53$MTLPixelFormatR8Sint@14$MTLPixelFormatR8Snorm@12$MTLPixelFormatR8Uint@13$MTLPixelFormatR8Unorm@10$MTLPixelFormatR8Unorm_sRGB@11$MTLPixelFormatRG11B10Float@92$MTLPixelFormatRG16Float@65$MTLPixelFormatRG16Sint@64$MTLPixelFormatRG16Snorm@62$MTLPixelFormatRG16Uint@63$MTLPixelFormatRG16Unorm@60$MTLPixelFormatRG32Float@105$MTLPixelFormatRG32Sint@104$MTLPixelFormatRG32Uint@103$MTLPixelFormatRG8Sint@34$MTLPixelFormatRG8Snorm@32$MTLPixelFormatRG8Uint@33$MTLPixelFormatRG8Unorm@30$MTLPixelFormatRG8Unorm_sRGB@31$MTLPixelFormatRGB10A2Uint@91$MTLPixelFormatRGB10A2Unorm@90$MTLPixelFormatRGB9E5Float@93$MTLPixelFormatRGBA16Float@115$MTLPixelFormatRGBA16Sint@114$MTLPixelFormatRGBA16Snorm@112$MTLPixelFormatRGBA16Uint@113$MTLPixelFormatRGBA16Unorm@110$MTLPixelFormatRGBA32Float@125$MTLPixelFormatRGBA32Sint@124$MTLPixelFormatRGBA32Uint@123$MTLPixelFormatRGBA8Sint@74$MTLPixelFormatRGBA8Snorm@72$MTLPixelFormatRGBA8Uint@73$MTLPixelFormatRGBA8Unorm@70$MTLPixelFormatRGBA8Unorm_sRGB@71$MTLPixelFormatStencil8@253$MTLPixelFormatX24_Stencil8@262$MTLPixelFormatX32_Stencil8@261$MTLPrimitiveTopologyClassLine@2$MTLPrimitiveTopologyClassPoint@1$MTLPrimitiveTopologyClassTriangle@3$MTLPrimitiveTopologyClassUnspecified@0$MTLPrimitiveTypeLine@1$MTLPrimitiveTypeLineStrip@2$MTLPrimitiveTypePoint@0$MTLPrimitiveTypeTriangle@3$MTLPrimitiveTypeTriangleStrip@4$MTLPurgeableStateEmpty@4$MTLPurgeableStateKeepCurrent@1$MTLPurgeableStateNonVolatile@2$MTLPurgeableStateVolatile@3$MTLReadWriteTextureTier1@1$MTLReadWriteTextureTier2@2$MTLReadWriteTextureTierNone@0$MTLRenderStageFragment@2$MTLRenderStageMesh@16$MTLRenderStageObject@8$MTLRenderStageTile@4$MTLRenderStageVertex@1$MTLResourceCPUCacheModeDefaultCache@0$MTLResourceCPUCacheModeShift@0$MTLResourceCPUCacheModeWriteCombined@1$MTLResourceHazardTrackingModeDefault@0$MTLResourceHazardTrackingModeShift@8$MTLResourceHazardTrackingModeTracked@512$MTLResourceHazardTrackingModeUntracked@256$MTLResourceOptionCPUCacheModeDefault@0$MTLResourceOptionCPUCacheModeWriteCombined@1$MTLResourceStorageModeManaged@16$MTLResourceStorageModeMemoryless@48$MTLResourceStorageModePrivate@32$MTLResourceStorageModeShared@0$MTLResourceStorageModeShift@4$MTLResourceUsageRead@1$MTLResourceUsageSample@4$MTLResourceUsageWrite@2$MTLSamplerAddressModeClampToBorderColor@5$MTLSamplerAddressModeClampToEdge@0$MTLSamplerAddressModeClampToZero@4$MTLSamplerAddressModeMirrorClampToEdge@1$MTLSamplerAddressModeMirrorRepeat@3$MTLSamplerAddressModeRepeat@2$MTLSamplerBorderColorOpaqueBlack@1$MTLSamplerBorderColorOpaqueWhite@2$MTLSamplerBorderColorTransparentBlack@0$MTLSamplerMinMagFilterLinear@1$MTLSamplerMinMagFilterNearest@0$MTLSamplerMipFilterLinear@2$MTLSamplerMipFilterNearest@1$MTLSamplerMipFilterNotMipmapped@0$MTLShaderValidationDefault@0$MTLShaderValidationDisabled@2$MTLShaderValidationEnabled@1$MTLSparsePageSize16@101$MTLSparsePageSize256@103$MTLSparsePageSize64@102$MTLSparseTextureMappingModeMap@0$MTLSparseTextureMappingModeUnmap@1$MTLSparseTextureRegionAlignmentModeInward@1$MTLSparseTextureRegionAlignmentModeOutward@0$MTLStencilOperationDecrementClamp@4$MTLStencilOperationDecrementWrap@7$MTLStencilOperationIncrementClamp@3$MTLStencilOperationIncrementWrap@6$MTLStencilOperationInvert@5$MTLStencilOperationKeep@0$MTLStencilOperationReplace@2$MTLStencilOperationZero@1$MTLStepFunctionConstant@0$MTLStepFunctionPerInstance@2$MTLStepFunctionPerPatch@3$MTLStepFunctionPerPatchControlPoint@4$MTLStepFunctionPerVertex@1$MTLStepFunctionThreadPositionInGridX@5$MTLStepFunctionThreadPositionInGridXIndexed@7$MTLStepFunctionThreadPositionInGridY@6$MTLStepFunctionThreadPositionInGridYIndexed@8$MTLStitchedLibraryOptionFailOnBinaryArchiveMiss@1$MTLStitchedLibraryOptionNone@0$MTLStitchedLibraryOptionStoreLibraryInMetalPipelinesScript@2$MTLStitchedLibraryOptionStoreLibraryInMetalScript@2$MTLStorageModeManaged@1$MTLStorageModeMemoryless@3$MTLStorageModePrivate@2$MTLStorageModeShared@0$MTLStoreActionCustomSampleDepthStore@5$MTLStoreActionDontCare@0$MTLStoreActionMultisampleResolve@2$MTLStoreActionOptionCustomSamplePositions@1$MTLStoreActionOptionNone@0$MTLStoreActionStore@1$MTLStoreActionStoreAndMultisampleResolve@3$MTLStoreActionUnknown@4$MTLTessellationControlPointIndexTypeNone@0$MTLTessellationControlPointIndexTypeUInt16@1$MTLTessellationControlPointIndexTypeUInt32@2$MTLTessellationFactorFormatHalf@0$MTLTessellationFactorStepFunctionConstant@0$MTLTessellationFactorStepFunctionPerInstance@2$MTLTessellationFactorStepFunctionPerPatch@1$MTLTessellationFactorStepFunctionPerPatchAndPerInstance@3$MTLTessellationPartitionModeFractionalEven@3$MTLTessellationPartitionModeFractionalOdd@2$MTLTessellationPartitionModeInteger@1$MTLTessellationPartitionModePow2@0$MTLTextureCompressionTypeLossless@0$MTLTextureCompressionTypeLossy@1$MTLTextureSwizzleAlpha@5$MTLTextureSwizzleBlue@4$MTLTextureSwizzleGreen@3$MTLTextureSwizzleOne@1$MTLTextureSwizzleRed@2$MTLTextureSwizzleZero@0$MTLTextureType1D@0$MTLTextureType1DArray@1$MTLTextureType2D@2$MTLTextureType2DArray@3$MTLTextureType2DMultisample@4$MTLTextureType2DMultisampleArray@8$MTLTextureType3D@7$MTLTextureTypeCube@5$MTLTextureTypeCubeArray@6$MTLTextureTypeTextureBuffer@9$MTLTextureUsagePixelFormatView@16$MTLTextureUsageRenderTarget@4$MTLTextureUsageShaderAtomic@32$MTLTextureUsageShaderRead@1$MTLTextureUsageShaderWrite@2$MTLTextureUsageUnknown@0$MTLTransformTypeComponent@1$MTLTransformTypePackedFloat4x3@0$MTLTriangleFillModeFill@0$MTLTriangleFillModeLines@1$MTLVertexFormatChar@46$MTLVertexFormatChar2@4$MTLVertexFormatChar2Normalized@10$MTLVertexFormatChar3@5$MTLVertexFormatChar3Normalized@11$MTLVertexFormatChar4@6$MTLVertexFormatChar4Normalized@12$MTLVertexFormatCharNormalized@48$MTLVertexFormatFloat@28$MTLVertexFormatFloat2@29$MTLVertexFormatFloat3@30$MTLVertexFormatFloat4@31$MTLVertexFormatFloatRG11B10@54$MTLVertexFormatFloatRGB9E5@55$MTLVertexFormatHalf@53$MTLVertexFormatHalf2@25$MTLVertexFormatHalf3@26$MTLVertexFormatHalf4@27$MTLVertexFormatInt@32$MTLVertexFormatInt1010102Normalized@40$MTLVertexFormatInt2@33$MTLVertexFormatInt3@34$MTLVertexFormatInt4@35$MTLVertexFormatInvalid@0$MTLVertexFormatShort@50$MTLVertexFormatShort2@16$MTLVertexFormatShort2Normalized@22$MTLVertexFormatShort3@17$MTLVertexFormatShort3Normalized@23$MTLVertexFormatShort4@18$MTLVertexFormatShort4Normalized@24$MTLVertexFormatShortNormalized@52$MTLVertexFormatUChar@45$MTLVertexFormatUChar2@1$MTLVertexFormatUChar2Normalized@7$MTLVertexFormatUChar3@2$MTLVertexFormatUChar3Normalized@8$MTLVertexFormatUChar4@3$MTLVertexFormatUChar4Normalized@9$MTLVertexFormatUChar4Normalized_BGRA@42$MTLVertexFormatUCharNormalized@47$MTLVertexFormatUInt@36$MTLVertexFormatUInt1010102Normalized@41$MTLVertexFormatUInt2@37$MTLVertexFormatUInt3@38$MTLVertexFormatUInt4@39$MTLVertexFormatUShort@49$MTLVertexFormatUShort2@13$MTLVertexFormatUShort2Normalized@19$MTLVertexFormatUShort3@14$MTLVertexFormatUShort3Normalized@20$MTLVertexFormatUShort4@15$MTLVertexFormatUShort4Normalized@21$MTLVertexFormatUShortNormalized@51$MTLVertexStepFunctionConstant@0$MTLVertexStepFunctionPerInstance@2$MTLVertexStepFunctionPerPatch@3$MTLVertexStepFunctionPerPatchControlPoint@4$MTLVertexStepFunctionPerVertex@1$MTLVisibilityResultModeBoolean@1$MTLVisibilityResultModeCounting@2$MTLVisibilityResultModeDisabled@0$MTLWindingClockwise@0$MTLWindingCounterClockwise@1$"""
misc.update(
    {
        "MTLTransformType": NewType("MTLTransformType", int),
        "MTLStepFunction": NewType("MTLStepFunction", int),
        "MTLCaptureDestination": NewType("MTLCaptureDestination", int),
        "MTLFunctionOptions": NewType("MTLFunctionOptions", int),
        "MTLSamplerAddressMode": NewType("MTLSamplerAddressMode", int),
        "MTLAccelerationStructureInstanceDescriptorType": NewType(
            "MTLAccelerationStructureInstanceDescriptorType", int
        ),
        "MTLCaptureError": NewType("MTLCaptureError", int),
        "MTLMutability": NewType("MTLMutability", int),
        "MTLFeatureSet": NewType("MTLFeatureSet", int),
        "MTLVertexFormat": NewType("MTLVertexFormat", int),
        "MTLSparseTextureMappingMode": NewType("MTLSparseTextureMappingMode", int),
        "MTLCPUCacheMode": NewType("MTLCPUCacheMode", int),
        "MTLMathMode": NewType("MTLMathMode", int),
        "MTLVisibilityResultMode": NewType("MTLVisibilityResultMode", int),
        "MTLTextureUsage": NewType("MTLTextureUsage", int),
        "MTLStitchedLibraryOptions": NewType("MTLStitchedLibraryOptions", int),
        "MTLIndirectCommandType": NewType("MTLIndirectCommandType", int),
        "MTLCompileSymbolVisibility": NewType("MTLCompileSymbolVisibility", int),
        "MTLCompareFunction": NewType("MTLCompareFunction", int),
        "MTLStoreAction": NewType("MTLStoreAction", int),
        "MTLTessellationControlPointIndexType": NewType(
            "MTLTessellationControlPointIndexType", int
        ),
        "MTLMultisampleStencilResolveFilter": NewType(
            "MTLMultisampleStencilResolveFilter", int
        ),
        "MTLLibraryError": NewType("MTLLibraryError", int),
        "MTLBarrierScope": NewType("MTLBarrierScope", int),
        "MTLSparsePageSize": NewType("MTLSparsePageSize", int),
        "MTLBinaryArchiveError": NewType("MTLBinaryArchiveError", int),
        "MTLLoadAction": NewType("MTLLoadAction", int),
        "MTLSamplerMinMagFilter": NewType("MTLSamplerMinMagFilter", int),
        "MTLResourceOptions": NewType("MTLResourceOptions", int),
        "MTLPixelFormat": NewType("MTLPixelFormat", int),
        "MTLAccelerationStructureRefitOptions": NewType(
            "MTLAccelerationStructureRefitOptions", int
        ),
        "MTLTessellationFactorStepFunction": NewType(
            "MTLTessellationFactorStepFunction", int
        ),
        "MTLCurveEndCaps": NewType("MTLCurveEndCaps", int),
        "MTLTessellationFactorFormat": NewType("MTLTessellationFactorFormat", int),
        "MTLBindingType": NewType("MTLBindingType", int),
        "MTLIOPriority": NewType("MTLIOPriority", int),
        "MTLLogLevel": NewType("MTLLogLevel", int),
        "MTLIOStatus": NewType("MTLIOStatus", int),
        "MTLCounterSamplingPoint": NewType("MTLCounterSamplingPoint", int),
        "MTLLogStateError": NewType("MTLLogStateError", int),
        "MTLDeviceLocation": NewType("MTLDeviceLocation", int),
        "MTLAccelerationStructureUsage": NewType("MTLAccelerationStructureUsage", int),
        "MTLArgumentAccess": NewType("MTLArgumentAccess", int),
        "MTLBlendFactor": NewType("MTLBlendFactor", int),
        "MTLIOCompressionStatus": NewType("MTLIOCompressionStatus", int),
        "MTLIOCommandQueueType": NewType("MTLIOCommandQueueType", int),
        "MTLHazardTrackingMode": NewType("MTLHazardTrackingMode", int),
        "MTLPrimitiveTopologyClass": NewType("MTLPrimitiveTopologyClass", int),
        "MTLVertexStepFunction": NewType("MTLVertexStepFunction", int),
        "MTLLibraryOptimizationLevel": NewType("MTLLibraryOptimizationLevel", int),
        "MTLFunctionType": NewType("MTLFunctionType", int),
        "MTLRenderStages": NewType("MTLRenderStages", int),
        "MTLCommandBufferError": NewType("MTLCommandBufferError", int),
        "MTLDepthClipMode": NewType("MTLDepthClipMode", int),
        "MTLTriangleFillMode": NewType("MTLTriangleFillMode", int),
        "MTLCommandEncoderErrorState": NewType("MTLCommandEncoderErrorState", int),
        "MTLResourceUsage": NewType("MTLResourceUsage", int),
        "MTLAttributeFormat": NewType("MTLAttributeFormat", int),
        "MTLIOError": NewType("MTLIOError", int),
        "MTLReadWriteTextureTier": NewType("MTLReadWriteTextureTier", int),
        "MTLPipelineOption": NewType("MTLPipelineOption", int),
        "MTLTextureSwizzle": NewType("MTLTextureSwizzle", int),
        "MTLMotionBorderMode": NewType("MTLMotionBorderMode", int),
        "MTLMultisampleDepthResolveFilter": NewType(
            "MTLMultisampleDepthResolveFilter", int
        ),
        "MTLColorWriteMask": NewType("MTLColorWriteMask", int),
        "MTLBlendOperation": NewType("MTLBlendOperation", int),
        "MTLIOCompressionMethod": NewType("MTLIOCompressionMethod", int),
        "MTLCommandBufferStatus": NewType("MTLCommandBufferStatus", int),
        "MTLFunctionLogType": NewType("MTLFunctionLogType", int),
        "MTLStoreActionOptions": NewType("MTLStoreActionOptions", int),
        "MTLMatrixLayout": NewType("MTLMatrixLayout", int),
        "MTLCommandBufferErrorOption": NewType("MTLCommandBufferErrorOption", int),
        "MTLArgumentBuffersTier": NewType("MTLArgumentBuffersTier", int),
        "MTLDispatchType": NewType("MTLDispatchType", int),
        "MTLBlitOption": NewType("MTLBlitOption", int),
        "MTLSparseTextureRegionAlignmentMode": NewType(
            "MTLSparseTextureRegionAlignmentMode", int
        ),
        "MTLTextureCompressionType": NewType("MTLTextureCompressionType", int),
        "MTLPurgeableState": NewType("MTLPurgeableState", int),
        "MTLStencilOperation": NewType("MTLStencilOperation", int),
        "MTLCurveBasis": NewType("MTLCurveBasis", int),
        "MTLSamplerBorderColor": NewType("MTLSamplerBorderColor", int),
        "MTLDataType": NewType("MTLDataType", int),
        "MTLShaderValidation": NewType("MTLShaderValidation", int),
        "MTLMathFloatingPointFunctions": NewType("MTLMathFloatingPointFunctions", int),
        "MTLArgumentType": NewType("MTLArgumentType", int),
        "MTLWinding": NewType("MTLWinding", int),
        "MTLGPUFamily": NewType("MTLGPUFamily", int),
        "MTLCounterSampleBufferError": NewType("MTLCounterSampleBufferError", int),
        "MTLIndexType": NewType("MTLIndexType", int),
        "MTLBindingAccess": NewType("MTLBindingAccess", int),
        "MTLAccelerationStructureInstanceOptions": NewType(
            "MTLAccelerationStructureInstanceOptions", int
        ),
        "MTLTextureType": NewType("MTLTextureType", int),
        "MTLSamplerMipFilter": NewType("MTLSamplerMipFilter", int),
        "MTLStorageMode": NewType("MTLStorageMode", int),
        "MTLTessellationPartitionMode": NewType("MTLTessellationPartitionMode", int),
        "MTLLanguageVersion": NewType("MTLLanguageVersion", int),
        "MTLCullMode": NewType("MTLCullMode", int),
        "MTLDynamicLibraryError": NewType("MTLDynamicLibraryError", int),
        "MTLPrimitiveType": NewType("MTLPrimitiveType", int),
        "MTLCurveType": NewType("MTLCurveType", int),
        "MTLHeapType": NewType("MTLHeapType", int),
        "MTLIntersectionFunctionSignature": NewType(
            "MTLIntersectionFunctionSignature", int
        ),
        "MTLPatchType": NewType("MTLPatchType", int),
        "MTLLibraryType": NewType("MTLLibraryType", int),
    }
)
misc.update(
    {
        "MTLCommonCounterSet": NewType("MTLCommonCounterSet", str),
        "MTLCommonCounter": NewType("MTLCommonCounter", str),
        "NSDeviceCertification": NewType("NSDeviceCertification", int),
        "MTLDeviceNotificationName": NewType("MTLDeviceNotificationName", str),
        "NSProcessPerformanceProfile": NewType("NSProcessPerformanceProfile", int),
    }
)
misc.update({})
functions = {
    "MTLTextureSwizzleChannelsMake": (b"{MTLTextureSwizzleChannels=CCCC}CCCC",),
    "MTLOriginMake": (b"{MTLOrigin=QQQ}QQQ",),
    "MTLSamplePositionMake": (b"{MTLSamplePosition=ff}ff",),
    "MTLRemoveDeviceObserver": (b"v@",),
    "MTLRegionMake3D": (b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}QQQQQQ",),
    "MTLRegionMake1D": (b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}QQ",),
    "MTLSizeMake": (b"{MTLSize=QQQ}QQQ",),
    "MTLPackedFloatQuaternionMake": (b"{MTLPackedFloatQuaternion=ffff}ffff",),
    "MTLRegionMake2D": (b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}QQQQ",),
    "MTLIOFlushAndDestroyCompressionContext": (b"q^v",),
    "MTLPackedFloat3Make": (b"{_MTLPackedFloat3=[3f]}fff",),
    "MTLCopyAllDevicesWithObserver": (
        b"@^@@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                0: {"type_modifier": "o"},
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "@"},
                            2: {"type": "@"},
                        },
                    }
                },
            },
        },
    ),
    "MTLCoordinate2DMake": (b"{MTLSamplePosition=ff}ff",),
    "MTLIndirectCommandBufferExecutionRangeMake": (
        b"{MTLIndirectCommandBufferExecutionRange=II}II",
    ),
    "MTLClearColorMake": (b"{MTLClearColor=dddd}dddd",),
    "MTLCreateSystemDefaultDevice": (
        b"@",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "MTLCopyAllDevices": (b"@", "", {"retval": {"already_cfretained": True}}),
    "MTLIOCreateCompressionContext": (
        b"^v^tqQ",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "MTLIOCompressionContextDefaultChunkSize": (b"Q",),
    "MTLIOCompressionContextAppendData": (
        b"v^v^vQ",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
}
aliases = {
    "MTLFeatureSet_OSX_GPUFamily1_v2": "MTLFeatureSet_macOS_GPUFamily1_v2",
    "MTLArgumentAccessReadOnly": "MTLBindingAccessReadOnly",
    "MTLFeatureSet_OSX_GPUFamily1_v1": "MTLFeatureSet_macOS_GPUFamily1_v1",
    "MTLArgumentAccessReadWrite": "MTLBindingAccessReadWrite",
    "MTLResourceOptionCPUCacheModeDefault": "MTLResourceCPUCacheModeDefaultCache",
    "MTLArgumentAccessWriteOnly": "MTLBindingAccessWriteOnly",
    "MTLFeatureSet_OSX_ReadWriteTextureTier2": "MTLFeatureSet_macOS_ReadWriteTextureTier2",
    "MTLResourceOptionCPUCacheModeWriteCombined": "MTLResourceCPUCacheModeWriteCombined",
    "MTLFeatureSet_TVOS_GPUFamily1_v1": "MTLFeatureSet_tvOS_GPUFamily1_v1",
}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"MTLAccelerationStructureGeometryDescriptor",
        b"allowDuplicateIntersectionFunctionInvocation",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLAccelerationStructureGeometryDescriptor",
        b"opaque",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLAccelerationStructureGeometryDescriptor",
        b"setAllowDuplicateIntersectionFunctionInvocation:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLAccelerationStructureGeometryDescriptor",
        b"setOpaque:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MTLArgument", b"isActive", {"retval": {"type": b"Z"}})
    r(b"MTLArgument", b"isDepthTexture", {"retval": {"type": b"Z"}})
    r(b"MTLAttribute", b"isActive", {"retval": {"type": b"Z"}})
    r(b"MTLAttribute", b"isPatchControlPointData", {"retval": {"type": b"Z"}})
    r(b"MTLAttribute", b"isPatchData", {"retval": {"type": b"Z"}})
    r(b"MTLCaptureManager", b"isCapturing", {"retval": {"type": b"Z"}})
    r(
        b"MTLCaptureManager",
        b"startCaptureWithDescriptor:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"MTLCaptureManager", b"supportsDestination:", {"retval": {"type": b"Z"}})
    r(b"MTLCommandBufferDescriptor", b"retainedReferences", {"retval": {"type": b"Z"}})
    r(
        b"MTLCommandBufferDescriptor",
        b"setRetainedReferences:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLCompileOptions",
        b"allowReferencingUndefinedSymbols",
        {"retval": {"type": b"Z"}},
    )
    r(b"MTLCompileOptions", b"enableLogging", {"retval": {"type": b"Z"}})
    r(b"MTLCompileOptions", b"fastMathEnabled", {"retval": {"type": b"Z"}})
    r(b"MTLCompileOptions", b"preserveInvariance", {"retval": {"type": b"Z"}})
    r(
        b"MTLCompileOptions",
        b"setAllowReferencingUndefinedSymbols:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MTLCompileOptions", b"setEnableLogging:", {"arguments": {2: {"type": b"Z"}}})
    r(b"MTLCompileOptions", b"setFastMathEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MTLCompileOptions",
        b"setPreserveInvariance:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLComputePipelineDescriptor",
        b"setSupportAddingBinaryFunctions:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLComputePipelineDescriptor",
        b"setSupportIndirectCommandBuffers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLComputePipelineDescriptor",
        b"setThreadGroupSizeIsMultipleOfThreadExecutionWidth:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLComputePipelineDescriptor",
        b"supportAddingBinaryFunctions",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLComputePipelineDescriptor",
        b"supportIndirectCommandBuffers",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLComputePipelineDescriptor",
        b"threadGroupSizeIsMultipleOfThreadExecutionWidth",
        {"retval": {"type": b"Z"}},
    )
    r(b"MTLDepthStencilDescriptor", b"isDepthWriteEnabled", {"retval": {"type": b"Z"}})
    r(
        b"MTLDepthStencilDescriptor",
        b"setDepthWriteEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MTLFunctionConstant", b"required", {"retval": {"type": b"Z"}})
    r(
        b"MTLIndirectCommandBufferDescriptor",
        b"inheritBuffers",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLIndirectCommandBufferDescriptor",
        b"inheritPipelineState",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLIndirectCommandBufferDescriptor",
        b"setInheritBuffers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLIndirectCommandBufferDescriptor",
        b"setInheritPipelineState:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLIndirectCommandBufferDescriptor",
        b"setSupportDynamicAttributeStride:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLIndirectCommandBufferDescriptor",
        b"setSupportRayTracing:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLIndirectCommandBufferDescriptor",
        b"supportDynamicAttributeStride",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLIndirectCommandBufferDescriptor",
        b"supportRayTracing",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"isAlphaToCoverageEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"isAlphaToOneEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"isRasterizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"meshThreadgroupSizeIsMultipleOfThreadExecutionWidth",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"objectThreadgroupSizeIsMultipleOfThreadExecutionWidth",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"setAlphaToCoverageEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"setAlphaToOneEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"setMeshThreadgroupSizeIsMultipleOfThreadExecutionWidth:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"setObjectThreadgroupSizeIsMultipleOfThreadExecutionWidth:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"setRasterizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"setSupportIndirectCommandBuffers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLMeshRenderPipelineDescriptor",
        b"supportIndirectCommandBuffers",
        {"retval": {"type": b"Z"}},
    )
    r(b"MTLPointerType", b"elementIsArgumentBuffer", {"retval": {"type": b"Z"}})
    r(
        b"MTLRasterizationRateLayerDescriptor",
        b"horizontalSampleStorage",
        {"retval": {"c_array_of_variable_length": True}},
    )
    r(
        b"MTLRasterizationRateLayerDescriptor",
        b"initWithSampleCount:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MTLRasterizationRateLayerDescriptor",
        b"initWithSampleCount:horizontal:vertical:",
        {
            "arguments": {
                2: {"type": b"{MTLSize=QQQ}"},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 2},
                4: {"type_modifier": b"n", "c_array_length_in_arg": 2},
            }
        },
    )
    r(
        b"MTLRasterizationRateLayerDescriptor",
        b"maxSampleCount",
        {"retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"MTLRasterizationRateLayerDescriptor",
        b"sampleCount",
        {"retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"MTLRasterizationRateLayerDescriptor",
        b"setSampleCount:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MTLRasterizationRateLayerDescriptor",
        b"verticalSampleStorage",
        {"retval": {"c_array_of_variable_length": True}},
    )
    r(
        b"MTLRasterizationRateMapDescriptor",
        b"rasterizationRateMapDescriptorWithScreenSize:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MTLRasterizationRateMapDescriptor",
        b"rasterizationRateMapDescriptorWithScreenSize:layer:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MTLRasterizationRateMapDescriptor",
        b"rasterizationRateMapDescriptorWithScreenSize:layerCount:layers:",
        {"arguments": {4: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"MTLRasterizationRateMapDescriptor",
        b"screenSize",
        {"retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"MTLRasterizationRateMapDescriptor",
        b"setScreenSize:",
        {"arguments": {2: {"type": b"{MTLSize=QQQ}"}}},
    )
    r(
        b"MTLRenderPassColorAttachmentDescriptor",
        b"clearColor",
        {"retval": {"type": b"{MTLClearColor=dddd}"}},
    )
    r(
        b"MTLRenderPassColorAttachmentDescriptor",
        b"setClearColor:",
        {"arguments": {2: {"type": b"{MTLClearColor=dddd}"}}},
    )
    r(
        b"MTLRenderPassDescriptor",
        b"getSamplePositions:count:",
        {
            "arguments": {
                2: {
                    "c_array_length_in_arg": 3,
                    "type": b"^{MTLSamplePosition=ff}",
                    "type_modifier": b"o",
                    "c_array_length_in_result": True,
                }
            }
        },
    )
    r(
        b"MTLRenderPassDescriptor",
        b"setSamplePositions:count:",
        {
            "arguments": {
                2: {
                    "type": b"^{MTLSamplePosition=ff}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"MTLRenderPipelineColorAttachmentDescriptor",
        b"isBlendingEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLRenderPipelineColorAttachmentDescriptor",
        b"setBlendingEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"isAlphaToCoverageEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"isAlphaToOneEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"isRasterizationEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"isTessellationFactorScaleEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"setAlphaToCoverageEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"setAlphaToOneEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"setRasterizationEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"setSupportAddingFragmentBinaryFunctions:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"setSupportAddingVertexBinaryFunctions:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"setSupportIndirectCommandBuffers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"setTessellationFactorScaleEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"supportAddingFragmentBinaryFunctions",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"supportAddingVertexBinaryFunctions",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLRenderPipelineDescriptor",
        b"supportIndirectCommandBuffers",
        {"retval": {"type": b"Z"}},
    )
    r(b"MTLSamplerDescriptor", b"lodAverage", {"retval": {"type": b"Z"}})
    r(b"MTLSamplerDescriptor", b"normalizedCoordinates", {"retval": {"type": b"Z"}})
    r(b"MTLSamplerDescriptor", b"setLodAverage:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"MTLSamplerDescriptor",
        b"setNormalizedCoordinates:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLSamplerDescriptor",
        b"setSupportArgumentBuffers:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"MTLSamplerDescriptor", b"supportArgumentBuffers", {"retval": {"type": b"Z"}})
    r(b"MTLTextureDescriptor", b"allowGPUOptimizedContents", {"retval": {"type": b"Z"}})
    r(
        b"MTLTextureDescriptor",
        b"setAllowGPUOptimizedContents:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLTextureDescriptor",
        b"setSwizzle:",
        {"arguments": {2: {"type": b"{MTLTextureSwizzleChannels=CCCC}"}}},
    )
    r(
        b"MTLTextureDescriptor",
        b"swizzle",
        {"retval": {"type": b"{MTLTextureSwizzleChannels=CCCC}"}},
    )
    r(
        b"MTLTextureDescriptor",
        b"texture2DDescriptorWithPixelFormat:width:height:mipmapped:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"MTLTextureDescriptor",
        b"textureCubeDescriptorWithPixelFormat:size:mipmapped:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(b"MTLTextureReferenceType", b"isDepthTexture", {"retval": {"type": b"Z"}})
    r(
        b"MTLTileRenderPipelineDescriptor",
        b"setSupportAddingBinaryFunctions:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLTileRenderPipelineDescriptor",
        b"setThreadgroupSizeMatchesTileSize:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"MTLTileRenderPipelineDescriptor",
        b"supportAddingBinaryFunctions",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"MTLTileRenderPipelineDescriptor",
        b"threadgroupSizeMatchesTileSize",
        {"retval": {"type": b"Z"}},
    )
    r(b"MTLVertexAttribute", b"isActive", {"retval": {"type": b"Z"}})
    r(b"MTLVertexAttribute", b"isPatchControlPointData", {"retval": {"type": b"Z"}})
    r(b"MTLVertexAttribute", b"isPatchData", {"retval": {"type": b"Z"}})
    r(b"NSObject", b"GPUEndTime", {"required": True, "retval": {"type": b"d"}})
    r(b"NSObject", b"GPUStartTime", {"required": True, "retval": {"type": b"d"}})
    r(b"NSObject", b"URL", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"accelerationStructureCommandEncoder",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"accelerationStructureCommandEncoderWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"accelerationStructureSizesWithDescriptor:",
        {
            "required": True,
            "retval": {"type": b"{MTLAccelerationStructureSizes=QQQ}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"access", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"addAllocation:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"addAllocations:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"addBarrier", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"addCompletedHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"addComputePipelineFunctionsWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"addDebugMarker:range:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"addFunctionWithDescriptor:library:error:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"addLibraryWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"addLogHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"q"},
                            4: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"addMeshRenderPipelineFunctionsWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"addPresentedHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"addRenderPipelineFunctionsWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"addResidencySet:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"addResidencySets:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"addScheduledHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"addTileRenderPipelineFunctionsWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "^@", "type_modifier": b"o"}},
        },
    )
    r(b"NSObject", b"alignment", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"allAllocations", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"allocatedSize", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"allocationCount", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"allowGPUOptimizedContents",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(b"NSObject", b"architecture", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"areBarycentricCoordsSupported",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"areProgrammableSamplePositionsSupported",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"areRasterOrderGroupsSupported",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"argumentBuffersSupport",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(b"NSObject", b"arrayLength", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"beginScope", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"blitCommandEncoder", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"blitCommandEncoderWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"buffer", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"bufferAlignment", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"bufferBytesPerRow", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"bufferDataSize", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"bufferDataType", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"bufferOffset", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"bufferPointerType", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"bufferStructType", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"buildAccelerationStructure:descriptor:scratchBuffer:scratchBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"clearBarrier", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"column", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"commandBuffer", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"commandBufferWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"commandBufferWithUnretainedReferences",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"commandQueue", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"commit", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"compressionType", {"required": True, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"computeCommandEncoder",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"computeCommandEncoderWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"computeCommandEncoderWithDispatchType:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"concurrentDispatchThreadgroups:threadsPerThreadgroup:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": "q"}, 3: {"type": "q"}},
        },
    )
    r(
        b"NSObject",
        b"concurrentDispatchThreads:threadsPerThreadgroup:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": "q"}, 3: {"type": "q"}},
        },
    )
    r(
        b"NSObject",
        b"constantDataAtIndex:",
        {"required": True, "retval": {"type": b"^v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"containsAllocation:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"contents",
        {
            "required": True,
            "retval": {"type": b"^v", "c_array_of_variable_length": True},
        },
    )
    r(
        b"NSObject",
        b"convertSparsePixelRegions:toTileRegions:withTileSize:alignmentMode:numRegions:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "type": "^{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 6,
                },
                3: {
                    "type": "^{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 6,
                },
                4: {"type": "{MTLSize=QQQ}"},
                5: {"type": "Q"},
                6: {"type": "Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"convertSparseTileRegions:toPixelRegions:withTileSize:numRegions:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "type": "^{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 5,
                },
                3: {
                    "type": "^{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 5,
                },
                4: {"type": "{MTLSize=QQQ}"},
                5: {"type": "Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyAccelerationStructure:toAccelerationStructure:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"copyAndCompactAccelerationStructure:toAccelerationStructure:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"copyFromBuffer:sourceOffset:sourceBytesPerRow:sourceBytesPerImage:sourceSize:toTexture:destinationSlice:destinationLevel:destinationOrigin:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
                6: {"type": b"{MTLSize=QQQ}"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
                10: {"type": b"{MTLOrigin=QQQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyFromBuffer:sourceOffset:sourceBytesPerRow:sourceBytesPerImage:sourceSize:toTexture:destinationSlice:destinationLevel:destinationOrigin:options:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
                6: {"type": b"{MTLSize=QQQ}"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
                10: {"type": b"{MTLOrigin=QQQ}"},
                11: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyFromBuffer:sourceOffset:toBuffer:destinationOffset:size:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
                6: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyFromTexture:sourceSlice:sourceLevel:sourceOrigin:sourceSize:toBuffer:destinationOffset:destinationBytesPerRow:destinationBytesPerImage:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"{MTLOrigin=QQQ}"},
                6: {"type": b"{MTLSize=QQQ}"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
                10: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyFromTexture:sourceSlice:sourceLevel:sourceOrigin:sourceSize:toBuffer:destinationOffset:destinationBytesPerRow:destinationBytesPerImage:options:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"{MTLOrigin=QQQ}"},
                6: {"type": b"{MTLSize=QQQ}"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
                10: {"type": b"Q"},
                11: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyFromTexture:sourceSlice:sourceLevel:sourceOrigin:sourceSize:toTexture:destinationSlice:destinationLevel:destinationOrigin:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"{MTLOrigin=QQQ}"},
                6: {"type": b"{MTLSize=QQQ}"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
                10: {"type": b"{MTLOrigin=QQQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyFromTexture:sourceSlice:sourceLevel:toTexture:destinationSlice:destinationLevel:sliceCount:levelCount:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyFromTexture:toTexture:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"copyIndirectCommandBuffer:sourceRange:destination:destinationIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{_NSRange=QQ}"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"copyParameterDataToBuffer:offset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"copyStatusToBuffer:offset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"counterSets", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"counters", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"cpuCacheMode", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"currentAllocatedSize",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(b"NSObject", b"debugLocation", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"debugSignposts", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"depth", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"device", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"didModifyRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"dispatchThreadgroups:threadsPerThreadgroup:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{MTLSize=QQQ}"}, 3: {"type": b"{MTLSize=QQQ}"}},
        },
    )
    r(
        b"NSObject",
        b"dispatchThreadgroupsWithIndirectBuffer:indirectBufferOffset:threadsPerThreadgroup:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"{MTLSize=QQQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"dispatchThreads:threadsPerThreadgroup:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{MTLSize=QQQ}"}, 3: {"type": b"{MTLSize=QQQ}"}},
        },
    )
    r(
        b"NSObject",
        b"dispatchThreadsPerTile:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": "{MTLSize=QQQ}"}},
        },
    )
    r(b"NSObject", b"dispatchType", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"drawIndexedPatches:patchIndexBuffer:patchIndexBufferOffset:controlPointIndexBuffer:controlPointIndexBufferOffset:indirectBuffer:indirectBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"@"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawIndexedPatches:patchStart:patchCount:patchIndexBuffer:patchIndexBufferOffset:controlPointIndexBuffer:controlPointIndexBufferOffset:instanceCount:baseInstance:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
                10: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawIndexedPatches:patchStart:patchCount:patchIndexBuffer:patchIndexBufferOffset:controlPointIndexBuffer:controlPointIndexBufferOffset:instanceCount:baseInstance:tessellationFactorBuffer:tessellationFactorBufferOffset:tessellationFactorBufferInstanceStride:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
                10: {"type": b"Q"},
                11: {"type": b"@"},
                12: {"type": b"Q"},
                13: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawIndexedPrimitives:indexCount:indexType:indexBuffer:indexBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawIndexedPrimitives:indexCount:indexType:indexBuffer:indexBufferOffset:instanceCount:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawIndexedPrimitives:indexCount:indexType:indexBuffer:indexBufferOffset:instanceCount:baseVertex:baseInstance:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
                8: {"type": b"q"},
                9: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawIndexedPrimitives:indexType:indexBuffer:indexBufferOffset:indirectBuffer:indirectBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
                6: {"type": b"@"},
                7: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawMeshThreadgroups:threadsPerObjectThreadgroup:threadsPerMeshThreadgroup:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"{MTLSize=QQQ}"},
                3: {"type": b"{MTLSize=QQQ}"},
                4: {"type": b"{MTLSize=QQQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawMeshThreadgroupsWithIndirectBuffer:indirectBufferOffset:threadsPerObjectThreadgroup:threadsPerMeshThreadgroup:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"{MTLSize=QQQ}"},
                5: {"type": b"{MTLSize=QQQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawMeshThreads:threadsPerObjectThreadgroup:threadsPerMeshThreadgroup:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"{MTLSize=QQQ}"},
                3: {"type": b"{MTLSize=QQQ}"},
                4: {"type": b"{MTLSize=QQQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawPatches:patchIndexBuffer:patchIndexBufferOffset:indirectBuffer:indirectBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"@"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawPatches:patchStart:patchCount:patchIndexBuffer:patchIndexBufferOffset:instanceCount:baseInstance:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
                8: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawPatches:patchStart:patchCount:patchIndexBuffer:patchIndexBufferOffset:instanceCount:baseInstance:tessellationFactorBuffer:tessellationFactorBufferOffset:tessellationFactorBufferInstanceStride:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
                8: {"type": b"Q"},
                9: {"type": b"@"},
                10: {"type": b"Q"},
                11: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawPrimitives:indirectBuffer:indirectBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"@"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"drawPrimitives:vertexStart:vertexCount:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"drawPrimitives:vertexStart:vertexCount:instanceCount:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"drawPrimitives:vertexStart:vertexCount:instanceCount:baseInstance:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
                6: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"drawableID", {"required": True, "retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"encodeSignalEvent:value:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"encodeWaitForEvent:value:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"encodedLength", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"encoderLabel", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"endEncoding", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"endResidency", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"endScope", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"enqueue", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"enqueueBarrier", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"error", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"errorOptions", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"errorState", {"required": True, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"executeCommandsInBuffer:inDirectBuffer:indirectBufferOffset:",
        {"arguments": {4: {"type": "Q"}}},
    )
    r(
        b"NSObject",
        b"executeCommandsInBuffer:indirectBuffer:indirectBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"executeCommandsInBuffer:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"fillBuffer:range:value:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{_NSRange=QQ}"},
                4: {"type": "z"},
            },
        },
    )
    r(b"NSObject", b"firstMipmapInTail", {"required": False, "retval": {"type": "Q"}})
    r(b"NSObject", b"function", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"functionConstantsDictionary",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"functionHandleWithFunction:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"functionHandleWithFunction:stage:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"functionName", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"functionNames", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"functionType", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"generateMipmapsForTexture:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"getBytes:bytesPerRow:bytesPerImage:fromRegion:mipmapLevel:slice:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "type": b"^v",
                    "type_modifier": b"o",
                    "c_array_of_variable_length": True,
                },
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"getBytes:bytesPerRow:fromRegion:mipmapLevel:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "type": b"^v",
                    "type_modifier": b"o",
                    "c_array_of_variable_length": True,
                },
                3: {"type": b"Q"},
                4: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"getDefaultSamplePositions:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "type": b"^{MTLSamplePosition=ff}",
                    "type_modifier": b"o",
                    "c_array_length_in_arg": 3,
                },
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"getTextureAccessCounters:region:mipLevel:slice:resetCounters:countersBuffer:countersBufferOffset:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                4: {"type": "Q"},
                5: {"type": "Q"},
                6: {"type": "Z"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"gpuAddress", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"gpuResourceID",
        {"required": True, "retval": {"type": b"{MTLResourceID=Q}"}},
    )
    r(b"NSObject", b"hasUnifiedMemory", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"hazardTrackingMode", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"heap", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"heapAccelerationStructureSizeAndAlignWithDescriptor:",
        {
            "required": True,
            "retval": {"type": b"{MTLSizeAndAlign=QQ}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"heapAccelerationStructureSizeAndAlignWithSize:",
        {
            "required": True,
            "retval": {"type": b"{MTLSizeAndAlign=QQ}"},
            "arguments": {2: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"heapBufferSizeAndAlignWithLength:options:",
        {
            "required": True,
            "retval": {"type": b"{MTLSizeAndAlign=QQ}"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"heapOffset", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"heapTextureSizeAndAlignWithDescriptor:",
        {
            "required": True,
            "retval": {"type": b"{MTLSizeAndAlign=QQ}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"height", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"imageblockMemoryLengthForDimensions:",
        {
            "required": True,
            "retval": {"type": "q"},
            "arguments": {2: {"type": "{MTLSize=QQQ}"}},
        },
    )
    r(
        b"NSObject",
        b"imageblockSampleLength",
        {"required": True, "retval": {"type": "q"}},
    )
    r(b"NSObject", b"index", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"indirectComputeCommandAtIndex:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"indirectRenderCommandAtIndex:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"insertDebugCaptureBoundary",
        {"required": True, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"insertDebugSignpost:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"installName", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"iosurface",
        {"required": True, "retval": {"type": b"^{__IOSurface=}"}},
    )
    r(b"NSObject", b"iosurfacePlane", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"isAliasable", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isArgument", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"isDepth24Stencil8PixelFormatSupported",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(b"NSObject", b"isDepthTexture", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isFramebufferOnly", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isHeadless", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isLowPower", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isRemovable", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isShareable", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"isSparse", {"required": False, "retval": {"type": "Z"}})
    r(b"NSObject", b"isUsed", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"kernelEndTime", {"required": True, "retval": {"type": b"d"}})
    r(b"NSObject", b"kernelStartTime", {"required": True, "retval": {"type": b"d"}})
    r(b"NSObject", b"label", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"layerCount", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"length", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"line", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"loadBuffer:offset:size:sourceHandle:sourceHandleOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"loadBytes:size:sourceHandle:sourceHandleOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"loadTexture:slice:level:size:sourceBytesPerRow:sourceBytesPerImage:destinationOrigin:sourceHandle:sourceHandleOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"{MTLSize=QQQ}"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
                8: {"type": b"{MTLOrigin=QQQ}"},
                9: {"type": b"@"},
                10: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"location", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"locationNumber", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"logs", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"makeAliasable", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"mapPhysicalToScreenCoordinates:forLayer:",
        {
            "required": True,
            "retval": {"type": b"{MTLSamplePosition=ff}"},
            "arguments": {2: {"type": b"{MTLSamplePosition=ff}"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"mapScreenToPhysicalCoordinates:forLayer:",
        {
            "required": True,
            "retval": {"type": b"{MTLSamplePosition=ff}"},
            "arguments": {2: {"type": b"{MTLSamplePosition=ff}"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"maxArgumentBufferSamplerCount",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"maxAvailableSizeWithAlignment:",
        {"required": True, "retval": {"type": b"Q"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(b"NSObject", b"maxBufferLength", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"maxThreadgroupMemoryLength",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"maxThreadsPerThreadgroup",
        {"required": True, "retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"NSObject",
        b"maxTotalThreadgroupsPerMeshGrid",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"maxTotalThreadsPerMeshThreadgroup",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"maxTotalThreadsPerObjectThreadgroup",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"maxTotalThreadsPerThreadgroup",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(b"NSObject", b"maxTransferRate", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"maximumConcurrentCompilationTaskCount",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"memoryBarrierWithResources:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"memoryBarrierWithResources:count:afterStages:beforeStages:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"memoryBarrierWithScope:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"memoryBarrierWithScope:afterStages:beforeStages:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"meshThreadExecutionWidth",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"minimumLinearTextureAlignmentForPixelFormat:",
        {"required": True, "retval": {"type": b"Q"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"minimumTextureBufferAlignmentForPixelFormat:",
        {"required": True, "retval": {"type": b"Q"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(b"NSObject", b"mipmapLevelCount", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"moveTextureMappingsFromTexture:sourceSlice:sourceLevel:sourceOrigin:sourceSize:toTexture:destinationSlice:destinationLevel:destinationOrigin:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"{MTLOrigin=QQQ}"},
                6: {"type": b"{MTLSize=QQQ}"},
                7: {"type": b"@"},
                8: {"type": b"Q"},
                9: {"type": b"Q"},
                10: {"type": b"{MTLOrigin=QQQ}"},
            },
        },
    )
    r(b"NSObject", b"name", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"newAccelerationStructureWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newAccelerationStructureWithDescriptor:offset:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"newAccelerationStructureWithSize:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"newAccelerationStructureWithSize:offset:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"newArgumentEncoderForBufferAtIndex:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"newArgumentEncoderWithArguments:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newArgumentEncoderWithBufferBinding:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newArgumentEncoderWithBufferIndex:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"newArgumentEncoderWithBufferIndex:reflection:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"^@"}},
        },
    )
    r(
        b"NSObject",
        b"newBinaryArchiveWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newBufferWithBytes:length:options:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"newBufferWithBytesNoCopy:length:options:deallocator:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"n^v"},
                            2: {"type": b"Q"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newBufferWithLength:options:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"newBufferWithLength:options:offset:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"newCommandQueue", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"newCommandQueueWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newCommandQueueWithMaxCommandBufferCount:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"newComputePipelineStateWithAdditionalBinaryFunctions:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newComputePipelineStateWithDescriptor:options:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newComputePipelineStateWithDescriptor:options:reflection:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"^@"},
                5: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newComputePipelineStateWithFunction:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newComputePipelineStateWithFunction:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newComputePipelineStateWithFunction:options:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newComputePipelineStateWithFunction:options:reflection:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"^@"},
                5: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newCounterSampleBufferWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(b"NSObject", b"newDefaultLibrary", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"newDefaultLibraryWithBundle:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newDepthStencilStateWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newDynamicLibrary:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newDynamicLibraryWithURL:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(b"NSObject", b"newEvent", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"newFence", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"newFunctionWithDescriptor:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newFunctionWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newFunctionWithName:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newFunctionWithName:constantValues:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newFunctionWithName:constantValues:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newHeapWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newIOCommandQueueWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newIOFileHandleWithURL:compressionMethod:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newIOFileHandleWithURL:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newIOHandleWithURL:compressionMethod:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newIOHandleWithURL:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newIndirectCommandBufferWithDescriptor:maxCommandCount:options:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"newIntersectionFunctionTableWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newIntersectionFunctionTableWithDescriptor:stage:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"newIntersectionFunctionWithDescriptor:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newIntersectionFunctionWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newLibraryWithData:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newLibraryWithFile:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newLibraryWithSource:options:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newLibraryWithSource:options:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newLibraryWithStitchedDescriptor:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newLibraryWithStitchedDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newLibraryWithURL:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newLogStateWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newRasterizationRateMapWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newRemoteBufferViewForDevice:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newRemoteTextureViewForDevice:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithAdditionalBinaryFunctions:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "o^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithDescriptor:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithDescriptor:options:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithDescriptor:options:reflection:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"^@"},
                5: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithMeshDescriptor:options:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithMeshDescriptor:options:reflection:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"^@"},
                5: {"type": b"^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithTileDescriptor:options:completionHandler:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"newRenderPipelineStateWithTileDescriptor:options:reflection:error:",
        {
            "required": True,
            "retval": {"type": "q"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "Q"},
                4: {"type": b"^@"},
                5: {"type": "^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"newResidencySetWithDescriptor:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"newSamplerStateWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newScratchBufferWithMinimumSize:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(b"NSObject", b"newSharedEvent", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"newSharedEventHandle",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"newSharedEventWithHandle:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newSharedTextureHandle",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"newSharedTextureWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newSharedTextureWithHandle:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newTextureViewWithPixelFormat:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"newTextureViewWithPixelFormat:textureType:levels:slices:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"{_NSRange=QQ}"},
                5: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"newTextureViewWithPixelFormat:textureType:levels:slices:swizzle:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"{_NSRange=QQ}"},
                5: {"type": b"{_NSRange=QQ}"},
                6: {"type": b"{MTLTextureSwizzleChannels=CCCC}"},
            },
        },
    )
    r(
        b"NSObject",
        b"newTextureWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newTextureWithDescriptor:iosurface:plane:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"^{__IOSurface=}"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"newTextureWithDescriptor:offset:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"newTextureWithDescriptor:offset:bytesPerRow:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"newVisibleFunctionTableWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newVisibleFunctionTableWithDescriptor:stage:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"notifyListener:atValue:block:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"objectPayloadAlignment",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"objectPayloadDataSize",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"objectThreadExecutionWidth",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"optimizeContentsForCPUAccess:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"optimizeContentsForCPUAccess:slice:level:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"optimizeContentsForGPUAccess:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"optimizeContentsForGPUAccess:slice:level:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"optimizeIndirectCommandBuffer:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(b"NSObject", b"options", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"parallelRenderCommandEncoderWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"parameterBufferSizeAndAlign",
        {"required": True, "retval": {"type": b"{MTLSizeAndAlign=QQ}"}},
    )
    r(b"NSObject", b"parentRelativeLevel", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"parentRelativeSlice", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"parentTexture", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"parseTileSizeInBytes", {"retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"patchControlPointCount",
        {"required": True, "retval": {"type": b"q"}},
    )
    r(b"NSObject", b"patchType", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"peerCount", {"required": True, "retval": {"type": b"I"}})
    r(b"NSObject", b"peerGroupID", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"peerIndex", {"required": True, "retval": {"type": b"I"}})
    r(
        b"NSObject",
        b"physicalGranularity",
        {"required": True, "retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"NSObject",
        b"physicalSizeForLayer:",
        {
            "required": True,
            "retval": {"type": b"{MTLSize=QQQ}"},
            "arguments": {2: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"pixelFormat", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"popDebugGroup", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"present", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"presentAfterMinimumDuration:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": "d"}}},
    )
    r(
        b"NSObject",
        b"presentAtTime:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"d"}}},
    )
    r(
        b"NSObject",
        b"presentDrawable:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentDrawable:afterMinimumDuration:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"d"}},
        },
    )
    r(
        b"NSObject",
        b"presentDrawable:atTime:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"d"}},
        },
    )
    r(b"NSObject", b"presentedTime", {"required": True, "retval": {"type": "d"}})
    r(
        b"NSObject",
        b"pushDebugGroup:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"readWriteTextureSupport",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"recommendedMaxWorkingSetSize",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"refitAccelerationStructure:descriptor:destination:scratchBuffer:scratchBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"refitAccelerationStructure:descriptor:destination:scratchBuffer:scratchBufferOffset:options:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"registryID", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"remoteStorageBuffer", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"remoteStorageTexture",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"removeAllAllocations",
        {"required": True, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"removeAllDebugMarkers",
        {"required": True, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"removeAllocation:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"removeAllocations:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"removeResidencySet:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"removeResidencySets:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"renderCommandEncoder",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"renderCommandEncoderWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"replaceRegion:mipmapLevel:slice:withBytes:bytesPerRow:bytesPerImage:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {
                    "type": b"^v",
                    "type_modifier": b"n",
                    "c_array_of_variable_length": True,
                },
                6: {"type": b"Q"},
                7: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"replaceRegion:mipmapLevel:withBytes:bytesPerRow:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                3: {"type": b"Q"},
                4: {
                    "type": b"^v",
                    "type_modifier": b"n",
                    "c_array_of_variable_length": True,
                },
                5: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"requestResidency", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"reset", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"resetCommandsInBuffer:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"resetTextureAccessCounters:region:mipLevel:slice:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                4: {"type": "Q"},
                5: {"type": "Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"resetWithRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"resolveCounterRange:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"resolveCounters:inRange:destinationBuffer:destinationOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"{_NSRange=QQ}"},
                4: {"type": b"@"},
                5: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"resourceOptions", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"resourceStateCommandEncoder",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"resourceStateCommandEncoderWithDescriptor:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"retainedReferences", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"rootResource", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"sampleCount", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"sampleCountersInBuffer:atSampleIndex:withBarrier:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Z"}},
        },
    )
    r(
        b"NSObject",
        b"sampleTimestamps:gpuTimestamp:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^Q", "type_modifier": b"o"},
                3: {"type": b"^Q", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"screenSize",
        {"required": True, "retval": {"type": b"{MTLSize=QQQ}"}},
    )
    r(
        b"NSObject",
        b"serializeToURL:error:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^@", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSObject",
        b"setAccelerationStructure:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setAccelerationStructure:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setArgumentBuffer:offset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setArgumentBuffer:startOffset:arrayElement:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"setBarrier", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"setBlendColorRed:green:blue:alpha:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"f"},
                3: {"type": b"f"},
                4: {"type": b"f"},
                5: {"type": b"f"},
            },
        },
    )
    r(
        b"NSObject",
        b"setBuffer:offset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setBuffer:offset:attributeStride:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setBufferOffset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setBufferOffset:attributeStride:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setBuffers:offsets:attributeStrides:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 5},
                3: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 5},
                4: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 5},
                5: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setBuffers:offsets:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setBytes:length:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setBytes:length:attributeStride:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setColorStoreAction:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setColorStoreActionOptions:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setComputePipelineState:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setComputePipelineState:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setComputePipelineStates:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setCullMode:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setDepthBias:slopeScale:clamp:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"f"}, 3: {"type": b"f"}, 4: {"type": b"f"}},
        },
    )
    r(
        b"NSObject",
        b"setDepthClipMode:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setDepthStencilState:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setDepthStoreAction:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setDepthStoreActionOptions:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setFragmentAccelerationStructure:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setFragmentBuffer:offset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setFragmentBufferOffset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setFragmentBuffers:offsets:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setFragmentBytes:length:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setFragmentIntersectionFunctionTable:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setFragmentIntersectionFunctionTables:withBufferRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setFragmentSamplerState:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setFragmentSamplerState:lodMinClamp:lodMaxClamp:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"f"},
                4: {"type": b"f"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setFragmentSamplerStates:lodMinClamps:lodMaxClamps:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 5},
                3: {"type": b"^f", "type_modifier": b"n", "c_array_length_in_arg": 5},
                4: {"type": b"^f", "type_modifier": b"n", "c_array_length_in_arg": 5},
                5: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setFragmentSamplerStates:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setFragmentTexture:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setFragmentTextures:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setFragmentVisibleFunctionTable:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setFragmentVisibleFunctionTables:withBufferRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setFrontFacingWinding:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setFunction:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setFunctions:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setImageblockWidth:height:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": "q"}, 3: {"type": "q"}},
        },
    )
    r(
        b"NSObject",
        b"setIndirectCommandBuffer:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setIndirectCommandBuffers:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setIntersectionFunctionTable:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setIntersectionFunctionTable:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setIntersectionFunctionTables:withBufferRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setIntersectionFunctionTables:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setKernelBuffer:offset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "q"}, 4: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"setKernelBuffer:offset:attributeStride:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setLabel:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setMeshBuffer:offset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setMeshBufferOffset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setMeshBuffers:offsets:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setMeshBytes:length:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setMeshSamplerState:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setMeshSamplerState:lodMinClamp:lodMaxClamp:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"f"},
                4: {"type": b"f"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setMeshSamplerStates:lodMinClamps:lodMaxClamps:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 5},
                3: {"type": b"^f", "type_modifier": b"n", "c_array_length_in_arg": 5},
                4: {"type": b"^f", "type_modifier": b"n", "c_array_length_in_arg": 5},
                5: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setMeshSamplerStates:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setMeshTexture:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setMeshTextures:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setObjectBuffer:offset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setObjectBufferOffset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setObjectBuffers:offsets:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setObjectBytes:length:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setObjectSamplerState:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setObjectSamplerState:lodMinClamp:lodMaxClamp:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"f"},
                4: {"type": b"f"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setObjectSamplerStates:lodMinClamps:lodMaxClamps:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@"},
                3: {"type": b"^f"},
                4: {"type": b"^f"},
                5: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setObjectSamplerStates:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setObjectTexture:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setObjectTextures:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setObjectThreadgroupMemoryLength:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setOpaqueCurveIntersectionFunctionWithSignature:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setOpaqueCurveIntersectionFunctionWithSignature:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"setOpaqueTriangleIntersectionFunctionWithSignature:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setOpaqueTriangleIntersectionFunctionWithSignature:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSObject",
        b"setOwnerWithIdentity:",
        {"required": True, "retval": {"type": b"i"}, "arguments": {2: {"type": b"I"}}},
    )
    r(
        b"NSObject",
        b"setPurgeableState:",
        {"required": True, "retval": {"type": b"Q"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setRenderPipelineState:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setRenderPipelineState:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setRenderPipelineStates:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setSamplerState:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setSamplerState:lodMinClamp:lodMaxClamp:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"f"},
                4: {"type": b"f"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setSamplerStates:lodMinClamps:lodMaxClamps:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 5},
                3: {"type": b"^f", "type_modifier": b"n", "c_array_length_in_arg": 5},
                4: {"type": b"^f", "type_modifier": b"n", "c_array_length_in_arg": 5},
                5: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setSamplerStates:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setScissorRect:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{MTLScissorRect=QQQQ}"}},
        },
    )
    r(
        b"NSObject",
        b"setScissorRects:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "type": b"^{MTLScissorRect=QQQQ}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                },
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setShouldMaximizeConcurrentCompilation:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSObject",
        b"setSignaledValue:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setStageInRegion:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"}},
        },
    )
    r(
        b"NSObject",
        b"setStageInRegionWithIndirectBuffer:indirectBufferOffset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setStencilFrontReferenceValue:backReferenceValue:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"I"}, 3: {"type": b"I"}},
        },
    )
    r(
        b"NSObject",
        b"setStencilReferenceValue:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"I"}}},
    )
    r(
        b"NSObject",
        b"setStencilStoreAction:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setStencilStoreActionOptions:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setTessellationFactorBuffer:offset:instanceStride:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTessellationFactorScale:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(
        b"NSObject",
        b"setTexture:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTextures:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setThreadgroupMemoryLength:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setThreadgroupMemoryLength:offset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": "Q"}, 3: {"type": "Q"}, 4: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTileAccelerationStructure:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTileBuffer:offset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}, 4: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTileBufferOffset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": "Q"}, 3: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTileBuffers:offsets:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": "^@", "type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type": "^Q", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": "{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setTileBytes:length:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": "Q"},
                4: {"type": "Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setTileIntersectionFunctionTable:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTileIntersectionFunctionTables:withBufferRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setTileSamplerState:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTileSamplerState:lodMinClamp:lodMaxClamp:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "Q"},
                4: {"type": "Q"},
                5: {"type": "Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setTileSamplerStates:lodMinClamps:lodMaxClamps:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": "^@", "type_modifier": b"n", "c_array_length_in_arg": 5},
                3: {"type": "^Q", "type_modifier": b"n", "c_array_length_in_arg": 5},
                4: {"type": "^Q", "type_modifier": b"n", "c_array_length_in_arg": 5},
                5: {"type": "{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setTileSamplerStates:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": "^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": "{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setTileTexture:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTileTextures:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": "^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": "{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setTileVisibleFunctionTable:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setTileVisibleFunctionTables:withBufferRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setTriangleFillMode:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"setVertexAccelerationStructure:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVertexAmplificationCount:viewMapping:",
        {
            "arguments": {
                2: {"type": "Q"},
                3: {"type": "{MTLVertexAmplificationViewMapping=II}"},
            }
        },
    )
    r(
        b"NSObject",
        b"setVertexAmplificationCount:viewMappings:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"^{MTLVertexAmplificationViewMapping=II}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexBuffer:offset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVertexBuffer:offset:attributeStride:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexBufferOffset:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVertexBufferOffset:attributeStride:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVertexBuffers:offsets:attributeStrides:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 5},
                3: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 5},
                4: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 5},
                5: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexBuffers:offsets:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type": b"^Q", "type_modifier": b"n", "c_array_length_in_arg": 4},
                4: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexBytes:length:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexBytes:length:attributeStride:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexIntersectionFunctionTable:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVertexIntersectionFunctionTables:withBufferRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexSamplerState:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVertexSamplerState:lodMinClamp:lodMaxClamp:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"f"},
                4: {"type": b"f"},
                5: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexSamplerStates:lodMinClamps:lodMaxClamps:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 5},
                3: {"type": b"^f", "type_modifier": b"n", "c_array_length_in_arg": 5},
                4: {"type": b"^f", "type_modifier": b"n", "c_array_length_in_arg": 5},
                5: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexSamplerStates:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexTexture:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVertexTextures:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVertexVisibleFunctionTable:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVertexVisibleFunctionTables:withBufferRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setViewport:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"{MTLViewport=dddddd}"}},
        },
    )
    r(
        b"NSObject",
        b"setViewports:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "type": b"^{MTLViewport=dddddd}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                },
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVisibilityResultMode:offset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVisibleFunctionTable:atBufferIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVisibleFunctionTable:atIndex:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"setVisibleFunctionTables:withBufferRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSObject",
        b"setVisibleFunctionTables:withRange:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"{_NSRange=QQ}"},
            },
        },
    )
    r(b"NSObject", b"shaderValidation", {"required": True, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"shouldMaximizeConcurrentCompilation",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"signalEvent:value:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"signaledValue", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"size", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"sparseTileSizeInBytes",
        {"required": True, "retval": {"type": "Q"}},
    )
    r(
        b"NSObject",
        b"sparseTileSizeInBytesForSparsePageSize:",
        {"required": True, "retval": {"type": b"Q"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"sparseTileSizeWithTextureType:pixelFormat:sampleCount:",
        {
            "required": True,
            "retval": {"type": b"{MTLSize=QQQ}"},
            "arguments": {2: {"type": "Q"}, 3: {"type": "Q"}, 4: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"sparseTileSizeWithTextureType:pixelFormat:sampleCount:sparsePageSize:",
        {
            "required": True,
            "retval": {"type": b"{MTLSize=QQQ}"},
            "arguments": {
                2: {"type": b"Q"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"sparseTileSizeWithTextureType_pixelFormat:sampleCount:",
        {"arguments": {2: {"type": "Q"}, 3: {"type": "Q"}, 4: {"type": "Q"}}},
    )
    r(
        b"NSObject",
        b"stageInputAttributes",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"staticThreadgroupMemoryLength",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(b"NSObject", b"status", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"storageMode", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"supportIndirectCommandBuffers",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supports32BitFloatFiltering",
        {"required": True, "retval": {"type": "Z"}},
    )
    r(b"NSObject", b"supports32BitMSAA", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"supportsBCTextureCompression",
        {"required": True, "retval": {"type": "Z"}},
    )
    r(
        b"NSObject",
        b"supportsCounterSampling:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"supportsDynamicLibraries",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsFamily:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"q"}}},
    )
    r(
        b"NSObject",
        b"supportsFeatureSet:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"supportsFunctionPointers",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsFunctionPointersFromRender",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsPrimitiveMotionBlur",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsPullModelInterpolation",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsQueryTextureLOD",
        {"required": True, "retval": {"type": "Z"}},
    )
    r(
        b"NSObject",
        b"supportsRasterizationRateMapWithLayerCount:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": "Q"}}},
    )
    r(b"NSObject", b"supportsRaytracing", {"required": True, "retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"supportsRaytracingFromRender",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsRenderDynamicLibraries",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsShaderBarycentricCoordinates",
        {"required": True, "retval": {"type": b"Z"}},
    )
    r(
        b"NSObject",
        b"supportsTextureSampleCount:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"Q"}}},
    )
    r(
        b"NSObject",
        b"supportsVertexAmplificationCount:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": "Q"}}},
    )
    r(
        b"NSObject",
        b"swizzle",
        {"required": True, "retval": {"type": b"{MTLTextureSwizzleChannels=CCCC}"}},
    )
    r(
        b"NSObject",
        b"synchronizeResource:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"synchronizeTexture:slice:level:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"tailSizeInBytes", {"required": False, "retval": {"type": "Q"}})
    r(b"NSObject", b"textureBarrier", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"textureDataType", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"textureType", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"threadExecutionWidth",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"threadgroupMemoryAlignment",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"threadgroupMemoryDataSize",
        {"required": True, "retval": {"type": b"Q"}},
    )
    r(
        b"NSObject",
        b"threadgroupSizeMatchesTileSize",
        {"required": True, "retval": {"type": "Z"}},
    )
    r(b"NSObject", b"tileHeight", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"tileWidth", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"tryCancel", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"type", {"required": True, "retval": {"type": b"q"}})
    r(
        b"NSObject",
        b"updateFence:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"updateFence:afterStages:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"updateTextureMapping:mode:indirectBuffer:indirectBufferOffset:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "Q"},
                4: {"type": b"@"},
                5: {"type": "Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"updateTextureMapping:mode:region:mipLevel:slice:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "Q"},
                4: {"type": "{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}"},
                5: {"type": "Q"},
                6: {"type": "Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"updateTextureMappings:mode:regions:mipLevels:slices:numRegions:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "Q"},
                4: {
                    "type": "^{MTLRegion={MTLOrigin=QQQ}{MTLSize=QQQ}}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 7,
                },
                5: {"type": "^Q", "type_modifier": b"n", "c_array_length_in_arg": 7},
                6: {"type": "^Q", "type_modifier": b"n", "c_array_length_in_arg": 7},
                7: {"type": "Q"},
            },
        },
    )
    r(b"NSObject", b"usage", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"useHeap:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"useHeap:stages:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"useHeaps:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"useHeaps:count:stages:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"useResidencySet:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"useResidencySets:count:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"useResource:usage:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"useResource:usage:stages:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"useResources:count:usage:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@", "type_modifier": b"n", "c_array_length_in_arg": 3},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
            },
        },
    )
    r(
        b"NSObject",
        b"useResources:count:usage:stages:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"^@"},
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(b"NSObject", b"usedSize", {"required": True, "retval": {"type": b"Q"}})
    r(b"NSObject", b"vertexAttributes", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"waitForEvent:value:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"waitForFence:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"waitForFence:beforeStages:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"waitUntilCompleted", {"required": True, "retval": {"type": b"v"}})
    r(b"NSObject", b"waitUntilScheduled", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"waitUntilSignaledValue:timeoutMS:",
        {
            "required": True,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"Q"}, 3: {"type": b"Q"}},
        },
    )
    r(b"NSObject", b"width", {"required": True, "retval": {"type": b"Q"}})
    r(
        b"NSObject",
        b"writeCompactedAccelerationStructureSize:toBuffer:offset:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"Q"}},
        },
    )
    r(
        b"NSObject",
        b"writeCompactedAccelerationStructureSize:toBuffer:offset:sizeDataType:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
            },
        },
    )
    r(b"NSProcessInfo", b"hasPerformanceProfile:", {"retval": {"type": b"Z"}})
    r(b"NSProcessInfo", b"isDeviceCertifiedFor:", {"retval": {"type": b"Z"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "MTLFunctionStitchingFunctionNode", b"initWithName:arguments:controlDependencies:"
)
objc.registerNewKeywordsFromSelector(
    "MTLFunctionStitchingGraph", b"initWithFunctionName:nodes:outputNode:attributes:"
)
objc.registerNewKeywordsFromSelector(
    "MTLFunctionStitchingInputNode", b"initWithArgumentIndex:"
)
objc.registerNewKeywordsFromSelector(
    "MTLRasterizationRateLayerDescriptor", b"initWithSampleCount:"
)
objc.registerNewKeywordsFromSelector(
    "MTLRasterizationRateLayerDescriptor", b"initWithSampleCount:horizontal:vertical:"
)
objc.registerNewKeywordsFromSelector(
    "MTLSharedEventListener", b"initWithDispatchQueue:"
)
expressions = {
    "MTLResourceHazardTrackingModeMask": "0x3 << MTLResourceHazardTrackingModeShift",
    "MTLResourceCPUCacheModeMask": "0xf << MTLResourceCPUCacheModeShift",
    "MTLBufferLayoutStrideDynamic": "NSUIntegerMax",
    "MTLAttributeStrideStatic": "NSUIntegerMax",
    "MTLTextureSwizzleChannelsDefault": "MTLTextureSwizzleChannelsMake(MTLTextureSwizzleRed, MTLTextureSwizzleGreen, MTLTextureSwizzleBlue, MTLTextureSwizzleAlpha)",
    "MTLResourceStorageModeMask": "0xf << MTLResourceStorageModeShift",
}

# END OF FILE
