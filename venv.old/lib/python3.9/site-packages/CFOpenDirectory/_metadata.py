# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 12:08:25 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$kODAttributeTypeAccessControlEntry$kODAttributeTypeAddressLine1$kODAttributeTypeAddressLine2$kODAttributeTypeAddressLine3$kODAttributeTypeAdminLimits$kODAttributeTypeAdvertisedServices$kODAttributeTypeAlias$kODAttributeTypeAllAttributes$kODAttributeTypeAllTypes$kODAttributeTypeAltSecurityIdentities$kODAttributeTypeAreaCode$kODAttributeTypeAttrListRefCount$kODAttributeTypeAttrListRefs$kODAttributeTypeAttrListValueRefCount$kODAttributeTypeAttrListValueRefs$kODAttributeTypeAuthCredential$kODAttributeTypeAuthMethod$kODAttributeTypeAuthenticationAuthority$kODAttributeTypeAuthenticationHint$kODAttributeTypeAuthorityRevocationList$kODAttributeTypeAutomaticSearchPath$kODAttributeTypeAutomountInformation$kODAttributeTypeBirthday$kODAttributeTypeBootParams$kODAttributeTypeBuildVersion$kODAttributeTypeBuilding$kODAttributeTypeCACertificate$kODAttributeTypeCapacity$kODAttributeTypeCertificateRevocationList$kODAttributeTypeCity$kODAttributeTypeComment$kODAttributeTypeCompany$kODAttributeTypeComputers$kODAttributeTypeConfigAvailable$kODAttributeTypeConfigFile$kODAttributeTypeContactGUID$kODAttributeTypeContactPerson$kODAttributeTypeCopyTimestamp$kODAttributeTypeCoreFWVersion$kODAttributeTypeCountry$kODAttributeTypeCreationTimestamp$kODAttributeTypeCrossCertificatePair$kODAttributeTypeCustomSearchPath$kODAttributeTypeDNSDomain$kODAttributeTypeDNSName$kODAttributeTypeDNSNameServer$kODAttributeTypeDataStamp$kODAttributeTypeDateRecordCreated$kODAttributeTypeDepartment$kODAttributeTypeDirRefCount$kODAttributeTypeDirRefs$kODAttributeTypeEMailAddress$kODAttributeTypeEMailContacts$kODAttributeTypeENetAddress$kODAttributeTypeExpire$kODAttributeTypeFWVersion$kODAttributeTypeFaxNumber$kODAttributeTypeFirstName$kODAttributeTypeFullName$kODAttributeTypeFunctionalState$kODAttributeTypeGUID$kODAttributeTypeGroup$kODAttributeTypeGroupMembers$kODAttributeTypeGroupMembership$kODAttributeTypeGroupServices$kODAttributeTypeHTML$kODAttributeTypeHardwareUUID$kODAttributeTypeHomeDirectory$kODAttributeTypeHomeDirectoryQuota$kODAttributeTypeHomeDirectorySoftQuota$kODAttributeTypeHomeLocOwner$kODAttributeTypeHomePhoneNumber$kODAttributeTypeIMHandle$kODAttributeTypeIPAddress$kODAttributeTypeIPAddressAndENetAddress$kODAttributeTypeIPv6Address$kODAttributeTypeInternetAlias$kODAttributeTypeJPEGPhoto$kODAttributeTypeJobTitle$kODAttributeTypeKDCAuthKey$kODAttributeTypeKDCConfigData$kODAttributeTypeKerberosRealm$kODAttributeTypeKerberosServices$kODAttributeTypeKeywords$kODAttributeTypeLDAPReadReplicas$kODAttributeTypeLDAPSearchBaseSuffix$kODAttributeTypeLDAPWriteReplicas$kODAttributeTypeLastName$kODAttributeTypeLocalOnlySearchPath$kODAttributeTypeLocaleRelay$kODAttributeTypeLocaleSubnets$kODAttributeTypeLocation$kODAttributeTypeMCXFlags$kODAttributeTypeMCXSettings$kODAttributeTypeMIME$kODAttributeTypeMailAttribute$kODAttributeTypeMapCoordinates$kODAttributeTypeMapGUID$kODAttributeTypeMapURI$kODAttributeTypeMetaAmbiguousName$kODAttributeTypeMetaAugmentedAttributes$kODAttributeTypeMetaAutomountMap$kODAttributeTypeMetaNodeLocation$kODAttributeTypeMetaRecordName$kODAttributeTypeMiddleName$kODAttributeTypeMobileNumber$kODAttributeTypeModificationTimestamp$kODAttributeTypeNFSHomeDirectory$kODAttributeTypeNTDomainComputerAccount$kODAttributeTypeNamePrefix$kODAttributeTypeNameSuffix$kODAttributeTypeNativeOnly$kODAttributeTypeNestedGroups$kODAttributeTypeNetGroupTriplet$kODAttributeTypeNetGroups$kODAttributeTypeNetworkInterfaces$kODAttributeTypeNetworkNumber$kODAttributeTypeNickName$kODAttributeTypeNodeOptions$kODAttributeTypeNodePath$kODAttributeTypeNodeRefCount$kODAttributeTypeNodeRefs$kODAttributeTypeNodeSASLRealm$kODAttributeTypeNote$kODAttributeTypeNumTableList$kODAttributeTypeOperatingSystem$kODAttributeTypeOperatingSystemVersion$kODAttributeTypeOrganizationInfo$kODAttributeTypeOrganizationName$kODAttributeTypeOriginalHomeDirectory$kODAttributeTypeOriginalNFSHomeDirectory$kODAttributeTypeOriginalNodeName$kODAttributeTypeOwner$kODAttributeTypeOwnerGUID$kODAttributeTypePGPPublicKey$kODAttributeTypePIDValue$kODAttributeTypePagerNumber$kODAttributeTypeParentLocales$kODAttributeTypePassword$kODAttributeTypePasswordPlus$kODAttributeTypePasswordPolicyOptions$kODAttributeTypePasswordServerList$kODAttributeTypePasswordServerLocation$kODAttributeTypePhoneContacts$kODAttributeTypePhoneNumber$kODAttributeTypePicture$kODAttributeTypePlugInInfo$kODAttributeTypePluginIndex$kODAttributeTypePort$kODAttributeTypePostalAddress$kODAttributeTypePostalAddressContacts$kODAttributeTypePostalCode$kODAttributeTypePresetUserIsAdmin$kODAttributeTypePrimaryComputerGUID$kODAttributeTypePrimaryComputerList$kODAttributeTypePrimaryGroupID$kODAttributeTypePrimaryLocale$kODAttributeTypePrimaryNTDomain$kODAttributeTypePrintServiceInfoText$kODAttributeTypePrintServiceInfoXML$kODAttributeTypePrintServiceUserData$kODAttributeTypePrinter1284DeviceID$kODAttributeTypePrinterLPRHost$kODAttributeTypePrinterLPRQueue$kODAttributeTypePrinterMakeAndModel$kODAttributeTypePrinterType$kODAttributeTypePrinterURI$kODAttributeTypePrinterXRISupported$kODAttributeTypeProcessName$kODAttributeTypeProfiles$kODAttributeTypeProfilesTimestamp$kODAttributeTypeProtocolNumber$kODAttributeTypeProtocols$kODAttributeTypePwdAgingPolicy$kODAttributeTypeRPCNumber$kODAttributeTypeReadOnlyNode$kODAttributeTypeRealUserID$kODAttributeTypeRecRefCount$kODAttributeTypeRecRefs$kODAttributeTypeRecordName$kODAttributeTypeRecordType$kODAttributeTypeRelationships$kODAttributeTypeRelativeDNPrefix$kODAttributeTypeResourceInfo$kODAttributeTypeResourceType$kODAttributeTypeSMBAcctFlags$kODAttributeTypeSMBGroupRID$kODAttributeTypeSMBHome$kODAttributeTypeSMBHomeDrive$kODAttributeTypeSMBKickoffTime$kODAttributeTypeSMBLogoffTime$kODAttributeTypeSMBLogonTime$kODAttributeTypeSMBPWDLastSet$kODAttributeTypeSMBPrimaryGroupSID$kODAttributeTypeSMBProfilePath$kODAttributeTypeSMBRID$kODAttributeTypeSMBSID$kODAttributeTypeSMBScriptPath$kODAttributeTypeSMBUserWorkstations$kODAttributeTypeSchema$kODAttributeTypeSearchPath$kODAttributeTypeSearchPolicy$kODAttributeTypeServiceType$kODAttributeTypeServicesLocator$kODAttributeTypeSetupAdvertising$kODAttributeTypeSetupAutoRegister$kODAttributeTypeSetupLocation$kODAttributeTypeSetupOccupation$kODAttributeTypeStandardOnly$kODAttributeTypeState$kODAttributeTypeStreet$kODAttributeTypeSubNodes$kODAttributeTypeTimePackage$kODAttributeTypeTimeToLive$kODAttributeTypeTotalRefCount$kODAttributeTypeTotalSize$kODAttributeTypeTrustInformation$kODAttributeTypeURL$kODAttributeTypeUniqueID$kODAttributeTypeUserCertificate$kODAttributeTypeUserPKCS12Data$kODAttributeTypeUserSMIMECertificate$kODAttributeTypeUserShell$kODAttributeTypeVFSDumpFreq$kODAttributeTypeVFSLinkDir$kODAttributeTypeVFSOpts$kODAttributeTypeVFSPassNo$kODAttributeTypeVFSType$kODAttributeTypeVersion$kODAttributeTypeWeblogURI$kODAttributeTypeXMLPlist$kODAuthenticationType2WayRandom$kODAuthenticationType2WayRandomChangePasswd$kODAuthenticationTypeAPOP$kODAuthenticationTypeCRAM_MD5$kODAuthenticationTypeChangePasswd$kODAuthenticationTypeClearText$kODAuthenticationTypeClearTextReadOnly$kODAuthenticationTypeCrypt$kODAuthenticationTypeDIGEST_MD5$kODAuthenticationTypeDeleteUser$kODAuthenticationTypeGetEffectivePolicy$kODAuthenticationTypeGetGlobalPolicy$kODAuthenticationTypeGetKerberosPrincipal$kODAuthenticationTypeGetPolicy$kODAuthenticationTypeGetUserData$kODAuthenticationTypeGetUserName$kODAuthenticationTypeKerberosTickets$kODAuthenticationTypeMPPEMasterKeys$kODAuthenticationTypeMPPEPrimaryKeys$kODAuthenticationTypeMSCHAP2$kODAuthenticationTypeNTLMv2$kODAuthenticationTypeNTLMv2WithSessionKey$kODAuthenticationTypeNewUser$kODAuthenticationTypeNewUserWithPolicy$kODAuthenticationTypeNodeNativeClearTextOK$kODAuthenticationTypeNodeNativeNoClearText$kODAuthenticationTypeReadSecureHash$kODAuthenticationTypeSMBNTv2UserSessionKey$kODAuthenticationTypeSMBWorkstationCredentialSessionKey$kODAuthenticationTypeSMB_LM_Key$kODAuthenticationTypeSMB_NT_Key$kODAuthenticationTypeSMB_NT_UserSessionKey$kODAuthenticationTypeSMB_NT_WithUserSessionKey$kODAuthenticationTypeSecureHash$kODAuthenticationTypeSetCertificateHashAsCurrent$kODAuthenticationTypeSetGlobalPolicy$kODAuthenticationTypeSetLMHash$kODAuthenticationTypeSetNTHash$kODAuthenticationTypeSetPassword$kODAuthenticationTypeSetPasswordAsCurrent$kODAuthenticationTypeSetPolicy$kODAuthenticationTypeSetPolicyAsCurrent$kODAuthenticationTypeSetUserData$kODAuthenticationTypeSetUserName$kODAuthenticationTypeSetWorkstationPassword$kODAuthenticationTypeWithAuthorizationRef$kODAuthenticationTypeWriteSecureHash$kODBackOffSeconds$kODErrorDomainFramework$kODModuleConfigOptionConnectionIdleDisconnect$kODModuleConfigOptionConnectionSetupTimeout$kODModuleConfigOptionManInTheMiddle$kODModuleConfigOptionPacketEncryption$kODModuleConfigOptionPacketSigning$kODModuleConfigOptionQueryTimeout$kODNodeOptionsQuerySkippedSubnode$kODPolicyAttributeCreationTime$kODPolicyAttributeCurrentDate$kODPolicyAttributeCurrentDayOfWeek$kODPolicyAttributeCurrentTime$kODPolicyAttributeCurrentTimeOfDay$kODPolicyAttributeDaysUntilExpiration$kODPolicyAttributeEnableAtTimeOfDay$kODPolicyAttributeEnableOnDate$kODPolicyAttributeEnableOnDayOfWeek$kODPolicyAttributeExpiresAtTimeOfDay$kODPolicyAttributeExpiresEveryNDays$kODPolicyAttributeExpiresOnDate$kODPolicyAttributeExpiresOnDayOfWeek$kODPolicyAttributeFailedAuthentications$kODPolicyAttributeLastAuthenticationTime$kODPolicyAttributeLastFailedAuthenticationTime$kODPolicyAttributeLastPasswordChangeTime$kODPolicyAttributeMaximumFailedAuthentications$kODPolicyAttributeNewPasswordRequiredTime$kODPolicyAttributePassword$kODPolicyAttributePasswordHashes$kODPolicyAttributePasswordHistory$kODPolicyAttributePasswordHistoryDepth$kODPolicyAttributeRecordName$kODPolicyAttributeRecordType$kODPolicyCategoryAuthentication$kODPolicyCategoryPasswordChange$kODPolicyCategoryPasswordContent$kODPolicyKeyContent$kODPolicyKeyContentDescription$kODPolicyKeyEvaluationDetails$kODPolicyKeyIdentifier$kODPolicyKeyParameters$kODPolicyKeyPolicySatisfied$kODPolicyTypeAccountExpiresOnDate$kODPolicyTypeAccountMaximumFailedLogins$kODPolicyTypeAccountMaximumMinutesOfNonUse$kODPolicyTypeAccountMaximumMinutesUntilDisabled$kODPolicyTypeAccountMinutesUntilFailedLoginReset$kODPolicyTypePasswordCannotBeAccountName$kODPolicyTypePasswordChangeRequired$kODPolicyTypePasswordHistory$kODPolicyTypePasswordMaximumAgeInMinutes$kODPolicyTypePasswordMaximumNumberOfCharacters$kODPolicyTypePasswordMinimumNumberOfCharacters$kODPolicyTypePasswordRequiresAlpha$kODPolicyTypePasswordRequiresMixedCase$kODPolicyTypePasswordRequiresNumeric$kODPolicyTypePasswordRequiresSymbol$kODPolicyTypePasswordSelfModification$kODRecordTypeAFPServer$kODRecordTypeAliases$kODRecordTypeAttributeTypes$kODRecordTypeAugments$kODRecordTypeAutoServerSetup$kODRecordTypeAutomount$kODRecordTypeAutomountMap$kODRecordTypeBootp$kODRecordTypeCertificateAuthorities$kODRecordTypeComputerGroups$kODRecordTypeComputerLists$kODRecordTypeComputers$kODRecordTypeConfiguration$kODRecordTypeEthernets$kODRecordTypeFTPServer$kODRecordTypeFileMakerServers$kODRecordTypeGroups$kODRecordTypeHostServices$kODRecordTypeHosts$kODRecordTypeLDAPServer$kODRecordTypeLocations$kODRecordTypeMounts$kODRecordTypeNFS$kODRecordTypeNetDomains$kODRecordTypeNetGroups$kODRecordTypeNetworks$kODRecordTypePeople$kODRecordTypePresetComputerGroups$kODRecordTypePresetComputerLists$kODRecordTypePresetComputers$kODRecordTypePresetGroups$kODRecordTypePresetUsers$kODRecordTypePrintService$kODRecordTypePrintServiceUser$kODRecordTypePrinters$kODRecordTypeProtocols$kODRecordTypeQTSServer$kODRecordTypeQueryInformation$kODRecordTypeRPC$kODRecordTypeRecordTypes$kODRecordTypeResources$kODRecordTypeSMBServer$kODRecordTypeServer$kODRecordTypeServices$kODRecordTypeSharePoints$kODRecordTypeUsers$kODRecordTypeWebServer$kODSessionDefault@^{__ODSession=}$kODSessionProxyAddress$kODSessionProxyPassword$kODSessionProxyPort$kODSessionProxyUsername$"""
enums = """$kODErrorCredentialsAccountDisabled@5301$kODErrorCredentialsAccountExpired@5302$kODErrorCredentialsAccountInactive@5303$kODErrorCredentialsAccountLocked@5305$kODErrorCredentialsAccountNotFound@5300$kODErrorCredentialsAccountTemporarilyLocked@5304$kODErrorCredentialsContactMaster@5204$kODErrorCredentialsContactPrimary@5204$kODErrorCredentialsInvalid@5000$kODErrorCredentialsInvalidComputer@5501$kODErrorCredentialsInvalidLogonHours@5500$kODErrorCredentialsMethodNotSupported@5100$kODErrorCredentialsNotAuthorized@5101$kODErrorCredentialsOperationFailed@5103$kODErrorCredentialsParameterError@5102$kODErrorCredentialsPasswordChangeRequired@5401$kODErrorCredentialsPasswordChangeTooSoon@5407$kODErrorCredentialsPasswordExpired@5400$kODErrorCredentialsPasswordNeedsDigit@5406$kODErrorCredentialsPasswordNeedsLetter@5405$kODErrorCredentialsPasswordQualityFailed@5402$kODErrorCredentialsPasswordTooLong@5404$kODErrorCredentialsPasswordTooShort@5403$kODErrorCredentialsPasswordUnrecoverable@5408$kODErrorCredentialsServerCommunicationError@5205$kODErrorCredentialsServerError@5202$kODErrorCredentialsServerNotFound@5201$kODErrorCredentialsServerTimeout@5203$kODErrorCredentialsServerUnreachable@5200$kODErrorDaemonError@10002$kODErrorNodeConnectionFailed@2100$kODErrorNodeDisabled@2002$kODErrorNodeUnknownHost@2200$kODErrorNodeUnknownName@2000$kODErrorNodeUnknownType@2001$kODErrorPluginError@10001$kODErrorPluginOperationNotSupported@10000$kODErrorPluginOperationTimeout@10003$kODErrorPolicyOutOfRange@6001$kODErrorPolicyUnsupported@6000$kODErrorQueryInvalidMatchType@3100$kODErrorQuerySynchronize@3000$kODErrorQueryTimeout@3102$kODErrorQueryUnsupportedMatchType@3101$kODErrorRecordAlreadyExists@4102$kODErrorRecordAttributeNotFound@4201$kODErrorRecordAttributeUnknownType@4200$kODErrorRecordAttributeValueNotFound@4203$kODErrorRecordAttributeValueSchemaError@4202$kODErrorRecordInvalidType@4101$kODErrorRecordNoLongerExists@4104$kODErrorRecordParameterError@4100$kODErrorRecordPermissionError@4001$kODErrorRecordReadOnlyNode@4000$kODErrorRecordTypeDisabled@4103$kODErrorSessionDaemonNotRunning@1002$kODErrorSessionDaemonRefused@1003$kODErrorSessionLocalOnlyDaemonInUse@1000$kODErrorSessionNormalDaemonInUse@1001$kODErrorSessionProxyCommunicationError@1100$kODErrorSessionProxyIPUnreachable@1102$kODErrorSessionProxyUnknownHost@1103$kODErrorSessionProxyVersionMismatch@1101$kODErrorSuccess@0$kODExpirationTimeExpired@0$kODExpirationTimeNeverExpires@-1$kODMatchAny@1$kODMatchBeginsWith@8194$kODMatchContains@8196$kODMatchEndsWith@8195$kODMatchEqualTo@8193$kODMatchGreaterThan@8198$kODMatchInsensitiveBeginsWith@8450$kODMatchInsensitiveContains@8452$kODMatchInsensitiveEndsWith@8451$kODMatchInsensitiveEqualTo@8449$kODMatchLessThan@8199$kODNodeTypeAuthentication@8705$kODNodeTypeConfigure@8706$kODNodeTypeContacts@8708$kODNodeTypeLocalNodes@8704$kODNodeTypeNetwork@8709$"""
misc.update({"ODFrameworkErrors": NewType("ODFrameworkErrors", int)})
misc.update({})
misc.update({})
functions = {
    "ODNodeCopySubnodeNames": (
        b"^{__CFArray=}^{_ODNode=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordVerifyPasswordExtended": (
        b"B^{_ODRecord=}@^{__CFArray=}^^{__CFArray}^^{_ODContext}^^{__CFError}",
        "",
        {
            "arguments": {
                3: {"type_modifier": "o"},
                4: {"type_modifier": "o"},
                5: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                },
            }
        },
    ),
    "ODNodeCustomFunction": (
        b"@^{__ODNode=}^{__CFString=}@^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODRecordSetPolicy": (
        b"B^{__ODRecord=}@@^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODNodeCreateCopy": (
        b"^{_ODNode=}^{__CFAllocator=}^{_ODNode=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODNodeGetTypeID": (b"Q",),
    "ODNodeCustomCall": (
        b"^{__CFData=}^{_ODNode=}q^{__CFData=}^^{__CFError}",
        "",
        {
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODRecordDelete": (
        b"B^{_ODRecord=}^^{__CFError}",
        "",
        {
            "arguments": {
                1: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODRecordSetNodeCredentials": (
        b"B^{_ODRecord=}^{__CFString=}^{__CFString=}^^{__CFError}",
        "",
        {
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODNodeCopySupportedPolicies": (
        b"^{__CFDictionary=}^{__ODNode=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            },
        },
    ),
    "ODRecordCopySupportedPolicies": (
        b"^{__CFDictionary=}^{__ODRecord=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            },
        },
    ),
    "ODRecordGetRecordName": (b"^{__CFString=}^{_ODRecord=}",),
    "ODNodeRemovePolicy": (
        b"B^{__ODNode=}@^^{__CFError=}",
        "",
        {
            "arguments": {
                2: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODNodeCopyUnreachableSubnodeNames": (
        b"^{__CFArray=}^{_ODNode=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordSecondsUntilAuthenticationsExpire": (b"q^{__ODRecord=}",),
    "ODQuerySetCallback": (
        b"v^{_ODQuery=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__ODQuery=}"},
                            1: {"type": b"@"},
                            2: {"type": b"^{__CFError=}"},
                            3: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "ODNodeSetCredentialsExtended": (
        b"B^{_ODNode=}@@^{__CFArray=}^^{__CFArray}^^{_ODContext}^^{__CFError}",
        "",
        {
            "arguments": {
                4: {"type_modifier": "o"},
                5: {"type_modifier": "o"},
                6: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                },
            }
        },
    ),
    "ODRecordPasswordChangeAllowed": (
        b"B^{__ODRecord=}^{__CFString=}^^{__CFError=}",
        "",
        {
            "arguments": {
                2: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODRecordSetPolicies": (
        b"B^{__ODRecord=}^{__CFDictionary=}^^{__CFError=}",
        "",
        {
            "arguments": {
                2: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODNodePasswordContentCheck": (
        b"B^{__ODNode=}^{__CFString=}^{__CFString=}^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODQuerySynchronize": (b"v^{_ODQuery=}",),
    "ODRecordSecondsUntilPasswordExpires": (b"q^{__ODRecord=}",),
    "ODSessionCopyNodeNames": (
        b"^{__CFArray=}^{__CFAllocator=}^{_ODSessionRef=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODNodeCopySupportedAttributes": (
        b"^{__CFArray=}^{_ODNode=}@^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordGetRecordType": (b"^{__CFString=}^{_ODRecord=}",),
    "ODRecordAddMember": (
        b"B^{_ODRecord=}^{_ODRecord=}^^{__CFError}",
        "",
        {
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODRecordCopyAccountPolicies": (
        b"^{__CFDictionary=}^{__ODRecord=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            },
        },
    ),
    "ODNodeCopyRecord": (
        b"^{_ODRecord=}^{_ODNode=}@^{__CFString=}@^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODQueryScheduleWithRunLoop": (b"v^{_ODQuery=}^{__CFRunLoop=}^{__CFString=}",),
    "ODNodeGetName": (b"^{__CFString=}^{_ODNode=}",),
    "ODSessionCreate": (
        b"^{_ODSessionRef=}^{__CFAllocator=}^{__CFDictionary=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordContainsMember": (
        b"B^{_ODRecord=}^{_ODRecord=}^^{__CFError}",
        "",
        {
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODRecordAddAccountPolicy": (
        b"B^{__ODRecord=}^{__CFDictionary=}@^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODRecordRemovePolicy": (
        b"B^{__ODRecord=}@^^{__CFError=}",
        "",
        {
            "arguments": {
                2: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODQueryCreateWithNodeType": (
        b"^{_ODQuery=}^{__CFAllocator=}I@@I@@q^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODNodeCopySupportedRecordTypes": (
        b"^{__CFArray=}^{_ODNode=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordWillPasswordExpire": (b"B^{__ODRecord=}Q",),
    "ODQuerySetDispatchQueue": (b"v^{_ODQuery=}^{dispatch_queue_s=}",),
    "ODRecordVerifyPassword": (
        b"B^{_ODRecord=}^{__CFString=}^^{__CFError}",
        "",
        {
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODNodeCopyDetails": (
        b"^{__CFDictionary=}^{_ODNode=}^{__CFArray=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODQueryCreateWithNode": (
        b"^{_ODQuery=}^{__CFAllocator=}^{_ODNode=}@@I@@q^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODContextGetTypeID": (b"Q",),
    "ODNodeRemoveAccountPolicy": (
        b"B^{__ODNode=}^{__CFDictionary=}@^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODNodeCopyAccountPolicies": (
        b"^{__CFDictionary=}^{__ODNode=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            },
        },
    ),
    "ODRecordCopyPasswordPolicy": (
        b"^{__CFDictionary=}^{__CFAllocator=}^{_ODRecord=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODNodeSetCredentials": (
        b"B^{_ODNode=}@^{__CFString=}^{__CFString=}^^{__CFError}",
        "",
        {
            "arguments": {
                4: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODNodeCreateRecord": (
        b"^{_ODRecord=}^{_ODNode=}@^{__CFString=}^{__CFDictionary=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordGetTypeID": (b"Q",),
    "ODNodeSetAccountPolicies": (
        b"B^{__ODNode=}^{__CFDictionary=}^^{__CFError=}",
        "",
        {
            "arguments": {
                2: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODRecordCopyValues": (
        b"^{__CFArray=}^{_ODRecord=}@^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordSetValue": (
        b"B^{_ODRecord=}@@^^{__CFError}",
        "",
        {
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODNodeCopyPolicies": (
        b"^{__CFDictionary=}^{__ODNode=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            },
        },
    ),
    "ODNodeSetPolicy": (
        b"B^{__ODNode=}@@^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODRecordSetNodeCredentialsExtended": (
        b"B^{_ODRecord=}@@^{__CFArray=}^^{__CFArray}^^{_ODContext}^^{__CFError}",
        "",
        {
            "arguments": {
                4: {"type_modifier": "o"},
                5: {"type_modifier": "o"},
                6: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                },
            }
        },
    ),
    "ODQueryGetTypeID": (b"Q",),
    "ODRecordSetNodeCredentialsUsingKerberosCache": (
        b"B^{_ODRecord=}^{__CFString=}^^{__CFError}",
        "",
        {
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODNodeCreateWithName": (
        b"^{_ODNode=}^{__CFAllocator=}^{_ODSessionRef=}^{__CFString=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODNodeSetPolicies": (
        b"B^{__ODNode=}^{__CFDictionary=}^^{__CFError=}",
        "",
        {
            "arguments": {
                2: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODRecordRemoveAccountPolicy": (
        b"B^{__ODRecord=}^{__CFDictionary=}@^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODRecordRemoveValue": (
        b"B^{_ODRecord=}@@^^{__CFError}",
        "",
        {
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODSessionGetTypeID": (b"Q",),
    "ODRecordCopyDetails": (
        b"^{__CFDictionary=}^{_ODRecord=}^{__CFArray=}^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODNodeSetCredentialsUsingKerberosCache": (
        b"B^{_ODNode=}^{__CFString=}^^{__CFError}",
        "",
        {
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODRecordChangePassword": (
        b"B^{_ODRecord=}^{__CFString=}^{__CFString=}^^{__CFError}",
        "",
        {
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODQueryCopyResults": (
        b"^{__CFArray=}^{_ODQuery=}B^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordSynchronize": (
        b"B^{_ODRecord=}^^{__CFError}",
        "",
        {
            "arguments": {
                1: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODRecordRemoveMember": (
        b"B^{_ODRecord=}^{_ODRecord=}^^{__CFError}",
        "",
        {
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODNodeCreateWithNodeType": (
        b"^{_ODNode=}^{__CFAllocator=}^{_ODSessionRef=}I^^{__CFError}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "ODRecordWillAuthenticationsExpire": (b"B^{__ODRecord=}Q",),
    "ODRecordCopyEffectivePolicies": (
        b"^{__CFDictionary=}^{__ODRecord=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            },
        },
    ),
    "ODRecordCopyPolicies": (
        b"^{__CFDictionary=}^{__ODRecord=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            },
        },
    ),
    "ODQueryUnscheduleFromRunLoop": (b"v^{_ODQuery=}^{__CFRunLoop=}^{__CFString=}",),
    "ODRecordSetAccountPolicies": (
        b"B^{__ODRecord=}^{__CFDictionary=}^^{__CFError=}",
        "",
        {
            "arguments": {
                2: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODRecordAddValue": (
        b"B^{_ODRecord=}@@^^{__CFError}",
        "",
        {
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "ODRecordAuthenticationAllowed": (
        b"B^{__ODRecord=}^^{__CFError=}",
        "",
        {
            "arguments": {
                1: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "ODNodeAddAccountPolicy": (
        b"B^{__ODNode=}^{__CFDictionary=}@^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
}
aliases = {"kODErrorCredentialsContactMaster": "kODErrorCredentialsContactPrimary"}
cftypes = [
    ("ODContextRef", b"^{__ODContext=}", "ODContextGetTypeID", None),
    ("ODNodeRef", b"^{__ODNode=}", "ODNodeGetTypeID", None),
    ("ODQueryRef", b"^{__ODQuery=}", "ODQueryGetTypeID", None),
    ("ODRecordRef", b"^{__ODRecord=}", "ODRecordGetTypeID", None),
    ("ODSessionRef", b"^{__ODSession=}", "ODSessionGetTypeID", None),
]
expressions = {}

# END OF FILE
