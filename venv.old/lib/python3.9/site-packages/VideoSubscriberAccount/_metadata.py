# This file is generated by objective.metadata
#
# Last update: Tue Jun 11 10:21:39 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$VSAccountProviderAuthenticationSchemeAPI$VSAccountProviderAuthenticationSchemeSAML$VSCheckAccessOptionPrompt$VSErrorDomain$VSErrorInfoKeyAccountProviderResponse$VSErrorInfoKeySAMLResponse$VSErrorInfoKeySAMLResponseStatus$VSErrorInfoKeyUnsupportedProviderIdentifier$VSOpenTVProviderSettingsURLString$"""
enums = """$VSAccountAccessStatusDenied@2$VSAccountAccessStatusGranted@3$VSAccountAccessStatusNotDetermined@0$VSAccountAccessStatusRestricted@1$VSErrorCodeAccessNotGranted@0$VSErrorCodeInvalidVerificationToken@5$VSErrorCodeProviderRejected@4$VSErrorCodeRejected@6$VSErrorCodeServiceTemporarilyUnavailable@3$VSErrorCodeUnsupported@7$VSErrorCodeUnsupportedProvider@1$VSErrorCodeUserCancelled@2$VSOriginatingDeviceCategoryMobile@0$VSOriginatingDeviceCategoryOther@1$VSSubscriptionAccessLevelFreeWithAccount@1$VSSubscriptionAccessLevelPaid@2$VSSubscriptionAccessLevelUnknown@0$VSUserAccountQueryAllDevices@1$VSUserAccountQueryNone@0$VSUserAccountTypeFree@0$VSUserAccountTypePaid@1$"""
misc.update(
    {
        "VSOriginatingDeviceCategory": NewType("VSOriginatingDeviceCategory", int),
        "VSSubscriptionAccessLevel": NewType("VSSubscriptionAccessLevel", int),
        "VSUserAccountType": NewType("VSUserAccountType", int),
        "VSAccountAccessStatus": NewType("VSAccountAccessStatus", int),
        "VSErrorCode": NewType("VSErrorCode", int),
        "VSUserAccountQueryOptions": NewType("VSUserAccountQueryOptions", int),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"NSObject",
        b"accountManager:dismissViewController:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"accountManager:presentViewController:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"accountManager:shouldAuthenticateAccountProviderWithIdentifier:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"VSAccountManager",
        b"checkAccessStatusWithOptions:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VSAccountManager",
        b"enqueueAccountMetadataRequest:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"VSAccountMetadataRequest", b"forceAuthentication", {"retval": {"type": b"Z"}})
    r(
        b"VSAccountMetadataRequest",
        b"includeAccountProviderIdentifier",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"VSAccountMetadataRequest",
        b"includeAuthenticationExpirationDate",
        {"retval": {"type": b"Z"}},
    )
    r(b"VSAccountMetadataRequest", b"isInterruptionAllowed", {"retval": {"type": b"Z"}})
    r(
        b"VSAccountMetadataRequest",
        b"setForceAuthentication:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VSAccountMetadataRequest",
        b"setIncludeAccountProviderIdentifier:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VSAccountMetadataRequest",
        b"setIncludeAuthenticationExpirationDate:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"VSAccountMetadataRequest",
        b"setInterruptionAllowed:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"VSUserAccount", b"isDeleted", {"retval": {"type": b"Z"}})
    r(b"VSUserAccount", b"isFromCurrentDevice", {"retval": {"type": b"Z"}})
    r(b"VSUserAccount", b"isSignedOut", {"retval": {"type": b"Z"}})
    r(b"VSUserAccount", b"requiresSystemTrust", {"retval": {"type": b"Z"}})
    r(b"VSUserAccount", b"setDeleted:", {"arguments": {2: {"type": b"Z"}}})
    r(b"VSUserAccount", b"setRequiresSystemTrust:", {"arguments": {2: {"type": b"Z"}}})
    r(b"VSUserAccount", b"setSignedOut:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"VSUserAccountManager",
        b"queryUserAccountsWithOptions:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"VSUserAccountManager",
        b"updateUserAccount:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "VSAccountApplicationProvider", b"initWithLocalizedDisplayName:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "VSAppleSubscription", b"initWithCustomerID:productCodes:"
)
objc.registerNewKeywordsFromSelector("VSUserAccount", b"initWithAccountType:updateURL:")
expressions = {}

# END OF FILE
