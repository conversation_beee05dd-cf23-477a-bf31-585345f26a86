# This file is generated by objective.metadata
#
# Last update: Fri Jun 21 10:17:22 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$PHASEAssetErrorDomain$PHASEErrorDomain$PHASESoundEventErrorDomain$PHASESpatialCategoryDirectPathTransmission$PHASESpatialCategoryEarlyReflections$PHASESpatialCategoryLateReverb$"""
enums = """$PHASEAssetErrorAlreadyExists@1346920804$PHASEAssetErrorBadParameters@1346920803$PHASEAssetErrorFailedToLoad@1346920801$PHASEAssetErrorGeneralError@1346920805$PHASEAssetErrorInvalidEngineInstance@1346920802$PHASEAssetErrorMemoryAllocation@1346920806$PHASEAssetTypeResident@0$PHASEAssetTypeStreamed@1$PHASEAutomaticHeadTrackingFlagOrientation@1$PHASECalibrationModeAbsoluteSpl@2$PHASECalibrationModeNone@0$PHASECalibrationModeRelativeSpl@1$PHASECullOptionDoNotCull@4$PHASECullOptionSleepWakeAtRandomOffset@2$PHASECullOptionSleepWakeAtRealtimeOffset@3$PHASECullOptionSleepWakeAtZero@1$PHASECullOptionTerminate@0$PHASECurveTypeCubed@1668432757$PHASECurveTypeHoldStartValue@1668434003$PHASECurveTypeInverseCubed@1668434243$PHASECurveTypeInverseSigmoid@1668434247$PHASECurveTypeInverseSine@1668434259$PHASECurveTypeInverseSquared@1668434257$PHASECurveTypeJumpToEndValue@1668434501$PHASECurveTypeLinear@1668435054$PHASECurveTypeSigmoid@1668436839$PHASECurveTypeSine@1668436846$PHASECurveTypeSquared@1668436849$PHASEErrorInitializeFailed@1346913633$PHASEErrorInvalidObject@1346913634$PHASEMaterialPresetBrick@1833071211$PHASEMaterialPresetCardboard@1833136740$PHASEMaterialPresetConcrete@1833132914$PHASEMaterialPresetDrywall@1833202295$PHASEMaterialPresetGlass@1833397363$PHASEMaterialPresetWood@1834448228$PHASEMediumPresetAir@1835286898$PHASENormalizationModeDynamic@1$PHASENormalizationModeNone@0$PHASEPlaybackModeLooping@1$PHASEPlaybackModeOneShot@0$PHASEPushStreamBufferDefault@1$PHASEPushStreamBufferInterrupts@4$PHASEPushStreamBufferInterruptsAtLoop@8$PHASEPushStreamBufferLoops@2$PHASEPushStreamCompletionDataRendered@0$PHASERenderingStatePaused@2$PHASERenderingStateStarted@1$PHASERenderingStateStopped@0$PHASEReverbPresetCathedral@1917023336$PHASEReverbPresetLargeChamber@1917600616$PHASEReverbPresetLargeHall@1917601841$PHASEReverbPresetLargeHall2@1917601842$PHASEReverbPresetLargeRoom@1917604401$PHASEReverbPresetLargeRoom2@1917604402$PHASEReverbPresetMediumChamber@1917666152$PHASEReverbPresetMediumHall@1917667377$PHASEReverbPresetMediumHall2@1917667378$PHASEReverbPresetMediumHall3@1917667379$PHASEReverbPresetMediumRoom@1917669997$PHASEReverbPresetNone@1917742958$PHASEReverbPresetSmallRoom@1918063213$PHASESoundEventErrorAPIMisuse@1346925668$PHASESoundEventErrorBadData@1346925666$PHASESoundEventErrorInvalidInstance@1346925667$PHASESoundEventErrorNotFound@1346925665$PHASESoundEventErrorOutOfMemory@1346925670$PHASESoundEventErrorSystemNotInitialized@1346925669$PHASESoundEventPrepareHandlerReasonFailure@0$PHASESoundEventPrepareHandlerReasonPrepared@1$PHASESoundEventPrepareHandlerReasonTerminated@2$PHASESoundEventPrepareStatePrepareInProgress@1$PHASESoundEventPrepareStatePrepareNotStarted@0$PHASESoundEventPrepareStatePrepared@2$PHASESoundEventSeekHandlerReasonFailure@0$PHASESoundEventSeekHandlerReasonFailureSeekAlreadyInProgress@1$PHASESoundEventSeekHandlerReasonSeekSuccessful@2$PHASESoundEventStartHandlerReasonFailure@0$PHASESoundEventStartHandlerReasonFinishedPlaying@1$PHASESoundEventStartHandlerReasonTerminated@2$PHASESpatialPipelineFlagDirectPathTransmission@1$PHASESpatialPipelineFlagEarlyReflections@2$PHASESpatialPipelineFlagLateReverb@4$PHASESpatializationModeAlwaysUseBinaural@1$PHASESpatializationModeAlwaysUseChannelBased@2$PHASESpatializationModeAutomatic@0$PHASEUpdateModeAutomatic@0$PHASEUpdateModeManual@1$"""
misc.update(
    {
        "PHASESpatialPipelineFlags": NewType("PHASESpatialPipelineFlags", int),
        "PHASESoundEventPrepareState": NewType("PHASESoundEventPrepareState", int),
        "PHASEPushStreamBufferOptions": NewType("PHASEPushStreamBufferOptions", int),
        "PHASERenderingState": NewType("PHASERenderingState", int),
        "PHASEAssetType": NewType("PHASEAssetType", int),
        "PHASECullOption": NewType("PHASECullOption", int),
        "PHASECalibrationMode": NewType("PHASECalibrationMode", int),
        "PHASESoundEventStartHandlerReason": NewType(
            "PHASESoundEventStartHandlerReason", int
        ),
        "PHASEMediumPreset": NewType("PHASEMediumPreset", int),
        "PHASEUpdateMode": NewType("PHASEUpdateMode", int),
        "PHASEPlaybackMode": NewType("PHASEPlaybackMode", int),
        "PHASEAssetError": NewType("PHASEAssetError", int),
        "PHASEAutomaticHeadTrackingFlags": NewType(
            "PHASEAutomaticHeadTrackingFlags", int
        ),
        "PHASEMaterialPreset": NewType("PHASEMaterialPreset", int),
        "PHASESoundEventPrepareHandlerReason": NewType(
            "PHASESoundEventPrepareHandlerReason", int
        ),
        "PHASESoundEventSeekHandlerReason": NewType(
            "PHASESoundEventSeekHandlerReason", int
        ),
        "PHASESpatializationMode": NewType("PHASESpatializationMode", int),
        "PHASECurveType": NewType("PHASECurveType", int),
        "PHASENormalizationMode": NewType("PHASENormalizationMode", int),
        "PHASESoundEventError": NewType("PHASESoundEventError", int),
        "PHASEError": NewType("PHASEError", int),
        "PHASEReverbPreset": NewType("PHASEReverbPreset", int),
        "PHASEPushStreamCompletionCallbackCondition": NewType(
            "PHASEPushStreamCompletionCallbackCondition", int
        ),
    }
)
misc.update({"PHASESpatialCategory": NewType("PHASESpatialCategory", str)})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"PHASEAmbientMixerDefinition",
        b"initWithChannelLayout:orientation:",
        {
            "full_signature": b"@@:@{simd_quatf=<4f>}",
            "arguments": {3: {"type": b"{simd_quatf=<4f>}"}},
        },
    )
    r(
        b"PHASEAmbientMixerDefinition",
        b"initWithChannelLayout:orientation:identifier:",
        {
            "full_signature": b"@@:@{simd_quatf=<4f>}@",
            "arguments": {3: {"type": b"{simd_quatf=<4f>}"}},
        },
    )
    r(
        b"PHASEAmbientMixerDefinition",
        b"orientation",
        {
            "full_signature": b"{simd_quatf=<4f>}@:",
            "retval": {"type": b"{simd_quatf=<4f>}"},
        },
    )
    r(
        b"PHASEAssetRegistry",
        b"registerGlobalMetaParameter:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"PHASEAssetRegistry",
        b"registerSoundAssetAtURL:identifier:assetType:channelLayout:normalizationMode:error:",
        {"arguments": {7: {"type_modifier": b"o"}}},
    )
    r(
        b"PHASEAssetRegistry",
        b"registerSoundAssetWithData:identifier:format:normalizationMode:error:",
        {"arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"PHASEAssetRegistry",
        b"registerSoundEventAssetWithRootNode:identifier:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"PHASEAssetRegistry",
        b"unregisterAssetWithIdentifier:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"B"}},
                    }
                }
            }
        },
    )
    r(b"PHASEDucker", b"isActive", {"retval": {"type": b"Z"}})
    r(
        b"PHASEEngine",
        b"startAndReturnError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"PHASEEnvelope",
        b"initWithStartPoint:segments:",
        {"full_signature": b"@@:<2d>@", "arguments": {2: {"type": b"<2d>"}}},
    )
    r(
        b"PHASEEnvelope",
        b"startPoint",
        {"full_signature": b"<2d>@:", "retval": {"type": b"<2d>"}},
    )
    r(
        b"PHASEEnvelopeSegment",
        b"endPoint",
        {"full_signature": b"<2d>@:", "retval": {"type": b"<2d>"}},
    )
    r(
        b"PHASEEnvelopeSegment",
        b"initWithEndPoint:curveType:",
        {"full_signature": b"@@:<2d>q", "arguments": {2: {"type": b"<2d>"}}},
    )
    r(
        b"PHASEEnvelopeSegment",
        b"setEndPoint:",
        {"full_signature": b"v@:<2d>", "arguments": {2: {"type": b"<2d>"}}},
    )
    r(b"PHASEGroup", b"isMuted", {"retval": {"type": b"Z"}})
    r(b"PHASEGroup", b"isSoloed", {"retval": {"type": b"Z"}})
    r(
        b"PHASEObject",
        b"addChild:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"PHASEObject",
        b"forward",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"PHASEObject",
        b"right",
        {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}},
    )
    r(
        b"PHASEObject",
        b"setTransform:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"PHASEObject",
        b"setWorldTransform:",
        {
            "full_signature": b"v@:{simd_float4x4=[4<4f>]}",
            "arguments": {2: {"type": b"{simd_float4x4=[4<4f>]}"}},
        },
    )
    r(
        b"PHASEObject",
        b"transform",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(b"PHASEObject", b"up", {"full_signature": b"<3f>@:", "retval": {"type": b"<3f>"}})
    r(
        b"PHASEObject",
        b"worldTransform",
        {
            "full_signature": b"{simd_float4x4=[4<4f>]}@:",
            "retval": {"type": b"{simd_float4x4=[4<4f>]}"},
        },
    )
    r(
        b"PHASEPullStreamNode",
        b"renderBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"i"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"N^Z"},
                        2: {"type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"},
                        3: {"type": b"I"},
                        4: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                    },
                }
            }
        },
    )
    r(
        b"PHASEPullStreamNode",
        b"setRenderBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"N^Z"},
                            2: {
                                "type": b"n^{AudioTimeStamp=dQdQ{SMPTETime=ssIIIssss}II}"
                            },
                            3: {"type": b"I"},
                            4: {"type": b"^{AudioBufferList=I[1{AudioBuffer=II^v}]}"},
                        },
                    }
                }
            }
        },
    )
    r(b"PHASEPullStreamNodeDefinition", b"normalize", {"retval": {"type": b"Z"}})
    r(
        b"PHASEPullStreamNodeDefinition",
        b"setNormalize:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"PHASEPushStreamNode",
        b"scheduleBuffer:atTime:options:completionCallbackType:completionHandler:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"PHASEPushStreamNode",
        b"scheduleBuffer:completionCallbackType:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(b"PHASEPushStreamNodeDefinition", b"normalize", {"retval": {"type": b"Z"}})
    r(
        b"PHASEPushStreamNodeDefinition",
        b"setNormalize:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"PHASESoundEvent",
        b"initWithEngine:assetIdentifier:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"PHASESoundEvent",
        b"initWithEngine:assetIdentifier:mixerParameters:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(b"PHASESoundEvent", b"isIndefinite", {"retval": {"type": b"Z"}})
    r(
        b"PHASESoundEvent",
        b"prepareWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"PHASESoundEvent",
        b"seekToTime:completion:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"PHASESoundEvent",
        b"startWithCompletion:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "PHASEAmbientMixerDefinition", b"initWithChannelLayout:orientation:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEAmbientMixerDefinition", b"initWithChannelLayout:orientation:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEBlendNodeDefinition", b"initDistanceBlendWithSpatialMixerDefinition:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEBlendNodeDefinition",
    b"initDistanceBlendWithSpatialMixerDefinition:identifier:",
)
objc.registerNewKeywordsFromSelector(
    "PHASEBlendNodeDefinition", b"initWithBlendMetaParameterDefinition:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEBlendNodeDefinition", b"initWithBlendMetaParameterDefinition:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASECardioidDirectivityModelParameters", b"initWithSubbandParameters:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEChannelMixerDefinition", b"initWithChannelLayout:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEChannelMixerDefinition", b"initWithChannelLayout:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEConeDirectivityModelParameters", b"initWithSubbandParameters:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEContainerNodeDefinition", b"initWithIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEDistanceModelFadeOutParameters", b"initWithCullDistance:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEDucker",
    b"initWithEngine:sourceGroups:targetGroups:gain:attackTime:releaseTime:attackCurve:releaseCurve:",
)
objc.registerNewKeywordsFromSelector("PHASEEngine", b"initWithUpdateMode:")
objc.registerNewKeywordsFromSelector("PHASEEnvelope", b"initWithStartPoint:segments:")
objc.registerNewKeywordsFromSelector(
    "PHASEEnvelopeDistanceModelParameters", b"initWithEnvelope:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEEnvelopeSegment", b"initWithEndPoint:curveType:"
)
objc.registerNewKeywordsFromSelector("PHASEGroup", b"initWithIdentifier:")
objc.registerNewKeywordsFromSelector(
    "PHASEGroupPreset", b"initWithEngine:settings:timeToTarget:timeToReset:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEGroupPresetSetting", b"initWithGain:rate:gainCurveType:rateCurveType:"
)
objc.registerNewKeywordsFromSelector("PHASEListener", b"initWithEngine:")
objc.registerNewKeywordsFromSelector(
    "PHASEMappedMetaParameterDefinition",
    b"initWithInputMetaParameterDefinition:envelope:",
)
objc.registerNewKeywordsFromSelector(
    "PHASEMappedMetaParameterDefinition",
    b"initWithInputMetaParameterDefinition:envelope:identifier:",
)
objc.registerNewKeywordsFromSelector(
    "PHASEMappedMetaParameterDefinition", b"initWithValue:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEMappedMetaParameterDefinition", b"initWithValue:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEMappedMetaParameterDefinition", b"initWithValue:minimum:maximum:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEMappedMetaParameterDefinition", b"initWithValue:minimum:maximum:identifier:"
)
objc.registerNewKeywordsFromSelector("PHASEMaterial", b"initWithEngine:preset:")
objc.registerNewKeywordsFromSelector("PHASEMedium", b"initWithEngine:preset:")
objc.registerNewKeywordsFromSelector(
    "PHASENumberMetaParameterDefinition", b"initWithValue:"
)
objc.registerNewKeywordsFromSelector(
    "PHASENumberMetaParameterDefinition", b"initWithValue:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASENumberMetaParameterDefinition", b"initWithValue:minimum:maximum:"
)
objc.registerNewKeywordsFromSelector(
    "PHASENumberMetaParameterDefinition", b"initWithValue:minimum:maximum:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASENumericPair", b"initWithFirstValue:secondValue:"
)
objc.registerNewKeywordsFromSelector("PHASEObject", b"initWithEngine:")
objc.registerNewKeywordsFromSelector("PHASEOccluder", b"initWithEngine:")
objc.registerNewKeywordsFromSelector("PHASEOccluder", b"initWithEngine:shapes:")
objc.registerNewKeywordsFromSelector(
    "PHASEPullStreamNodeDefinition", b"initWithMixerDefinition:format:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEPullStreamNodeDefinition", b"initWithMixerDefinition:format:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEPushStreamNodeDefinition", b"initWithMixerDefinition:format:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEPushStreamNodeDefinition", b"initWithMixerDefinition:format:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASERandomNodeDefinition", b"initWithIdentifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASESamplerNodeDefinition", b"initWithSoundAssetIdentifier:mixerDefinition:"
)
objc.registerNewKeywordsFromSelector(
    "PHASESamplerNodeDefinition",
    b"initWithSoundAssetIdentifier:mixerDefinition:identifier:",
)
objc.registerNewKeywordsFromSelector("PHASEShape", b"initWithEngine:mesh:")
objc.registerNewKeywordsFromSelector("PHASEShape", b"initWithEngine:mesh:materials:")
objc.registerNewKeywordsFromSelector(
    "PHASESoundEvent", b"initWithEngine:assetIdentifier:error:"
)
objc.registerNewKeywordsFromSelector(
    "PHASESoundEvent", b"initWithEngine:assetIdentifier:mixerParameters:error:"
)
objc.registerNewKeywordsFromSelector("PHASESource", b"initWithEngine:")
objc.registerNewKeywordsFromSelector("PHASESource", b"initWithEngine:shapes:")
objc.registerNewKeywordsFromSelector(
    "PHASESpatialMixerDefinition", b"initWithSpatialPipeline:"
)
objc.registerNewKeywordsFromSelector(
    "PHASESpatialMixerDefinition", b"initWithSpatialPipeline:identifier:"
)
objc.registerNewKeywordsFromSelector("PHASESpatialPipeline", b"initWithFlags:")
objc.registerNewKeywordsFromSelector(
    "PHASEStringMetaParameterDefinition", b"initWithValue:"
)
objc.registerNewKeywordsFromSelector(
    "PHASEStringMetaParameterDefinition", b"initWithValue:identifier:"
)
objc.registerNewKeywordsFromSelector(
    "PHASESwitchNodeDefinition", b"initWithSwitchMetaParameterDefinition:"
)
objc.registerNewKeywordsFromSelector(
    "PHASESwitchNodeDefinition", b"initWithSwitchMetaParameterDefinition:identifier:"
)
expressions = {}

# END OF FILE
