"""
Python mapping for the GameController framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import AppKit
    import objc
    from . import _metadata, _GameController

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="GameController",
        frameworkIdentifier="com.apple.GameController",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/GameController.framework"
        ),
        globals_dict=globals(),
        inline_list=None,
        parents=(
            _GameController,
            AppKit,
        ),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    for cls, sel in (
        ("GCSteeringWheelElement", b"init"),
        ("GCDeviceHaptics", b"init"),
        ("GCPhysicalInputElementCollection", b"init"),
        ("GCPhysicalInputElementCollection", b"new"),
        ("GCDeviceBattery", b"init"),
        ("GCDeviceLight", b"init"),
        ("GCRacingWheel", b"init"),
        ("GCColor", b"init"),
    ):
        objc.registerUnavailableMethod(cls, sel)

    del sys.modules["GameController._metadata"]


globals().pop("_setup")()
