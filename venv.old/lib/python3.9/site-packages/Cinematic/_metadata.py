# This file is generated by objective.metadata
#
# Last update: Tu<PERSON> 11 10:06:23 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
constants = """$CNCinematicErrorDomain$"""
enums = """$CNCinematicErrorCodeCancelled@7$CNCinematicErrorCodeIncompatible@6$CNCinematicErrorCodeIncomplete@3$CNCinematicErrorCodeMalformed@4$CNCinematicErrorCodeUnknown@1$CNCinematicErrorCodeUnreadable@2$CNCinematicErrorCodeUnsupported@5$CNDetectionTypeAutoFocus@100$CNDetectionTypeCatBody@4$CNDetectionTypeCatHead@9$CNDetectionType<PERSON>ustom@102$CNDetectionTypeDogBody@5$CNDetectionTypeDogHead@10$CNDetectionTypeFixedFocus@101$CNDetectionTypeHumanFace@1$CNDetectionTypeHumanHead@2$CNDetectionTypeHumanTorso@3$CNDetectionTypeSportsBall@11$CNDetectionTypeUnknown@0$CNRenderingQualityExport@2$CNRenderingQualityExportHigh@3$CNRenderingQualityPreview@1$CNRenderingQualityThumbnail@0$"""
misc.update(
    {
        "CNDetectionType": NewType("CNDetectionType", int),
        "CNRenderingQuality": NewType("CNRenderingQuality", int),
        "CNCinematicErrorCode": NewType("CNCinematicErrorCode", int),
    }
)
misc.update({})
misc.update({})
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"CNAssetInfo",
        b"checkIfCinematic:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"CNAssetInfo",
        b"loadFromAsset:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CNCompositionInfo",
        b"insertTimeRange:ofCinematicAssetInfo:atTime:error:",
        {"retval": {"type": b"Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"CNCustomDetectionTrack",
        b"initWithDetections:smooth:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"CNDecision",
        b"initWithTime:detectionGroupID:strong:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"CNDecision",
        b"initWithTime:detectionID:strong:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(b"CNDecision", b"isGroupDecision", {"retval": {"type": b"Z"}})
    r(b"CNDecision", b"isStrongDecision", {"retval": {"type": b"Z"}})
    r(b"CNDecision", b"isUserDecision", {"retval": {"type": b"Z"}})
    r(b"CNDetection", b"isValidDetectionGroupID:", {"retval": {"type": b"Z"}})
    r(b"CNDetection", b"isValidDetectionID:", {"retval": {"type": b"Z"}})
    r(b"CNDetectionTrack", b"isDiscrete", {"retval": {"type": b"Z"}})
    r(b"CNDetectionTrack", b"isUserCreated", {"retval": {"type": b"Z"}})
    r(b"CNObjectTracker", b"isSupported", {"retval": {"type": b"Z"}})
    r(
        b"CNObjectTracker",
        b"startTrackingAt:within:sourceImage:sourceDisparity:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"CNRenderingSession",
        b"encodeRenderToCommandBuffer:frameAttributes:sourceImage:sourceDisparity:destinationImage:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"CNRenderingSession",
        b"encodeRenderToCommandBuffer:frameAttributes:sourceImage:sourceDisparity:destinationLuma:destinationChroma:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"CNRenderingSession",
        b"encodeRenderToCommandBuffer:frameAttributes:sourceImage:sourceDisparity:destinationRGBA:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"CNRenderingSessionAttributes",
        b"loadFromAsset:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"CNScript", b"addUserDecision:", {"retval": {"type": b"Z"}})
    r(
        b"CNScript",
        b"loadFromAsset:changes:progress:completionHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"CNScript", b"removeDetectionTrack:", {"retval": {"type": b"Z"}})
    r(b"CNScript", b"removeUserDecision:", {"retval": {"type": b"Z"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "CNCustomDetectionTrack", b"initWithDetections:smooth:"
)
objc.registerNewKeywordsFromSelector(
    "CNDecision", b"initWithTime:detectionGroupID:strong:"
)
objc.registerNewKeywordsFromSelector("CNDecision", b"initWithTime:detectionID:strong:")
objc.registerNewKeywordsFromSelector(
    "CNDetection", b"initWithTime:detectionType:normalizedRect:focusDisparity:"
)
objc.registerNewKeywordsFromSelector(
    "CNFixedDetectionTrack", b"initWithFocusDisparity:"
)
objc.registerNewKeywordsFromSelector(
    "CNFixedDetectionTrack", b"initWithOriginalDetection:"
)
objc.registerNewKeywordsFromSelector("CNObjectTracker", b"initWithCommandQueue:")
objc.registerNewKeywordsFromSelector(
    "CNRenderingSession",
    b"initWithCommandQueue:sessionAttributes:preferredTransform:quality:",
)
objc.registerNewKeywordsFromSelector(
    "CNRenderingSessionFrameAttributes", b"initWithSampleBuffer:sessionAttributes:"
)
objc.registerNewKeywordsFromSelector(
    "CNRenderingSessionFrameAttributes",
    b"initWithTimedMetadataGroup:sessionAttributes:",
)
objc.registerNewKeywordsFromSelector("CNScriptChanges", b"initWithDataRepresentation:")
expressions = {}

# END OF FILE
