"""
Python mapping for the BusinessChat framework.

This module does not contain docstrings for the wrapped code, check Apple's
documentation for details on how to use these functions and classes.
"""


def _setup():
    import sys

    import AppKit
    import objc
    from . import _metadata

    dir_func, getattr_func = objc.createFrameworkDirAndGetattr(
        name="BusinessChat",
        frameworkIdentifier="com.apple.icloud.messages.apps.businessframework",
        frameworkPath=objc.pathForFramework(
            "/System/Library/Frameworks/BusinessChat.framework"
        ),
        globals_dict=globals(),
        inline_list=None,
        parents=(AppKit,),
        metadict=_metadata.__dict__,
    )

    globals()["__dir__"] = dir_func
    globals()["__getattr__"] = getattr_func

    del sys.modules["BusinessChat._metadata"]


globals().pop("_setup")()
