"""Flask application factory and configuration"""

import os
import secrets
import logging
from pathlib import Path
from flask import Flask
from flask_cors import CORS
from flask_socketio import SocketIO
from dotenv import load_dotenv

try:
    from .routes import health_bp, jira_bp, review_bp
    from .worktree_endpoints import worktree_bp
except ImportError:
    from routes import health_bp, jira_bp, review_bp
    from worktree_endpoints import worktree_bp

logger = logging.getLogger(__name__)


def create_app(config=None):
    """Create and configure Flask application"""
    
    # Load environment variables
    load_environment()
    
    # Create Flask app
    app = Flask(__name__)
    
    # Configure app
    configure_app(app, config)
    
    # Setup CORS
    setup_cors(app)
    
    # Setup WebSocket support
    socketio = setup_websocket(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Setup logging
    setup_logging()
    
    return app, socketio


def load_environment():
    """Load environment variables from .env files"""
    # Path to .env.local in web-app/review-comment-responder-react/
    dotenv_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
        'web-app', 
        'review-comment-responder-react', 
        '.env.local'
    )
    load_dotenv(dotenv_path=dotenv_path)
    load_dotenv()  # Also load from default .env file


def configure_app(app: Flask, config=None):
    """Configure Flask application"""
    app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY') or secrets.token_hex(32)
    
    if app.config['SECRET_KEY'] == 'dev-secret-key-change-in-production':
        logger.warning("Using development secret key - NOT FOR PRODUCTION!")
    
    # Apply custom config if provided
    if config:
        app.config.update(config)


def setup_cors(app: Flask):
    """Setup CORS for React frontend"""
    CORS(app, origins=["http://localhost:5173", "http://localhost:5174"], supports_credentials=True)


def setup_websocket(app: Flask) -> SocketIO:
    """Setup WebSocket support for real-time updates"""
    return SocketIO(app, cors_allowed_origins=["http://localhost:5173", "http://localhost:5174"])


def register_blueprints(app: Flask):
    """Register all route blueprints"""
    app.register_blueprint(health_bp)
    app.register_blueprint(jira_bp)
    app.register_blueprint(review_bp)
    app.register_blueprint(worktree_bp)


def setup_logging():
    """Setup application logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
