"""Jira integration routes"""

import os
import logging
import sys
from flask import Blueprint, request, jsonify
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from utils.text_processing import TextProcessor
from services.acceptance_criteria_service import AcceptanceCriteriaService

logger = logging.getLogger(__name__)
jira_bp = Blueprint('jira', __name__)

# Create service instance for acceptance criteria processing
ac_service = AcceptanceCriteriaService()


@jira_bp.route('/api/jira/exchange-token', methods=['POST'])
def exchange_jira_token():
    """Exchange Jira authorization code for access token"""
    try:
        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'error': 'JSON data is required'
            }), 400
            
        code = data.get('code')
        
        if not code:
            return jsonify({
                'success': False,
                'error': 'Authorization code is required'
            }), 400
        
        # Exchange code for tokens
        import requests
        
        # Try both VITE_ prefixed and non-prefixed environment variables
        client_id = os.getenv('JIRA_CLIENT_ID') or os.getenv('VITE_JIRA_CLIENT_ID')
        client_secret = os.getenv('JIRA_CLIENT_SECRET') or os.getenv('VITE_JIRA_CLIENT_SECRET')
        
        if not client_id or not client_secret:
            logger.error(f"Missing OAuth credentials: client_id={bool(client_id)}, client_secret={bool(client_secret)}")
            return jsonify({
                'success': False,
                'error': 'OAuth credentials not configured'
            }), 500
        
        token_response = requests.post('https://auth.atlassian.com/oauth/token', {
            'grant_type': 'authorization_code',
            'client_id': client_id,
            'client_secret': client_secret,
            'code': code,
            'redirect_uri': data.get('redirect_uri', 'http://localhost:5173/auth/jira/callback')
        })
        
        if not token_response.ok:
            logger.error(f"Token exchange failed: {token_response.text}")
            return jsonify({
                'success': False,
                'error': 'Failed to exchange authorization code'
            }), 400
        
        token_data = token_response.json()
        
        return jsonify({
            'success': True,
            'access_token': token_data.get('access_token'),
            'refresh_token': token_data.get('refresh_token'),
            'expires_in': token_data.get('expires_in')
        })
        
    except Exception as e:
        logger.error(f"Error in Jira token exchange: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@jira_bp.route('/api/jira/tickets', methods=['GET'])  
def get_jira_tickets():
    """Get assigned Jira tickets for current user"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'error': 'Bearer token required'
            }), 401
        
        access_token = auth_header.split(' ')[1]
        
        # Validate and sanitize max_results parameter
        try:
            max_results = int(request.args.get('maxResults', 50))
            max_results = min(max(1, max_results), 100)  # Clamp between 1 and 100
        except ValueError:
            max_results = 50
        
        # Get Jira base URL from header or use default
        jira_base_url = request.headers.get('X-Jira-Base-URL', 'https://regionalmediendigital.atlassian.net')
        
        # Use Cloud ID from header if provided, otherwise extract from URL
        cloud_id = request.headers.get('X-Jira-Cloud-ID')
        if cloud_id:
            # Use the proper Cloud ID from OAuth discovery
            api_url = f'https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/search'
        else:
            # Extract cloud ID from the Jira base URL as fallback
            import re
            match = re.search(r'https://([^.]+)\\.atlassian\\.net', jira_base_url)
            if match:
                cloud_id = match.group(1)
                api_url = f'https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/search'
            else:
                # Fallback to direct URL (shouldn't happen with OAuth)
                api_url = f'{jira_base_url}/rest/api/3/search'
        
        # JQL to get tickets that need code review
        jql = 'status = "Code Review" AND resolution = Unresolved ORDER BY updated DESC'
        
        logger.info(f"🔍 Jira Search Request: BaseURL={jira_base_url}, ApiURL={api_url}, JQL={jql}")
        
        # Call Jira Search API
        import requests
        search_response = requests.post(
            api_url,
            headers={
                'Authorization': f'Bearer {access_token}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            json={
                'jql': jql,
                'maxResults': max_results,
                'fields': [
                    'summary', 'description', 'status', 'priority',
                    'assignee', 'reporter', 'created', 'updated', 
                    'customfield_10020',  # Common AC field
                    'customfield_10021',  # Alternative AC field
                    'customfield_*'       # Include all custom fields
                ],
                'expand': ['renderedFields']  # Get full rendered description
            }
        )
        
        if not search_response.ok:
            logger.error(f"❌ Jira API error: {search_response.status_code} - {search_response.text}")
            return jsonify({
                'success': False,
                'error': f'Jira API error: {search_response.status_code}'
            }), search_response.status_code
        
        search_data = search_response.json()
        issues = search_data.get('issues', [])
        
        # Transform to our format
        tickets = []
        for issue in issues:
            fields = issue.get('fields', {})
            
            # For ticket list, only extract existing AC (don't generate with Claude to save time)
            acceptance_criteria = ac_service.extract_existing_only(
                description_text=TextProcessor.get_plain_text_description(fields),
                custom_fields=fields
            )
            
            # Get full description - prioritize rendered HTML for better formatting
            description = ''
            
            if 'renderedFields' in issue and 'description' in issue['renderedFields']:
                # Rendered fields contain HTML with proper formatting
                description = issue['renderedFields']['description']
                logger.info(f"Using rendered HTML description for {issue['key']}")
            elif 'description' in fields and fields['description']:
                # Fallback to raw description if no rendered version
                desc_field = fields['description']
                if isinstance(desc_field, dict) and 'content' in desc_field:
                    # Parse ADF format to plain text
                    description = TextProcessor.parse_adf_to_text(desc_field)
                    logger.info(f"Parsed ADF description for {issue['key']}")
                elif isinstance(desc_field, str):
                    description = desc_field
                else:
                    description = str(desc_field) if desc_field else ''
            
            # Log description length for debugging
            logger.info(f"Ticket {issue['key']}: Description length = {len(description)} chars")
            
            ticket = {
                'id': issue['id'],
                'key': issue['key'],
                'summary': fields.get('summary', ''),
                'description': description,
                'status': {
                    'name': fields.get('status', {}).get('name', 'Unknown'),
                    'statusCategory': fields.get('status', {}).get('statusCategory', {})
                },
                'priority': {
                    'name': fields.get('priority', {}).get('name', 'Medium'),
                    'iconUrl': fields.get('priority', {}).get('iconUrl', '')
                },
                'assignee': {
                    'accountId': fields.get('assignee', {}).get('accountId', '') if fields.get('assignee') else '',
                    'displayName': fields.get('assignee', {}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned',
                    'emailAddress': fields.get('assignee', {}).get('emailAddress', '') if fields.get('assignee') else ''
                },
                'reporter': {
                    'accountId': fields.get('reporter', {}).get('accountId', '') if fields.get('reporter') else '',
                    'displayName': fields.get('reporter', {}).get('displayName', 'Unknown') if fields.get('reporter') else 'Unknown',
                    'emailAddress': fields.get('reporter', {}).get('emailAddress', '') if fields.get('reporter') else ''
                },
                'created': fields.get('created', ''),
                'updated': fields.get('updated', ''),
                'acceptance_criteria': acceptance_criteria,
                'acceptance_criteria_count': len(acceptance_criteria),
                'customFields': {k: v for k, v in fields.items() if k.startswith('customfield_')}
            }
            tickets.append(ticket)
        
        # Transform tickets to AssignedTicket format expected by frontend
        assigned_tickets = []
        for ticket in tickets:
            assigned_ticket = {
                'ticket_id': ticket['key'],
                'summary': ticket['summary'],
                'description': ticket['description'],
                'status': ticket['status']['name'],
                'priority': ticket['priority']['name'],
                'assignee': ticket['assignee']['displayName'],
                'created_date': ticket['created'],
                'updated_date': ticket['updated'],
                'acceptance_criteria_count': ticket['acceptance_criteria_count'],
                'acceptance_criteria': ticket['acceptance_criteria'],
                'acceptanceCriteria': ticket['acceptance_criteria'],  # Support both formats
                'related_prs': []  # Would need to determine from branch names
            }
            assigned_tickets.append(assigned_ticket)
        
        logger.info(f"✅ Successfully fetched {len(assigned_tickets)} Jira tickets")
        
        return jsonify({
            'success': True,
            'tickets': assigned_tickets,
            'total': search_data.get('total', len(assigned_tickets))
        })
        
    except Exception as e:
        logger.error(f"Error fetching Jira tickets: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


# Legacy function removed - now using AcceptanceCriteriaService for enhanced AC processing


@jira_bp.route('/api/jira/ticket/<ticket_key>', methods=['GET'])
def get_jira_ticket_details(ticket_key):
    """Get detailed information for a specific Jira ticket"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'error': 'Bearer token required'
            }), 401
        
        access_token = auth_header.split(' ')[1]
        
        # Get Jira base URL from header or use default
        jira_base_url = request.headers.get('X-Jira-Base-URL', 'https://regionalmediendigital.atlassian.net')
        cloud_id = request.headers.get('X-Jira-Cloud-ID')
        
        if cloud_id:
            api_url = f'https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/issue/{ticket_key}'
        else:
            # Extract cloud ID from the Jira base URL as fallback
            import re
            match = re.search(r'https://([^.]+)\\.atlassian\\.net', jira_base_url)
            if match:
                cloud_id = match.group(1)
                api_url = f'https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/issue/{ticket_key}'
            else:
                api_url = f'{jira_base_url}/rest/api/3/issue/{ticket_key}'
        
        # Fetch ticket details with rendered fields
        import requests
        response = requests.get(
            api_url,
            headers={
                'Authorization': f'Bearer {access_token}',
                'Accept': 'application/json'
            },
            params={
                'expand': 'renderedFields',
                'fields': '*all'  # Get all fields including custom fields
            }
        )
        
        if response.status_code == 404:
            return jsonify({
                'success': False,
                'error': 'Ticket not found'
            }), 404
        
        if not response.ok:
            return jsonify({
                'success': False,
                'error': f'Failed to fetch ticket: {response.status_code}'
            }), response.status_code
        
        issue = response.json()
        fields = issue.get('fields', {})
        
        # Extract or generate acceptance criteria using Claude AI (restored missing feature!)
        ac_result = ac_service.extract_or_generate_acceptance_criteria(
            ticket_key=issue['key'],
            summary=fields.get('summary', ''),
            description_text=TextProcessor.get_plain_text_description(fields),
            custom_fields=fields
        )
        
        acceptance_criteria = ac_result.criteria
        
        # Enhanced logging for debugging  
        if ac_result.source == 'existing':
            logger.info(f"✅ AC for {issue['key']}: EXTRACTED {len(acceptance_criteria)} existing criteria via {ac_result.method}")
        else:
            logger.warning(f"🤖 AC for {issue['key']}: GENERATED {len(acceptance_criteria)} criteria via {ac_result.method}")
            
        # Log first few criteria for verification
        for i, ac in enumerate(acceptance_criteria[:3], 1):
            logger.info(f"   AC{i}: {ac[:80]}...")
        
        # Get complete raw Jira text for description field
        description = ''
        
        # Priority 1: Try rendered HTML first (has formatting)
        if 'renderedFields' in issue and 'description' in issue['renderedFields']:
            description = issue['renderedFields']['description']
            logger.info(f"✅ Using rendered HTML description for {issue['key']}")
        # Priority 2: Parse ADF to readable text with formatting
        elif 'description' in fields and fields['description']:
            desc_field = fields['description']
            if isinstance(desc_field, dict) and 'content' in desc_field:
                description = TextProcessor.parse_adf_to_text(desc_field)
                logger.info(f"✅ Parsed ADF description for {issue['key']}")
            elif isinstance(desc_field, str):
                description = desc_field
                logger.info(f"✅ Using string description for {issue['key']}")
            else:
                description = str(desc_field) if desc_field else ''
                logger.info(f"⚠️ Using fallback description for {issue['key']}")
        else:
            logger.info(f"❌ No description found for {issue['key']}")
            
        # If we still don't have a description, create a fallback from available fields
        if not description and fields.get('summary'):
            description = f"**{fields['summary']}**\\n\\n_No detailed description available in this Jira ticket._"
        
        # Transform to AssignedTicket format
        ticket = {
            'ticket_id': issue['key'],
            'summary': fields.get('summary', ''),
            'description': description,
            'status': fields.get('status', {}).get('name', 'Unknown'),
            'priority': fields.get('priority', {}).get('name', 'Medium') if fields.get('priority') else 'Medium',
            'assignee': fields.get('assignee', {}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned',
            'created_date': fields.get('created', ''),
            'updated_date': fields.get('updated', ''),
            'acceptance_criteria_count': len(acceptance_criteria),
            'acceptance_criteria': acceptance_criteria,
            'acceptanceCriteria': acceptance_criteria,  # Support both formats
            'related_prs': []
        }
        
        logger.info(f"📋 Fetched ticket details: {ticket['ticket_id']}")
        
        return jsonify({
            'success': True,
            'ticket': ticket
        })
        
    except Exception as e:
        logger.error(f"Error fetching ticket {ticket_key}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500