#!/usr/bin/env python3
"""
Enhanced Claude Code PR Reviewer using Claude Code CLI - FIXED VERSION
Uses subprocess.Popen to avoid asyncio issues in threaded contexts
"""

import json
import os
import subprocess
import sys
import uuid
import logging
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Callable

# Import base classes from existing implementation
try:
    from .jira_integration import JiraIntegration, JiraTicket
    from .prompt_manager import PromptManager, get_prompt_manager
except ImportError:
    # Fallback for when running as script
    from jira_integration import JiraIntegration, JiraTicket
    from prompt_manager import PromptManager, get_prompt_manager

try:
    from core.utils.worktree_utils import get_configured_worktree_path, generate_worktree_path, ensure_worktree_base_directory
except ImportError:
    from utils.worktree_utils import get_configured_worktree_path, generate_worktree_path, ensure_worktree_base_directory

logger = logging.getLogger(__name__)


class ClaudeCodeCLIError(Exception):
    """Exception raised when Claude Code CLI fails"""
    pass


class EnhancedClaudeReviewerSDK:
    """Enhanced Claude Code Reviewer using Claude Code CLI with structured output"""
    
    def __init__(self, config: Dict, worktree_path=None, repo_path=".", pr_url=None, branch_name=None):
        """Initialize with same parameters as CLI version"""
        self.config = config
        
        # Use configured worktree path if not explicitly provided
        if worktree_path:
            self.worktree_path = Path(worktree_path)
        elif branch_name:
            # Generate worktree path using configured base path
            configured_path = generate_worktree_path(branch_name)
            self.worktree_path = Path(configured_path)
            logger.info(f"📁 Generated worktree path: {configured_path}")
        else:
            self.worktree_path = None
            
        self.repo_path = Path(repo_path).resolve()
        self.working_path = self.worktree_path if self.worktree_path else self.repo_path
        self.pr_url = pr_url
        self.branch_name = branch_name
        
        # Initialize Prompt Manager for clean prompt handling
        self.prompt_manager = get_prompt_manager()
        
        # Initialize Jira integration (same as CLI)
        self.jira_integration = JiraIntegration(config)
        
        # Setup worktree if needed
        if self.worktree_path and self.branch_name:
            self._ensure_worktree_exists()
        
        # Session tracking
        self.session_id = str(uuid.uuid4())
        self.start_time = datetime.now()
        
        # Verify Claude Code CLI is available
        self._verify_claude_cli()
        
        logger.info(f"🚀 SDK Enhanced Reviewer initialized for {self.working_path}")
        logger.info(f"📋 Session ID: {self.session_id}")
    
    def _verify_claude_cli(self):
        """Verify that Claude Code CLI is installed and accessible"""
        try:
            # Add nvm path to environment
            env = os.environ.copy()
            nvm_path = "/Users/<USER>/.nvm/versions/node/v22.14.0/bin"
            if nvm_path not in env.get('PATH', ''):
                env['PATH'] = f"{nvm_path}:{env.get('PATH', '')}"
                
            result = subprocess.run(['claude', '--version'], 
                                  capture_output=True, text=True, timeout=10, env=env)
            if result.returncode == 0:
                logger.info(f"✅ Claude Code CLI found: {result.stdout.strip()}")
            else:
                raise ClaudeCodeCLIError("Claude Code CLI not responding properly")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            raise ClaudeCodeCLIError(
                "Claude Code CLI not found. Please install it with: npm install -g @anthropic-ai/claude-cli"
            )
    
    async def perform_enhanced_review_async(
        self, 
        review_type="comprehensive_with_ac", 
        include_summary=False,
        progress_callback: Optional[Callable] = None
    ) -> Dict:
        """
        Perform enhanced review using Claude Code CLI with structured JSON output
        
        Args:
            review_type: Type of review (comprehensive_with_ac, ac_focused, bug_analysis)
            include_summary: Whether to include Phase 3 tutorial summary
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dict with structured review results matching CLI quality
        """
        
        logger.info(f"🔍 Starting perform_enhanced_review_async")
        
        # Send initial progress
        if progress_callback:
            try:
                import inspect
                if callable(progress_callback) and not inspect.iscoroutinefunction(progress_callback):
                    progress_callback("session_started", "Starting enhanced review session", {
                        "session_id": self.session_id,
                        "review_type": review_type,
                        "include_summary": include_summary
                    })
            except Exception as e:
                logger.warning(f"Progress callback error: {e}")
        
        try:
            # Get Jira ticket context if available
            jira_ticket = None
            if self.branch_name:
                try:
                    ticket_id = self.jira_integration.extract_ticket_id_from_branch(self.branch_name)
                    if ticket_id:
                        jira_ticket = self.jira_integration.get_ticket(ticket_id)
                except Exception as e:
                    logger.warning(f"⚠️ Could not fetch Jira context: {e}")
            
            # Build the enhanced prompt
            prompt = self._build_enhanced_prompt(review_type, jira_ticket)
            
            # Execute Claude Code CLI (synchronously to avoid asyncio issues)
            review_result = self._execute_claude_code_review_sync(prompt, progress_callback)
            
            # Structure the result
            structured_result = self._structure_review_result(
                review_result, review_type, jira_ticket
            )
            
            # Generate Phase 3 summary if requested
            if include_summary:
                logger.info(f"🔍 Generating Phase 3 summary for session {self.session_id}")
                tutorial_data = self._generate_phase3_summary_sync(structured_result, progress_callback)
                structured_result["phase3_summary"] = tutorial_data
            
            logger.info(f"✅ Enhanced review completed for session {self.session_id}")
            return structured_result
            
        except Exception as e:
            import traceback
            logger.error(f"❌ Enhanced review failed: {str(e)}")
            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            
            error_result = {
                "session_id": self.session_id,
                "review_type": review_type,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "session_id": self.session_id,
                    "is_error": True,
                    "error_type": type(e).__name__
                }
            }
            
            if progress_callback:
                try:
                    # Only call if it's a regular function, not a coroutine
                    import inspect
                    if callable(progress_callback) and not inspect.iscoroutinefunction(progress_callback):
                        progress_callback("review_error", f"Review failed: {str(e)}", {
                            "session_id": self.session_id,
                            "error": str(e)
                        })
                except:
                    pass
            
            return error_result
    
    def _build_enhanced_prompt(self, review_type: str, jira_ticket: Optional[JiraTicket]) -> str:
        """Build enhanced prompt using German prompts from CLI for same quality"""
        
        # Get base prompt (German for quality)
        base_prompt = self.prompt_manager.get_enhanced_review_prompt(
            review_type=review_type,
            ticket_context=jira_ticket.summary if jira_ticket else None
        )
        
        # Add Jira ticket context if available
        if jira_ticket:
            jira_context = f"""
## Jira Ticket Context
**Ticket ID:** {jira_ticket.ticket_id}
**Summary:** {jira_ticket.summary}
**Status:** {jira_ticket.status}

**Acceptance Criteria:**
{chr(10).join(f"- {ac}" for ac in jira_ticket.acceptance_criteria)}

**Description:**
{jira_ticket.description}
"""
            base_prompt = base_prompt.replace("{{JIRA_CONTEXT}}", jira_context)
        else:
            base_prompt = base_prompt.replace("{{JIRA_CONTEXT}}", "")
        
        # Add repository context
        repo_context = f"""
## Repository Context
**Working Directory:** {self.working_path}
**Branch:** {self.branch_name or 'unknown'}
**PR URL:** {self.pr_url or 'N/A'}
"""
        base_prompt = base_prompt.replace("{{REPO_CONTEXT}}", repo_context)
        
        return base_prompt
    
    def _execute_claude_code_review_sync(self, prompt: str, progress_callback: Optional[Callable]) -> Dict:
        """Execute Claude Code CLI synchronously using subprocess.Popen"""
        
        logger.info("🔧 Executing Claude Code CLI synchronously")
        
        # Prepare environment with nvm path
        env = os.environ.copy()
        nvm_path = "/Users/<USER>/.nvm/versions/node/v22.14.0/bin"
        if nvm_path not in env.get('PATH', ''):
            env['PATH'] = f"{nvm_path}:{env.get('PATH', '')}"
        
        # Command to execute - USE CLI METHOD with JSON output for structured data
        cmd = ['claude', '-p', prompt, '--output-format', 'json']
        
        # Use the working_path that was correctly set in constructor
        working_dir = str(self.working_path)
        logger.info(f"📂 Using working directory: {working_dir}")
        logger.info(f"🔧 Command: claude -p [PROMPT] --output-format json")
        logger.info(f"🔧 Prompt length: {len(prompt)} characters")
        
        # Create process in correct directory - NO STDIN needed with -p parameter
        try:
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=working_dir,
                timeout=600,  # 10 minutes like CLI version
                check=True
            )
            stdout_data = result.stdout
            stderr_data = result.stderr
            returncode = 0
        except subprocess.TimeoutExpired:
            logger.error("❌ Claude CLI timeout after 600 seconds")
            raise ClaudeCodeCLIError("Claude CLI timeout after 600 seconds")
        except subprocess.CalledProcessError as e:
            stdout_data = e.stdout
            stderr_data = e.stderr
            returncode = e.returncode
        
        if returncode != 0:
            logger.error(f"❌ Claude CLI failed with code {returncode}")
            logger.error(f"📍 stderr: {stderr_data}")
            raise ClaudeCodeCLIError(f"Claude CLI failed: {stderr_data}")
        
        # Parse JSON response from Claude Code CLI
        logger.info("✅ Claude Code CLI completed successfully")
        logger.info(f"🔥 Raw stdout length: {len(stdout_data)}")
        logger.info(f"🔥 First 200 chars: {stdout_data[:200]}...")
        
        # Parse JSON output
        try:
            result = json.loads(stdout_data.strip())
            logger.info(f"🔥 Successfully parsed JSON result")
            logger.info(f"🔥 Result keys: {list(result.keys()) if isinstance(result, dict) else type(result)}")
            return result
        except json.JSONDecodeError as e:
            logger.warning(f"⚠️ JSON parsing failed, using fallback: {e}")
            # Fallback to raw text content
            return {
                "content": stdout_data.strip(),
                "raw_content": stdout_data.strip(),
                "success": True,
                "metadata": {
                    "parsing_method": "fallback_text",
                    "json_error": str(e)
                }
            }
    
    def _generate_phase3_summary_sync(self, review_result: Dict, progress_callback: Optional[Callable]) -> Dict:
        """Generate Phase 3 tutorial summary synchronously"""
        
        # Build Phase 3 prompt
        phase3_prompt = self.prompt_manager.get_phase3_prompt(
            review_result.get("raw_content", ""),
            review_result.get("review_type", "comprehensive")
        )
        
        try:
            # Execute Phase 3 generation
            tutorial_result = self._execute_claude_code_review_sync(phase3_prompt, progress_callback)
            
            return {
                "tutorial_id": f"tutorial_{self.session_id}",
                "raw_content": tutorial_result.get("content", tutorial_result.get("raw_content", "")),
                "structured_sections": {
                    "business_context": "Generated from Phase 3 analysis",
                    "architecture_overview": "Implementation architecture",
                    "implementation_guide": "Step-by-step guide",
                    "testing": "Testing recommendations",
                    "deployment": "Deployment considerations"
                },
                "metadata": {
                    "cost_usd": tutorial_result.get("cost_usd", 0.0),
                    "duration_ms": tutorial_result.get("duration_ms", 0),
                    "session_id": self.session_id
                }
            }
            
        except Exception as e:
            logger.warning(f"⚠️ Phase 3 generation failed: {e}")
            return {
                "tutorial_id": f"tutorial_{self.session_id}",
                "raw_content": "Phase 3 tutorial generation failed",
                "structured_sections": {},
                "metadata": {
                    "error": str(e),
                    "session_id": self.session_id
                }
            }
    
    async def _perform_phase3_summary(self, review_content: str, review_type: str, progress_callback: Optional[Callable] = None) -> Dict:
        """
        Perform Phase 3 summary generation (API compatibility method)
        """
        return self._generate_phase3_summary_sync(
            {"raw_content": review_content, "review_type": review_type},
            progress_callback
        )
    
    def _ensure_worktree_exists(self):
        """Ensure the worktree exists, create it if necessary"""
        import subprocess
        
        if not self.worktree_path or not self.branch_name:
            return
            
        worktree_path = Path(self.worktree_path)
        
        # If worktree already exists, we're done
        if worktree_path.exists() and (worktree_path / '.git').exists():
            logger.info(f"✅ Worktree already exists: {worktree_path}")
            return
            
        try:
            # Get master repository path from config
            master_repo_path = get_configured_worktree_path()
            master_path = Path(master_repo_path)
            
            if not master_path.exists():
                raise ValueError(f"Master repository not found: {master_repo_path}")
            
            # Ensure worktree base directory exists
            ensure_worktree_base_directory(master_repo_path)
            
            # Check and cleanup stale worktrees
            self._cleanup_stale_worktrees(master_path, worktree_path)
            
            # Fetch latest changes
            logger.info(f"📡 Fetching latest changes for {self.branch_name}")
            subprocess.run(["git", "fetch", "--all"], cwd=master_path, check=True)
            
            # Create the worktree
            logger.info(f"🔧 Creating worktree for {self.branch_name} at {worktree_path}")
            
            try:
                subprocess.run([
                    "git", "worktree", "add", str(worktree_path), self.branch_name
                ], cwd=master_path, check=True)
            except subprocess.CalledProcessError as e:
                # If normal creation fails, try with --force flag
                if "already registered" in str(e.stderr) or "already exists" in str(e.stderr):
                    logger.warning(f"⚠️ Worktree creation failed, trying with --force: {e}")
                    subprocess.run([
                        "git", "worktree", "add", "--force", str(worktree_path), self.branch_name
                    ], cwd=master_path, check=True)
                else:
                    raise  # Re-raise if it's a different error
            
            logger.info(f"✅ Worktree created successfully: {worktree_path}")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to create worktree: {e}")
            raise RuntimeError(f"Could not create worktree for {self.branch_name}: {e}")
        except Exception as e:
            logger.error(f"❌ Worktree setup failed: {e}")
            raise
    
    def _cleanup_stale_worktrees(self, master_path: Path, target_worktree_path: Path):
        """Clean up stale or conflicting worktrees"""
        import subprocess
        
        try:
            # First, prune any missing worktrees
            logger.info("🧹 Pruning missing worktrees...")
            subprocess.run([
                "git", "worktree", "prune"
            ], cwd=master_path, check=True, capture_output=True)
            
            # Get current worktree list
            result = subprocess.run([
                "git", "worktree", "list", "--porcelain"
            ], cwd=master_path, capture_output=True, text=True, check=True)
            
            # Parse worktree list to find conflicts
            lines = result.stdout.strip().split('\n') if result.stdout.strip() else []
            worktrees = []
            current_worktree = {}
            
            for line in lines:
                if line.startswith('worktree '):
                    if current_worktree:
                        worktrees.append(current_worktree)
                    current_worktree = {'path': line.replace('worktree ', '')}
                elif line.startswith('branch '):
                    current_worktree['branch'] = line.replace('branch ', '').replace('refs/heads/', '')
                elif line.startswith('HEAD '):
                    current_worktree['head'] = line.replace('HEAD ', '')
            
            if current_worktree:
                worktrees.append(current_worktree)
            
            # Check for conflicts
            for worktree in worktrees:
                worktree_path = Path(worktree.get('path', ''))
                worktree_branch = worktree.get('branch', '')
                
                # Case 1: Same branch name, different location
                if worktree_branch == self.branch_name and worktree_path != target_worktree_path:
                    logger.warning(f"⚠️ Found worktree for {self.branch_name} at different location: {worktree_path}")
                    
                    # If the directory doesn't exist, just remove the worktree entry
                    if not worktree_path.exists():
                        logger.info(f"🗑️ Removing stale worktree entry (directory missing): {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                    else:
                        logger.info(f"🗑️ Removing worktree from wrong location: {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                
                # Case 2: Same target path, different branch
                elif worktree_path == target_worktree_path and worktree_branch != self.branch_name:
                    logger.warning(f"⚠️ Target path {target_worktree_path} occupied by branch {worktree_branch}")
                    
                    if not worktree_path.exists():
                        logger.info(f"🗑️ Removing stale worktree entry: {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                    else:
                        logger.info(f"🗑️ Removing conflicting worktree: {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                
                # Case 3: Same path and branch - worktree already exists correctly
                elif worktree_path == target_worktree_path and worktree_branch == self.branch_name:
                    if worktree_path.exists() and (worktree_path / '.git').exists():
                        logger.info(f"✅ Worktree already exists correctly: {worktree_path}")
                        return  # We're done, worktree is ready
                    else:
                        # Directory missing but worktree registered - remove and recreate
                        logger.info(f"🗑️ Removing incomplete worktree (directory missing): {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                        
        except subprocess.CalledProcessError as e:
            logger.warning(f"⚠️ Worktree cleanup failed: {e}")
            # Continue anyway, creation might still work with --force
        except Exception as e:
            logger.warning(f"⚠️ Worktree cleanup error: {e}")
            # Continue anyway
    
    def _structure_review_result(
        self, 
        claude_result: Dict, 
        review_type: str, 
        jira_ticket: Optional[JiraTicket]
    ) -> Dict:
        """Structure the Claude Code CLI result to match API expectations"""
        
        end_time = datetime.now()
        duration_ms = int((end_time - self.start_time).total_seconds() * 1000)
        
        # Extract content from Claude CLI JSON response
        raw_content = (
            claude_result.get("result", "") or  # Claude CLI JSON format 
            claude_result.get("content", "") or # Fallback format
            claude_result.get("raw_content", "")  # Legacy format
        )
        
        # Base structured result
        structured_result = {
            "session_id": self.session_id,
            "review_type": review_type,
            "timestamp": end_time.isoformat(),
            "success": not claude_result.get("is_error", False),
            "raw_content": raw_content,
            
            # Enhanced structured data
            "structured_data": self._parse_structured_data(claude_result),
            
            # Enhanced metadata from Claude CLI JSON
            "metadata": {
                "cost_usd": claude_result.get("total_cost_usd", claude_result.get("cost_usd", 0.0)),
                "duration_ms": claude_result.get("duration_ms", duration_ms),
                "api_duration_ms": claude_result.get("duration_api_ms", duration_ms),
                "num_turns": claude_result.get("num_turns", 1),
                "session_id": self.session_id,
                "claude_session_id": claude_result.get("session_id"),
                "is_error": claude_result.get("is_error", False),
                "success": not claude_result.get("is_error", False),
                "worktree_path": str(self.worktree_path) if self.worktree_path else None,
                "branch_name": self.branch_name,
                "pr_url": self.pr_url,
                "changed_files": self._get_changed_files(),
                "diff_summary": f"Analysis of {self.branch_name or 'current branch'}"
            },
            
            # Jira integration
            "jira_ticket": self._format_jira_ticket(jira_ticket) if jira_ticket else None
        }
        
        return structured_result
    
    def _parse_structured_data(self, raw_result: Dict) -> Dict:
        """Parse Claude's response to extract structured data"""
        
        # Extract content from different possible JSON fields
        raw_content = (
            raw_result.get("result", "") or  # Claude CLI JSON format 
            raw_result.get("content", "") or # Fallback format
            raw_result.get("raw_content", "")  # Legacy format
        )
        logger.info(f"🔥 Parsing structured data from content length: {len(raw_content)}")
        
        # Parse acceptance criteria
        acceptance_criteria = self._parse_acceptance_criteria(raw_content)
        
        # Parse code quality findings
        code_quality_findings = self._parse_code_quality_findings(raw_content)
        
        # Parse action items
        action_items = self._parse_action_items(raw_content)
        
        # Parse questions
        questions = self._parse_questions_and_clarifications(raw_content)
        
        # Calculate executive summary
        critical_issues = len([f for f in code_quality_findings if f.get('severity') == 'critical'])
        warning_issues = len([f for f in code_quality_findings if f.get('severity') in ['high', 'medium']])
        
        # Calculate AC compliance
        ac_compliance = None
        if acceptance_criteria:
            fulfilled = len([ac for ac in acceptance_criteria if ac.get('status') == 'FULFILLED'])
            total = len(acceptance_criteria)
            ac_compliance = {
                "fulfilled": fulfilled,
                "total": total,
                "percentage": int((fulfilled / total) * 100) if total > 0 else 0
            }
        
        structured_data = {
            "executive_summary": {
                "critical_issues": critical_issues,
                "warning_issues": warning_issues,
                "has_ac_analysis": len(acceptance_criteria) > 0,
                "has_code_analysis": len(code_quality_findings) > 0,
                "ac_compliance": ac_compliance,
                "code_quality_score": max(8 - critical_issues - (warning_issues // 2), 1)
            },
            "acceptance_criteria": acceptance_criteria,
            "code_quality_findings": code_quality_findings,
            "action_items": action_items,
            "questions": questions
        }
        
        logger.info(f"✅ Parsed structured data - AC: {len(acceptance_criteria)}, Findings: {len(code_quality_findings)}, Actions: {len(action_items.get('critical', []))}")
        return structured_data
    
    def _parse_acceptance_criteria(self, content: str) -> List[Dict]:
        """Parse acceptance criteria from Claude's review"""
        import re
        ac_list = []
        
        # Look for AC analysis sections
        ac_patterns = [
            r'(?:#### ✅|#### ❌|#### ⚠️)\s*AC\s*\d+[:\s]*(.+?)(?=#### |$)',
            r'(?:✅|❌|⚠️)\s*AC\s*\d+[:\s]*(.+?)(?=\n(?:✅|❌|⚠️)|$)',
            r'\d+\.\s*(.+?)(?=\n\d+\.|$)'
        ]
        
        for pattern in ac_patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            for match in matches:
                if len(match.strip()) > 10:  # Filter out short matches
                    status = 'FULFILLED' if '✅' in match else 'NOT_FULFILLED' if '❌' in match else 'PARTIAL'
                    ac_list.append({
                        "id": f"ac_{len(ac_list) + 1}",
                        "title": match.strip()[:200] + ("..." if len(match.strip()) > 200 else ""),
                        "status": status,
                        "icon": "✅" if status == 'FULFILLED' else "❌" if status == 'NOT_FULFILLED' else "⚠️"
                    })
                    
        return ac_list[:10]  # Limit to 10 ACs
    
    def _parse_code_quality_findings(self, content: str) -> List[Dict]:
        """Parse code quality findings from Claude's review"""
        import re
        findings = []
        
        # Look for bug/issue patterns
        bug_patterns = [
            r'(?:#### 🚨|🚨)\s*(.+?)(?=####|$)',
            r'(?:Critical\s*Bug|Security\s*Issue)[:\s]*(.+?)(?=\n|$)',
            r'(?:Problem|Issue)[:\s]*(.+?)(?=\n|$)',
            r'(?:❌|⚠️)\s*([^✅]+?)(?=\n(?:✅|❌|⚠️)|$)'
        ]
        
        for pattern in bug_patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            for match in matches:
                clean_match = match.strip()
                if len(clean_match) > 20 and 'ac' not in clean_match.lower()[:10]:
                    severity = 'critical' if any(word in clean_match.lower() for word in ['critical', 'security', 'crash']) else 'high'
                    findings.append({
                        "type": "Bug Detection",
                        "severity": severity,
                        "description": clean_match[:300] + ("..." if len(clean_match) > 300 else ""),
                        "full_text": clean_match
                    })
                    
        return findings[:20]  # Limit to 20 findings
    
    def _parse_action_items(self, content: str) -> Dict:
        """Parse action items from Claude's review"""
        import re
        
        action_items = {
            "critical": [],
            "important": [],
            "suggestions": []
        }
        
        # Look for action item sections
        critical_patterns = [
            r'(?:### 🚨 CRITICAL|CRITICAL.*?:)\s*(.*?)(?=###|$)',
            r'(?:Must.*?Fix|Critical)[:\s]*(.+?)(?=\n|$)'
        ]
        
        important_patterns = [
            r'(?:### ⚠️ IMPORTANT|IMPORTANT.*?:)\s*(.*?)(?=###|$)',
            r'(?:Should.*?Fix|Important)[:\s]*(.+?)(?=\n|$)'
        ]
        
        suggestion_patterns = [
            r'(?:### 💡 SUGGESTIONS|SUGGESTIONS.*?:)\s*(.*?)(?=###|$)',
            r'(?:Nice.*?to.*?Have|Suggestion)[:\s]*(.+?)(?=\n|$)'
        ]
        
        # Parse each category
        for patterns, category in [(critical_patterns, 'critical'), 
                                  (important_patterns, 'important'), 
                                  (suggestion_patterns, 'suggestions')]:
            for pattern in patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                for match in matches:
                    items = [item.strip() for item in match.split('\n') if item.strip() and len(item.strip()) > 10]
                    action_items[category].extend(items[:5])  # Limit per category
                    
        return action_items
    
    def _parse_questions_and_clarifications(self, content: str) -> List[str]:
        """Parse questions from Claude's review"""
        import re
        questions = []
        
        # Look for question patterns
        question_patterns = [
            r'(?:## ❓ QUESTIONS|QUESTIONS.*?:)\s*(.*?)(?=##|$)',
            r'\d+\.\s*(.+?\?)',
            r'(?:Question|Clarification)[:\s]*(.+?\?)'
        ]
        
        for pattern in question_patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            for match in matches:
                if '?' in match:
                    questions.extend([q.strip() for q in match.split('\n') if '?' in q and len(q.strip()) > 10])
                    
        return questions[:10]  # Limit to 10 questions
    
    def _get_changed_files(self) -> List[str]:
        """Get list of changed files in current branch"""
        try:
            result = subprocess.run(
                ['git', 'diff', '--name-only', 'HEAD^..HEAD'],
                capture_output=True, text=True, cwd=self.working_path
            )
            if result.returncode == 0:
                return [f.strip() for f in result.stdout.split('\n') if f.strip()]
        except:
            pass
        return []
    
    def _format_jira_ticket(self, jira_ticket: JiraTicket) -> Dict:
        """Format Jira ticket for API response"""
        return {
            "ticket_id": jira_ticket.ticket_id,
            "summary": jira_ticket.summary,
            "status": jira_ticket.status,
            "priority": jira_ticket.priority,
            "acceptance_criteria_count": len(jira_ticket.acceptance_criteria),
            "acceptance_criteria": jira_ticket.acceptance_criteria
        }


# Factory function for backward compatibility
def create_enhanced_reviewer_sdk(config: Dict, **kwargs) -> EnhancedClaudeReviewerSDK:
    """Factory function to create enhanced reviewer SDK instance"""
    return EnhancedClaudeReviewerSDK(config, **kwargs)


# Test function
if __name__ == "__main__":
    import asyncio
    
    async def test_review():
        config = {
            'pr_config': {
                'repository': {
                    'path': '.'
                }
            }
        }
        
        reviewer = EnhancedClaudeReviewerSDK(config, branch_name="test-branch")
        result = await reviewer.perform_enhanced_review_async(
            review_type="comprehensive_with_ac",
            include_summary=True
        )
        
        print(json.dumps(result, indent=2))
    
    asyncio.run(test_review())
