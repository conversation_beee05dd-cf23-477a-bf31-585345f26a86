import json
from pathlib import Path
from typing import Optional
import logging

logger = logging.getLogger(__name__)

def get_configured_worktree_path() -> str:
    """
    Get configured worktree base path from config file.
    Falls back to default if no valid configuration exists.
    
    Returns:
        str: The configured or default worktree base path
    """
    config_file = Path.home() / ".review-tool" / "worktree-config.json"
    default_path = "/Users/<USER>/dev"
    
    try:
        if config_file.exists():
            with open(config_file, 'r') as f:
                data = json.load(f)
                user_config = data.get("default", {})
                
                if user_config.get("is_valid", False):
                    configured_path = user_config.get("base_path")
                    if configured_path and Path(configured_path).exists():
                        logger.info(f"📁 Using configured worktree path: {configured_path}")
                        return configured_path
                        
    except Exception as e:
        logger.warning(f"⚠️ Failed to load worktree config: {e}")
    
    # Fallback to default
    logger.info(f"📁 Using default worktree path: {default_path}")
    return default_path

def generate_worktree_path(branch_name: str, base_path: Optional[str] = None) -> str:
    """
    Generate worktree path for a given branch name.
    
    Args:
        branch_name: The branch name to generate path for
        base_path: Optional base path, uses configured path if not provided
        
    Returns:
        str: Full path to the worktree directory
    """
    if base_path is None:
        base_path = get_configured_worktree_path()
    
    # Generate worktree name using same pattern as existing scripts
    branch_short = branch_name[:10] if len(branch_name) > 10 else branch_name
    branch_safe = ''.join(c if c.isalnum() or c in '-_' else '-' for c in branch_short)
    worktree_name = f"{branch_safe}-review"
    
    return str(Path(base_path) / worktree_name)

def ensure_worktree_base_directory(base_path: Optional[str] = None) -> bool:
    """
    Ensure the worktree base directory exists and is writable.
    
    Args:
        base_path: Optional base path, uses configured path if not provided
        
    Returns:
        bool: True if directory is ready for use, False otherwise
    """
    if base_path is None:
        base_path = get_configured_worktree_path()
    
    try:
        base_dir = Path(base_path)
        base_dir.mkdir(parents=True, exist_ok=True)
        
        # Test write permissions
        test_file = base_dir / ".worktree-test"
        test_file.touch()
        test_file.unlink()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to prepare worktree directory {base_path}: {e}")
        return False