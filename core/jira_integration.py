#!/usr/bin/env python3
"""
Jira Integration Module
Handles Jira API integration with fallback to local ticket files for PR review enhancement.
"""

import requests
import json
import re
import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Union
from datetime import datetime
import base64


class JiraTicket:
    """Represents a Jira ticket with all relevant information"""
    
    def __init__(self, ticket_data: Dict):
        self.ticket_id = ticket_data.get('key', 'Unknown')
        self.summary = ticket_data.get('summary', '')
        self.description = ticket_data.get('description', '')
        self.issue_type = ticket_data.get('issue_type', 'Unknown')
        self.status = ticket_data.get('status', 'Unknown')
        self.assignee = ticket_data.get('assignee', 'Unassigned')
        self.reporter = ticket_data.get('reporter', 'Unknown')
        self.priority = ticket_data.get('priority', 'Medium')
        self.labels = ticket_data.get('labels', [])
        self.components = ticket_data.get('components', [])
        self.acceptance_criteria = ticket_data.get('acceptance_criteria', [])
        self.story_points = ticket_data.get('story_points', None)
        self.epic_link = ticket_data.get('epic_link', None)
        self.created = ticket_data.get('created', None)
        self.updated = ticket_data.get('updated', None)
        self.raw_data = ticket_data
    
    def to_dict(self) -> Dict:
        """Convert ticket to dictionary for serialization"""
        return {
            'ticket_id': self.ticket_id,
            'summary': self.summary,
            'description': self.description,
            'issue_type': self.issue_type,
            'status': self.status,
            'assignee': self.assignee,
            'reporter': self.reporter,
            'priority': self.priority,
            'labels': self.labels,
            'components': self.components,
            'acceptance_criteria': self.acceptance_criteria,
            'story_points': self.story_points,
            'epic_link': self.epic_link,
            'created': self.created,
            'updated': self.updated
        }
    
    def has_acceptance_criteria(self) -> bool:
        """Check if ticket has acceptance criteria"""
        return bool(self.acceptance_criteria)
    
    def get_acceptance_criteria_count(self) -> int:
        """Get number of acceptance criteria items"""
        return len(self.acceptance_criteria)


class JiraIntegration:
    """Handles Jira API integration with local file fallback"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.jira_config = config.get('jira_config', {})
        self.enabled = self.jira_config.get('enabled', False)
        self.server_url = self.jira_config.get('server_url', '')
        self.credentials = self.jira_config.get('credentials', {})
        self.session = None
        
        # Initialize API session if enabled
        if self.enabled:
            self._init_api_session()
        
        print(f"🎫 Jira Integration {'aktiviert' if self.enabled else 'deaktiviert'}")
    
    def _init_api_session(self):
        """Initialize Jira API session with authentication"""
        try:
            self.session = requests.Session()
            
            # Get credentials from config or environment
            email = self.credentials.get('email', '')
            api_token = self.credentials.get('api_token', '')
            
            # Replace environment variables
            if api_token.startswith('${') and api_token.endswith('}'):
                env_var = api_token[2:-1]
                api_token = os.getenv(env_var, '')
            
            if not email or not api_token:
                raise Exception("Jira credentials incomplete: email and api_token required")
            
            # Setup authentication
            auth_string = f"{email}:{api_token}"
            encoded_auth = base64.b64encode(auth_string.encode()).decode()
            
            self.session.headers.update({
                'Authorization': f'Basic {encoded_auth}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            })
            
            # Test connection
            response = self.session.get(f"{self.server_url}/rest/api/3/myself")
            if response.status_code == 200:
                user_info = response.json()
                print(f"✅ Jira API verbunden als: {user_info.get('displayName', email)}")
            else:
                raise Exception(f"Jira authentication failed: {response.status_code}")
                
        except Exception as e:
            print(f"⚠️  Jira API Verbindung fehlgeschlagen: {e}")
            print("🔄 Fallback zu lokalen Ticket-Dateien aktiviert")
            self.enabled = False
            self.session = None
    
    def extract_ticket_id_from_branch(self, branch_name: str) -> Optional[str]:
        """Extract Jira ticket ID from branch name using configured patterns"""
        if not branch_name:
            return None
        
        patterns = self.jira_config.get('ticket_extraction', {}).get('branch_patterns', [
            r'feature/([A-Z]+-\d+)',
            r'bugfix/([A-Z]+-\d+)',
            r'hotfix/([A-Z]+-\d+)',
            r'^([A-Z]+-\d+)-',  # Match patterns like CMS20-1255-push-service-tagids
            r'([A-Z]+-\d+)',
            r'([A-Z]+_\d+)'
        ])
        
        print(f"🔍 Extrahiere Ticket ID aus Branch: '{branch_name}'")
        
        for pattern in patterns:
            match = re.search(pattern, branch_name, re.IGNORECASE)
            if match:
                ticket_id = match.group(1).upper()
                print(f"✅ Ticket ID gefunden: {ticket_id}")
                return ticket_id
        
        # Check for manual ticket ID if no pattern matches
        manual_ticket_id = self.jira_config.get('ticket_extraction', {}).get('manual_ticket_id')
        if manual_ticket_id:
            print(f"💡 Verwende manuell konfigurierte Ticket ID: {manual_ticket_id}")
            return manual_ticket_id
        
        print(f"❌ Keine Ticket ID im Branch Namen gefunden")
        return None
    
    def get_ticket(self, ticket_id: str) -> Optional[JiraTicket]:
        """
        Get ticket information from Jira API or local files
        
        Args:
            ticket_id: Jira ticket ID (e.g., 'CMS20-1166')
            
        Returns:
            JiraTicket object or None if not found
        """
        if not ticket_id:
            return None
        
        print(f"🎫 Lade Ticket: {ticket_id}")
        
        # Try Jira API first if enabled
        if self.enabled and self.session:
            try:
                return self._get_ticket_from_api(ticket_id)
            except Exception as e:
                print(f"⚠️  Jira API Fehler: {e}")
                print("🔄 Fallback zu lokalen Dateien...")
        
        # Fallback to local files
        return self._get_ticket_from_local_files(ticket_id)
    
    def _get_ticket_from_api(self, ticket_id: str) -> Optional[JiraTicket]:
        """Get ticket from Jira API"""
        try:
            # Build API request
            fields = [
                'summary', 'description', 'issuetype', 'status', 'assignee',
                'reporter', 'priority', 'labels', 'components', 'created',
                'updated', 'customfield_10016'  # Story points (may vary)
            ]
            
            # Add custom fields for acceptance criteria if configured
            ac_fields = self.jira_config.get('acceptance_criteria_fields', [
                'customfield_10020',  # Common AC field
                'customfield_10021',  # Alternative AC field
            ])
            fields.extend(ac_fields)
            
            url = f"{self.server_url}/rest/api/3/issue/{ticket_id}"
            params = {
                'fields': ','.join(fields),
                'expand': 'renderedFields'
            }
            
            response = self.session.get(url, params=params)
            
            if response.status_code == 404:
                print(f"❌ Ticket {ticket_id} nicht gefunden in Jira")
                return None
            elif response.status_code != 200:
                raise Exception(f"API request failed: {response.status_code} - {response.text}")
            
            data = response.json()
            
            # Parse ticket data
            fields_data = data.get('fields', {})
            rendered_fields = data.get('renderedFields', {})
            
            ticket_data = {
                'key': data.get('key', ticket_id),
                'summary': fields_data.get('summary', ''),
                'description': rendered_fields.get('description', fields_data.get('description', '')),
                'issue_type': fields_data.get('issuetype', {}).get('name', 'Unknown'),
                'status': fields_data.get('status', {}).get('name', 'Unknown'),
                'assignee': fields_data.get('assignee', {}).get('displayName', 'Unassigned') if fields_data.get('assignee') else 'Unassigned',
                'reporter': fields_data.get('reporter', {}).get('displayName', 'Unknown') if fields_data.get('reporter') else 'Unknown',
                'priority': fields_data.get('priority', {}).get('name', 'Medium') if fields_data.get('priority') else 'Medium',
                'labels': fields_data.get('labels', []),
                'components': [c.get('name', '') for c in fields_data.get('components', [])],
                'story_points': fields_data.get('customfield_10016'),
                'created': fields_data.get('created'),
                'updated': fields_data.get('updated')
            }
            
            # Extract acceptance criteria from various possible fields
            acceptance_criteria = self._extract_acceptance_criteria_from_api(fields_data, rendered_fields)
            ticket_data['acceptance_criteria'] = acceptance_criteria
            
            print(f"✅ Ticket aus Jira API geladen: {ticket_data['summary']}")
            print(f"📋 Acceptance Criteria gefunden: {len(acceptance_criteria)} Items")
            
            return JiraTicket(ticket_data)
            
        except Exception as e:
            raise Exception(f"Fehler beim Laden von Ticket {ticket_id} via API: {e}")
    
    def _extract_acceptance_criteria_from_api(self, fields_data: Dict, rendered_fields: Dict) -> List[str]:
        """Extract acceptance criteria from Jira API response"""
        acceptance_criteria = []
        
        # Try various common sources for acceptance criteria
        sources = [
            ('description', rendered_fields.get('description', '')),
            ('description_raw', fields_data.get('description', '')),
        ]
        
        # Add custom AC fields
        ac_fields = self.jira_config.get('acceptance_criteria_fields', [])
        for field in ac_fields:
            if field in fields_data:
                sources.append((field, fields_data[field]))
            if field in rendered_fields:
                sources.append((f"{field}_rendered", rendered_fields[field]))
        
        for source_name, content in sources:
            if not content:
                continue
                
            print(f"🔍 Suche AC in {source_name}...")
            extracted = self._parse_acceptance_criteria_from_text(content)
            if extracted:
                acceptance_criteria.extend(extracted)
                print(f"✅ {len(extracted)} AC aus {source_name} extrahiert")
        
        # Remove duplicates while preserving order
        seen = set()
        unique_criteria = []
        for criterion in acceptance_criteria:
            if criterion not in seen:
                seen.add(criterion)
                unique_criteria.append(criterion)
        
        return unique_criteria
    
    def _get_ticket_from_local_files(self, ticket_id: str) -> Optional[JiraTicket]:
        """Get ticket from local files (fallback)"""
        
        # Try different file locations and formats
        possible_files = [
            f"ticket.md",
            f"ticket.yaml",
            f"ticket.json",
            f"tickets/{ticket_id}.md",
            f"tickets/{ticket_id}.yaml",
            f"tickets/{ticket_id}.json",
            f".devtools/tickets/{ticket_id}.md",
            f".devtools/tickets/{ticket_id}.yaml",
            f".devtools/tickets/{ticket_id}.json"
        ]
        
        # Check manual ticket file from config
        manual_file = self.jira_config.get('ticket_extraction', {}).get('manual_ticket_file')
        if manual_file:
            possible_files.insert(0, manual_file)
        
        for file_path in possible_files:
            try:
                path = Path(file_path)
                if path.exists():
                    print(f"📁 Lade Ticket aus lokaler Datei: {path}")
                    
                    if path.suffix == '.json':
                        return self._parse_json_ticket(path)
                    elif path.suffix in ['.yaml', '.yml']:
                        return self._parse_yaml_ticket(path)
                    elif path.suffix == '.md':
                        return self._parse_markdown_ticket(path)
                        
            except Exception as e:
                print(f"⚠️  Fehler beim Laden von {file_path}: {e}")
                continue
        
        print(f"❌ Ticket {ticket_id} nicht in lokalen Dateien gefunden")
        print(f"💡 Erstellen Sie eine der folgenden Dateien:")
        for file_path in possible_files[:3]:
            print(f"   - {file_path}")
        
        return None
    
    def _parse_json_ticket(self, file_path: Path) -> JiraTicket:
        """Parse ticket from JSON file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Ensure acceptance_criteria is a list
        if 'acceptance_criteria' in data and isinstance(data['acceptance_criteria'], str):
            data['acceptance_criteria'] = self._parse_acceptance_criteria_from_text(data['acceptance_criteria'])
        
        return JiraTicket(data)
    
    def _parse_yaml_ticket(self, file_path: Path) -> JiraTicket:
        """Parse ticket from YAML file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        # Ensure acceptance_criteria is a list
        if 'acceptance_criteria' in data and isinstance(data['acceptance_criteria'], str):
            data['acceptance_criteria'] = self._parse_acceptance_criteria_from_text(data['acceptance_criteria'])
        
        return JiraTicket(data)
    
    def _parse_markdown_ticket(self, file_path: Path) -> JiraTicket:
        """Parse ticket from Markdown file with frontmatter"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse frontmatter if exists
        ticket_data = {}
        if content.startswith('---'):
            try:
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    frontmatter = yaml.safe_load(parts[1])
                    if isinstance(frontmatter, dict):
                        ticket_data.update(frontmatter)
                    content = parts[2].strip()
                else:
                    content = content.replace('---', '', 1).strip()
            except Exception as e:
                print(f"⚠️  Frontmatter parsing fehler: {e}")
        
        # Parse content for additional information
        ticket_data['description'] = content
        
        # Extract acceptance criteria from content
        ac_from_content = self._parse_acceptance_criteria_from_text(content)
        if ac_from_content:
            existing_ac = ticket_data.get('acceptance_criteria', [])
            if isinstance(existing_ac, str):
                existing_ac = self._parse_acceptance_criteria_from_text(existing_ac)
            elif not isinstance(existing_ac, list):
                existing_ac = []
            
            # Combine and deduplicate
            all_ac = existing_ac + ac_from_content
            seen = set()
            unique_ac = []
            for criterion in all_ac:
                if criterion not in seen:
                    seen.add(criterion)
                    unique_ac.append(criterion)
            
            ticket_data['acceptance_criteria'] = unique_ac
        
        return JiraTicket(ticket_data)
    
    def _parse_acceptance_criteria_from_text(self, text: str) -> List[str]:
        """Parse acceptance criteria from text using various patterns"""
        if not text:
            return []
        
        criteria = []
        
        # Pattern 1: Markdown headers with "Acceptance Criteria"
        ac_sections = re.findall(
            r'#{1,6}\s*(?:acceptance\s*criteria|ac\b).*?\n(.*?)(?=\n#{1,6}|\Z)',
            text,
            re.IGNORECASE | re.DOTALL
        )
        
        for section in ac_sections:
            # Extract numbered or bulleted lists
            items = re.findall(r'^\s*(?:\d+\.|\*|-|\+)\s*(.+?)(?=\n\s*(?:\d+\.|\*|-|\+|$)|\Z)', section, re.MULTILINE | re.DOTALL)
            criteria.extend([item.strip().replace('\n', ' ') for item in items if item.strip()])
        
        # Pattern 2: "Given/When/Then" BDD format
        bdd_patterns = re.findall(
            r'(?:Given|When|Then|And)\s+(.+?)(?=(?:Given|When|Then|And)\s|\n\n|\Z)',
            text,
            re.IGNORECASE | re.DOTALL
        )
        criteria.extend([pattern.strip().replace('\n', ' ') for pattern in bdd_patterns if pattern.strip()])
        
        # Pattern 3: Simple numbered lists anywhere in text
        if not criteria:
            numbered_items = re.findall(r'^\s*\d+\.\s*(.+?)(?=\n\s*\d+\.|\n\n|\Z)', text, re.MULTILINE | re.DOTALL)
            criteria.extend([item.strip().replace('\n', ' ') for item in numbered_items if item.strip() and len(item) > 10])
        
        # Pattern 4: Checkbox lists
        checkbox_items = re.findall(r'^\s*-\s*\[\s*\]\s*(.+?)(?=\n\s*-\s*\[|\n\n|\Z)', text, re.MULTILINE | re.DOTALL)
        criteria.extend([item.strip().replace('\n', ' ') for item in checkbox_items if item.strip()])
        
        # Clean up criteria
        cleaned_criteria = []
        for criterion in criteria:
            cleaned = criterion.strip()
            # Skip very short or common non-AC text
            if len(cleaned) > 5 and not any(skip in cleaned.lower() for skip in ['todo', 'note:', 'example']):
                cleaned_criteria.append(cleaned)
        
        return cleaned_criteria
    
    def create_sample_ticket_file(self, ticket_id: str = "CMS20-1166", file_format: str = "md") -> Path:
        """Create a sample ticket file for manual editing"""
        
        ticket_dir = Path("tickets")
        ticket_dir.mkdir(exist_ok=True)
        
        sample_data = {
            'ticket_id': ticket_id,
            'summary': 'Auto-refresh for air quality articles',
            'description': 'Implement automatic refresh functionality for air quality articles with current data.',
            'issue_type': 'Story',
            'status': 'In Progress',
            'assignee': 'Developer',
            'reporter': 'Product Owner',
            'priority': 'High',
            'labels': ['backend', 'automation'],
            'components': ['articleupdate-service'],
            'acceptance_criteria': [
                'System should automatically refresh air quality articles every morning at 6 AM',
                'New articles must include current air quality index for major cities',
                'Users should receive notification when articles are updated',
                'System should handle API failures gracefully and retry'
            ]
        }
        
        if file_format == "json":
            file_path = ticket_dir / f"{ticket_id}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, indent=2, ensure_ascii=False)
                
        elif file_format == "yaml":
            file_path = ticket_dir / f"{ticket_id}.yaml"
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(sample_data, f, default_flow_style=False, allow_unicode=True)
                
        else:  # markdown
            file_path = ticket_dir / "ticket.md" if ticket_id == "CMS20-1166" else ticket_dir / f"{ticket_id}.md"
            
            # Create markdown with frontmatter
            md_content = f"""---
ticket_id: {sample_data['ticket_id']}
summary: {sample_data['summary']}
issue_type: {sample_data['issue_type']}
status: {sample_data['status']}
assignee: {sample_data['assignee']}
priority: {sample_data['priority']}
labels: {sample_data['labels']}
components: {sample_data['components']}
---

# {sample_data['summary']}

{sample_data['description']}

## Acceptance Criteria

1. {sample_data['acceptance_criteria'][0]}
2. {sample_data['acceptance_criteria'][1]}
3. {sample_data['acceptance_criteria'][2]}
4. {sample_data['acceptance_criteria'][3]}

## Technical Notes

- API Endpoint: `/api/air-quality/refresh`
- Schedule: Cron job at 6:00 AM daily
- Notification system: Email + In-app

## Definition of Done

- [ ] All acceptance criteria implemented
- [ ] Unit tests written and passing
- [ ] Integration tests written and passing
- [ ] Code reviewed and approved
- [ ] Documentation updated
"""
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
        
        print(f"📝 Sample ticket erstellt: {file_path}")
        print(f"💡 Bearbeiten Sie die Datei mit Ihren Ticket-Informationen")
        
        return file_path
    
    def validate_config(self) -> Dict[str, bool]:
        """Validate Jira configuration"""
        validation = {
            'jira_enabled': self.enabled,
            'api_connection': False,
            'credentials_valid': False,
            'server_reachable': False
        }
        
        if not self.enabled:
            print("ℹ️  Jira Integration deaktiviert")
            return validation
        
        try:
            if self.session:
                validation['credentials_valid'] = True
                
                # Test server connection
                response = self.session.get(f"{self.server_url}/rest/api/3/myself", timeout=10)
                if response.status_code == 200:
                    validation['server_reachable'] = True
                    validation['api_connection'] = True
                    
                    user_info = response.json()
                    print(f"✅ Jira API Validierung erfolgreich")
                    print(f"   Server: {self.server_url}")
                    print(f"   User: {user_info.get('displayName', 'Unknown')}")
                else:
                    print(f"❌ Jira Server nicht erreichbar: {response.status_code}")
            else:
                print("❌ Jira Session nicht initialisiert")
                
        except Exception as e:
            print(f"❌ Jira Validierung fehlgeschlagen: {e}")
        
        return validation


def main():
    """CLI interface for testing Jira integration"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Jira Integration Testing')
    parser.add_argument('--ticket-id', '-t', help='Jira ticket ID to test')
    parser.add_argument('--branch', '-b', help='Branch name to extract ticket ID from')
    parser.add_argument('--create-sample', '-c', help='Create sample ticket file')
    parser.add_argument('--config', help='Path to config file')
    parser.add_argument('--validate', action='store_true', help='Validate Jira configuration')
    
    args = parser.parse_args()
    
    # Default config for testing
    config = {
        'jira_config': {
            'enabled': True,
            'server_url': 'https://your-company.atlassian.net',
            'credentials': {
                'email': '<EMAIL>',
                'api_token': '${JIRA_API_TOKEN}'
            }
        }
    }
    
    if args.config:
        with open(args.config, 'r') as f:
            config = json.load(f)
    
    jira = JiraIntegration(config)
    
    if args.validate:
        validation = jira.validate_config()
        print(f"\n📊 Validation Results:")
        for key, value in validation.items():
            status = "✅" if value else "❌"
            print(f"   {status} {key}: {value}")
        return
    
    if args.create_sample:
        jira.create_sample_ticket_file(args.create_sample)
        return
    
    if args.branch:
        ticket_id = jira.extract_ticket_id_from_branch(args.branch)
        if ticket_id:
            ticket = jira.get_ticket(ticket_id)
            if ticket:
                print(f"\n📋 Ticket Information:")
                print(f"   ID: {ticket.ticket_id}")
                print(f"   Summary: {ticket.summary}")
                print(f"   Status: {ticket.status}")
                print(f"   AC Count: {ticket.get_acceptance_criteria_count()}")
                for i, ac in enumerate(ticket.acceptance_criteria, 1):
                    print(f"     {i}. {ac[:100]}...")
    
    elif args.ticket_id:
        ticket = jira.get_ticket(args.ticket_id)
        if ticket:
            print(f"\n📋 Ticket Information:")
            print(json.dumps(ticket.to_dict(), indent=2, ensure_ascii=False))
    
    else:
        print("❌ Bitte geben Sie --ticket-id oder --branch an")


if __name__ == "__main__":
    main()