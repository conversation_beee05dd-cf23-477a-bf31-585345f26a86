{"version": "0.2.0", "configurations": [{"name": "Debug Flask Backend", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/review-comment-responder-web/claude-integration.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/review-comment-responder-web", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}, "python": "${workspaceFolder}/venv/bin/python", "args": [], "justMyCode": false, "stopOnEntry": false}, {"name": "Debug Code Reviewer API", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/claude-code-reviewer-api.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1"}, "python": "${workspaceFolder}/venv/bin/python", "args": [], "justMyCode": false}, {"name": "Debug Frontend (Vite Dev Server)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/review-comment-responder-react", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Frontend (Chrome)", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/review-comment-responder-react/src", "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*"}, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile"}, {"name": "Attach to Frontend", "type": "chrome", "request": "attach", "port": 9222, "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/review-comment-responder-react/src"}], "compounds": [{"name": "Debug Full Stack", "configurations": ["Debug Flask Backend", "Debug Frontend (Vite Dev Server)"], "stopAll": true}]}