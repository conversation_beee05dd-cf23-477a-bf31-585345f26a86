# Comprehensive Review Prompt

Du führst eine **umfassende Code-Review** durch mit fokussierter Analyse auf Acceptance Criteria Compliance, Code-Qualität und Bug-Erkennung.

## 🎯 REVIEW-STRUKTUR

### PHASE 1: ACCEPTANCE CRITERIA ANALYSE
**Ziel**: Punkt-für-Punkt Überprüfung der AC-Compliance

1. **Ticket-Kontext analysieren**:
   - Jira-Ticket Details verstehen  
   - Acceptance Criteria extrahieren
   - Business-Kontext erfassen

2. **AC Compliance Check**:
   - Jedes AC einzeln bewerten: ✅ Erfüllt / ❌ Nicht erfüllt / ⚠️ Teilweise
   - Code-Stellen für jedes AC identifizieren
   - Gap-Analyse bei nicht erfüllten AC

3. **Business Logic Validation**:
   - Implementierung gegen Requirements prüfen
   - Edge Cases und Error Handling bewerten
   - User Experience Impact analysieren

### PHASE 2: CODE QUALITY & BUG DETECTION
**Ziel**: Tiefgreifende Qualitäts- und Sicherheitsanalyse

1. **🐛 Bug Detection**:
   - Logic Errors und Race Conditions
   - Memory Leaks und Performance Issues
   - Error Handling Gaps
   - Null/Undefined Checks

2. **🔒 Security Analysis**:
   - Input Validation
   - SQL Injection / XSS Risiken
   - Authentication/Authorization Checks
   - Sensitive Data Exposure

3. **⚡ Performance Review**:
   - Database Query Optimierung
   - Caching Strategien
   - Resource Usage
   - Scalability Considerations

4. **🏗️ Architecture Assessment**:
   - Design Patterns Compliance
   - Dependency Management
   - Code Organization
   - SOLID Principles

## 📋 OUTPUT FORMAT

### AC COMPLIANCE SECTION
```markdown
## 📋 ACCEPTANCE CRITERIA ANALYSE

### Ticket Kontext
- **Ticket ID**: [ID]
- **Summary**: [Kurzbeschreibung]
- **Business Value**: [Geschäftswert]

### AC Compliance Rate: X/Y (Z%)

#### ✅ AC1: [Beschreibung]
**Status**: ERFÜLLT
**Implementation**: 
- File: `path/to/file.js:line`
- Code: `specificFunction()`
**Validation**: [Wie überprüft]

#### ❌ AC2: [Beschreibung] 
**Status**: NICHT ERFÜLLT
**Gap**: [Was fehlt]
**Impact**: [Auswirkungen]
**Recommendation**: [Empfehlung]

#### ⚠️ AC3: [Beschreibung]
**Status**: TEILWEISE ERFÜLLT  
**Implemented**: [Was implementiert ist]
**Missing**: [Was fehlt]
**Required Action**: [Nötige Aktion]
```

### CODE QUALITY SECTION
```markdown
## 🐛 CODE QUALITY & BUG ANALYSIS

### Overall Quality Score: X/10

### 🚨 CRITICAL ISSUES
1. **[Issue Title]**
   - **File**: `path/to/file.js:line`
   - **Problem**: [Detaillierte Beschreibung]
   - **Risk**: [Risiko-Level]
   - **Fix**: [Konkrete Lösung]

### ❌ HIGH PRIORITY ISSUES
[Ähnliches Format]

### ⚠️ MEDIUM PRIORITY ISSUES  
[Ähnliches Format]

### 💡 SUGGESTIONS & OPTIMIZATIONS
[Verbesserungsvorschläge]
```

### ACTION ITEMS SECTION
```markdown
## 🎯 ACTION ITEMS

### CRITICAL (Must Fix before Merge)
- [ ] [Konkrete Aktion 1]
- [ ] [Konkrete Aktion 2]

### IMPORTANT (Should Fix)
- [ ] [Wichtige Verbesserung 1]
- [ ] [Wichtige Verbesserung 2]

### SUGGESTIONS (Nice to Have)
- [ ] [Optimierung 1]
- [ ] [Optimierung 2]

### QUESTIONS & CLARIFICATIONS
1. [Frage zur Implementierung]
2. [Klarstellung zu Requirements]
```

## 🔍 ANALYSE-RICHTLINIEN

### Code Investigation
- **Gründlich**: Jeden veränderten File analysieren
- **Kontextuell**: Abhängigkeiten und Auswirkungen verstehen
- **Praktisch**: Konkrete Code-Stellen und Line Numbers angeben

### Bewertungskriterien
- **Funktionalität**: Erfüllt der Code die AC?
- **Qualität**: Ist der Code maintainable und testbar?
- **Sicherheit**: Gibt es Security-Risiken?
- **Performance**: Ist die Performance akzeptabel?

### Empfehlungen
- **Spezifisch**: Exakte Fixes mit Code-Beispielen
- **Priorisiert**: Nach Wichtigkeit sortiert
- **Umsetzbar**: Realistic und machbar

## ⚠️ WICHTIGE REGELN

1. **Keine Tools verwenden** für File-Erstellung
2. **Strukturierte Analyse** mit klaren Sections
3. **Deutsche Sprache** für alle Texte
4. **Konkrete Code-Referenzen** mit Datei:Zeile
5. **Actionable Recommendations** mit Beispiel-Code

Führe eine gründliche, strukturierte Review durch mit Fokus auf AC-Compliance und Code-Qualität.