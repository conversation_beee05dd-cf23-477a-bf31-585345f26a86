# Bug Analysis Review Prompt

Du führst eine **tiefgreifende Bug Detection und Code Quality Analyse** durch mit Fokus auf Sicherheit, Performance und Maintainability.

## 🎯 REVIEW-FOKUS: BUG DETECTION & QUALITY

### HAUPTZIEL
Systematische Identifikation von Bugs, Security-Vulnerabilities, Performance-Issues und Code-Qualitätsproblemen mit konkreten Fix-Empfehlungen.

## 🔍 ANALYSE-KATEGORIEN

### 1. 🐛 LOGIC BUGS & RUNTIME ERRORS
**Schwerpunkt**: Funktionale Korrektheit und Error Handling

- **Null/Undefined Access**: Fehlende Null-Checks
- **Array/Object Bounds**: Index-out-of-bounds, Property-Access
- **Type Errors**: Implicit conversions, Type mismatches
- **Race Conditions**: Async/await issues, Promise handling
- **Loop Logic**: Infinite loops, wrong break conditions
- **Error Propagation**: Try/catch gaps, unhandled exceptions

### 2. 🔒 SECURITY VULNERABILITIES
**Schwerpunkt**: Application Security und Data Protection

- **Input Validation**: SQL Injection, XSS, Command Injection
- **Authentication**: JWT handling, Session management
- **Authorization**: Access control, Permission checks
- **Data Exposure**: Sensitive data in logs, API responses
- **Crypto Issues**: Weak encryption, hardcoded secrets
- **Dependencies**: Vulnerable package versions

### 3. ⚡ PERFORMANCE ISSUES
**Schwerpunkt**: Scalability und Resource Usage

- **Database**: N+1 queries, missing indexes, slow queries
- **Memory**: Leaks, large object retention, inefficient data structures
- **Network**: Unnecessary API calls, large payloads, missing caching
- **Frontend**: Bundle size, render performance, memory usage
- **Algorithms**: O(n²) where O(n) possible, inefficient searches

### 4. 🏗️ CODE QUALITY & MAINTAINABILITY
**Schwerpunkt**: Long-term Code Health

- **Architecture**: SOLID violations, coupling issues
- **Patterns**: Anti-patterns, inconsistent implementations
- **Readability**: Complex functions, unclear naming
- **Testing**: Missing test coverage, brittle tests
- **Documentation**: Missing or outdated docs

## 📊 DETAILLIERTES OUTPUT FORMAT

### EXECUTIVE DASHBOARD
```markdown
## 🐛 BUG ANALYSIS EXECUTIVE DASHBOARD

**Overall Risk Level**: [HIGH/MEDIUM/LOW]
**Code Quality Score**: X/10
**Security Risk**: [CRITICAL/HIGH/MEDIUM/LOW]
**Performance Impact**: [HIGH/MEDIUM/LOW]

### Issue Summary
- 🚨 Critical Issues: X (Must fix before merge)
- ❌ High Priority: Y (Should fix before merge)  
- ⚠️ Medium Priority: Z (Fix in next sprint)
- 💡 Suggestions: W (Nice to have)

### Risk Categories
- 🔒 Security Issues: X
- ⚡ Performance Issues: Y
- 🐛 Logic Bugs: Z
- 🏗️ Architecture Issues: W
```

### CRITICAL ISSUES SECTION
```markdown
## 🚨 CRITICAL ISSUES (Must Fix)

### 1. SQL Injection Vulnerability
**File**: `src/services/userService.ts:67`
**Severity**: CRITICAL
**Risk**: Data breach, unauthorized access

**Vulnerable Code**:
```typescript
const query = `SELECT * FROM users WHERE email = '${email}'`;
const result = await db.query(query);
```

**Problem**: Direct string interpolation in SQL query
**Attack Vector**: `email = "'; DROP TABLE users; --"`
**Impact**: Complete database compromise

**Fix**:
```typescript
const query = 'SELECT * FROM users WHERE email = ?';
const result = await db.query(query, [email]);
```

**Verification**:
- Test with malicious input: `'; DROP TABLE test; --`
- Use static analysis tools (ESLint security rules)
- Add integration tests for SQL injection attempts

---

### 2. Race Condition in Payment Processing
**File**: `src/controllers/paymentController.ts:89-123`
**Severity**: CRITICAL
**Risk**: Double charges, data corruption

**Problematic Code**:
```typescript
async function processPayment(userId, amount) {
  const balance = await getBalance(userId);
  if (balance >= amount) {
    await deductBalance(userId, amount);
    await createTransaction(userId, amount);
  }
}
```

**Problem**: Time-of-check vs time-of-use race condition
**Scenario**: Concurrent requests can bypass balance check
**Impact**: Users can spend more than available balance

**Fix**:
```typescript
async function processPayment(userId, amount) {
  const transaction = await db.transaction();
  try {
    const balance = await getBalance(userId, { transaction, lock: true });
    if (balance >= amount) {
      await deductBalance(userId, amount, { transaction });
      await createTransaction(userId, amount, { transaction });
    }
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```
```

### HIGH PRIORITY SECTION
```markdown
## ❌ HIGH PRIORITY ISSUES

### 1. Memory Leak in Event Listeners
**File**: `src/components/DataVisualization.tsx:45-78`
**Severity**: HIGH
**Impact**: Browser memory growth, performance degradation

**Problem**: Event listeners not cleaned up in useEffect
```typescript
useEffect(() => {
  window.addEventListener('resize', handleResize);
  // Missing cleanup!
}, []);
```

**Fix**:
```typescript
useEffect(() => {
  window.addEventListener('resize', handleResize);
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);
```

**Testing**: Monitor memory usage with React DevTools Profiler

---

### 2. N+1 Query Problem
**File**: `src/services/articleService.ts:134-156`
**Severity**: HIGH
**Impact**: Database performance, slow API responses

**Problem**: Loading authors in loop
```typescript
const articles = await Article.findAll();
for (const article of articles) {
  article.author = await User.findById(article.authorId);
}
```

**Fix**: Use eager loading
```typescript
const articles = await Article.findAll({
  include: [{ model: User, as: 'author' }]
});
```

**Performance Impact**: Reduces 1+N queries to 1 query
```

### MEDIUM PRIORITY & SUGGESTIONS
```markdown
## ⚠️ MEDIUM PRIORITY ISSUES

### 1. Error Handling Inconsistency
**Files**: Multiple controller files
**Impact**: Poor user experience, debugging difficulties

**Problem**: Inconsistent error response formats
**Fix**: Implement centralized error handling middleware

### 2. Missing Input Validation
**File**: `src/controllers/profileController.ts`
**Impact**: Data integrity issues
**Fix**: Add Joi/Zod validation schemas

## 💡 SUGGESTIONS & OPTIMIZATIONS

### Performance Optimizations
1. **Bundle Splitting**: Implement code splitting for large components
2. **Caching**: Add Redis cache for frequently accessed data
3. **Image Optimization**: Implement lazy loading and WebP format

### Code Quality Improvements
1. **Type Safety**: Add stricter TypeScript configurations
2. **Testing**: Increase test coverage from 67% to 85%
3. **Documentation**: Add JSDoc comments for public APIs
```

### SECURITY ASSESSMENT
```markdown
## 🔒 SECURITY ASSESSMENT

### Current Security Posture: MEDIUM RISK

### Identified Vulnerabilities
1. **Input Validation**: 3 endpoints missing validation
2. **Authentication**: JWT tokens not properly verified
3. **CORS**: Overly permissive CORS configuration
4. **Dependencies**: 2 packages with known vulnerabilities

### Security Recommendations
1. **Immediate**: Fix SQL injection (Critical)
2. **Short-term**: Implement input validation everywhere
3. **Long-term**: Security audit and penetration testing

### Compliance Notes
- GDPR: Personal data handling needs review
- PCI DSS: Payment processing security gaps identified
```

### ACTION PLAN
```markdown
## 🎯 STRUCTURED ACTION PLAN

### PHASE 1: Critical Fixes (This Sprint)
**Estimated Time**: 16 hours
- [ ] Fix SQL injection vulnerability (4h)
- [ ] Resolve payment race condition (6h)
- [ ] Add missing error handling (3h)
- [ ] Update vulnerable dependencies (2h)
- [ ] Add unit tests for fixes (1h)

### PHASE 2: High Priority (Next Sprint)  
**Estimated Time**: 24 hours
- [ ] Fix memory leaks in React components (8h)
- [ ] Optimize database queries (6h)
- [ ] Implement input validation (8h)
- [ ] Security audit and fixes (2h)

### PHASE 3: Improvements (Future Sprints)
- [ ] Performance optimizations
- [ ] Code quality enhancements
- [ ] Documentation improvements

### Verification Strategy
1. **Automated Tests**: Add tests for each fix
2. **Security Scan**: Run OWASP ZAP or similar
3. **Performance Testing**: Load testing with fixed issues
4. **Code Review**: Secondary review of all fixes
```

## 🔍 ANALYSE-RICHTLINIEN

### Deep Code Investigation
- **Systematisch**: Jeden File und jede Funktion analysieren
- **Kontextuell**: Auswirkungen auf andere Components verstehen
- **Praktisch**: Konkrete Code-Stellen mit Line Numbers

### Risk Assessment
- **Business Impact**: Wie kritisch ist der Bug für Business?
- **Technical Impact**: Performance, Security, Stability
- **User Impact**: UX-Degradation, Data Loss Risk

### Solution Quality
- **Konkrete Fixes**: Exakte Code-Beispiele
- **Testbare Lösungen**: Wie wird der Fix verifiziert?
- **Nachhaltige Ansätze**: Prevent similar bugs in future

## ⚠️ WICHTIGE ANALYSE-REGELN

1. **Security First**: Security-Issues haben höchste Priorität
2. **Evidence-Based**: Konkrete Code-Stellen und Line Numbers
3. **Practical Fixes**: Umsetzbare Lösungen mit Code-Beispielen
4. **Risk-Prioritized**: Nach Business/Technical Impact sortieren
5. **Verification Focus**: Wie wird jeder Fix getestet und validiert?

Führe eine systematische, tiefgreifende Bug-Analyse durch mit Fokus auf Security, Performance und langfristige Code-Qualität.