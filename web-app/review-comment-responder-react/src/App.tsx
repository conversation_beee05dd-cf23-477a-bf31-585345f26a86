import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Navigation } from './components/Navigation'
import { CodeReviewResponder } from './pages/CodeReviewResponder'
import { CodeReviewer } from './pages/CodeReviewer'
import { Settings } from './pages/Settings'
import { AuthCallback } from './components/auth/AuthCallback'
import { JiraAuthCallback } from './components/auth/JiraAuthCallback'
import { AuthProvider } from './components/auth/AuthProvider'
import { ToastProvider } from './components/ui/toast'
import { ErrorBoundary } from './components/ErrorBoundary'
import { WorktreeConfigProvider } from './contexts/WorktreeConfigContext'
import { WorktreeStatusProvider } from './contexts/WorktreeStatusContext'

function App() {
  return (
    <ErrorBoundary showDetails={import.meta.env.DEV}>
      <Router>
        <AuthProvider>
          <ToastProvider>
            <WorktreeStatusProvider>
              <WorktreeConfigProvider>
              <div className="min-h-screen bg-background relative overflow-hidden">
              {/* Modern Blurry Violet Radial Decorator - Top Left */}
              <div className="fixed -top-32 -left-32 w-[800px] h-[800px] pointer-events-none z-0">
                <div className="absolute inset-0 bg-gradient-radial from-violet-500/15 via-purple-600/8 to-transparent blur-3xl opacity-50"></div>
                <div className="absolute inset-16 bg-gradient-radial from-fuchsia-400/12 via-violet-500/6 to-transparent blur-2xl opacity-60"></div>
                <div className="absolute inset-32 bg-gradient-radial from-purple-400/10 via-indigo-500/4 to-transparent blur-xl opacity-55"></div>
              </div>
              
              {/* Subtle secondary blur decoration - Top Right */}
              <div className="fixed -top-16 -right-16 w-96 h-96 pointer-events-none z-0">
                <div className="absolute inset-0 bg-gradient-radial from-cyan-400/8 via-blue-500/4 to-transparent blur-2xl opacity-35"></div>
              </div>

              {/* Navigation */}
              <Navigation />

              {/* Main Content */}
              <main className="relative z-10">
                <Routes>
                  <Route path="/" element={<CodeReviewResponder />} />
                  <Route path="/reviewer" element={<CodeReviewer />} />
                  <Route path="/settings" element={<Settings />} />
                  <Route path="/auth/bitbucket/callback" element={<AuthCallback />} />
                  <Route path="/auth/jira/callback" element={<JiraAuthCallback />} />
                </Routes>
              </main>
              </div>
              </WorktreeConfigProvider>
            </WorktreeStatusProvider>
          </ToastProvider>
        </AuthProvider>
      </Router>
    </ErrorBoundary>
  )
}

export default App
