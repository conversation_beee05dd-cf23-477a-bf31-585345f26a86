import React, { useState, useEffect } from 'react'
import { GitPullRequest, Loader2, RefreshCw, MessageSquare, Clock, CheckCircle, XCircle, FolderGit2, AlertCircle } from 'lucide-react'
import { But<PERSON> } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import type { BitbucketRepository, BitbucketBranch, BitbucketPullRequest } from '../../types/bitbucket.types'
import { useRepositoryService } from '../../services/bitbucket/RepositoryService'
import { LocalBranchDetector } from '../../services/git/LocalBranchDetector'
import { useCommentStore } from '../../store/useCommentStore'
import { useWorktreeConfig } from '../../contexts/WorktreeConfigContext'
import { DirectoryPicker } from '../worktree/DirectoryPicker'

interface PullRequestSelectorProps {
  repository: BitbucketRepository | null
  branch: BitbucketBranch | null
  selectedPullRequest: BitbucketPullRequest | null
  onPullRequestSelect: (pullRequest: BitbucketPullRequest) => void
  className?: string
}

export const PullRequestSelector: React.FC<PullRequestSelectorProps> = ({
  repository,
  branch,
  selectedPullRequest,
  onPullRequestSelect,
  className
}) => {
  const { getRepositoryPullRequests } = useRepositoryService()
  const { updatePRContext } = useCommentStore()
  const { config: worktreeConfig } = useWorktreeConfig()
  
  const [pullRequests, setPullRequests] = useState<BitbucketPullRequest[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [worktreeStatus, setWorktreeStatus] = useState<Record<number, { exists: boolean, path?: string, checking?: boolean }>>({})
  const [showWorktreeConfig, setShowWorktreeConfig] = useState(false)

  // Load pull requests when repository or branch changes
  useEffect(() => {
    if (repository) {
      loadPullRequests()
    } else {
      setPullRequests([])
      setError(null)
    }
  }, [repository, branch])

  const loadPullRequests = async () => {
    if (!repository) return

    try {
      setIsLoading(true)
      setError(null)
      
      const data = await getRepositoryPullRequests(
        repository.workspace.slug,
        repository.name,
        branch?.name // Filter by branch if selected
      )
      
      setPullRequests(data)
      
      // Check worktree status for each PR
      checkWorktreeStatusForPRs(data)
    } catch (error) {
      console.error('Failed to load pull requests:', error)
      setError(error instanceof Error ? error.message : 'Failed to load pull requests')
    } finally {
      setIsLoading(false)
    }
  }

  const checkWorktreeStatusForPRs = async (prs: BitbucketPullRequest[]) => {
    if (!repository) return

    for (const pr of prs) {
      // Set checking status
      setWorktreeStatus(prev => ({
        ...prev,
        [pr.id]: { exists: false, checking: true }
      }))

      try {
        const worktreePath = await LocalBranchDetector.autoDetectWorktree(
          repository.workspace.slug,
          repository.name,
          pr.source.branch.name
        )

        setWorktreeStatus(prev => ({
          ...prev,
          [pr.id]: { 
            exists: !!worktreePath, 
            path: worktreePath || undefined,
            checking: false 
          }
        }))
      } catch (error) {
        console.warn(`Failed to check worktree for PR #${pr.id}:`, error)
        setWorktreeStatus(prev => ({
          ...prev,
          [pr.id]: { exists: false, checking: false }
        }))
      }
    }
  }

  const handlePullRequestClick = async (pullRequest: BitbucketPullRequest) => {
    onPullRequestSelect(pullRequest)

    // Auto-set worktree context if available
    const status = worktreeStatus[pullRequest.id]
    if (status?.exists && status.path) {
      console.log(`🚀 Auto-switching to worktree mode for PR #${pullRequest.id}`)
      updatePRContext({
        prUrl: pullRequest.links.html.href,
        branchName: pullRequest.source.branch.name,
        worktreePath: status.path,
        ticketDescription: pullRequest.title,
        changesSummary: pullRequest.description || ''
      })
    } else {
      console.log(`ℹ️ No local worktree found for PR #${pullRequest.id} - using API mode`)
      updatePRContext({
        prUrl: pullRequest.links.html.href,
        branchName: pullRequest.source.branch.name,
        worktreePath: '', // Clear worktree path to use API mode
        ticketDescription: pullRequest.title,
        changesSummary: pullRequest.description || ''
      })
    }
  }

  const getStateIcon = (state: string) => {
    switch (state) {
      case 'OPEN':
        return <GitPullRequest className="h-4 w-4 text-green-500" />
      case 'MERGED':
        return <CheckCircle className="h-4 w-4 text-purple-500" />
      case 'DECLINED':
      case 'SUPERSEDED':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <GitPullRequest className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getStateBadgeVariant = (state: string) => {
    switch (state) {
      case 'OPEN':
        return 'default'
      case 'MERGED':
        return 'secondary'
      case 'DECLINED':
      case 'SUPERSEDED':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return 'Today'
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const renderPullRequestItem = (pr: BitbucketPullRequest) => {
    const isSelected = selectedPullRequest?.id === pr.id
    const createdDate = formatDate(pr.created_on)
    const updatedDate = formatDate(pr.updated_on)

    return (
      <div
        key={pr.id}
        className={cn(
          'p-4 rounded-lg cursor-pointer transition-all duration-200 border',
          isSelected 
            ? 'border-primary bg-primary/10 shadow-md' 
            : 'border-border hover:border-primary/50 hover:bg-muted/30'
        )}
        onClick={() => handlePullRequestClick(pr)}
      >
        <div className="space-y-3">
          {/* Header Row - PR Number, Title, State */}
          <div className="flex items-start gap-3">
            <div className="shrink-0 mt-0.5">
              {getStateIcon(pr.state)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <h3 className={cn(
                    'font-medium text-sm leading-tight line-clamp-2',
                    isSelected ? 'text-primary' : 'text-foreground'
                  )}>
                    <span className="font-mono text-xs opacity-75">#{pr.id}</span>{' '}
                    {pr.title}
                  </h3>
                </div>
                
                <div className="shrink-0">
                  <Badge variant={getStateBadgeVariant(pr.state)} className="text-xs">
                    {pr.state.toLowerCase()}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Metadata Row - Author, Date, Comments */}
          <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-xs text-muted-foreground">
            <span className="font-medium">by {pr.author.display_name}</span>
            
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 shrink-0" />
              <span className="whitespace-nowrap">Created {createdDate}</span>
            </div>
            
            <div className="flex items-center gap-1">
              <span className="whitespace-nowrap">Updated {updatedDate}</span>
            </div>
            
            {pr.comment_count > 0 && (
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3 shrink-0" />
                <span className="whitespace-nowrap">{pr.comment_count} comments</span>
              </div>
            )}
            
            {pr.reviewers && pr.reviewers.length > 0 && (
              <span className="whitespace-nowrap">{pr.reviewers.length} reviewers</span>
            )}
          </div>

          {/* Branch Flow Row & Worktree Status */}
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <span className="px-2 py-1 bg-blue-500/10 text-blue-400 rounded text-xs font-mono truncate max-w-32" title={pr.source.branch.name}>
                {pr.source.branch.name}
              </span>
              <span className="text-muted-foreground shrink-0">→</span>
              <span className="px-2 py-1 bg-green-500/10 text-green-400 rounded text-xs font-mono truncate max-w-32" title={pr.destination.branch.name}>
                {pr.destination.branch.name}
              </span>
            </div>

            {/* Worktree Status */}
            <div className="shrink-0">
              {(() => {
                const status = worktreeStatus[pr.id]
                if (status?.checking) {
                  return (
                    <div className="flex items-center gap-1.5 px-2 py-1 bg-muted/50 rounded text-xs">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      <span className="text-muted-foreground">Checking...</span>
                    </div>
                  )
                } else if (status?.exists) {
                  return (
                    <div className="flex items-center gap-1.5 px-2 py-1 bg-emerald-500/10 text-emerald-600 rounded text-xs" title={`Local worktree: ${status.path}`}>
                      <FolderGit2 className="h-3 w-3" />
                      <span className="font-medium">Local</span>
                    </div>
                  )
                } else if (status && !status.exists) {
                  return (
                    <div className="flex items-center gap-1.5 px-2 py-1 bg-amber-500/10 text-amber-600 rounded text-xs" title="No local worktree found - using API mode">
                      <AlertCircle className="h-3 w-3" />
                      <span className="font-medium">API</span>
                    </div>
                  )
                }
                return null
              })()}
            </div>
          </div>

          {/* Description Row (if available) */}
          {pr.description && pr.description.trim() && (
            <div className="pt-2 border-t border-border/50">
              <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed">
                {pr.description}
              </p>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Filter PRs by branch if selected
  const filteredPullRequests = branch 
    ? pullRequests.filter(pr => 
        pr.source.branch.name === branch.name || 
        pr.destination.branch.name === branch.name
      )
    : pullRequests

  // Group PRs by state
  const openPRs = filteredPullRequests.filter(pr => pr.state === 'OPEN')
  const mergedPRs = filteredPullRequests.filter(pr => pr.state === 'MERGED')
  const otherPRs = filteredPullRequests.filter(pr => !['OPEN', 'MERGED'].includes(pr.state))

  if (!repository) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <GitPullRequest className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            Select Repository First
          </h3>
          <p className="text-muted-foreground">
            Please select a repository to view its pull requests.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <GitPullRequest className="h-5 w-5 text-primary" />
            Select Pull Request
            {filteredPullRequests.length > 0 && (
              <span className="text-sm font-normal text-muted-foreground">
                ({filteredPullRequests.length} available)
              </span>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={loadPullRequests}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn(
              'h-4 w-4',
              isLoading && 'animate-spin'
            )} />
            Refresh
          </Button>
        </CardTitle>
        
        <div className="text-sm text-muted-foreground space-y-1">
          <p>Repository: <span className="font-medium">{repository.full_name}</span></p>
          {branch && (
            <p>Branch: <span className="font-medium">{branch.name}</span></p>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {error && (
          <div className="p-4 bg-red-500/10 border-b border-red-500/20 text-red-400 text-sm">
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={loadPullRequests}
              className="ml-2 text-red-400 hover:text-red-300"
            >
              Retry
            </Button>
          </div>
        )}

        <div className="max-h-[500px] overflow-y-auto custom-scrollbar">
          {isLoading ? (
            <div className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
              <p className="text-muted-foreground">Loading pull requests...</p>
            </div>
          ) : filteredPullRequests.length > 0 ? (
            <div className="space-y-6">
              {/* Worktree Configuration Section */}
              <div className="mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowWorktreeConfig(!showWorktreeConfig)}
                  className="flex items-center gap-2"
                >
                  <FolderGit2 className="h-4 w-4" />
                  Configure Worktree Directory
                  {worktreeConfig && !worktreeConfig.isValid && (
                    <AlertCircle className="h-4 w-4 text-destructive" />
                  )}
                </Button>
                
                {worktreeConfig && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Current: {worktreeConfig.basePath}
                    {!worktreeConfig.isValid && " (Invalid - needs configuration)"}
                  </p>
                )}
              </div>

              {/* Worktree Configuration Panel */}
              {showWorktreeConfig && (
                <div className="mb-6">
                  <DirectoryPicker 
                    onPathSelected={() => {
                      setShowWorktreeConfig(false)
                      // Refresh PR worktree status if needed
                      // This would trigger a re-check of worktree paths
                    }}
                  />
                </div>
              )}

              {/* Open PRs */}
              {openPRs.length > 0 && (
                <div className="px-4 pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Open Pull Requests
                    </h4>
                    <Badge variant="secondary" className="text-xs">
                      {openPRs.length}
                    </Badge>
                  </div>
                  <div className="space-y-3">
                    {openPRs
                      .sort((a, b) => new Date(b.updated_on).getTime() - new Date(a.updated_on).getTime())
                      .map(pr => renderPullRequestItem(pr))
                    }
                  </div>
                </div>
              )}

              {/* Merged PRs */}
              {mergedPRs.length > 0 && (
                <div className="px-4">
                  <div className="flex items-center justify-between mb-3 pt-2 border-t border-border/50">
                    <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      Recently Merged
                    </h4>
                    <Badge variant="outline" className="text-xs">
                      {mergedPRs.length > 5 ? `${mergedPRs.length} (showing 5)` : mergedPRs.length}
                    </Badge>
                  </div>
                  <div className="space-y-3">
                    {mergedPRs
                      .sort((a, b) => new Date(b.updated_on).getTime() - new Date(a.updated_on).getTime())
                      .slice(0, 5) // Show only last 5 merged PRs
                      .map(pr => renderPullRequestItem(pr))
                    }
                  </div>
                </div>
              )}

              {/* Other PRs (declined, superseded) */}
              {otherPRs.length > 0 && (
                <div className="px-4 pb-4">
                  <div className="flex items-center justify-between mb-3 pt-2 border-t border-border/50">
                    <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      Other Pull Requests
                    </h4>
                    <Badge variant="outline" className="text-xs">
                      {otherPRs.length > 3 ? `${otherPRs.length} (showing 3)` : otherPRs.length}
                    </Badge>
                  </div>
                  <div className="space-y-3">
                    {otherPRs
                      .sort((a, b) => new Date(b.updated_on).getTime() - new Date(a.updated_on).getTime())
                      .slice(0, 3) // Show only last 3 other PRs
                      .map(pr => renderPullRequestItem(pr))
                    }
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="p-8 text-center">
              <GitPullRequest className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">
                {branch 
                  ? `No pull requests found for branch "${branch.name}"`
                  : 'No pull requests found'
                }
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
