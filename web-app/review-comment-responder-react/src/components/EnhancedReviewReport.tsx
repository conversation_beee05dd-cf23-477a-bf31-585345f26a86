import React, { useState, use<PERSON>emo, useCallback } from 'react'
import { 
  FileText, 
  Target, 
  Bug, 
  Shield, 
  AlertTriangle,
  Download,
  ExternalLink,
  BarChart3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from 'lucide-react'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Progress } from './ui/progress'
import type { StructuredReviewResult } from '../services/codeReviewer/CodeReviewerService'

// Component imports for sections
import { ACAnalysisSection } from './enhanced-report/ACAnalysisSection'
import { CodeQualitySection } from './enhanced-report/CodeQualitySection'
import { BugDetectionSection } from './enhanced-report/BugDetectionSection'
import { ActionItemsSection } from './enhanced-report/ActionItemsSection'
import { ArchitecturalAssessmentSection } from './enhanced-report/ArchitecturalAssessmentSection'
import { VariableAnalysisSection } from './enhanced-report/VariableAnalysisSection'
import { NextStepsSection } from './enhanced-report/NextStepsSection'

interface EnhancedReportData {
  metadata: {
    generated_at: string
    review_type: string
    fallback_mode?: boolean
    parsing_error?: string
  }
  acceptance_criteria_analysis: {
    executive_summary: {
      total_ac: number
      fulfilled: number
      partially_fulfilled: number
      not_fulfilled: number
      compliance_rate: number
      business_alignment_score: number
    }
    detailed_results: Array<{
      id: string
      text: string
      status: 'fulfilled' | 'partially_fulfilled' | 'not_fulfilled' | 'pending'
      implementation_evidence: string
      issues: string[]
    }>
  }
  code_quality_analysis: {
    executive_summary: {
      overall_score: number
      critical_issues: number
      code_smells: number
      duplication_level: number
    }
    code_duplication: any[]
    complexity_issues: any[]
    naming_consistency: any[]
  }
  bug_detection_results: {
    critical_bugs: Array<{
      text: string
      severity: string
      file?: string
      type: string
    }>
    logic_errors: any[]
    runtime_risks: any[]
  }
  action_items: {
    critical: Array<{
      text: string
      priority: string
      category: string
    }>
    important: any[]
    suggestions: any[]
  }
  architectural_assessment: {
    design_patterns: any[]
    integration_quality: any[]
    violations: any[]
  }
  security_performance: {
    security_findings: any[]
    performance_analysis: any[]
  }
  variable_parameter_analysis: {
    executive_summary: {
      total_variables: number
      naming_issues: number
      scope_issues: number
      consistency_score: number
    }
    naming_analysis: Array<{
      concept: string
      file: string
      variable_name: string
      pattern: string
      status: 'consistent' | 'inconsistent' | 'needs_review'
      suggestion?: string
      line_number?: number
    }>
    scope_analysis: Array<{
      issue_type: 'shadowing' | 'unused' | 'scope_pollution'
      file: string
      variable_name: string
      description: string
      recommendation: string
      line_number?: number
    }>
    type_consistency: Array<{
      concept: string
      files: string[]
      patterns: string[]
      consistency_status: 'good' | 'mixed' | 'poor'
      recommendation: string
    }>
  }
  next_steps: {
    priority_assessment: {
      critical_blockers: number
      high_priority: number
      medium_priority: number
      can_merge: boolean
      estimated_effort: string
    }
    immediate_actions: Array<{
      action: string
      category: 'ac_compliance' | 'security' | 'bugs' | 'quality'
      priority: 'critical' | 'high' | 'medium'
      effort: string
      description: string
    }>
    follow_up_tasks: Array<{
      task: string
      category: string
      timeline: string
      assignee_suggestion?: string
      description: string
    }>
    merge_readiness: {
      status: 'ready' | 'blocked' | 'needs_review'
      blockers: string[]
      recommendations: string[]
    }
    post_merge_actions: Array<{
      action: string
      timeline: string
      importance: 'high' | 'medium' | 'low'
      description: string
    }>
  }
  questions_clarifications: any[]
}

interface ReviewResults {
  session_id: string
  review_mode: string
  branch_name: string
  pr_url?: string
  timestamp: string
  raw_review: string
  enhanced_report?: EnhancedReportData
  jira_ticket?: {
    ticket_id: string
    summary: string
    status: string
    acceptance_criteria_count: number
    acceptance_criteria: string[]
  }
  metadata: {
    changed_files: string[]
    diff_summary?: string
    file_count: number
  }
  summary: {
    total_files_reviewed: number
    total_findings: number
    security_issues: number
    potential_bugs: number
    quality_suggestions?: number
    review_length?: number
    estimated_review_time?: string
  }
  structured_findings?: {
    acceptance_criteria: any[]
    code_quality: any[]
    security_issues: any[]
    performance_issues: any[]
    bugs: any[]
    suggestions: any[]
  }
  parsing_metadata?: {
    fallback_mode?: boolean
    error_details?: {
      error_type: string
      error_message: string
      timestamp: string
    }
  }
}

interface EnhancedReviewReportProps {
  results: StructuredReviewResult | ReviewResults // Support both formats
  className?: string
  onExportReport?: (results: StructuredReviewResult | ReviewResults) => void
}

// Helper function to detect if results are new StructuredReviewResult format
const isStructuredReviewResult = (results: any): results is StructuredReviewResult => {
  return results && results.structured_data && results.structured_data.executive_summary
}

export const EnhancedReviewReport: React.FC<EnhancedReviewReportProps> = React.memo(({ 
  results, 
  className = '',
  onExportReport 
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['executive-summary']))

  // Enhanced debugging
  console.log('🔥 ENHANCED REVIEW REPORT - Input results:', results)
  console.log('🔥 ENHANCED REVIEW REPORT - Is StructuredReviewResult?', isStructuredReviewResult(results))
  console.log('🔥 ENHANCED REVIEW REPORT - Results type:', typeof results)
  console.log('🔥 ENHANCED REVIEW REPORT - Results keys:', Object.keys(results || {}))
  if (isStructuredReviewResult(results)) {
    console.log('🔥 ENHANCED REVIEW REPORT - Structured data:', results.structured_data)
    console.log('🔥 ENHANCED REVIEW REPORT - AC data:', results.structured_data?.acceptance_criteria)
    console.log('🔥 ENHANCED REVIEW REPORT - Code quality findings:', results.structured_data?.code_quality_findings)
  }

  // Memoize enhanced report data to prevent unnecessary re-renders
  const enhanced = useMemo(() => {
    if (isStructuredReviewResult(results)) {
      // Convert new StructuredReviewResult to legacy EnhancedReportData format
      const structuredData = results.structured_data
      const metadata = results.metadata
      
      return {
        metadata: {
          generated_at: results.timestamp,
          review_type: results.review_type,
          fallback_mode: false
        },
        acceptance_criteria_analysis: {
          executive_summary: {
            total_ac: structuredData.acceptance_criteria?.length || 0,
            fulfilled: structuredData.acceptance_criteria?.filter(ac => ac.status === 'FULFILLED').length || 0,
            partially_fulfilled: structuredData.acceptance_criteria?.filter(ac => ac.status === 'PARTIAL').length || 0,
            not_fulfilled: structuredData.acceptance_criteria?.filter(ac => ac.status === 'NOT_FULFILLED').length || 0,
            compliance_rate: structuredData.executive_summary?.ac_compliance?.percentage || 0,
            business_alignment_score: Math.round((structuredData.executive_summary?.ac_compliance?.percentage || 0) / 10)
          },
          detailed_results: structuredData.acceptance_criteria?.map(ac => ({
            id: ac.id,
            text: ac.title,
            status: ac.status === 'FULFILLED' ? 'fulfilled' as const : 
                   ac.status === 'PARTIAL' ? 'partially_fulfilled' as const : 
                   'not_fulfilled' as const,
            implementation_evidence: `Status: ${ac.status}`,
            issues: []
          })) || []
        },
        code_quality_analysis: {
          executive_summary: {
            overall_score: structuredData.executive_summary?.code_quality_score || 8,
            critical_issues: structuredData.executive_summary?.critical_issues || 0,
            code_smells: structuredData.executive_summary?.warning_issues || 0,
            duplication_level: 0
          },
          code_duplication: [],
          complexity_issues: [],
          naming_consistency: []
        },
        bug_detection_results: {
          critical_bugs: structuredData.code_quality_findings?.filter(f => f.severity === 'critical').map(f => ({
            text: f.description,
            severity: f.severity,
            type: f.type
          })) || [],
          logic_errors: [],
          runtime_risks: []
        },
        action_items: {
          critical: structuredData.action_items?.critical?.map(item => ({
            text: item,
            priority: 'critical',
            category: 'critical'
          })) || [],
          important: structuredData.action_items?.important?.map(item => ({
            text: item,
            priority: 'important',
            category: 'important'
          })) || [],
          suggestions: structuredData.action_items?.suggestions?.map(item => ({
            text: item,
            priority: 'suggestion',
            category: 'suggestion'
          })) || []
        },
        architectural_assessment: {
          design_patterns: [],
          integration_quality: [],
          violations: []
        },
        security_performance: {
          security_findings: structuredData.code_quality_findings?.filter(f => 
            f.type.toLowerCase().includes('security')
          ) || [],
          performance_analysis: structuredData.code_quality_findings?.filter(f => 
            f.type.toLowerCase().includes('performance')
          ) || []
        },
        variable_parameter_analysis: {
          executive_summary: {
            total_variables: 0,
            naming_issues: 0,
            scope_issues: 0,
            consistency_score: 8
          },
          naming_analysis: [],
          scope_analysis: [],
          type_consistency: []
        },
        next_steps: {
          priority_assessment: {
            critical_blockers: structuredData.executive_summary?.critical_issues || 0,
            high_priority: structuredData.executive_summary?.warning_issues || 0,
            medium_priority: 0,
            can_merge: (structuredData.executive_summary?.critical_issues || 0) === 0,
            estimated_effort: metadata.duration_ms ? `${Math.round(metadata.duration_ms / 60000)} min` : '15-30 min'
          },
          immediate_actions: structuredData.action_items?.critical?.map(item => ({
            action: item,
            category: 'bugs' as const,
            priority: 'critical' as const,
            effort: '1-2 hours',
            description: item
          })) || [],
          follow_up_tasks: [],
          merge_readiness: {
            status: (structuredData.executive_summary?.critical_issues || 0) === 0 ? 'ready' : 'blocked',
            blockers: structuredData.action_items?.critical || [],
            recommendations: structuredData.action_items?.suggestions || []
          },
          post_merge_actions: []
        },
        questions_clarifications: structuredData.questions || []
      } as EnhancedReportData
    }
    
    // Legacy format
    return (results as ReviewResults).enhanced_report
  }, [results])
  
  // Extract parsing error details for user notification
  const parsingErrorInfo = useMemo(() => {
    if (isStructuredReviewResult(results)) {
      return null // New format doesn't have parsing errors
    }
    const legacyResults = results as ReviewResults
    if (legacyResults.parsing_metadata?.error_details) {
      return {
        errorType: legacyResults.parsing_metadata.error_details.error_type,
        errorMessage: legacyResults.parsing_metadata.error_details.error_message,
        timestamp: legacyResults.parsing_metadata.error_details.timestamp
      }
    }
    return enhanced?.metadata?.parsing_error ? {
      errorType: 'Unknown',
      errorMessage: enhanced.metadata.parsing_error,
      timestamp: enhanced.metadata.generated_at
    } : null
  }, [enhanced, results])

  // Memoize toggle function to prevent re-creation on every render
  const toggleSection = useCallback((sectionId: string) => {
    setExpandedSections(prev => {
      const newExpanded = new Set(prev)
      if (newExpanded.has(sectionId)) {
        newExpanded.delete(sectionId)
      } else {
        newExpanded.add(sectionId)
      }
      return newExpanded
    })
  }, [])


  // Memoize date formatting
  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleString('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }, [])

  // Generate fallback enhanced report when main enhanced report is missing
  const generateFallbackEnhancedReport = useCallback((rawReview: string, structuredFindings: any, jiraTicket: any): EnhancedReportData => {
    const now = new Date().toISOString()
    
    // Parse basic AC analysis from raw review text
    const parseACFromRawReview = (text: string, jiraTicket: any) => {
      const results: Array<{
        id: string
        text: string
        status: 'fulfilled' | 'partially_fulfilled' | 'not_fulfilled' | 'pending'
        implementation_evidence: string
        issues: string[]
      }> = []
      if (jiraTicket?.acceptance_criteria) {
        jiraTicket.acceptance_criteria.forEach((ac: string, index: number) => {
          if (!ac || typeof ac !== 'string') return
          const acId = `ac_${index + 1}`
          let status: 'fulfilled' | 'partially_fulfilled' | 'not_fulfilled' | 'pending' = 'pending'
          let evidence = ''
          
          // Look for AC mentions in raw review
          const acRegex = new RegExp(`##\\s*ac[\\s_-]*${index + 1}[:\\s]`, 'gi')
          const sections = text.split(/(?=##|\n\s*\n)/)
          
          for (const section of sections) {
            if (acRegex.test(section)) {
              evidence = section.trim().substring(0, 300) + '...'
              
              if (/(?:erfüllt|fulfilled|implemented|✅|completed)/gi.test(section)) {
                status = 'fulfilled'
              } else if (/(?:teilweise|partially|⚠️|partial)/gi.test(section)) {
                status = 'partially_fulfilled'
              } else if (/(?:nicht.*erfüllt|not.*fulfilled|❌|missing|todo)/gi.test(section)) {
                status = 'not_fulfilled'
              }
              break
            }
          }
          
          results.push({
            id: acId,
            text: ac,
            status,
            implementation_evidence: evidence,
            issues: []
          })
        })
      }
      return results
    }

    // Extract bugs from structured findings or raw review
    const extractBugsFromFindings = (findings: any, rawText: string) => {
      const bugs = findings?.bugs || []
      const criticalBugs: Array<{
        text: string
        severity: string
        file?: string
        type: string
      }> = []
      
      // Add structured findings
      bugs.forEach((bug: any) => {
        if (!bug || typeof bug !== 'object' && typeof bug !== 'string') return
        const text = typeof bug === 'string' ? bug : (bug.text || String(bug))
        if (!text || text === '[object Object]') return
        
        criticalBugs.push({
          text,
          severity: bug.severity || 'medium',
          file: bug.file,
          type: 'bug'
        })
      })
      
      // Parse additional bugs from raw text
      const bugPatterns = [
        /[-*]\s*(?:critical\s*bug|security\s*issue)[:\s]*(.+?)(?:\n|$)/gi,
        /(?:error|exception|crash)[:\s](.+?)(?:\n|$)/gi,
        /race\s*condition[:\s](.+?)(?:\n|$)/gi
      ]
      
      bugPatterns.forEach(pattern => {
        let match
        while ((match = pattern.exec(rawText)) !== null) {
          criticalBugs.push({
            text: match[1].trim(),
            severity: 'high',
            file: undefined,
            type: 'critical'
          })
        }
      })
      
      return criticalBugs
    }

    const acResults = parseACFromRawReview(rawReview, jiraTicket)
    const totalAC = acResults.length
    const fulfilled = acResults.filter(ac => ac.status === 'fulfilled').length
    const partiallyFulfilled = acResults.filter(ac => ac.status === 'partially_fulfilled').length
    const notFulfilled = acResults.filter(ac => ac.status === 'not_fulfilled').length
    
    const criticalBugs = extractBugsFromFindings(structuredFindings, rawReview)
    
    return {
      metadata: {
        generated_at: now,
        review_type: 'fallback',
        fallback_mode: true,
        parsing_error: 'Enhanced report parsing failed, using fallback generation'
      },
      acceptance_criteria_analysis: {
        executive_summary: {
          total_ac: totalAC,
          fulfilled,
          partially_fulfilled: partiallyFulfilled,
          not_fulfilled: notFulfilled,
          compliance_rate: totalAC > 0 ? Math.round((fulfilled / totalAC) * 100) : 0,
          business_alignment_score: totalAC > 0 ? Math.min(10, Math.round((fulfilled + partiallyFulfilled * 0.5) / totalAC * 10)) : 0
        },
        detailed_results: acResults
      },
      code_quality_analysis: {
        executive_summary: {
          overall_score: 6, // Conservative estimate for fallback
          critical_issues: criticalBugs.length,
          code_smells: structuredFindings?.code_quality?.length || 0,
          duplication_level: 5 // Default estimate
        },
        code_duplication: [],
        complexity_issues: [],
        naming_consistency: []
      },
      bug_detection_results: {
        critical_bugs: criticalBugs,
        logic_errors: [],
        runtime_risks: []
      },
      action_items: {
        critical: criticalBugs.map(bug => ({
          text: `Fix critical bug: ${bug.text}`,
          priority: 'critical',
          category: 'bugs'
        })),
        important: [],
        suggestions: []
      },
      architectural_assessment: {
        design_patterns: [],
        integration_quality: [],
        violations: []
      },
      security_performance: {
        security_findings: structuredFindings?.security_issues || [],
        performance_analysis: structuredFindings?.performance_issues || []
      },
      variable_parameter_analysis: {
        executive_summary: {
          total_variables: 0,
          naming_issues: 0,
          scope_issues: 0,
          consistency_score: 5
        },
        naming_analysis: [],
        scope_analysis: [],
        type_consistency: []
      },
      next_steps: {
        priority_assessment: {
          critical_blockers: criticalBugs.length,
          high_priority: 0,
          medium_priority: 0,
          can_merge: criticalBugs.length === 0 && notFulfilled === 0,
          estimated_effort: criticalBugs.length > 0 ? '2-4 hours' : fulfilled === totalAC ? 'Ready to merge' : '1-2 hours'
        },
        immediate_actions: [
          ...criticalBugs.map(bug => ({
            action: `Fix: ${bug.text}`,
            category: 'bugs' as const,
            priority: 'critical' as const,
            effort: '1-2 hours',
            description: `Address critical bug: ${bug.text}`
          })),
          ...acResults.filter(ac => ac.status === 'not_fulfilled').map(ac => ({
            action: `Implement AC: ${ac.text.substring(0, 50)}...`,
            category: 'ac_compliance' as const,
            priority: 'high' as const,
            effort: '1-3 hours',
            description: `Complete implementation of acceptance criteria: ${ac.text}`
          }))
        ],
        follow_up_tasks: [],
        merge_readiness: {
          status: criticalBugs.length === 0 && notFulfilled === 0 ? 'ready' : 'blocked',
          blockers: [
            ...criticalBugs.map(bug => `Critical bug: ${bug.text}`),
            ...acResults.filter(ac => ac.status === 'not_fulfilled').map(ac => `Missing AC: ${ac.text.substring(0, 50)}...`)
          ],
          recommendations: ['Review fallback analysis', 'Consider re-running enhanced review']
        },
        post_merge_actions: []
      },
      questions_clarifications: []
    }
  }, [])

  // Generate fallback enhanced report if missing
  const fallbackEnhanced = useMemo(() => {
    if (enhanced) return null
    if (isStructuredReviewResult(results)) return null // New format doesn't need fallback
    if (!(results as ReviewResults).raw_review) return null
    
    const legacyResults = results as ReviewResults
    return generateFallbackEnhancedReport(
      legacyResults.raw_review,
      legacyResults.structured_findings,
      legacyResults.jira_ticket
    )
  }, [enhanced, results, generateFallbackEnhancedReport])

  // Use enhanced report or fallback
  const activeEnhanced = enhanced || fallbackEnhanced

  // Detect fallback mode from backend or generated fallback
  const isInFallbackMode = useMemo(() => {
    if (isStructuredReviewResult(results)) {
      return false // New format is never in fallback mode
    }
    const legacyResults = results as ReviewResults
    return enhanced?.metadata?.fallback_mode === true || 
           enhanced?.metadata?.review_type === 'fallback' ||
           legacyResults.parsing_metadata?.fallback_mode === true ||
           (!enhanced && fallbackEnhanced) // Generated fallback case
  }, [enhanced, results, fallbackEnhanced])

  // Memoize export handler to prevent re-creation
  const handleExportReport = useCallback(() => {
    if (onExportReport) {
      onExportReport(results)
    } else {
      // Enhanced export functionality with fallback data indicators
      const exportData = {
        ...results,
        export_metadata: {
          export_timestamp: new Date().toISOString(),
          fallback_mode: !!fallbackEnhanced,
          data_source: fallbackEnhanced ? 'fallback_generated' : 'backend_parsed',
          enhanced_report_available: !!enhanced,
          generation_method: fallbackEnhanced ? 'client_side_parsing' : 'backend_processing'
        }
      }
      
      const reportData = JSON.stringify(exportData, null, 2)
      const blob = new Blob([reportData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      const fallbackSuffix = fallbackEnhanced ? '-fallback' : ''
      link.download = `enhanced-review-${results.session_id}${fallbackSuffix}-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }
  }, [results, onExportReport, fallbackEnhanced, enhanced])

  // Memoize computed values for dashboard metrics
  const dashboardMetrics = useMemo(() => {
    if (!activeEnhanced) return null

    const totalAC = activeEnhanced.acceptance_criteria_analysis?.detailed_results?.length || 0
    const fulfilledAC = activeEnhanced.acceptance_criteria_analysis?.detailed_results?.filter(ac => ac.status === 'fulfilled').length || 0
    const complianceRate = totalAC > 0 ? Math.round((fulfilledAC / totalAC) * 100) : 0

    const totalFindings = (activeEnhanced.bug_detection_results?.critical_bugs?.length || 0) +
                         (activeEnhanced.bug_detection_results?.logic_errors?.length || 0) +
                         (activeEnhanced.bug_detection_results?.runtime_risks?.length || 0)

    const criticalIssues = activeEnhanced.bug_detection_results?.critical_bugs?.length || 0
    const qualityScore = activeEnhanced.code_quality_analysis?.executive_summary?.overall_score || 8

    return {
      totalAC,
      fulfilledAC,
      complianceRate,
      totalFindings,
      criticalIssues,
      qualityScore
    }
  }, [activeEnhanced])

  // Memoize section visibility checks to avoid repeated calculations
  const sectionVisibility = useMemo(() => {
    if (!activeEnhanced) return {}

    return {
      hasACAnalysis: activeEnhanced.acceptance_criteria_analysis?.detailed_results?.length > 0,
      hasVariableAnalysis: activeEnhanced.variable_parameter_analysis && (
        activeEnhanced.variable_parameter_analysis.naming_analysis?.length > 0 ||
        activeEnhanced.variable_parameter_analysis.scope_analysis?.length > 0
      ),
      hasNextSteps: activeEnhanced.next_steps && (
        activeEnhanced.next_steps.immediate_actions?.length > 0 ||
        activeEnhanced.next_steps.follow_up_tasks?.length > 0
      ),
      hasQuestions: activeEnhanced.questions_clarifications?.length > 0
    }
  }, [activeEnhanced])

  // State for user toggle between fallback and raw review modes
  const [viewMode, setViewMode] = useState<'enhanced' | 'raw'>('enhanced')

  if (!activeEnhanced) {
    return (
      <Card className={`${className} border-amber-500/50 bg-amber-500/10`}>
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-amber-400 mb-2">Enhanced Report Not Available</h3>
            <p className="text-amber-300/80 text-sm">
              The enhanced report data is not available for this review session. 
              This might be due to an incomplete review or parsing issues.
            </p>
            {!isStructuredReviewResult(results) && (results as ReviewResults).raw_review && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-amber-400 hover:text-amber-300 mb-2">
                  Show raw review output
                </summary>
                <pre className="mt-2 p-4 bg-black/20 rounded text-xs overflow-auto max-h-96 whitespace-pre-wrap text-amber-100">
                  {(results as ReviewResults).raw_review}
                </pre>
              </details>
            )}
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-4 border-amber-500 text-amber-500 hover:bg-amber-500/10"
              onClick={() => window.location.reload()}
            >
              Retry Loading
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Fallback Mode Warning Banner with User Controls */}
      {isInFallbackMode && (
        <Card className="border-orange-500/50 bg-orange-500/10">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-semibold text-orange-400">
                    {fallbackEnhanced ? 'Fallback Analysis Mode' : 'Limited Analysis Mode'}
                  </h4>
                  {!isStructuredReviewResult(results) && (results as ReviewResults).raw_review && (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setViewMode(viewMode === 'enhanced' ? 'raw' : 'enhanced')}
                        className="border-orange-500 text-orange-400 hover:bg-orange-500/20"
                      >
                        {viewMode === 'enhanced' ? 'Show Raw Review' : 'Show Enhanced View'}
                      </Button>
                    </div>
                  )}
                </div>
                <p className="text-orange-300/80 text-sm mb-2">
                  {fallbackEnhanced 
                    ? 'Enhanced report parsing failed. Generated fallback analysis from raw review and structured findings.'
                    : 'Enhanced report parsing encountered issues. Showing basic analysis with available data.'
                  }
                </p>
                {parsingErrorInfo && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-orange-400 hover:text-orange-300 mb-1">
                      Technical Details
                    </summary>
                    <div className="bg-black/20 p-2 rounded mt-1 space-y-1">
                      <div><strong>Error Type:</strong> {parsingErrorInfo.errorType}</div>
                      <div><strong>Message:</strong> {parsingErrorInfo.errorMessage}</div>
                      <div><strong>Time:</strong> {new Date(parsingErrorInfo.timestamp).toLocaleString()}</div>
                    </div>
                  </details>
                )}
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline" className="border-orange-500 text-orange-400">
                    {fallbackEnhanced ? 'Fallback Generated' : 'Degraded Experience'}
                  </Badge>
                  {fallbackEnhanced && (
                    <Badge variant="outline" className="border-blue-500 text-blue-400">
                      Export Available
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-orange-300/60 mt-2">
                  {fallbackEnhanced 
                    ? 'Fallback analysis provides basic AC compliance and bug detection. Consider re-running the enhanced review.'
                    : 'The raw review output is still available for manual review.'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Raw Review Mode Display */}
      {viewMode === 'raw' && !isStructuredReviewResult(results) && (results as ReviewResults).raw_review && (
        <Card className="border-gray-500/50 bg-gray-500/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-400">
              <FileText className="h-5 w-5" />
              Raw Review Output
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="p-4 bg-black/20 rounded text-xs overflow-auto max-h-96 whitespace-pre-wrap text-gray-100">
              {(results as ReviewResults).raw_review}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Report Sections (only show in enhanced mode) */}
      {viewMode === 'enhanced' && (
        <div className="space-y-6">
      {/* Report Header */}
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 via-background to-primary/5">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 rounded-lg bg-primary shadow-lg shadow-primary/30">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="text-2xl text-white font-black">
                  Enhanced Code Review Report
                </CardTitle>
                <p className="text-muted-foreground mt-1">
                  Comprehensive AI-powered analysis with Jira integration
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportReport}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export Report
              </Button>
              {(isStructuredReviewResult(results) ? results.metadata.pr_url : (results as ReviewResults).pr_url) && (
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="flex items-center gap-2"
                >
                  <a href={isStructuredReviewResult(results) ? results.metadata.pr_url : (results as ReviewResults).pr_url} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4" />
                    View PR
                  </a>
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Report Metadata */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Generated:</span>
              <div className="font-mono text-white">{formatDate(activeEnhanced.metadata.generated_at)}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Review Type:</span>
              <div className="font-semibold text-primary uppercase">
                {isStructuredReviewResult(results) 
                  ? results.review_type 
                  : (results as ReviewResults).review_mode
                }
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Branch:</span>
              <div className="font-mono text-white">
                {isStructuredReviewResult(results) 
                  ? results.metadata.branch_name 
                  : (results as ReviewResults).branch_name
                }
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Files Changed:</span>
              <div className="font-semibold text-white">
                {isStructuredReviewResult(results) 
                  ? results.metadata.changed_files?.length || 0
                  : (results as ReviewResults).metadata.file_count
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Executive Summary Dashboard */}
      <Card className="border-green-500/30 bg-green-500/5">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-green-400">
            <BarChart3 className="h-5 w-5" />
            Executive Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          {dashboardMetrics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* AC Compliance */}
              {dashboardMetrics.totalAC > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">AC Compliance</span>
                    <span className="text-2xl font-bold text-green-400">
                      {dashboardMetrics.complianceRate}%
                    </span>
                  </div>
                  <Progress 
                    value={dashboardMetrics.complianceRate} 
                    className="h-2" 
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>{dashboardMetrics.fulfilledAC}/{dashboardMetrics.totalAC} Fulfilled</span>
                    <span>Score: {activeEnhanced.acceptance_criteria_analysis?.executive_summary?.business_alignment_score || 0}/10</span>
                  </div>
                </div>
              )}

              {/* Code Quality */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">Code Quality</span>
                  <span className="text-2xl font-bold text-blue-400">
                    {dashboardMetrics.qualityScore}/10
                  </span>
                </div>
                <Progress 
                  value={dashboardMetrics.qualityScore * 10} 
                  className="h-2" 
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{dashboardMetrics.criticalIssues} Critical Issues</span>
                  <span>{activeEnhanced.code_quality_analysis?.executive_summary?.duplication_level || 0}% Duplication</span>
                </div>
              </div>

              {/* Issues Summary */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">Total Issues</span>
                  <span className="text-2xl font-bold text-orange-400">
                    {dashboardMetrics.totalFindings}
                  </span>
                </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-red-400 flex items-center gap-1">
                    <Bug className="h-3 w-3" />
                    Critical Bugs
                  </span>
                  <span className="text-red-400">{activeEnhanced.bug_detection_results.critical_bugs.length}</span>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-amber-400 flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    Security Issues
                  </span>
                  <span className="text-amber-400">
                    {isStructuredReviewResult(results) 
                      ? results.structured_data.code_quality_findings?.filter(f => 
                          f.type.toLowerCase().includes('security')
                        ).length || 0
                      : (results as ReviewResults).summary.security_issues
                    }
                  </span>
                </div>
              </div>
            </div>
          </div>
          )}
        </CardContent>
      </Card>

      {/* Jira Ticket Information */}
      {(isStructuredReviewResult(results) ? results.jira_ticket : (results as ReviewResults).jira_ticket) && (
        <Card className="border-purple-500/30 bg-purple-500/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-400">
              <Target className="h-5 w-5" />
              Jira Ticket Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const jiraTicket = isStructuredReviewResult(results) ? results.jira_ticket : (results as ReviewResults).jira_ticket
              return (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm text-muted-foreground">Ticket ID:</span>
                        <div className="font-mono text-purple-400 font-semibold">{jiraTicket?.ticket_id}</div>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Summary:</span>
                        <div className="text-white">{jiraTicket?.summary}</div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm text-muted-foreground">Status:</span>
                        <Badge variant="outline" className="ml-2 border-purple-500 text-purple-400">
                          {jiraTicket?.status}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Acceptance Criteria:</span>
                        <div className="text-white font-semibold">{jiraTicket?.acceptance_criteria_count} Items</div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })()}
          </CardContent>
        </Card>
      )}

      {/* Main Report Sections */}
      <div className="space-y-4">
        {/* AC Analysis Section */}
        {sectionVisibility.hasACAnalysis && (
          <ACAnalysisSection 
            data={activeEnhanced.acceptance_criteria_analysis}
            expanded={expandedSections.has('ac-analysis')}
            onToggle={() => toggleSection('ac-analysis')}
          />
        )}

        {/* Code Quality Section */}
        <CodeQualitySection 
          data={activeEnhanced.code_quality_analysis}
          expanded={expandedSections.has('code-quality')}
          onToggle={() => toggleSection('code-quality')}
        />

        {/* Bug Detection Section */}
        <BugDetectionSection 
          data={activeEnhanced.bug_detection_results}
          expanded={expandedSections.has('bug-detection')}
          onToggle={() => toggleSection('bug-detection')}
        />

        {/* Action Items Section */}
        <ActionItemsSection 
          data={activeEnhanced.action_items}
          expanded={expandedSections.has('action-items')}
          onToggle={() => toggleSection('action-items')}
        />

        {/* Architectural Assessment Section */}
        <ArchitecturalAssessmentSection 
          data={activeEnhanced.architectural_assessment}
          expanded={expandedSections.has('architectural')}
          onToggle={() => toggleSection('architectural')}
        />

        {/* Variable & Parameter Analysis Section */}
        {sectionVisibility.hasVariableAnalysis && (
          <VariableAnalysisSection 
            data={activeEnhanced.variable_parameter_analysis}
            expanded={expandedSections.has('variable-analysis')}
            onToggle={() => toggleSection('variable-analysis')}
          />
        )}
      </div>

      {/* Next Steps Section */}
      {sectionVisibility.hasNextSteps && (
        <NextStepsSection 
          data={activeEnhanced.next_steps}
          prUrl={isStructuredReviewResult(results) ? results.metadata.pr_url : (results as ReviewResults).pr_url}
          jiraTicketId={(isStructuredReviewResult(results) ? results.jira_ticket : (results as ReviewResults).jira_ticket)?.ticket_id}
        />
      )}

      {/* Questions & Next Steps */}
      {sectionVisibility.hasQuestions && (
        <Card className="border-yellow-500/30 bg-yellow-500/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-400">
              <AlertCircle className="h-5 w-5" />
              Questions & Clarifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {activeEnhanced.questions_clarifications.map((question, index) => (
                <div key={index} className="p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                  <p className="text-yellow-200">{question}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Report Footer */}
      <Card className="border-gray-700">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Brain className="h-4 w-4 text-primary" />
              <span>Generated by Enhanced Claude Code PR Reviewer</span>
              {fallbackEnhanced && (
                <Badge variant="outline" className="border-orange-500 text-orange-400 ml-2">
                  Fallback Mode
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-4">
              <span>Session: {results.session_id}</span>
              <span>•</span>
              <span>Generated: {formatDate(isStructuredReviewResult(results) ? results.timestamp : (results as ReviewResults).timestamp)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
        </div>
      )}
    </div>
  )
})