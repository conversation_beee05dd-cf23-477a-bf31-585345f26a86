import React, { useState } from 'react'
import { 
  Book<PERSON>pen,
  ArrowRight,
  Clock,
  Target,
  Code,
  Shield,
  Zap,
  CheckCircle,
  FileText,
  ExternalLink
} from 'lucide-react'
import { Button } from './ui/button'
import type { StructuredReviewResult, TutorialData } from '../services/codeReviewer/CodeReviewerService'
import type { EnhancedReviewResults } from '../types/enhanced-review'

interface TutorialSection {
  id: string
  title: string
  icon: React.ReactNode
  content: string
  subsections?: Array<{
    id: string
    title: string
    content: string
  }>
}

interface TutorialSummaryViewerProps {
  results: StructuredReviewResult | EnhancedReviewResults
  onExportTutorial?: (tutorial: TutorialData) => void
  className?: string
}

export const TutorialSummaryViewer: React.FC<TutorialSummaryViewerProps> = ({
  results,
  onExportTutorial,
  className = ''
}) => {
  const [activeSection, setActiveSection] = useState<string>('overview')
  const [activePage, setActivePage] = useState<number>(0)

  // Extract tutorial data from results
  const tutorialData = React.useMemo(() => {
    if ('phase3_summary' in results && results.phase3_summary) {
      return results.phase3_summary as TutorialData
    }
    if ('tutorial' in results && results.tutorial) {
      return results.tutorial as TutorialData
    }
    return null
  }, [results])

  // Generate tutorial sections from the data
  const tutorialSections = React.useMemo(() => {
    if (!tutorialData) {
      return generateFallbackSections(results)
    }

    return generateSectionsFromTutorialData(tutorialData)
  }, [tutorialData, results])

  // Handle section navigation
  const handleSectionClick = (sectionId: string) => {
    setActiveSection(sectionId)
    // Find page containing this section
    const pageIndex = Math.floor(tutorialSections.findIndex(s => s.id === sectionId) / 3)
    setActivePage(pageIndex)
  }

  // Get sections for current page (3 sections per page)
  const sectionsPerPage = 3
  const totalPages = Math.ceil(tutorialSections.length / sectionsPerPage)
  const currentSections = tutorialSections.slice(
    activePage * sectionsPerPage,
    (activePage + 1) * sectionsPerPage
  )

  const currentSection = tutorialSections.find(s => s.id === activeSection) || tutorialSections[0]

  return (
    <div className={`tutorial-summary-viewer ${className}`}>
      <div className="grid grid-cols-12 gap-6 h-[calc(100vh-12rem)]">
        
        {/* Left Sidebar - Main Navigation */}
        <div className="col-span-3 border-r border-border/40 pr-4 overflow-y-auto">
          <div className="sticky top-0 bg-background/95 backdrop-blur pb-4 mb-4 border-b">
            <div className="flex items-center gap-2 mb-4">
              <BookOpen className="h-5 w-5 text-emerald-600" />
              <h2 className="text-lg font-semibold">Implementation Tutorial</h2>
            </div>
            
            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex items-center gap-2 mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setActivePage(Math.max(0, activePage - 1))}
                  disabled={activePage === 0}
                  className="flex-1"
                >
                  Previous
                </Button>
                <span className="text-xs text-muted-foreground px-2">
                  {activePage + 1} / {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setActivePage(Math.min(totalPages - 1, activePage + 1))}
                  disabled={activePage === totalPages - 1}
                  className="flex-1"
                >
                  Next
                </Button>
              </div>
            )}
          </div>

          {/* Navigation Menu */}
          <nav className="space-y-1">
            {currentSections.map((section) => (
              <button
                key={section.id}
                onClick={() => handleSectionClick(section.id)}
                className={`w-full text-left p-3 rounded-lg transition-colors ${
                  activeSection === section.id
                    ? 'bg-emerald-50 text-emerald-700 border border-emerald-200'
                    : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                }`}
              >
                <div className="flex items-center gap-2">
                  {section.icon}
                  <span className="font-medium text-sm">{section.title}</span>
                </div>
              </button>
            ))}
          </nav>

          {/* Tutorial Metadata */}
          <div className="mt-6 pt-4 border-t border-border/40">
            <div className="space-y-2 text-xs text-muted-foreground">
              <div className="flex items-center gap-2">
                <Clock className="h-3 w-3" />
                <span>Generated: {new Date().toLocaleDateString()}</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-3 w-3" />
                <span>{tutorialSections.length} sections</span>
              </div>
              {tutorialData?.tutorial_id && (
                <div className="flex items-center gap-2">
                  <Target className="h-3 w-3" />
                  <span>Tutorial ID: {tutorialData.tutorial_id.slice(-8)}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="col-span-6 overflow-y-auto">
          <div className="max-w-none prose prose-sm prose-slate dark:prose-invert">
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-2">
                {currentSection.icon}
                <h1 className="text-2xl font-bold mb-0">{currentSection.title}</h1>
              </div>
              
              {/* Content rendering */}
              <div 
                className="tutorial-content"
                dangerouslySetInnerHTML={{ 
                  __html: formatTutorialContent(currentSection.content) 
                }}
              />

              {/* Subsections */}
              {currentSection.subsections && (
                <div className="mt-8 space-y-6">
                  {currentSection.subsections.map((subsection) => (
                    <div key={subsection.id} className="border-l-4 border-emerald-200 pl-4">
                      <h3 className="text-lg font-semibold mb-2">{subsection.title}</h3>
                      <div 
                        dangerouslySetInnerHTML={{ 
                          __html: formatTutorialContent(subsection.content) 
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8 pt-6 border-t border-border/40">
              <Button
                variant="outline"
                onClick={() => {
                  const currentIndex = tutorialSections.findIndex(s => s.id === activeSection)
                  if (currentIndex > 0) {
                    handleSectionClick(tutorialSections[currentIndex - 1].id)
                  }
                }}
                disabled={tutorialSections.findIndex(s => s.id === activeSection) === 0}
                className="flex items-center gap-2"
              >
                <ArrowRight className="h-4 w-4 rotate-180" />
                Previous Section
              </Button>
              
              <Button
                variant="outline"
                onClick={() => {
                  const currentIndex = tutorialSections.findIndex(s => s.id === activeSection)
                  if (currentIndex < tutorialSections.length - 1) {
                    handleSectionClick(tutorialSections[currentIndex + 1].id)
                  }
                }}
                disabled={tutorialSections.findIndex(s => s.id === activeSection) === tutorialSections.length - 1}
                className="flex items-center gap-2"
              >
                Next Section
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Right Sidebar - On This Page Navigation */}
        <div className="col-span-3 border-l border-border/40 pl-4 overflow-y-auto">
          <div className="sticky top-0 bg-background/95 backdrop-blur pb-4">
            <h3 className="font-medium text-sm mb-4">On this page</h3>
            
            <nav className="space-y-1">
              {currentSection.subsections ? (
                currentSection.subsections.map((subsection) => (
                  <a
                    key={subsection.id}
                    href={`#${subsection.id}`}
                    className="block text-xs text-muted-foreground hover:text-foreground py-1 pl-2 border-l-2 border-transparent hover:border-emerald-200 transition-colors"
                    onClick={(e) => {
                      e.preventDefault()
                      document.getElementById(subsection.id)?.scrollIntoView({ behavior: 'smooth' })
                    }}
                  >
                    {subsection.title}
                  </a>
                ))
              ) : (
                <div className="text-xs text-muted-foreground py-1 pl-2">
                  Single section
                </div>
              )}
            </nav>

            {/* Export Button */}
            {tutorialData && onExportTutorial && (
              <div className="mt-6 pt-4 border-t border-border/40">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onExportTutorial(tutorialData!)}
                  className="w-full flex items-center gap-2"
                >
                  <ExternalLink className="h-3 w-3" />
                  Export Tutorial
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Helper functions
function generateSectionsFromTutorialData(tutorialData: TutorialData): TutorialSection[] {
  const sections: TutorialSection[] = []

  // Business Context Section
  if (tutorialData.structured_sections?.business_context) {
    sections.push({
      id: 'business-context',
      title: 'Business Context',
      icon: <Target className="h-4 w-4 text-emerald-600" />,
      content: tutorialData.structured_sections.business_context
    })
  }

  // Architecture Overview
  if (tutorialData.structured_sections?.architecture_overview) {
    sections.push({
      id: 'architecture',
      title: 'Architecture Overview',
      icon: <Code className="h-4 w-4 text-blue-600" />,
      content: tutorialData.structured_sections.architecture_overview
    })
  }

  // Implementation Guide
  if (tutorialData.structured_sections?.implementation_guide) {
    sections.push({
      id: 'implementation',
      title: 'Implementation Guide',
      icon: <CheckCircle className="h-4 w-4 text-green-600" />,
      content: tutorialData.structured_sections.implementation_guide
    })
  }

  // Testing Section
  if (tutorialData.structured_sections?.testing) {
    sections.push({
      id: 'testing',
      title: 'Testing Strategy',
      icon: <Shield className="h-4 w-4 text-purple-600" />,
      content: tutorialData.structured_sections.testing
    })
  }

  // Deployment Section
  if (tutorialData.structured_sections?.deployment) {
    sections.push({
      id: 'deployment',
      title: 'Deployment Guide',
      icon: <Zap className="h-4 w-4 text-orange-600" />,
      content: tutorialData.structured_sections.deployment
    })
  }

  // If no structured sections, create from raw content
  if (sections.length === 0 && tutorialData.raw_content) {
    sections.push({
      id: 'overview',
      title: 'Tutorial Overview',
      icon: <BookOpen className="h-4 w-4 text-emerald-600" />,
      content: tutorialData.raw_content
    })
  }

  return sections
}

function generateFallbackSections(results: StructuredReviewResult | EnhancedReviewResults): TutorialSection[] {
  return [
    {
      id: 'overview',
      title: 'Overview',
      icon: <BookOpen className="h-4 w-4 text-emerald-600" />,
      content: `
# Implementation Overview

This tutorial provides a comprehensive analysis of the changes made in this pull request.

## Summary
- **Session ID**: ${results.session_id}
- **Review Type**: ${'review_type' in results ? results.review_type : 'Enhanced Review'}
- **Timestamp**: ${results.timestamp || new Date().toISOString()}

## Key Features
The implementation includes significant changes across multiple areas:
- Business logic enhancements
- Code quality improvements
- Security considerations
- Performance optimizations

*Note: Detailed tutorial content will be generated when Phase 3 summary is available.*
      `
    },
    {
      id: 'changes',
      title: 'Key Changes',
      icon: <Code className="h-4 w-4 text-blue-600" />,
      content: `
# Key Changes Analysis

## Modified Files
${('metadata' in results && results.metadata?.changed_files) 
  ? results.metadata.changed_files.map(file => `- \`${file}\``).join('\n')
  : 'File changes information not available'
}

## Implementation Approach
The changes follow established patterns and maintain code quality standards.

*Detailed implementation guide will be available with Phase 3 tutorial generation.*
      `
    },
    {
      id: 'next-steps',
      title: 'Next Steps',
      icon: <ArrowRight className="h-4 w-4 text-green-600" />,
      content: `
# Next Steps

## Immediate Actions
1. Review the acceptance criteria compliance
2. Validate code quality metrics
3. Test the implementation thoroughly

## Future Considerations
- Monitor performance in production
- Consider additional optimizations
- Plan for maintenance and updates

*Complete tutorial with step-by-step instructions will be generated with Phase 3 analysis.*
      `
    }
  ]
}

function formatTutorialContent(content: string): string {
  // Convert markdown-like content to HTML
  let formatted = content
    .replace(/^# (.+)$/gm, '<h1>$1</h1>')
    .replace(/^## (.+)$/gm, '<h2>$1</h2>')
    .replace(/^### (.+)$/gm, '<h3>$1</h3>')
    .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.+?)\*/g, '<em>$1</em>')
    .replace(/`(.+?)`/g, '<code>$1</code>')
    .replace(/^- (.+)$/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/^(.+)$/gm, '<p>$1</p>')

  // Clean up HTML
  formatted = formatted
    .replace(/<p><h([1-6])>/g, '<h$1>')
    .replace(/<\/h([1-6])><\/p>/g, '</h$1>')
    .replace(/<p><ul>/g, '<ul>')
    .replace(/<\/ul><\/p>/g, '</ul>')

  return formatted
}