import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, Check, AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Alert, AlertDescription } from '../ui/alert'
import { useWorktreeConfig } from '../../contexts/WorktreeConfigContext'

interface DirectoryPickerProps {
  onPathSelected?: (path: string) => void
  className?: string
}

interface DirectorySuggestion {
  path: string
  name: string
}

export const DirectoryPicker: React.FC<DirectoryPickerProps> = ({ 
  onPathSelected, 
  className 
}) => {
  const { config, updateConfig, validatePath } = useWorktreeConfig()
  const [customPath, setCustomPath] = useState('')
  const [suggestions, setSuggestions] = useState<DirectorySuggestion[]>([])
  const [validating, setValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean
    message: string
  } | null>(null)

  // Load directory suggestions
  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        const response = await fetch('http://localhost:5001/api/worktree/list-dirs', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          mode: 'cors'
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setSuggestions(data.suggestions)
          }
        }
      } catch (error) {
        console.warn('Failed to fetch directory suggestions:', error)
      }
    }

    fetchSuggestions()
  }, [])

  // Initialize custom path with current config
  useEffect(() => {
    if (config && !customPath) {
      setCustomPath(config.basePath)
    }
  }, [config, customPath])

  const handleValidateAndSave = async (path: string) => {
    setValidating(true)
    setValidationResult(null)

    try {
      const validation = await validatePath(path)
      setValidationResult(validation)

      if (validation.isValid) {
        const success = await updateConfig(path)
        if (success && onPathSelected) {
          onPathSelected(path)
        }
      }
    } catch (error) {
      setValidationResult({
        isValid: false,
        message: error instanceof Error ? error.message : 'Validation failed'
      })
    } finally {
      setValidating(false)
    }
  }

  const handleSuggestionClick = (suggestion: DirectorySuggestion) => {
    setCustomPath(suggestion.path)
    handleValidateAndSave(suggestion.path)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Folder className="h-5 w-5" />
          Worktree Directory Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Configuration */}
        {config && (
          <div className="p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Current Directory:</p>
                <p className="text-sm text-muted-foreground font-mono">{config.basePath}</p>
              </div>
              <Badge variant={config.isValid ? "default" : "destructive"}>
                {config.isValid ? "Valid" : "Invalid"}
              </Badge>
            </div>
          </div>
        )}

        {/* Custom Path Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Custom Directory Path:</label>
          <div className="flex gap-2">
            <Input
              value={customPath}
              onChange={(e) => setCustomPath(e.target.value)}
              placeholder="/path/to/your/worktree/directory"
              className="font-mono"
            />
            <Button
              onClick={() => handleValidateAndSave(customPath)}
              disabled={validating || !customPath.trim()}
              size="sm"
            >
              {validating ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Check className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Validation Result */}
        {validationResult && (
          <Alert variant={validationResult.isValid ? "default" : "destructive"}>
            {validationResult.isValid ? (
              <Check className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription>{validationResult.message}</AlertDescription>
          </Alert>
        )}

        {/* Directory Suggestions */}
        {suggestions.length > 0 && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Suggested Directories:</label>
            <div className="grid gap-2">
              {suggestions.map((suggestion) => (
                <Button
                  key={suggestion.path}
                  variant="outline"
                  className="justify-start h-auto p-3"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <div className="text-left">
                    <div className="font-medium">{suggestion.name}</div>
                    <div className="text-sm text-muted-foreground font-mono">
                      {suggestion.path}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Directory must exist and be writable</p>
          <p>• Git must be available in system PATH</p>
          <p>• Worktrees will be created as: {`{directory}/{branch-name}-review`}</p>
        </div>
      </CardContent>
    </Card>
  )
}