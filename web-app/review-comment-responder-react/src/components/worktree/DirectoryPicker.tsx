import React, { useState, useEffect } from 'react'
import { <PERSON>old<PERSON>, Check, AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Alert, AlertDescription } from '../ui/alert'
import { useWorktreeStatus } from '../../contexts/WorktreeStatusContext'
import { DirectoryBrowser } from './DirectoryBrowser'

interface DirectoryPickerProps {
  onPathSelected?: (path: string) => void
  className?: string
}

interface DirectorySuggestion {
  path: string
  name: string
}

export const DirectoryPicker: React.FC<DirectoryPickerProps> = ({ 
  onPathSelected, 
  className 
}) => {
  const { status, setMasterRepo } = useWorktreeStatus()
  const [customPath, setCustomPath] = useState<string>('')
  const [suggestions, setSuggestions] = useState<DirectorySuggestion[]>([])
  const [validating, setValidating] = useState(false)
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean
    message: string
  } | null>(null)

  // Load directory suggestions
  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        const response = await fetch('http://localhost:5002/api/worktree/list-dirs', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          mode: 'cors'
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setSuggestions(data.suggestions)
          }
        }
      } catch (error) {
        console.warn('Failed to fetch directory suggestions:', error)
      }
    }

    fetchSuggestions()
  }, [])

  // Initialize custom path with current status
  useEffect(() => {
    if (status.masterRepoPath) {
      console.log('📥 DirectoryPicker: Setting customPath from status:', status.masterRepoPath)
      setCustomPath(status.masterRepoPath)
    }
  }, [status.masterRepoPath])

  const handleValidateAndSave = async (path: string) => {
    setValidating(true)
    setValidationResult(null)

    try {
      // First validate the path using the validation API
      const validateResponse = await fetch('http://localhost:5002/api/worktree/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors',
        body: JSON.stringify({ path })
      })

      const validateData = await validateResponse.json()
      const validation = validateData.success ? validateData.validation : { isValid: false, message: 'Validation failed' }
      setValidationResult(validation)

      if (validation.isValid) {
        // Use WorktreeStatusContext to set the master repo
        console.log('✅ Path validation successful, saving via WorktreeStatusContext...')
        const success = await setMasterRepo(path)
        if (success) {
          console.log('✅ Config saved successfully via WorktreeStatusContext')
          
          if (onPathSelected) {
            onPathSelected(path)
          }
        } else {
          console.error('❌ Failed to save config via WorktreeStatusContext')
        }
      } else {
        console.warn('⚠️ Path validation failed:', validation.message)
      }
    } catch (error) {
      console.error('❌ Error in handleValidateAndSave:', error)
      setValidationResult({
        isValid: false,
        message: error instanceof Error ? error.message : 'Validation failed'
      })
    } finally {
      setValidating(false)
    }
  }

  const handleSuggestionClick = (suggestion: DirectorySuggestion) => {
    setCustomPath(suggestion.path)
    handleValidateAndSave(suggestion.path)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Folder className="h-5 w-5" />
          Worktree Directory Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Configuration */}
        {status.masterRepoPath && (
          <div className="p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Current Directory:</p>
                <p className="text-sm text-muted-foreground font-mono">{status.masterRepoPath}</p>
              </div>
              <Badge variant={status.isValid ? "default" : "destructive"}>
                {status.isValid ? "Valid" : "Invalid"}
              </Badge>
            </div>
          </div>
        )}

        {/* Directory Browser */}
        <DirectoryBrowser 
          onMasterRepoSelected={(masterRepoPath) => {
            setCustomPath(masterRepoPath)
            handleValidateAndSave(masterRepoPath)
          }}
        />

        {/* Manual Path Input (Advanced) */}
        <details className="space-y-2">
          <summary className="text-sm font-medium cursor-pointer text-muted-foreground hover:text-foreground">
            Erweitert: Manueller Pfad
          </summary>
          <div className="flex gap-2 pt-2">
            <Input
              value={customPath}
              onChange={(e) => setCustomPath(e.target.value)}
              placeholder="/path/to/your/master/repository"
              className="font-mono"
            />
            <Button
              onClick={() => handleValidateAndSave(customPath)}
              disabled={validating || !customPath?.trim()}
              size="sm"
            >
              {validating ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Check className="h-4 w-4" />
              )}
            </Button>
          </div>
        </details>

        {/* Validation Result */}
        {validationResult && (
          <Alert variant={validationResult.isValid ? "success" : "destructive"}>
            {validationResult.isValid ? (
              <Check className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription>{validationResult.message}</AlertDescription>
          </Alert>
        )}

        {/* Directory Suggestions */}
        {suggestions.length > 0 && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Suggested Directories:</label>
            <div className="grid gap-2">
              {suggestions.map((suggestion) => (
                <Button
                  key={suggestion.path}
                  variant="outline"
                  className="justify-start h-auto p-3"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <div className="text-left">
                    <div className="font-medium">{suggestion.name}</div>
                    <div className="text-sm text-muted-foreground font-mono">
                      {suggestion.path}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Directory must exist and be writable</p>
          <p>• Git must be available in system PATH</p>
          <p>• Worktrees will be created as: {`{directory}/{branch-name}-review`}</p>
        </div>
      </CardContent>
    </Card>
  )
}