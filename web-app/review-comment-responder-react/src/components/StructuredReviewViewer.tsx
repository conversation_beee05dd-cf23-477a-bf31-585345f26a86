import React, { useState } from 'react'
import { 
  CheckCircle, 
  Bug, 
  Shield, 
  Gauge, 
  Lightbulb,
  ChevronDown,
  ChevronRight,
  FileText,
  Target,
  Filter,
  Search,
  ExternalLink,
  Copy,
  Download
} from 'lucide-react'
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Input } from './ui/input'
import type { StructuredReviewResult } from '../services/codeReviewer/CodeReviewerService'

interface ReviewFinding {
  text: string
  severity: 'high' | 'medium' | 'low'
  file?: string
  line?: number
  category?: string
  suggestion?: string
}

interface StructuredFindings {
  acceptance_criteria: ReviewFinding[]
  code_quality: ReviewFinding[]
  security_issues: ReviewFinding[]
  performance_issues: ReviewFinding[]
  bugs: ReviewFinding[]
  suggestions: ReviewFinding[]
}

interface ReviewSummary {
  total_findings: number
  high_severity_count: number
  files_changed: number
  review_length: number
  categories: Record<string, number>
  completion_status: string
}

interface EnhancedReviewResults {
  session_id: string
  review_mode: string
  branch_name: string
  pr_url?: string
  worktree_path?: string
  timestamp: string
  raw_review: string
  metadata: {
    changed_files: string[]
    diff_summary?: string
    file_count: number
  }
  jira_ticket?: {
    ticket_id: string
    summary: string
    status: string
    acceptance_criteria_count: number
    acceptance_criteria: string[]
  }
  structured_findings: StructuredFindings
  summary: ReviewSummary
}

interface StructuredReviewViewerProps {
  results: StructuredReviewResult | EnhancedReviewResults // Support both formats
  className?: string
  onFileClick?: (filename: string, line?: number) => void
  onExportResults?: (results: StructuredReviewResult | EnhancedReviewResults) => void
  compact?: boolean
}

// Helper function to detect if results are new StructuredReviewResult format
const isStructuredReviewResult = (results: any): results is StructuredReviewResult => {
  return results && results.structured_data && results.structured_data.executive_summary
}

// Helper function to normalize data format
const normalizeResults = (results: StructuredReviewResult | EnhancedReviewResults) => {
  if (isStructuredReviewResult(results)) {
    // Convert new format to legacy format for component compatibility
    const legacyFindings: StructuredFindings = {
      acceptance_criteria: results.structured_data.acceptance_criteria?.map((ac) => ({
        text: `${ac.icon} ${ac.title}`,
        severity: ac.status === 'NOT_FULFILLED' ? 'high' : ac.status === 'PARTIAL' ? 'medium' : 'low'
      })) || [],
      code_quality: results.structured_data.code_quality_findings?.map(f => ({
        text: f.description,
        severity: f.severity === 'critical' ? 'high' : f.severity as 'high' | 'medium' | 'low'
      })) || [],
      security_issues: results.structured_data.code_quality_findings?.filter(f => 
        f.type.toLowerCase().includes('security')
      ).map(f => ({
        text: f.description,
        severity: f.severity === 'critical' ? 'high' : f.severity as 'high' | 'medium' | 'low'
      })) || [],
      performance_issues: results.structured_data.code_quality_findings?.filter(f => 
        f.type.toLowerCase().includes('performance')
      ).map(f => ({
        text: f.description,
        severity: f.severity === 'critical' ? 'high' : f.severity as 'high' | 'medium' | 'low'
      })) || [],
      bugs: results.structured_data.code_quality_findings?.filter(f => 
        f.type.toLowerCase().includes('bug') || f.severity === 'critical'
      ).map(f => ({
        text: f.description,
        severity: f.severity === 'critical' ? 'high' : f.severity as 'high' | 'medium' | 'low'
      })) || [],
      suggestions: results.structured_data.action_items?.suggestions?.map(s => ({
        text: s,
        severity: 'low' as const
      })) || []
    }

    const legacySummary: ReviewSummary = {
      total_findings: results.structured_data.executive_summary.critical_issues + results.structured_data.executive_summary.warning_issues,
      high_severity_count: results.structured_data.executive_summary.critical_issues,
      files_changed: results.metadata.changed_files?.length || 0,
      review_length: results.raw_content?.length || 0,
      categories: {
        security_issues: legacyFindings.security_issues.length,
        bugs: legacyFindings.bugs.length,
        suggestions: legacyFindings.suggestions.length,
        code_quality: legacyFindings.code_quality.length,
        acceptance_criteria: legacyFindings.acceptance_criteria.length,
        performance_issues: legacyFindings.performance_issues.length
      },
      completion_status: 'completed'
    }

    return {
      session_id: results.session_id,
      review_mode: results.review_type || 'comprehensive',
      branch_name: results.metadata.branch_name || 'unknown',
      pr_url: results.metadata.pr_url,
      timestamp: results.timestamp,
      structured_findings: legacyFindings,
      summary: legacySummary,
      metadata: {
        changed_files: results.metadata.changed_files || [],
        diff_summary: results.metadata.diff_summary || '',
        file_count: results.metadata.changed_files?.length || 0
      },
      jira_ticket: results.jira_ticket
    }
  }
  
  // Already legacy format
  return results as EnhancedReviewResults
}

type Severity = 'high' | 'medium' | 'low'

const categoryConfig = {
  acceptance_criteria: {
    label: 'Acceptance Criteria',
    icon: Target,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'dark:bg-green-950 dark:border-green-800',
    description: 'Compliance with Jira ticket requirements'
  },
  code_quality: {
    label: 'Code Quality',
    icon: CheckCircle,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'dark:bg-blue-950 dark:border-blue-800',
    description: 'Code style, patterns, and maintainability'
  },
  security_issues: {
    label: 'Security Issues',
    icon: Shield,
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'dark:bg-red-950 dark:border-red-800',
    description: 'Security vulnerabilities and risks'
  },
  performance_issues: {
    label: 'Performance Issues',
    icon: Gauge,
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'dark:bg-orange-950 dark:border-orange-800',
    description: 'Performance bottlenecks and optimizations'
  },
  bugs: {
    label: 'Potential Bugs',
    icon: Bug,
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'dark:bg-purple-950 dark:border-purple-800',
    description: 'Logic errors and potential issues'
  },
  suggestions: {
    label: 'Suggestions',
    icon: Lightbulb,
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'dark:bg-yellow-950 dark:border-yellow-800',
    description: 'Improvement recommendations'
  }
}

export const StructuredReviewViewer: React.FC<StructuredReviewViewerProps> = ({
  results,
  className,
  onFileClick,
  onExportResults,
  compact = false
}) => {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['security_issues', 'bugs']))
  const [severityFilter, setSeverityFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showFilesOnly, setShowFilesOnly] = useState(false)

  // Normalize the data format
  const normalizedResults = normalizeResults(results)
  const { structured_findings, summary, metadata, jira_ticket } = normalizedResults

  // Add comprehensive debugging for StructuredReviewViewer
  React.useEffect(() => {
    console.log('📋 STRUCTURED REVIEW VIEWER - Received results:', results)
    console.log('📋 STRUCTURED REVIEW VIEWER - Is new format?', isStructuredReviewResult(results))
    console.log('📋 STRUCTURED REVIEW VIEWER - Normalized results:', normalizedResults)
    console.log('📋 STRUCTURED REVIEW VIEWER - Structured findings:', structured_findings)
    console.log('📋 STRUCTURED REVIEW VIEWER - Summary:', summary)
    console.log('📋 STRUCTURED REVIEW VIEWER - Metadata:', metadata)
    
    if (structured_findings) {
      Object.entries(structured_findings).forEach(([category, findings]) => {
        console.log(`📋 STRUCTURED REVIEW VIEWER - ${category}:`, findings)
        if (Array.isArray(findings)) {
          findings.forEach((finding, index) => {
            if (!finding || typeof finding !== 'object') {
              console.error(`⚠️ STRUCTURED REVIEW VIEWER - Invalid finding in ${category}[${index}]:`, finding)
            } else if (!finding.text) {
              console.error(`⚠️ STRUCTURED REVIEW VIEWER - Missing text in ${category}[${index}]:`, finding)
            } else if (finding.text.length < 3) {
              console.warn(`⚠️ STRUCTURED REVIEW VIEWER - Very short text in ${category}[${index}]:`, finding.text)
            }
          })
        } else {
          console.error(`⚠️ STRUCTURED REVIEW VIEWER - ${category} is not an array:`, findings)
        }
      })
    }
  }, [results, normalizedResults, structured_findings, summary, metadata])

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  const expandAllCategories = () => {
    setExpandedCategories(new Set(Object.keys(categoryConfig)))
  }

  const collapseAllCategories = () => {
    setExpandedCategories(new Set())
  }

  const sanitizeFindings = (findings: any[]): ReviewFinding[] => {
    if (!Array.isArray(findings)) {
      console.warn('📋 SANITIZE - Findings is not an array:', findings)
      return []
    }
    
    const sanitizedFindings: ReviewFinding[] = []
    
    findings.forEach((finding, index) => {
      // Handle string findings
      if (typeof finding === 'string') {
        console.warn(`📋 SANITIZE - Converting string finding[${index}]:`, finding)
        sanitizedFindings.push({
          text: finding,
          severity: 'medium' as const,
          file: undefined,
          line: undefined,
          category: undefined,
          suggestion: undefined
        })
        return
      }
      
      // Handle null/undefined findings
      if (!finding || typeof finding !== 'object') {
        console.error(`📋 SANITIZE - Skipping invalid finding[${index}]:`, finding)
        return
      }
      
      // Handle objects without text
      if (!finding.text || typeof finding.text !== 'string') {
        console.warn(`📋 SANITIZE - Missing or invalid text in finding[${index}]:`, finding)
        // Try to extract text from other fields
        const extractedText = finding.message || finding.description || finding.title || 
                             String(finding).substring(0, 100) || `Invalid finding data [${index}]`
        sanitizedFindings.push({
          text: extractedText,
          severity: (finding.severity as 'high' | 'medium' | 'low') || 'medium',
          file: finding.file,
          line: finding.line,
          category: finding.category,
          suggestion: finding.suggestion
        })
        return
      }
      
      // Handle malformed text (too short, only special characters, etc.)
      const cleanText = finding.text.trim()
      if (cleanText.length < 3) {
        console.warn(`📋 SANITIZE - Very short text in finding[${index}]:`, cleanText)
        sanitizedFindings.push({
          text: `${cleanText} [Note: Original text was very short]`,
          severity: (finding.severity as 'high' | 'medium' | 'low') || 'low',
          file: finding.file,
          line: finding.line,
          category: finding.category,
          suggestion: finding.suggestion
        })
        return
      }
      
      // Handle findings with only special characters or markdown artifacts
      if (/^[*#\-\s]*$/.test(cleanText)) {
        console.warn(`📋 SANITIZE - Text contains only special characters in finding[${index}]:`, cleanText)
        sanitizedFindings.push({
          text: `[Formatting issue detected] ${cleanText}`,
          severity: (finding.severity as 'high' | 'medium' | 'low') || 'low',
          file: finding.file,
          line: finding.line,
          category: finding.category,
          suggestion: finding.suggestion
        })
        return
      }
      
      // Validate and clean severity
      const validSeverities: ('high' | 'medium' | 'low')[] = ['high', 'medium', 'low']
      const cleanSeverity = validSeverities.includes(finding.severity as 'high' | 'medium' | 'low') 
        ? (finding.severity as 'high' | 'medium' | 'low') 
        : 'medium'
      
      sanitizedFindings.push({
        text: cleanText,
        severity: cleanSeverity,
        file: finding.file,
        line: finding.line,
        category: finding.category,
        suggestion: finding.suggestion
      })
    })
    
    return sanitizedFindings
  }

  const filterFindings = (findings: ReviewFinding[]) => {
    const sanitizedFindings = sanitizeFindings(findings)
    
    return sanitizedFindings.filter(finding => {
      // Severity filter
      if (severityFilter !== 'all' && finding.severity !== severityFilter) {
        return false
      }
      
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase()
        const matchesText = finding.text.toLowerCase().includes(searchLower)
        const matchesFile = finding.file?.toLowerCase().includes(searchLower)
        const matchesSuggestion = finding.suggestion?.toLowerCase().includes(searchLower)
        
        if (!matchesText && !matchesFile && !matchesSuggestion) {
          return false
        }
      }
      
      // Files only filter
      if (showFilesOnly && !finding.file) {
        return false
      }
      
      return true
    })
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-950 dark:text-red-200 dark:border-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-200 dark:border-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-950 dark:text-green-200 dark:border-green-800'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-950 dark:text-gray-200 dark:border-gray-800'
    }
  }

  const handleCopyResults = () => {
    const text = JSON.stringify(results, null, 2)
    navigator.clipboard.writeText(text)
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTotalFindings = () => {
    return Object.values(structured_findings).reduce((total, findings) => total + findings.length, 0)
  }

  const getHighSeverityCount = () => {
    return Object.values(structured_findings)
      .flat()
      .filter(finding => finding.severity === 'high').length
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Review Summary Header */}
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-transparent dark:from-primary/10">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-primary text-white dark:bg-primary dark:text-white">
                  <FileText className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold">
                    Review Results - {normalizedResults.review_mode?.charAt(0).toUpperCase() + normalizedResults.review_mode?.slice(1) || 'Unknown'} Mode
                  </h3>
                  <p className="text-sm text-muted-foreground font-normal">
                    {normalizedResults.branch_name} • {formatTimestamp(normalizedResults.timestamp)}
                  </p>
                </div>
              </CardTitle>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyResults}
                className="flex items-center gap-2"
              >
                <Copy className="h-4 w-4" />
                Copy JSON
              </Button>
              {onExportResults && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onExportResults(results)}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Key Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{summary.files_changed}</div>
              <div className="text-xs text-muted-foreground">Files</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{getTotalFindings()}</div>
              <div className="text-xs text-muted-foreground">Findings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">{getHighSeverityCount()}</div>
              <div className="text-xs text-muted-foreground">High Priority</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{summary.categories.security_issues || 0}</div>
              <div className="text-xs text-muted-foreground">Security</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{summary.categories.bugs || 0}</div>
              <div className="text-xs text-muted-foreground">Bugs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">{summary.categories.suggestions || 0}</div>
              <div className="text-xs text-muted-foreground">Quality</div>
            </div>
          </div>

          {/* Jira Ticket Info */}
          {jira_ticket && (
            <div className="p-4 bg-muted/30 dark:bg-muted/50 rounded-lg border border-muted dark:border-muted">
              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                <Target className="h-4 w-4 text-green-600 dark:text-green-400" />
                Linked Jira Ticket
              </h4>
              <div className="text-sm space-y-1">
                <div><strong>{jira_ticket.ticket_id}:</strong> {jira_ticket.summary}</div>
                <div className="text-muted-foreground">
                  Status: {jira_ticket.status} • {jira_ticket.acceptance_criteria_count} Acceptance Criteria
                </div>
              </div>
            </div>
          )}

          {/* Changed Files */}
          {metadata.changed_files.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Changed Files ({metadata.changed_files.length})
              </h4>
              <div className="flex flex-wrap gap-1">
                {metadata.changed_files.slice(0, compact ? 3 : 10).map((file, index) => (
                  <Badge 
                    key={index} 
                    variant="outline" 
                    className="text-xs cursor-pointer hover:bg-muted"
                    onClick={() => onFileClick?.(file)}
                  >
                    {file.split('/').pop()}
                  </Badge>
                ))}
                {metadata.changed_files.length > (compact ? 3 : 10) && (
                  <Badge variant="secondary" className="text-xs">
                    +{metadata.changed_files.length - (compact ? 3 : 10)} more
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Filters and Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap items-center gap-4 mb-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search findings, files, or suggestions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <select
                value={severityFilter}
                onChange={(e) => setSeverityFilter(e.target.value as Severity)}
                className="px-3 py-1 text-sm border border-border rounded-md bg-background text-foreground dark:bg-muted dark:text-foreground dark:border-border"
              >
                <option value="all" className="bg-background dark:bg-muted text-foreground dark:text-foreground">All Severity</option>
                <option value="high" className="bg-background dark:bg-muted text-foreground dark:text-foreground">High Only</option>
                <option value="medium" className="bg-background dark:bg-muted text-foreground dark:text-foreground">Medium Only</option>
                <option value="low" className="bg-background dark:bg-muted text-foreground dark:text-foreground">Low Only</option>
              </select>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilesOnly(!showFilesOnly)}
              className={`flex items-center gap-2 ${showFilesOnly ? 'bg-primary/10 dark:bg-primary/20' : ''}`}
            >
              <FileText className="h-4 w-4" />
              Files Only
            </Button>
            
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={expandAllCategories}
                className="text-xs"
              >
                Expand All
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={collapseAllCategories}
                className="text-xs"
              >
                Collapse All
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Findings by Category */}
      <div className="space-y-4">
        {Object.entries(structured_findings).map(([categoryKey, findings]) => {
          const config = categoryConfig[categoryKey as keyof typeof categoryConfig]
          const filteredFindings = filterFindings(findings)
          const isExpanded = expandedCategories.has(categoryKey)
          
          if (filteredFindings.length === 0) return null

          return (
            <Card key={categoryKey} className={`border-l-4 ${config.bgColor}`}>
              <CardHeader 
                className="cursor-pointer"
                onClick={() => toggleCategory(categoryKey)}
              >
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <config.icon className={`h-5 w-5 ${config.color}`} />
                    <div>
                      <h3 className="text-lg font-medium">{config.label}</h3>
                      <p className="text-sm text-muted-foreground font-normal">
                        {config.description}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">
                      {filteredFindings.length}
                      {filteredFindings.length !== findings.length && (
                        <span className="text-muted-foreground"> / {findings.length}</span>
                      )}
                    </Badge>
                    {isExpanded ? (
                      <ChevronDown className="h-5 w-5 text-muted-foreground" />
                    ) : (
                      <ChevronRight className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              
              {isExpanded && (
                <CardContent>
                  <div className="space-y-3">
                    {filteredFindings.map((finding, index) => (
                      <div 
                        key={index}
                        className="p-4 rounded-lg border border-border dark:border-border bg-background dark:bg-background hover:bg-muted/30 dark:hover:bg-muted/30 transition-colors"
                      >
                        <div className="flex items-start justify-between gap-3 mb-2">
                          <div className="flex-1">
                            <div className="text-sm leading-relaxed">
                              {/* Render finding text with basic markdown support */}
                              {finding.text.split('\n').map((line, lineIndex) => {
                                // Handle markdown-style formatting
                                if (line.startsWith('**') && line.endsWith('**')) {
                                  return (
                                    <div key={lineIndex} className="font-semibold text-foreground">
                                      {line.replace(/\*\*/g, '')}
                                    </div>
                                  )
                                }
                                if (line.startsWith('### ')) {
                                  return (
                                    <div key={lineIndex} className="font-medium text-primary mt-2 mb-1">
                                      {line.replace('### ', '')}
                                    </div>
                                  )
                                }
                                if (line.trim() === '') {
                                  return <br key={lineIndex} />
                                }
                                return (
                                  <div key={lineIndex} className="text-foreground">
                                    {line}
                                  </div>
                                )
                              })}
                            </div>
                            {finding.suggestion && (
                              <div className="mt-2 p-3 border border-blue-200 rounded-lg dark:border-blue-800">
                                <div className="flex items-center gap-2 mb-1">
                                  <Lightbulb className="h-4 w-4 text-light-foreground dark:text-light-foreground" />
                                  <span className="text-sm font-medium text-light-foreground dark:text-light-foreground">Suggestion</span>
                                </div>
                                <div className="text-sm text-light-foreground dark:text-light-foreground">
                                  {finding.suggestion.split('\n').map((line, lineIndex) => (
                                    <div key={lineIndex}>{line}</div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                          
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getSeverityColor(finding.severity)}`}
                          >
                            {finding.severity.toUpperCase()}
                          </Badge>
                        </div>
                        
                        {finding.file && (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <FileText className="h-3 w-3" />
                            <button
                              onClick={() => onFileClick?.(finding.file!, finding.line)}
                              className="hover:text-primary dark:hover:text-primary hover:underline"
                            >
                              {finding.file}
                              {finding.line && `:${finding.line}`}
                            </button>
                            <ExternalLink className="h-3 w-3" />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          )
        })}
      </div>

      {/* Empty State */}
      {getTotalFindings() === 0 && (
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardContent className="pt-6 text-center">
            <CheckCircle className="h-12 w-12 text-green-600 dark:text-green-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-green-800 dark:text-green-200 mb-2">
              Excellent Work!
            </h3>
            <p className="text-sm text-green-700 dark:text-green-300">
              No issues found in this review. The code meets all quality standards.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}