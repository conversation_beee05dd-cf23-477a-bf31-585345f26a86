import React, { createContext, useContext, useState, useEffect } from 'react'
import type { ReactNode } from 'react'

export interface WorktreeStatus {
  isConfigured: boolean
  masterRepoPath: string | null
  isValid: boolean
  lastValidated: string | null
  loading: boolean
  error: string | null
}

interface WorktreeStatusContextType {
  status: WorktreeStatus
  refreshStatus: () => Promise<void>
  setMasterRepo: (path: string) => Promise<boolean>
  clearConfig: () => Promise<void>
}

const WorktreeStatusContext = createContext<WorktreeStatusContextType | undefined>(undefined)

export const useWorktreeStatus = () => {
  const context = useContext(WorktreeStatusContext)
  if (context === undefined) {
    throw new Error('useWorktreeStatus must be used within a WorktreeStatusProvider')
  }
  return context
}

interface WorktreeStatusProviderProps {
  children: ReactNode
}

export const WorktreeStatusProvider: React.FC<WorktreeStatusProviderProps> = ({ children }) => {
  const [status, setStatus] = useState<WorktreeStatus>({
    isConfigured: false,
    masterRepoPath: null,
    isValid: false,
    lastValidated: null,
    loading: true,
    error: null
  })

  const fetchStatus = async () => {
    try {
      console.log('🔄 WorktreeStatusContext: Fetching status...')
      setStatus(prev => ({ ...prev, loading: true, error: null }))
      
      const response = await fetch('http://localhost:5002/api/worktree/config', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors'
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch status: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('📥 WorktreeStatusContext: Received data:', data)
      
      if (data.success) {
        const config = data.config
        const newStatus = {
          isConfigured: !!(config.base_path && config.is_valid),
          masterRepoPath: config.base_path || null,
          isValid: config.is_valid || false,
          lastValidated: config.last_validated || null,
          loading: false,
          error: null
        }
        console.log('✅ WorktreeStatusContext: Setting new status:', newStatus)
        setStatus(newStatus)
      } else {
        throw new Error(data.error || 'Failed to fetch status')
      }
    } catch (err) {
      console.error('❌ WorktreeStatusContext: Error fetching status:', err)
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: err instanceof Error ? err.message : 'Unknown error',
        isConfigured: false,
        isValid: false
      }))
    }
  }

  const setMasterRepo = async (path: string): Promise<boolean> => {
    try {
      console.log('🔧 WorktreeStatusContext: setMasterRepo() called with path:', path)
      setStatus(prev => ({ ...prev, loading: true, error: null }))
      
      const response = await fetch('http://localhost:5002/api/worktree/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors',
        body: JSON.stringify({
          base_path: path,
          user_id: 'default'
        })
      })

      const data = await response.json()
      console.log('📥 WorktreeStatusContext: setMasterRepo response:', data)
      
      if (data.success && data.validation.is_valid) {
        const newStatus = {
          isConfigured: true,
          masterRepoPath: path,
          isValid: true,
          lastValidated: data.config.last_validated,
          loading: false,
          error: null
        }
        console.log('✅ WorktreeStatusContext: setMasterRepo setting status:', newStatus)
        setStatus(newStatus)
        return true
      } else {
        console.warn('⚠️ WorktreeStatusContext: setMasterRepo validation failed:', data.validation?.message)
        setStatus(prev => ({
          ...prev,
          loading: false,
          error: data.validation?.message || 'Configuration failed',
          isConfigured: false,
          isValid: false
        }))
        return false
      }
    } catch (err) {
      console.error('❌ WorktreeStatusContext: setMasterRepo error:', err)
      setStatus(prev => ({
        ...prev,
        loading: false,
        error: err instanceof Error ? err.message : 'Configuration failed',
        isConfigured: false,
        isValid: false
      }))
      return false
    }
  }

  const clearConfig = async (): Promise<void> => {
    try {
      console.log('🗑️ WorktreeStatusContext: clearConfig() called - THIS SHOULD NOT HAPPEN AUTOMATICALLY!')
      console.trace('clearConfig call stack')
      
      // Send empty config to backend
      await fetch('http://localhost:5002/api/worktree/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors',
        body: JSON.stringify({
          base_path: '',
          user_id: 'default'
        })
      })

      setStatus({
        isConfigured: false,
        masterRepoPath: null,
        isValid: false,
        lastValidated: null,
        loading: false,
        error: null
      })
    } catch (err) {
      console.error('Failed to clear config:', err)
    }
  }

  const refreshStatus = async () => {
    console.log('🔄 WorktreeStatusContext: refreshStatus() called')
    await fetchStatus()
  }

  useEffect(() => {
    fetchStatus()
  }, [])

  const value: WorktreeStatusContextType = {
    status,
    refreshStatus,
    setMasterRepo,
    clearConfig
  }

  return (
    <WorktreeStatusContext.Provider value={value}>
      {children}
    </WorktreeStatusContext.Provider>
  )
}