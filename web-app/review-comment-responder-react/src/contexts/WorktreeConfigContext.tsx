import React, { createContext, useContext, useState, useEffect } from 'react'
import type { ReactNode } from 'react'

interface WorktreeConfig {
  basePath: string
  isValid: boolean
  lastValidated: string
  userId: string
}

interface WorktreeConfigContextType {
  config: WorktreeConfig | null
  loading: boolean
  error: string | null
  updateConfig: (basePath: string) => Promise<boolean>
  validatePath: (path: string) => Promise<{ isValid: boolean; message: string }>
  refreshConfig: () => Promise<void>
}

const WorktreeConfigContext = createContext<WorktreeConfigContextType | undefined>(undefined)

export const useWorktreeConfig = () => {
  const context = useContext(WorktreeConfigContext)
  if (context === undefined) {
    throw new Error('useWorktreeConfig must be used within a WorktreeConfigProvider')
  }
  return context
}

interface WorktreeConfigProviderProps {
  children: ReactNode
}

export const WorktreeConfigProvider: React.FC<WorktreeConfigProviderProps> = ({ children }) => {
  const [config, setConfig] = useState<WorktreeConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchConfig = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('http://localhost:5001/api/worktree/config', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors'
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch config: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success) {
        setConfig(data.config)
      } else {
        throw new Error(data.error || 'Failed to fetch config')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      console.error('Failed to fetch worktree config:', err)
    } finally {
      setLoading(false)
    }
  }

  const updateConfig = async (basePath: string): Promise<boolean> => {
    try {
      setError(null)
      
      const response = await fetch('http://localhost:5001/api/worktree/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors',
        body: JSON.stringify({
          base_path: basePath,
          user_id: 'default'
        })
      })

      const data = await response.json()
      if (data.success) {
        setConfig(data.config)
        return true
      } else {
        setError(data.error || 'Failed to update config')
        return false
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      return false
    }
  }

  const validatePath = async (path: string): Promise<{ isValid: boolean; message: string }> => {
    try {
      const response = await fetch('http://localhost:5001/api/worktree/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors',
        body: JSON.stringify({ path })
      })

      const data = await response.json()
      if (data.success) {
        return data.validation
      } else {
        return { isValid: false, message: data.error || 'Validation failed' }
      }
    } catch (err) {
      return { 
        isValid: false, 
        message: err instanceof Error ? err.message : 'Validation error' 
      }
    }
  }

  const refreshConfig = async () => {
    await fetchConfig()
  }

  useEffect(() => {
    fetchConfig()
  }, [])

  const value: WorktreeConfigContextType = {
    config,
    loading,
    error,
    updateConfig,
    validatePath,
    refreshConfig
  }

  return (
    <WorktreeConfigContext.Provider value={value}>
      {children}
    </WorktreeConfigContext.Provider>
  )
}